##!/bin/bash
## File: .git/hooks/pre-merge-commit
#
## Get the target branch
#TARGET_BRANCH=$(git rev-parse --abbrev-ref HEAD)
#
## Only proceed if we're merging into main
#if [ "$TARGET_BRANCH" != "main" ]; then
#    exit 0
#fi
#
## Read the current version from package.json
#CURRENT_VERSION=$(node -p "require('./package.json').version")
#
## Split version into components
#IFS='.' read -r -a VERSION_PARTS <<< "$CURRENT_VERSION"
#MAJOR="${VERSION_PARTS[0]}"
#MINOR="${VERSION_PARTS[1]}"
#PATCH="${VERSION_PARTS[2]}"
#
## Increment patch version
#NEW_PATCH=$((PATCH + 1))
#NEW_VERSION="$MAJOR.$MINOR.$NEW_PATCH"
#
## Update package.json with new version
#node -e "
#    const fs = require('fs');
#    const package = require('./package.json');
#    package.version = '$NEW_VERSION';
#    fs.writeFileSync('./package.json', JSON.stringify(package, null, 2) + '\n');
#"
#
## Stage the modified package.json
#git add package.json
#
## Commit the version bump
#git commit -m "Set version to $NEW_VERSION"
#
## Echo the changes
#echo "Version bumped from $CURRENT_VERSION to $NEW_VERSION"
#
#exit 0