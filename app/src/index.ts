import "reflect-metadata";
import Fastify from 'fastify';
import dotenv from 'dotenv';

import AppDataSource from './config/database.js';
import seedDatabase from '@/seeders/seed-database.js';
import { sendSlackNotification } from '@/utils/system-notices.utils.js';
import {routerConfig} from "@/config/router.js";
dotenv.config();


const start = async () => {
	try {
		// Initialize database
		await AppDataSource.initialize();
		await AppDataSource.runMigrations();
		// Run database seeding
		try {
			await seedDatabase();
			console.log("Database seeding completed successfully");
		} catch (seedingError) {
			console.error("Error during database seeding:", seedingError);
		}
		await routerConfig(Fastify({
			logger: true
		}));
	} catch (err) {
		console.error(err);
		// const messErr =`💀 API DEAD!\n\n`;
		// await sendSlackNotification(messErr);
		// await sendSlackNotification(JSON.stringify(err));
		process.exit(1);
	}
};

start().catch((err) => {
	console.error(err);
	process.exit(1);
});