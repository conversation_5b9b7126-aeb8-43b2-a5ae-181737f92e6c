import { FastifyInstance } from 'fastify'
import { verifyToken } from '@/utils/jwt.utils.js'

let fastifyInstance: FastifyInstance

export const setupWebSocket = (fastify: FastifyInstance) => {
	fastifyInstance = fastify

	fastify.get('/ws', { websocket: true }, (connection, req) => {
		connection.socket.on('message', (message: { toString: () => string }) => {
			const { token } = JSON.parse(message.toString())
			try {
				const decoded = verifyToken(token)
				const userId = decoded.userId

				// Subscribe to user-specific channel
				connection.socket.join(`user-${userId}`)

				connection.socket.send(JSON.stringify({ message: 'Successfully subscribed to notifications' }))
			} catch (error) {
				connection.socket.send(JSON.stringify({ error: 'Invalid token' }))
				connection.socket.close()
			}
		})
	})
}

export const sendNotification = (userId: number, message: string) => {
	if (!fastifyInstance) {
		throw new Error('WebSocket server not initialized')
	}
	fastifyInstance.websocketServer.to(`user-${userId}`).emit('message', JSON.stringify({ message }))
}