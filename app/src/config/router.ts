import jwt from '@fastify/jwt';
import cookie from '@fastify/cookie';
import dotenv from 'dotenv';
import websocket from '@fastify/websocket';
import cors, { FastifyCorsOptions } from '@fastify/cors';
import fastifyAuth from "@fastify/auth";
import messageCollectorPlugin from "@/utils/plugins/fastify-message-collector.plugin.js";
import Fastify, {FastifyInstance} from "fastify";
import { setupWebSocket } from "@/websockets/websocket.js";
import {setupErrorHandlers} from "@/middleware/error.handler.middleware.js";

import {adminRouterRegister} from "@/routes/route-register.js";
import {sendSlackNotification} from "@/utils/system-notices.utils.js";
import {guestRouterRegister} from "@/routes/guest.route-register.js";


const port: number = parseInt(process.env.NODE_PORT || '3001', 10);
const host = '0.0.0.0';

export const routerConfig = async (fastify:FastifyInstance) => {
	try{
		// CORS configuration
		const corsOptions: FastifyCorsOptions = {
			// ... (keep your existing CORS configuration)
		};

		// Register plugins
		await fastify.register(messageCollectorPlugin);

		await fastify.register(jwt, {
			secret: process.env.JWT_SECRET || 'your-secret-key'
		});
		await fastify.register(cookie);
		await fastify.register(cors, corsOptions);
		await fastify.register(fastifyAuth);
		await fastify.register(websocket);

		// Setup WebSocket
		//	setupWebSocket(fastify);

		setupErrorHandlers(fastify);

		await adminRouterRegister(fastify);
		await guestRouterRegister(fastify);
		// Wait for fastify to be ready
		await fastify.ready();
		console.log("Fastify is ready");

		// Start listening
		await fastify.listen({
			port: port,
			host: host,
		});

		console.log(`Server listening on ${port}`);
		fastify.log.info(`Server listening on ${port}`);

		//send slack notification
		await sendSlackNotification(`🚀 API UP!\n\n${process.env.NODE_ENV} API is up and running on port ${port}`);

	} catch (err) {
		console.error(err);
		process.exit(1);
	}

}