// config/database.ts
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { DataSource, DataSourceOptions } from "typeorm";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const dataSourceOptions: DataSourceOptions = {
	type: 'postgres',
	host: process.env.POSTGRES_HOST,
	port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
	username: process.env.POSTGRES_USER || 'root',
	password: process.env.POSTGRES_PASSWORD || '',
	database: process.env.POSTGRES_DB || 'mydatabase',
	synchronize: true,
	logging: false,
	entities: [join(__dirname, '..', 'models', '*.{js,ts}')],
	migrations: [join(__dirname, '..', 'migrations', '*.{js,ts}')],

	// Connection pool and other settings in extra
	extra: {
		// Pool configuration
		poolSize: 20,                 // Maximum number of connections in pool
		connectionTimeoutMillis: 2000, // Maximum time to wait for connection
		idleTimeoutMillis: 30000,     // Time a connection can be idle before being closed
		maxUses: 7500,                // Number of times a connection can be used before being recycled
		statement_timeout: 60000,      // Maximum time for a query to execute (ms)
	},

	// Query cache settings
	cache: {
		type: "database",
		duration: 60000,              // Cache duration in milliseconds (1 minute)
		options: {
			max: 100                  // Maximum number of queries to cache
		}
	}
};

const dataSource = new DataSource(dataSourceOptions);

export default dataSource;