import {AccessType} from "@/interfaces/database.interface.js";

export interface CreateUserDTO {
	email: string;
	firstName: string;
	lastName: string;
	password: string;
	roleId: string|number;
}

export const createUserSchema = {
	body: {
		type: 'object',
		required: ['email', 'firstName', 'lastName'],
		properties: {
			email: { type: 'string', format: 'email' },
			firstName: { type: 'string', minLength: 1, maxLength: 100 },
			lastName: { type: 'string', minLength: 1, maxLength: 100 },
			password: {
				type: 'string',
				default: "3ss93fA!!!sdkjafd#aedflka34553DDDD2309adfa32_Fas3lkFck" // Set default to current
			}
		}
	}
};

export interface AssignUserToOperationDTO {
	operationId: string;
	accessType?: string;
}

export const assignUserToOperationSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id'],
	},
	body: {
		type: 'object',
		properties: {
			operationId: { type: 'number' },
			accessType: { type: 'string', enum: Object.values(AccessType) }
		},
		required: ['operationId', 'accessType'],
	}
};

export interface UpdateUserOperationsAccessDTO {
	accessType?: AccessType;
}

export const updateUserOperationsAccessSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			operationId: { type: 'string' }
		},
		required: ['id', 'operationId'],
	},
	body: {
		type: 'object',
		properties: {
			accessType: { type: 'string', enum: Object.values(AccessType) }
		},
		required: ['accessType'],
	}
};

export interface AssignRolesBodyDTO {
	roleIds: string[];
}

export const assignRolesSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		required: ['roleIds'],
		properties: {
			roleIds: { 
				type: 'array',
				items: { type: 'string' },
				minItems: 1
			}
		}
	}
};

export const removeRoleSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			roleId: { type: 'string' }
		},
		required: ['id', 'roleId']
	}
};

export interface UpdateUserDTO {
	firstName?: string;
	lastName?: string;
	avatarPath?: string;
	password?: string;
	oldPassword?: string;
}

export const updateUserSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			firstName: { type: 'string', minLength: 1, maxLength: 100 },
			lastName: { type: 'string', minLength: 1, maxLength: 100 },
			email: { type: 'string', format: 'email' },
			avatarPath: { type: 'string' },
		},
		additionalProperties: false
	}
};

export interface UpdateUserPasswordDTO {
	password: string;
	oldPassword: string;
}

export const updateUserPasswordSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			password: { type: 'string', minLength: 8 },
			oldPassword: { type: 'string', minLength: 8 }
		}
	}
};

export interface RefreshTokenDTO {
	refreshToken: string;
}


export interface getUserProfileParamsDTO {
	id: string;
}

export const getUserProfileSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	}
};

export interface LoginUserDTO {
	email: string;
	password: string;
	organizationId?: string;
}

export const loginSchema = {
	body: {
		type: 'object',
		required: ['email', 'password'],
		properties: {
			email: { type: 'string', format: 'email' },
			password: { type: 'string', minLength: 8 },
			organizationId: {type: 'string'}
		}
	}
};

export const refreshTokenSchema = {
	body: {
		type: 'object',
		required: ['refreshToken'],
		properties: {
			refreshToken: { type: 'string' }
		}
	}
};

export interface ActivateUserParamsDTO {
	hashCode: string;
}

export interface ActivateUserBodyDTO {
	firstName: string;
	lastName: string;
	password: string;
}

export const activateSchema = {
	params: {
		type: 'object',
		properties: {
			hashCode: { type: 'string', minLength: 4, maxLength: 64 }
		},
		required: ['hashCode']
	},
	body: {
		type: 'object',
		properties: {
			firstName: { type: 'string', minLength: 1, maxLength: 100 },
			lastName: { type: 'string', minLength: 1, maxLength: 100 },
			password: {
				type: 'string',
				minLength: 8,
				pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$'
			}
		},
		required: ['firstName', 'lastName', 'password']
	}
};

export interface RequestPasswordResetBodyDTO {
	email: string;
}

export const requestPasswordResetSchema = {
	body: {
		type: 'object',
		required: ['email'],
		properties: {
			email: { type: 'string', format: 'email' }
		}
	}
};

export interface ResetPasswordBodyDTO {
	email: string;
	resetToken: string;
	newPassword: string;
}

export const resetPasswordSchema = {
	body: {
		type: 'object',
		required: ['email', 'resetToken', 'newPassword'],
		properties: {
			email: { type: 'string', format: 'email' },
			resetToken: { type: 'string' },
			newPassword: { type: 'string', minLength: 8 }
		}
	}
};

export const switchOrgSchema = {
    params: {
        type: 'object',
        properties: {
            id: { type: 'string' }
        },
        required: ['id'],
    },
    body : {
        type: 'object',
        required: ['newOrgId'],
        properties: {
            newOrgId: { type: 'string'}
        }
    }
}

export const organizationSchemas = {
    params: {
        type: 'object',
        properties: {
            id: { type: 'string' }
        },
        required: ['id'],
    }
}

export const LoginBody = {
	type: 'object',
	required: ['email', 'password'],
	properties: {
		email: { type: 'string', format: 'email' },
		password: { type: 'string', minLength: 8 }
	}
};

