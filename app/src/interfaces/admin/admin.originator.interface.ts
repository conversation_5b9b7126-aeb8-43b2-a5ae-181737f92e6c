// export interface Originator{
// 	id: string | number;
// 	title: string;
// 	designation: string;
// 	description: string;
// 	operationId: number;
// 	contactDetails: string;
// 	operation: Operation;
// }


export interface CreateOriginatorDTO {
	title: string;
	contactDetails: string;
	description: string;
	organizationId?: number|string;
}


export const createOriginatorSchema = {
	body: {
		type: 'object',
		required: ['title'],
		properties: {
			title: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 1000 },
			contactDetails: { type: 'string', maxLength: 1000 },
		}
	}
};

export interface UpdateOriginatorDTO {
	title: string;
	description: string;
	contactDetails: string;
}

export const updateOriginatorSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			title: { type: 'string', minLength: 1, maxLength: 255 },
			contactDetails: { type: 'string', maxLength: 1000 },
			description: { type: 'string', minLength: 1, maxLength: 1000 },
		}
	}
};