// src/interfaces/rules/rules.isr-track.interface.ts

import {
	ISRStatus,
	Priority,
	BorderType,
	GeometryType,
	RGBAColor,
	SupportedGeometry,
	EGeometryType
} from '@/interfaces/database.interface.js';
import { pointSchema, polygonSchema, lineStringSchema, rgbaColorSchema } from './admin.aoi.interface.js';

// DTOs
export interface CreateISRTrackDTO {
	label?: string;
	operationId: number;
	status?: ISRStatus;
	priority?: Priority;
	commenceAt?: Date;
	concludeAt?: Date;
	ltiovDate?: Date;
	collectionCapabilityIds?: number[];
	mapElement?: {
		element: SupportedGeometry;
		elementType: GeometryType;
		elementColor?: RGBAColor;
		borderType?: BorderType;
		borderThickness?: number;
		borderColor?: RGBAColor;
	};
}

export interface UpdateISRTrackDTO {
	label?: string;
	status?: ISRStatus;
	priority?: Priority;
	commenceAt?: Date;
	concludeAt?: Date;
	ltiovDate?: Date;
	collectionCapabilityIds?: number[];
	mapElement?: {
		element?: SupportedGeometry;
		elementType?: GeometryType;
		elementColor?: RGBAColor;
		borderType?: BorderType;
		borderThickness?: number;
		borderColor?: RGBAColor;
	};
}

export interface AssignAssetsToISRTrackDTO {
	assetIds: number[];
}

// Schemas for validation
export const createISRTrackSchema = {
	body: {
		type: 'object',
		required: ['operationId'],
		properties: {
			label: {
				type: 'string',
				nullable: true,
				maxLength: 255
			},
			operationId: {
				type: 'number',
				description: 'ID of the operation this ISR Track is associated with'
			},
			status: {
				type: 'string',
				enum: Object.values(ISRStatus),
				nullable: true
			},
			priority: {
				type: 'string',
				enum: Object.values(Priority),
				nullable: true
			},
			commenceAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			concludeAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			ltiovDate: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			mapElement: {
				type: 'object',
				required: ['element', 'elementType', 'elementColor'],
				properties: {
					element: {
						oneOf: [pointSchema, polygonSchema, lineStringSchema],
					},
					elementType: {
						type: 'string',
						enum: Object.values(EGeometryType)
					},
					elementColor: rgbaColorSchema,
					borderType: {
						type: 'string',
						enum: Object.values(BorderType)
					},
					borderThickness: {
						type: 'number',
						minimum: 0
					},
					borderColor: rgbaColorSchema,
				}
			}
		},
		additionalProperties: false
	}
};

export const updateISRTrackSchema = {
	params: {
		type: 'object',
		required: ['id'],
		properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		properties: {
			label: {
				type: 'string',
				nullable: true,
				maxLength: 255
			},
			status: {
				type: 'string',
				enum: Object.values(ISRStatus),
				nullable: true
			},
			priority: {
				type: 'string',
				enum: Object.values(Priority),
				nullable: true
			},
			commenceAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			concludeAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			ltiovDate: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			mapElement: {
				type: 'object',
				required: [],
				properties: {
					element: {
						oneOf: [pointSchema, polygonSchema, lineStringSchema],
					},
					elementType: {
						type: 'string',
						enum: Object.values(EGeometryType)
					},
					elementColor: rgbaColorSchema,
					borderType: {
						type: 'string',
						enum: Object.values(BorderType)
					},
					borderThickness: {
						type: 'number',
						minimum: 0
					},
					borderColor: rgbaColorSchema,
				}
			}
		},
		additionalProperties: false
	}
};

// Error messages
export const schemaErrorMessages = {
	dates: {
		invalidFormat: 'Dates must be in ISO 8601 format',
		invalidRange: 'Conclude date must be after commence date',
		ltiovInvalid: 'LTIOV date must be between commence and conclude dates'
	},
	collectionCapabilities: {
		invalidIds: 'One or more collection capability IDs are invalid',
		notFound: 'One or more collection capabilities not found'
	},
	mapElement: {
		invalidGeometry: 'Invalid geometry type or format',
		invalidColor: 'Invalid color format',
		invalidBorder: 'Invalid border configuration'
	},
	general: {
		missingRequired: 'Missing required field',
		invalidEnum: 'Invalid enumeration value',
		invalidType: 'Invalid type provided'
	}
};

export const assignAssetsToIsrTrackSchema = {
	params: {
		type: 'object',
		required: ['id'],
		properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		required: ['assetId', 'missionId'],
		properties: {
			assetId: {
				type: 'number',
			},
            missionId: {
                type: 'number'
            }
		}
	}
}

export const deleteAssetFromISRTrackSchema = {
    params: {
        type: 'object',
        required: ['id', 'assetId'],
        properties: {
            id: { type: 'string' },
            assetId: { type: 'string' }
        }
    }
}