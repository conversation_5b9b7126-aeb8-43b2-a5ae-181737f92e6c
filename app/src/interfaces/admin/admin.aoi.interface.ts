// src/interfaces/rules/rules.aoi.interface.ts
import { Point, Polygon, LineString } from 'geojson';
import {
	BorderType,
	EGeometryType,
	GeometryType,
	RGBAColor,
	SupportedGeometry
} from '@/interfaces/database.interface.js';

export interface CreateAoiDTO {
	description?: string;
	name: string;
	zoom?: number;
	operationId: string;
	isApproved?: boolean;
	isTargetable?: boolean;
	mapElement: {
		element: SupportedGeometry;
		elementType: string;
		elementColor?: RGBAColor;
		borderType?: BorderType;
		borderThickness?: number;
		borderColor?: RGBAColor;
	};
}

export interface UpdateAoiDTO {
	name?: string;
	description?: string;
	zoom?: number;
	mapElement?: {
		element?: SupportedGeometry;
		elementType?: GeometryType;
		elementColor?: RGBAColor;
		borderType?: BorderType;
		borderThickness?: number;
		borderColor?: RGBAColor;
	};
}

export interface ApproveAoiDTO {
	isApproved: boolean;
}

export interface ToTAIAoiDTO {
	isTargetable: boolean;
}

export interface SearchAoiDTO {
	searchTerm: string;
}

export interface ApproveAoiRequest {
	Params: {
		id: string;
	};
	Body: ApproveAoiDTO;
}

export interface ToTAIAoiRequest {
	Params: {
		id: string;
	};
	Body: ToTAIAoiDTO;
}

// JSON Schema definitions for validation
export const pointSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['Point'] },
		coordinates: {
			type: 'array',
			items: { type: 'number' },
			minItems: 2,
			maxItems: 2,
		},
	},
};

export const polygonSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['Polygon'] },
		coordinates: {
			type: 'array',
			items: {
				type: 'array',
				items: {
					type: 'array',
					items: { type: 'number' },
					minItems: 2,
				},
				minItems: 3,
			},
		},
	},
};

export const lineStringSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['LineString'] },
		coordinates: {
			type: 'array',
			items: {
				type: 'array',
				items: { type: 'number' },
				minItems: 2,
			},
			minItems: 2,
		},
	},
};


export const rgbaColorSchema = {
	type: 'array',
	items: { type: 'integer', minimum: 0, maximum: 255 },
	minItems: 4,
	maxItems: 4,
};

export const createAoiSchema = {
	body: {
		type: 'object',
		required: ['name', 'operationId', 'mapElement'],
		properties: {
			name: { type: 'string' },
			description: { type: 'string', nullable: true },
			operationId: { type: 'string' },
			zoom: { type: 'number', nullable: true },
			mapElement: {
				type: 'object',
				required: ['element', 'elementType', 'elementColor'],
				properties: {
					element: {
						oneOf: [pointSchema, polygonSchema, lineStringSchema],
					},
					elementType: { type: 'string', enum: Object.values(EGeometryType) },
					elementColor: rgbaColorSchema,
					borderType: { type: 'string', enum: Object.values(BorderType) },
					borderThickness: { type: 'number', minimum: 0 },
					borderColor: rgbaColorSchema,
				},
			},
		},
	},
};

export const updateAoiSchema = {
	body: {
		type: 'object',
		required: [],
		properties: {
			name: { type: 'string' },
			description: { type: 'string', nullable: true },
			mapElement: {
				type: 'object',
				required: [],
				properties: {
					element: { oneOf: [pointSchema, polygonSchema, lineStringSchema] },
					elementType: { type: 'string', enum: Object.values(EGeometryType) },
					elementColor: rgbaColorSchema,
					borderType: { type: 'string', enum: Object.values(BorderType) },
					borderThickness: { type: 'number', minimum: 0 },
					borderColor: rgbaColorSchema,
				},
			},
		},
	},
};

export const approveAoiSchema = {
	params: {
		type: 'object',
		required: ['id'],
		properties: {
			id: { type: 'string' },
		},
	},
	body: {
		type: 'object',
		required: ['isApproved'],
		properties: {
			isApproved: { type: 'boolean' },
		},
		additionalProperties: false,
	},
};

export const toTAIAoiSchema = {
	params: {
		type: 'object',
		required: ['id'],
		properties: {
			id: { type: 'string' },
		},
		additionalProperties: false,
	},
	body: {
		type: 'object',
		required: ['isTargetable'],
		properties: {
			isTargetable: { type: 'boolean' },
		},
		additionalProperties: false,
	},
};