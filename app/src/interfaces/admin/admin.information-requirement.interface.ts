// src/interfaces/rules/rules.information-requirement.interface.ts

import { Priority } from "@/interfaces/database.interface.js";

export interface CreateInformationRequirementDTO {
	originator: string;
	informationRequirement: string;
	ltiovDate: Date;
	priority: Priority;
	pirId: number|string;
}

export interface UpdateInformationRequirementDTO {
	originator?: string;
	informationRequirement?: string;
	ltiovDate?: Date;
	priority?: Priority;
	isAnswered?: boolean;
}

export interface SearchInformationRequirementsDTO {
	searchTerm: string;
}

export const searchInformationRequirementsSchema = {
	querystring: {
		type: 'object',
		properties: {
			searchTerm: { type: 'string', minLength: 1, maxLength: 100 }
		}
	}
};

export const createInformationRequirementSchema = {
	body: {
		type: 'object',
		required: ['pirId', 'originator', 'informationRequirement', 'ltiovDate', 'priority'],
		properties: {
			pirId: { type: 'number' },
			originator: { type: 'string', minLength: 1, maxLength: 255 },
			informationRequirement: { type: 'string', minLength: 1, maxLength: 1200 },
			ltiovDate: { type: 'string', format: 'date-time' },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			}
		}
	}
};

export const updateInformationRequirementSchema = {
	body: {
		type: 'object',
		properties: {
			originator: { type: 'string', minLength: 1, maxLength: 255 },
			informationRequirement: { type: 'string', minLength: 1, maxLength: 1200 },
			ltiovDate: { type: 'string', format: 'date-time' },
			isAnswered: { type: 'boolean' },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			}
		}
	}
};