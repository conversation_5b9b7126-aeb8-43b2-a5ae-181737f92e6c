// src/interfaces/rules/rules.pir.interface.ts
import { Priority } from "@/interfaces/database.interface.js";


export interface CreatePirDTO {
	question: string;
	description?: string;
	isActive?: boolean;
	operationId: string;
	priority?: Priority;
}

export interface UpdatePirDTO {
	question?: string;
	description?: string;
	isActive?: boolean;
	priority?: Priority;
	originator?: string;
}

export interface SearchPirsDTO {
	searchTerm: string;
}

export const createPirSchema = {
	body: {
		type: 'object',
		required: ['question', 'operationId', 'isActive'],
		properties: {
			operationId: { type: 'string' },
			question: { type: 'string', minLength: 1, maxLength: 1200 },
			isActive: { type: 'boolean' },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			}
		}
	}
};

export const updatePirSchema = {
	body: {
		type: 'object',
		properties: {
			question: { type: 'string', minLength: 1, maxLength: 1200 },
			description: { type: 'string', minLength: 1, maxLength: 1200 },
			isActive: { type: 'boolean' },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			}
		}
	}
};