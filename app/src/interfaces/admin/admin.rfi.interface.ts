import {Priority, RFIStatus} from "@/interfaces/database.interface.js";

export interface CreateRFIDTO {
	title: string;
	operationId: number;
	originatorId: number;
	ltiovDate: Date;
	priority: Priority;
	checkSource: boolean;
}


export const createRFISchema = {
	body: {
		type: 'object',
		required: ['title', 'operationId', 'originatorId', 'ltiovDate', 'priority', 'checkSource'],
		properties: {
			title: { type: 'string', minLength: 1, maxLength: 255 },
			operationId: { type: 'number' },
			originatorId: { type: 'number' },
			ltiovDate: { type: 'string', format: 'date-time' },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			},
			checkSource: { type: 'boolean' },
		}
	}
};

export interface UpdateRFIDTO {
	title: string;
	ltiovDate: Date;
	priority: Priority;
	status: RFIStatus;
	checkSource: boolean;
}

export const updateRFISchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			title: { type: 'string', minLength: 1, maxLength: 255 },
			ltiovDate: { type: 'string', format: 'date-time' },
			status: {
				type: 'string',
				enum: Object.values(RFIStatus)
			},
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			},
			checkSource: { type: 'boolean' },
		}
	}
};