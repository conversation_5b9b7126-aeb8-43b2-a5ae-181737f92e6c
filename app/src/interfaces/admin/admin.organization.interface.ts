
export interface CreateOrganizationDTO {
	name: string;
	description: string;
	isMilitary: boolean;
	logoUrl: string;
	isoCountryCode?: string;
	sovereignty: string;
}

export const createOrganizationSchema = {
	body: {
		type: 'object',
		required: ['name', 'description', 'isMilitary', 'logoUrl', 'sovereignty'],
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 255 },
			isMilitary: { type: 'boolean' },
			logoUrl: { type: 'string', minLength: 1, maxLength: 255 },
			isoCountryCode: { type: 'string', minLength: 2, maxLength: 2 },
			sovereignty: { type: 'string', minLength: 1, maxLength: 255 }
		}
	}
};

export interface UpdateOrganizationDTO extends Partial<CreateOrganizationDTO> {}

export const updateOrganizationSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 255 },
			isMilitary: { type: 'boolean' },
			logoUrl: { type: 'string', minLength: 1, maxLength: 255 },
			isoCountryCode: { type: 'string', minLength: 2, maxLength: 2 },
			sovereignty: { type: 'string', minLength: 1, maxLength: 255 }
		}
	}
};
