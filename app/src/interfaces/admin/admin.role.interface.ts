
export interface CreateRoleDTO {
	roleName: string;
	roleDescription: string;
	unitLevel: string;
    roleType: string;
	staffDesignation: string;
	functionalArea: string;
	rank: string;
	isCommandRole: boolean;
	isStaffRole: boolean;
	typicalUnitSize: number;
	serviceBranch: string;
	countryOrOrganization: string;
	superiorRoleIds: number[] | [];
	subordinateRoleIds: number[] | [];
	organizationId?: number;
}


//update role
export interface UpdateRoleDTO {
	roleName: string;
	roleDescription: string;
	unitLevel: string;
	staffDesignation: string;
	functionalArea: string;
	rank: string;
	isCommandRole: boolean;
	isStaffRole: boolean;
	typicalUnitSize: number;
	serviceBranch: string;
	countryOrOrganization: string;
}

export const CreateRoleBodySchema = {
	body: {
		type: 'object',
			required: ['roleName', 'roleDescription', 'staffDesignation'],
			properties: {
			roleName: { type: 'string' },
			roleDescription: { type: 'string' },
			unitLevel: { type: 'string' },
			staffDesignation: { type: 'string' },
			functionalArea: { type: 'string' },
			rank: { type: 'string' },
			isCommandRole: { type: 'boolean' },
			isStaffRole: { type: 'boolean' },
			typicalUnitSize: { type: 'number' },
			serviceBranch: { type: 'string' },
			countryOrOrganization: { type: 'string' },
			organizationId: { type: 'string' },
		}
	}

}

export const UpdateRoleBodySchema = {
	params: {
		type: 'object',
			properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
			properties: {
			roleName: { type: 'string' },
			roleDescription: { type: 'string' },
			unitLevel: { type: 'string' },
			staffDesignation: { type: 'string' },
			functionalArea: { type: 'string' },
			rank: { type: 'string' },
			isCommandRole: { type: 'boolean' },
			isStaffRole: { type: 'boolean' },
			typicalUnitSize: { type: 'number' },
			serviceBranch: { type: 'string' },
			countryOrOrganization: { type: 'string' },
			organizationId: { type: 'string' }
		}
	}
};


export const UpdateSuperiorRoleSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		required: ['superiorRoleIds'],
		properties: {
			superiorRoleIds: {
				type: 'array',
				items: { type: 'number' }
			}
		}
	}
}

export const UpdateSubordinateRoleSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		required: ['subordinateRoleIds'],
		properties: {
			subordinateRoleIds: {
				type: 'array',
				items: { type: 'number' }
			}
		}
	}
}

export const RemoveSubordinateRoleSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			roleId: { type: 'string' }
		}
	}
}

export const RemoveSuperiorRoleSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			roleId: { type: 'string' }
		}
	}
}