export const createEngagedMissionAssetSchema = {
    missionId: {
        type: 'string',
        required: true,
    },
    assetId: {
        type: 'string',
        required: true,
    },
    isrTrackId: {
        type: 'string',
        required: true,
    },
};

export interface CreateEngagedMissionAssetDTO {
    missionId: number;
    assetId: number;
    isrTrackId: number;
}

export interface UpdateEngagedMissionAssetDTO {
    startAt: Date;
    endAt: Date;
}

export const updateEngagedMissionAssetSchema = {
   startAt: {
        type: 'string',
        required: true,
    },
    endAt: {
        type: 'string',
        required: true,
    },
};