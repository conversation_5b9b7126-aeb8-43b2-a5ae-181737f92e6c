export type PlatformType = 'space' | 'air' | 'land' | 'sea' | 'hybrid' | 'other';

export interface CreatePlatformDTO {
	name: string;
	countryIsoCode: string;
	type: PlatformType;
	description?: string;
	designation?: string;
	combatRadius?: number;
	footprintArea?: number;
	hasCollectionCapability?: boolean;
	configuration?: any;
	quantity?: number;
}

export const createPlatformSchema = {
	body: {
		type: 'object',
		required: ['name', 'countryIsoCode', 'type'],
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			countryIsoCode: { type: 'string', minLength: 2, maxLength: 3 },
			type: { type: 'string', enum: ['space', 'air', 'land', 'sea', 'hybrid', 'other'] },
			description: { type: 'string', maxLength: 1000 },
			combatRadius: { type: 'number' },
			footprintArea: { type: 'number' },
			hasCollectionCapability: { type: 'boolean' },
			configuration: { type: 'object' },
			quantity: { type: 'number' }
		}
	}
};

export interface UpdatePlatformDTO {
	name?: string;
	countryIsoCode?: string;
	type?: PlatformType;
	description?: string;
	combatRadius?: number;
	footprintArea?: number;
	hasCollectionCapability?: boolean;
	configuration?: any;
	quantity?: number;
}

export const updatePlatformSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			countryIsoCode: { type: 'string', minLength: 2, maxLength: 3 },
			type: { type: 'string', enum: ['space', 'air', 'land', 'sea', 'hybrid', 'other'] },
			description: { type: 'string', maxLength: 1000 },
			combatRadius: { type: 'number' },
			footprintArea: { type: 'number' },
			hasCollectionCapability: { type: 'boolean' },
			configuration: { type: 'object' },
			quantity: { type: 'number' }
		}
	}
};

export interface SearchPlatformsDTO {
	searchTerm?: string;
	countryIsoCode?: string;
	type?: string;
}

export const searchPlatformsSchema = {

		querystring: {
			type: 'object',
			properties: {
				searchTerm: {
					type: 'string',
					minLength: 1,
					maxLength: 100
				},
				countryIsoCode: {
					type: 'string',
					minLength: 2,
					maxLength: 2
				},
				platformType: {
					type: 'string',
					minLength: 1,
					maxLength: 100
				}
			}
		}

};

export interface platformSearchQuery {
	name?: string;
	countryIsoCode?: string;
	type?: string;
}

export interface GetPlatformDTO {
	id: string;
}

export const getPlatformSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	}
};

