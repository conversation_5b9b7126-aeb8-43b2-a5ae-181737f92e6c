export interface Distance {
	unitCode: string;
	value: number;
}

export interface Bearing {
	unitCode: string;
	value: number;
}

export interface LocationProperties {
	city: string;
	state: string;
	distance: Distance;
	bearing: Bearing;
}

export interface DewPoint {
	unitCode: string;
	value: number;
}

export interface RelativeHumidity {
	unitCode: string;
	value: number;
}

export interface PrecipitationProbability {
	unitCode: string;
	value: number | null;
}

export interface WeatherValue<T> {
	validTime: string;
	value: T;
}

export interface WeatherValueArray<T> extends WeatherValue<T[]> {
	coverage?: string;
	weather?: string;
	intensity?: string;
	visibility?: {
		unitCode: string;
		value: number | null;
	};
	attributes?: string[];
}

export interface WeatherHazard {
	phenomenon: string;
	significance: string;
	event_number: number | null;
}