import { Priority, ISRStatus } from "@/interfaces/database.interface.js";

// Basic coordinate types
type Position = number[];  // [longitude, latitude]
type LineStringCoordinates = Position[];  // [[lng, lat], [lng, lat], ...]
type PolygonCoordinates = LineStringCoordinates[];  // Array of LinearRings
type MultiPolygonCoordinates = PolygonCoordinates[];

// Individual geometry types
interface PointGeometry {
	type: "Point";
	coordinates: Position;
}

interface LineStringGeometry {
	type: "LineString";
	coordinates: LineStringCoordinates;
}

interface PolygonGeometry {
	type: "Polygon";
	coordinates: PolygonCoordinates;
}

// GeometryCollection type
export interface GeometryCollection {
	type: "GeometryCollection";
	geometries: Array<PointGeometry | LineStringGeometry | PolygonGeometry>;
}

// Point specific interface
interface Point {
	type: "Point";
	coordinates: [number, number];  // [longitude, latitude]
}

// Create DTO
export interface CreateGlobalISRDTO {
	assetId: number;
	label?: string;
	type?: "line" | "point" | "polygon";
	coordinates?: GeometryCollection;
	zoom?: number;
	centerCoordinates?: Point;
	priority?: Priority;
	commenceAt?: Date;
	concludeAt?: Date;
	ltiovDate?: Date;
}

// Update DTO
export interface UpdateGlobalISRDTO {
	label?: string;
	type?: "line" | "point" | "polygon";
	coordinates?: GeometryCollection;
	zoom?: number;
	centerCoordinates?: Point;
	priority?: Priority;
	commenceAt?: Date;
	concludeAt?: Date;
	ltiovDate?: Date;
}

// Schema for coordinate validation
const coordinateSchema = {
	point: {
		type: 'array',
		items: { type: 'number' },
		minItems: 2,
		maxItems: 2
	},
	lineString: {
		type: 'array',
		items: {
			type: 'array',
			items: { type: 'number' },
			minItems: 2,
			maxItems: 2
		},
		minItems: 2
	},
	polygon: {
		type: 'array',
		items: {
			type: 'array',
			items: {
				type: 'array',
				items: { type: 'number' },
				minItems: 2,
				maxItems: 2
			},
			minItems: 4 // Minimum points to form a closed polygon
		},
		minItems: 1 // At least one ring
	}
};

// Create Schema
export const createGlobalISRSchema = {
	body: {
		type: 'object',
		required: ['assetId', 'operationId', 'type'],
		properties: {
			assetId: {
				type: 'number',
				description: 'ID of the asset associated with this GlobalISR'
			},
			label: {
				type: 'string',
				nullable: true,
				maxLength: 255,
				description: 'Optional label for the GlobalISR'
			},
			operationId: {
				type: 'number',
				description: 'ID of the operation this GlobalISR is associated with'
			},
			type: {
				type: 'string',
				enum: ['line', 'point', 'polygon'],
				description: 'Geometry type of the GlobalISR'
			},
			// coordinates: {
			// 	type: 'object',
			// 	nullable: true,
			// 	properties: {
			// 		type: {
			// 			type: 'string',
			// 			enum: ['GeometryCollection']
			// 		},
			// 		geometries: {
			// 			type: 'array',
			// 			items: {
			// 				type: 'object',
			// 				properties: {
			// 					type: {
			// 						type: 'string',
			// 						enum: ['Point', 'LineString', 'Polygon']
			// 					},
			// 					coordinates: {
			// 						oneOf: [
			// 							coordinateSchema.point,
			// 							coordinateSchema.lineString,
			// 							coordinateSchema.polygon
			// 						]
			// 					}
			// 				},
			// 				required: ['type', 'coordinates']
			// 			}
			// 		}
			// 	},
			// 	required: ['type', 'geometries']
			// },
			// zoom: {
			// 	type: 'number',
			// 	nullable: true,
			// 	minimum: 0,
			// 	maximum: 22,
			// 	description: 'Map zoom level'
			// },
			// centerCoordinates: {
			// 	type: 'object',
			// 	nullable: true,
			// 	properties: {
			// 		type: {
			// 			type: 'string',
			// 			enum: ['Point']
			// 		},
			// 		coordinates: {
			// 			type: 'array',
			// 			items: { type: 'number' },
			// 			minItems: 2,
			// 			maxItems: 2,
			// 			description: '[longitude, latitude]'
			// 		}
			// 	},
			// 	required: ['type', 'coordinates']
			// },
			priority: {
				type: 'string',
				enum: Object.values(Priority),
				nullable: true,
				description: 'Priority level of the GlobalISR'
			},
			commenceAt: {
				type: 'string',
				format: 'date-time',
				nullable: true,
				description: 'Start date and time'
			},
			concludeAt: {
				type: 'string',
				format: 'date-time',
				nullable: true,
				description: 'End date and time'
			},
			ltiovDate: {
				type: 'string',
				format: 'date-time',
				nullable: true,
				description: 'Latest time information of value'
			}
		},
		additionalProperties: false
	}
};

// Update Schema
export const updateGlobalISRSchema = {
	params: {
		type: 'object',
		properties: {
			id: {
				type: 'string',
				description: 'GlobalISR ID to update'
			}
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			label: {
				type: 'string',
				nullable: true,
				maxLength: 255
			},
			type: {
				type: 'string',
				enum: ['line', 'point', 'polygon']
			},
			coordinates: {
				type: 'object',
				nullable: true,
				properties: {
					type: {
						type: 'string',
						enum: ['GeometryCollection']
					},
					geometries: {
						type: 'array',
						items: {
							type: 'object',
							properties: {
								type: {
									type: 'string',
									enum: ['Point', 'LineString', 'Polygon']
								},
								coordinates: {
									oneOf: [
										coordinateSchema.point,
										coordinateSchema.lineString,
										coordinateSchema.polygon
									]
								}
							},
							required: ['type', 'coordinates']
						}
					}
				},
				required: ['type', 'geometries']
			},
			zoom: {
				type: 'number',
				nullable: true,
				minimum: 0,
				maximum: 22
			},
			centerCoordinates: {
				type: 'object',
				nullable: true,
				properties: {
					type: {
						type: 'string',
						enum: ['Point']
					},
					coordinates: {
						type: 'array',
						items: { type: 'number' },
						minItems: 2,
						maxItems: 2
					}
				},
				required: ['type', 'coordinates']
			},
			priority: {
				type: 'string',
				enum: Object.values(Priority),
				nullable: true
			},
			commenceAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			concludeAt: {
				type: 'string',
				format: 'date-time',
				nullable: true
			},
			ltiovDate: {
				type: 'string',
				format: 'date-time',
				nullable: true
			}
		},
		additionalProperties: false
	}
};

// Example validation messages
export const schemaErrorMessages = {
	coordinates: {
		invalidType: 'Geometry type must be Point, LineString, or Polygon',
		invalidFormat: 'Coordinates must be an array of [longitude, latitude] pairs',
		outOfRange: 'Coordinates must be within valid ranges (longitude: -180 to 180, latitude: -90 to 90)',
		invalidPolygon: 'Polygon must be closed (first and last points must be identical)',
		insufficientPoints: 'Polygon must have at least 4 points to form a closed shape'
	},
	zoom: {
		outOfRange: 'Zoom level must be between 0 and 22'
	},
	dates: {
		invalidFormat: 'Dates must be in ISO 8601 format',
		invalidRange: 'Conclude date must be after commence date',
		ltiovInvalid: 'LTIOV date must be between commence and conclude dates'
	},
	general: {
		missingRequired: 'Missing required field',
		invalidEnum: 'Invalid enumeration value',
		invalidType: 'Invalid type provided'
	}
};

// Coordinate validation helpers
export const coordinateRangeCheck = {
	longitude: {
		minimum: -180,
		maximum: 180
	},
	latitude: {
		minimum: -90,
		maximum: 90
	}
};

// Custom formats if needed
export const customFormats = {
	'coordinate-pair': /^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/
};