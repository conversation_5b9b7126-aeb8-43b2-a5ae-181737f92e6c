// generic-crud.interface.ts
import { FastifyRequest, FastifyReply } from 'fastify';

export interface ICrudService<T, CreateDTO, UpdateDTO> {
	create(data: CreateDTO): Promise<T | null>;
	findAll(params: any): Promise<{ items: T[], count: number }>;
	findById(id: string, load?: string): Promise<T | null>;
	update(id: string, data: UpdateDTO): Promise<T | null>;
	delete(id: string): Promise<boolean>;
	search(searchTerm: string, fields?: string[]): Promise<{ items: T[], count: number }>;
}

export interface ICrudController<T, CreateDTO, UpdateDTO> {
	create(request: FastifyRequest<{ Body: CreateDTO }>, reply: FastifyReply): Promise<any>;
	getAll(request: FastifyRequest<{ Querystring: any }>, reply: FastifyReply): Promise<any>;
	getById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply): Promise<any>;
	update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateDTO }>, reply: FastifyReply): Promise<any>;
	delete(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply): Promise<any>;
}

export interface EntityLabels {
	singular: string;
	plural: string;
}

export interface IPaginationParams {
	page?: number | string;
	perPage?: number | string;
	sortBy?: string;
	orderBy?: string;
	[key: string]: string | number | undefined; // Allow additional parameters
}