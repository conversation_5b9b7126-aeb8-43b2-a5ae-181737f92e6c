import {Priority} from "@/interfaces/database.interface.js";

interface ReportTypeConfiguration {
	reportType: string[];
	submittedReport: string;
}

export interface CreateMissionDTO {
	name: string;
	description: string;
	type: string;
	classification: string;
	priority: string;
	reportTypeConfiguration?: ReportTypeConfiguration,
	startAt: string;
	endAt: string;
	operationId: number;
}

export const createMissionSchema = {
	body: {
		type: 'object',
		required: ['name', 'operationId', 'type', 'classification', 'priority', 'reportTypeConfiguration', 'startAt', 'endAt'],
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			operationId: { type: 'number' },
			description: { type: 'string', minLength: 1, maxLength: 1000 },
			type: { type: 'string', minLength: 1, maxLength: 255 },
			classification: { type: 'string', minLength: 1, maxLength: 255 },
			reportTypeConfiguration: { type: 'object', minLength: 1, maxLength: 5000 },
			startAt: {
				type: 'string',
				format: 'date-time',
				pattern: '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:?\\d{2})$'
			},
			endAt: {
				type: 'string',
				format: 'date-time',
				pattern: '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:?\\d{2})$'
			},
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			},
			indicatorsDescription: { type: 'string', nullable: true },
			warningsDescription: { type: 'string', nullable: true }
		}
	}
};



export interface UpdateMissionDTO {
	name: string;
	description: string;
	type: string;
	classification: string;
	priority: string;
	reportTypeConfiguration: ReportTypeConfiguration;
	startAt: string;
	endAt: string;
}

export const updateMissionSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			name: { type: 'string', minLength: 1, maxLength: 255 },
			description: { type: 'string', minLength: 1, maxLength: 1000 },
			type: { type: 'string', minLength: 1, maxLength: 255 },
			classification: { type: 'string', minLength: 1, maxLength: 255 },
			reportTypeConfiguration: { type: 'object', minLength: 1, maxLength: 255 },
			startAt: { type: 'string', minLength: 1, maxLength: 255 },
			endAt: { type: 'string', minLength: 1, maxLength: 255 },
			priority: {
				type: 'string',
				enum: Object.values(Priority)
			},
		}
	}
};

// copy global isr to local isr
export interface CopyGlobalISRTOLocalISRDTO {
	globalIsrIds: number[];
}

export const copyGlobalISRTOLocalISRSchema = {
	params: {
		type: 'object',
		properties: {
			id: {type: 'string'}
		},
		required: ['id']
	},
	body: {
		type: 'object',
		required: ['globalIsrIds'],
		properties: {
			globalIsrIds: {
				type: 'array',
				items: {type: 'number'}
			}
		}
	}, required: ['globalIsrIds']
};




export interface addTaisDTO{
	taiIds: number[];
}

export const addTaisSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	properties: {
		taiIds: {
			type: 'array',
			items: {
				type: 'number'
			}
		}
	},
	required: ['taiIds']
};

export interface removeTaiDTO{
	taiId: number;
}

export const removeTaiSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			taiId: { type: 'number' }
		},
		required: ['id', 'taiId']
	}
};

export interface addAssetDTO{
	assetIds: number[];
}

export const addAssetSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	properties: {
		assetIds: {
			type: 'array',
			items: {
				type: 'number'
			}
		}
	}, required: ['assetIds']
};

export interface removeAssetDTO{
	assetId: number;
}

export const removeAssetSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' },
			assetId: { type: 'number' }
		},
		required: ['id', 'assetId']
	}
};

// informationRequirements
export interface InformationRequirementIdParams {
	irIds: number[];
}

export const putInformationRequirementSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	properties: {
		irIds: {
			type: 'array',
			items: {
				type: 'number'
			}
		}
	},
	required: ['irIds']
};

//