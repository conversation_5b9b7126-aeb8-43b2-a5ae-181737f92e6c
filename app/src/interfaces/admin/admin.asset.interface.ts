import {AssetStatus} from "@/interfaces/database.interface.js";


export interface CreateAssetDTO {
	title: string;
	callSign: string;
	platformId: number;
	operationId: number;
	assetDetails?: any;
    color?: string;
	status:  AssetStatus;
}


export const createAssetSchema = {
	body: {
		type: 'object',
		required: ['title', 'platformId', 'operationId'],
		properties: {
            color: { type: 'string' },
			title: { type: 'string', minLength: 1, maxLength: 255 },
			callSign: { type: 'string', minLength: 1, maxLength: 100 },
			platformId: { type: 'number' },
			operationId: { type: 'number' },
			assetDetails: { type: 'object' },
			status: { type: 'string', enum: Object.values(AssetStatus) }
		}
	}
};

export interface UpdateAssetDTO {
	title: string;
    color?: string;
	status:  AssetStatus;
}

export const updateAssetSchema = {
	params: {
		type: 'object',
		properties: {
			id: { type: 'string' }
		},
		required: ['id']
	},
	body: {
		type: 'object',
		properties: {
			title: { type: 'string', minLength: 1, maxLength: 255 },
			status: { type: 'string', enum: Object.values(AssetStatus) },
            color: { type: 'string' }
		}
	}
};