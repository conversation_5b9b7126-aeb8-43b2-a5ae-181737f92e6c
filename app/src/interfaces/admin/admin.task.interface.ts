
import { TaskStatus, Priority } from "@/interfaces/database.interface.js";

export interface CreateTaskDTO {
  title: string;
  description: string;
  priority: Priority;
  dueDate?: String;
  operationId: number;
}

export const createTaskSchema = {
  body: {
    type: 'object',
    required: ['title', 'priority', 'operationId'],
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 255 },
      description: { type: 'string' },
      priority: { type: 'string', enum: Object.values(Priority) },
      dueDate: { type: 'string' },
      operationId: { type: 'number' },
	}

  }
};

export interface UpdateTaskDTO {
  title?: string;
  description?: string;
  priority?: Priority;
  status?: TaskStatus;
  dueDate?: String;
  completedAt?: String;
}

export const updateTaskSchema = {
  body: {
    type: 'object',
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 255 },
      description: { type: 'string' },
      priority: { type: 'string', enum: Object.values(Priority) },
      status: { type: 'string', enum: Object.values(TaskStatus) },
      dueDate: { type: 'string' },
      completedAt: { type: 'string' }
    }
  }
};

export interface AssignMembersDTO {
  userIds: number[];
}

export const assignMembersSchema = {
  body: {
    type: 'object',
    required: ['userIds'],
    properties: {
      userIds: { 
        type: 'array',
        items: { type: 'number' },
        minItems: 1
      }
    }
  }
};

export interface RemoveMemberDTO {
  memberId: string;
}

export const removeMemberSchema = {
  params: {
    type: 'object',
    required: ['id', 'memberId'],
    properties: {
      id: { type: 'string' },
      memberId: { type: 'string' }
    }
  }
};

export interface SearchTasksDTO {
  searchTerm: string;
}

export const searchTasksSchema = {
  querystring: {
    type: 'object',
    properties: {
      searchTerm: { type: 'string', minLength: 1, maxLength: 100 }
    }
  }
};
