// src/interfaces/rules/rules.collection-capability.interface.ts

import { Point, Polygon, LineString } from 'geojson';
import { AvailabilityStatus, ApprovalStatus } from "@/interfaces/database.interface.js";

export interface CreateCollectionCapabilityDTO {
	description: string;
	operationId: string;
	assetId: string;
	assigned?: string;
	pointOfContactUserId?: string;
	contactDetails?: string;
	availability: AvailabilityStatus;
}

export interface UpdateCollectionCapabilityDTO {
	description?: string;
	assigned?: string;
	pointOfContactUserId?: string;
	contactDetails?: string;
	availability?: AvailabilityStatus;
}

export interface UpdateStatusDTO {
	status: ApprovalStatus;
}

export interface SearchCollectionCapabilitiesDTO {
	searchTerm: string;
}

export interface UpdateStatusRequest {
	Params: {
		id: string;
	};
	Body: UpdateStatusDTO;
}

// Schema for validating GeoJSON Point
const pointSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['Point'] },
		coordinates: {
			type: 'array',
			minItems: 2,
			maxItems: 2,
			items: { type: 'number' }
		}
	}
};

// Schema for validating GeoJSON LineString
const lineStringSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['LineString'] },
		coordinates: {
			type: 'array',
			minItems: 2,
			items: {
				type: 'array',
				minItems: 2,
				maxItems: 2,
				items: { type: 'number' }
			}
		}
	}
};

// Schema for validating GeoJSON Polygon
const polygonSchema = {
	type: 'object',
	required: ['type', 'coordinates'],
	properties: {
		type: { type: 'string', enum: ['Polygon'] },
		coordinates: {
			type: 'array',
			items: {
				type: 'array',
				items: {
					type: 'array',
					minItems: 2,
					items: { type: 'number' }
				}
			}
		}
	}
};

export const createCollectionCapabilitySchema = {
	body: {
		type: 'object',
		required: ['description', 'assetId', 'operationId'],
		properties: {
			description: { type: 'string', minLength: 1, maxLength: 1000 },
			assetId: { type: 'string' },
			assigned: { type: 'string', minLength: 1, maxLength: 200 },
			pointOfContactUserId: { type: 'string' },
			contactDetails: { type: 'string', maxLength: 500 },
			operationId: { type: 'string' },
			availability: {
				type: 'string',
				enum: Object.values(AvailabilityStatus)
			}
		}
	}
};

export const updateCollectionCapabilitySchema = {
	body: {
		type: 'object',
		properties: {
			description: { type: 'string', minLength: 1, maxLength: 1000 },
			assigned: { type: 'string', minLength: 1, maxLength: 200 },
			pointOfContactUserId: { type: 'string' },
			contactDetails: { type: 'string', maxLength: 500 },
			availability: {
				type: 'string',
				enum: Object.values(AvailabilityStatus)
			}
		},
		additionalProperties: false
	}
};

export const updateStatusSchema = {
	params: {
		type: 'object',
		required: ['id'],
		properties: {
			id: { type: 'string' }
		}
	},
	body: {
		type: 'object',
		required: ['status'],
		properties: {
			status: {
				type: 'string',
				enum: Object.values(ApprovalStatus)
			}
		},
		additionalProperties: false
	}
};

export const searchCollectionCapabilitySchema = {
	querystring: {
		type: 'object',
		required: ['searchTerm', 'fields'],
		properties: {
			searchTerm: { type: 'string', minLength: 2 },
			fields: { type: 'string' }
		}
	}
};