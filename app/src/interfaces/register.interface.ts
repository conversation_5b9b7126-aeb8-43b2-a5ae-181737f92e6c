
export interface createOrganizationDTO {
	name: string;
	description: string;
	isMilitary: boolean;
	isoCountryCode: string;
	sovereignty: string;
}

export interface createUserDTO {
	firstName: string;
	lastName: string;
	email: string;
	password: string;
}

export interface createRegistrationDTO {
	organization: createOrganizationDTO;
	user: createUserDTO;
}

export interface verifyEmailDTO {
	email: string;
	verifyHash: string;
}

export const verifyEmailSchema = {
	body: {
		type: 'object',
		required: ['email', 'verifyHash'],
		properties: {
			email: { type: 'string', format: 'email' },
			verifyHash: { type: 'string' }
		}
	}
};

export const createRegistrationSchema = {
	body: {
		type: 'object',
		required: ['organization', 'user'],
		properties: {
			organization: {
				type: 'object',
				required: ['name', 'description', 'isMilitary', 'isoCountryCode', 'sovereignty'],
				properties: {
					name: { type: 'string', minLength: 1, maxLength: 255 },
					description: { type: 'string', minLength: 1, maxLength: 255 },
					isMilitary: { type: 'boolean' },
					isoCountryCode: { type: 'string', minLength: 2, maxLength: 2 },
					sovereignty: { type: 'string', minLength: 1, maxLength: 255 }
				}
			},
			user: {
				type: 'object',
				required: ['firstName', 'lastName', 'email', 'password'],
				properties: {
					firstName: { type: 'string', minLength: 1, maxLength: 100 },
					lastName: { type: 'string', minLength: 1, maxLength: 100 },
					email: { type: 'string', format: 'email' },
					password: { type: 'string', minLength: 8 }
				}
			}
		}
	}
};