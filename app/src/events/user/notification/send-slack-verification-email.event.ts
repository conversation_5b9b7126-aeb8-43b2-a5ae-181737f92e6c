import {sendSlackNotification} from "@/utils/system-notices.utils.js";

export default async function sendSlackVerificationEmail(email: string, verificationToken: string) {
	// TODO: Send slack verification email
	const encodedEmail = encodeURIComponent(email);
	const encodedVerificationToken = encodeURIComponent(verificationToken);
	const PROD_URL = "https://app.lumio.au";
	const DEV_URL = "http://localhost:3001";



	const mess = `[FOR TESTING PURPOSES ONLY] You have signed up for Lu<PERSON>! Please verify your email address by clicking on the following link:\n\n` +
	`${DEV_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}\n\n` +
		`OR production link:\n\n` +
	`${PROD_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}\n\n` +
	`If you did not sign up for Lu<PERSON>, please ignore this email.`;
	await sendSlackNotification(mess);
}

