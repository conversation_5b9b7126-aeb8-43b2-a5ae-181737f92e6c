import { sendEmail} from "@/utils/email.utils.js";

export default async function sendResetPassword(email: string, resetToken: string) {

	const PROD_URL = "https://app.lumio.au";
	
	const encodedEmail = encodeURIComponent(email);
	const encodedResetToken = encodeURIComponent(resetToken);

	const bodyHTML = `
		<h1>Reset Password</h1>
		<p>Hello ${email},</p>
		<p>You are receiving this email because you (or someone else) has requested the reset of the password for your account.</p>
		<p>Please click on the following link, or paste this into your browser to complete the process:</p>
		<p><a href="${PROD_URL}/reset-password?email=${encodedEmail}&token=${encodedResetToken}">Reset Password</a></p>
		<p>If you did not request this, please ignore this email and your password will remain unchanged.</p>
	`;
	const bodyText = `
		Hello ${email},
		You are receiving this email because you (or someone else) has requested the reset of the password for your account.
		Please click on the following link, or paste this into your browser to complete the process:
		${PROD_URL}/reset-password?email=${encodedEmail}&token=${encodedResetToken}
		If you did not request this, please ignore this email and your password will remain unchanged.
	`;
	const subject = "Reset Password";
	await sendEmail({ to: email, subject, text: bodyText, html: bodyHTML });

}