import {sendSlackNotification} from "@/utils/system-notices.utils.js";
import UserModel from '@/models/user.model.js';

export default async function sendSlackOrganizationUserCreated(user: UserModel, organizationName: string, adminEmail: string) {

	const userEmail = user.email;
	const userAccountType = user.accountType;
	const userName = user.firstName + " " + user.lastName;
	const userIsActive = user.isActive ? "ACTIVE" : "INACTIVE";
	const userIsVerified = user.isVerified ? "VERIFIED" : "NOT VERIFIED";

	const mess = `[FOR TESTING PURPOSES ONLY] Manager of *${organizationName}* has created a new user!\n\n` +
		`Email: *${userEmail}*\n\n` +
		`Account Type: *${userAccountType}*\n\n` +
		`Name: *${userName}*\n\n` +
		`Organization: *${organizationName}*\n\n` +
		`Admin email: *${adminEmail}*\n\n` +
		`User is *${userIsActive}* and ${userIsVerified}. Password will be provided by Organization Admin (${adminEmail}).`;
	await sendSlackNotification(mess);
}

