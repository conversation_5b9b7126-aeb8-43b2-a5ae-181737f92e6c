import { sendEmail } from "@/utils/email.utils.js";
import UserModel from '@/models/user.model.js';

export default async function sendWelcomeEmail(user: UserModel, organizationName: string, adminEmail: string) {
	const userName = user.firstName ? `${user.firstName} ${user.lastName}` : user.email;
	const PROD_URL = "https://app.lumio.au";

	const bodyHTML = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <h1 style="color: #3f51b5;">Welcome to Lumio!</h1>
      <p>Hello ${userName},</p>
      <p>Welcome to <strong>${organizationName}</strong>! Your account has been created by your organization administrator.</p>
      
      <div style="background-color: #f5f5f5; border-left: 4px solid #3f51b5; padding: 15px; margin: 20px 0;">
        <h3 style="margin-top: 0;">Account Details</h3>
        <p><strong>Email:</strong> ${user.email}</p>
        <p><strong>Account Type:</strong> ${user.accountType}</p>
        <p><strong>Organization:</strong> ${organizationName}</p>
        <p><strong>Status:</strong> ${user.isActive ? "Active" : "Inactive"} / ${user.isVerified ? "Verified" : "Not Verified"}</p>
      </div>
      
      <h3>Getting Started</h3>
      <ol>
        <li>Your organization administrator (${adminEmail}) will provide your password separately.</li>
        <li>Visit <a href="${PROD_URL}" style="color: #3f51b5;">${PROD_URL}</a> to access your account.</li>
        <li>Log in with your email address and the provided password.</li>
        <li>For security reasons, we recommend changing your password after your first login.</li>
      </ol>
      
      <p>If you have any questions or need assistance, please contact your organization administrator at ${adminEmail}.</p>
      <p>Thank you for joining Lumio!</p>
      <p>Best regards,<br>The Lumio Team</p>
    </div>
  `;

	const bodyText = `
    Welcome to Lumio!
    
    Hello ${userName},
    
    Welcome to ${organizationName}! Your account has been created by your organization administrator.
    
    Account Details:
    - Email: ${user.email}
    - Account Type: ${user.accountType}
    - Organization: ${organizationName}
    - Status: ${user.isActive ? "Active" : "Inactive"} / ${user.isVerified ? "Verified" : "Not Verified"}
    
    Getting Started:
    1. Your organization administrator (${adminEmail}) will provide your password separately.
    2. Visit ${PROD_URL} to access your account.
    3. Log in with your email address and the provided password.
    4. For security reasons, we recommend changing your password after your first login.
    
    If you have any questions or need assistance, please contact your organization administrator at ${adminEmail}.
    
    Thank you for joining Lumio!
    
    Best regards,
    The Lumio Team
  `;

	const subject = `Welcome to ${organizationName} on Lumio`;
	await sendEmail({ to: user.email, subject, text: bodyText, html: bodyHTML });
}