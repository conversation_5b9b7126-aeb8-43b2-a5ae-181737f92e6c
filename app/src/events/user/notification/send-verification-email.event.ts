import { sendEmail} from "@/utils/email.utils.js";

export default async function sendVerificationEmail(email: string, verificationToken: string) {

	const PROD_URL = "https://app.lumio.au";
	const DEV_URL = "http://localhost:3001";

	const encodedEmail = encodeURIComponent(email);
	const encodedVerificationToken = encodeURIComponent(verificationToken);
	const bodyHTML = `
		<h1>Please Verify Your Email</h1>
		<p>You are receiving this email because you (or someone else) has requested the verification of your email address.</p>
		<p>Please click on the following link, or paste this into your browser to complete the process:</p>
		<p>TEST: <a href="${DEV_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}" >Verify Email</a></p>
		<p>PROD: <a href="${PROD_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}">Verify Email</a></p>	
`;


	//	const mess = `[FOR TESTING PURPOSES ONLY] You have signed up for Lumio! Please verify your email address by clicking on the following link:\n\n` +
	// 	`${DEV_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}\n\n` +
	// 		`OR production link:\n\n` +
	// 	`${PROD_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}\n\n` +
	// 	`If you did not sign up for Lumio, please ignore this email.`;

	const bodyText = `
		Hello,
		You are receiving this email because you (or someone else) has requested the verification of your email address.
		Please click on the following link, or paste this into your browser to complete the process: 
		TEST: ${DEV_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}
		PROD: ${PROD_URL}/verify?email=${encodedEmail}&token=${encodedVerificationToken}
	`;
	const subject = "Verify Your Lumio Account";
	await sendEmail({ to: email, subject, text: bodyText, html: bodyHTML });

}