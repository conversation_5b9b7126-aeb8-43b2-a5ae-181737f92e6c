import { sendEmail} from "@/utils/email.utils.js";
import UserModel from "@/models/user.model.js";

export default async function sendCreatePassword(email: string, resetToken: string, orgAdmin: UserModel, orgName:string = 'Organization on Lumio') {

    const PROD_URL = "https://app.lumio.au";

    const orgAdminName = orgAdmin.firstName + " " + orgAdmin.lastName;
    const orgAdminEmail = orgAdmin.email;

    const encodedEmail = encodeURIComponent(email);
    const encodedResetToken = encodeURIComponent(resetToken);

    const bodyHTML = `
		<p>Hello ${email},</p>
		<p>You were invited to Lumio by ${orgAdminName} to join ${orgName}</p>
		<p>Please click on the following link, or paste this into your browser to complete the process.</p>
		<p>You will need to create a password to log into your organization:</p>
		<p><a href="${PROD_URL}/reset-password?email=${encodedEmail}&token=${encodedResetToken}">Create Password</a></p>
		<p>If you have further questions, contact your Organization Administrator: ${orgAdminEmail}</p>
	`;
    const bodyText = `
          	Hello ${email},\n
		You were invited to Lumio by ${orgAdminName} to join ${orgName}\n
		Please click on the following link, or paste this into your browser to complete the process.\n
		You will need to create a password to log into your organization:\n
		${PROD_URL}/reset-password?email=${encodedEmail}&token=${encodedResetToken}\n
		If you have further questions, contact your Organization Administrator: ${orgAdminEmail}\n
	`;
    const subject = "[ACTION NEEDED] You were added to "+orgName;
    await sendEmail({ to: email, subject, text: bodyText, html: bodyHTML });

}