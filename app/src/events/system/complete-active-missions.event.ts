import AppDataSource from "@/config/database.js";
import MissionModel from '@/models/mission.model.js';
import {MissionStatus } from "@/interfaces/database.interface.js";
import { LessThanOrEqual } from "typeorm";
import {sendSlackNotification} from "@/utils/system-notices.utils.js";
const missionRepository = AppDataSource.getRepository(MissionModel);


export const completeActiveMissionsEvent = async () => {
	//get all missions
	const missions = await missionRepository.find({
		where: {
			status: MissionStatus.ACTIVE,
			endAt: LessThanOrEqual(new Date())
		}
	}) as MissionModel[];
	//update status to active
	for (const mission of missions) {
		mission.status = MissionStatus.COMPLETED;
		await missionRepository.save(mission);

		const slackMessage = "Mission *" + mission.name + "* is now *COMPLETED* because it ended at " + mission.endAt;
		await sendSlackNotification(slackMessage);
	}
}

