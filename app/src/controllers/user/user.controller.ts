import {FastifyReply, FastifyRequest} from 'fastify'
import UserService from '@/services/user/user.service.js'
import {userUserSanitizationRules} from "@/acl/sanitization/user/user.sanitization.js";
import {
	LoginUserDTO,
	UpdateUserDTO, UpdateUserPasswordDTO
} from '@/interfaces/admin/admin.user.interface.js';
import {
	returnApiResponse
} from '@/utils/response.util.js';
import {createSanitizer, SanitizationRules} from "@/utils/sanitize.utils.js";
import UserModel from "@/models/user.model.js";

type FlexibleSanitizerFunction = (data: any, rules?: SanitizationRules<any>) => any;


export default class UserController {
	private userService: UserService;
	private sanitizer: FlexibleSanitizerFunction;

	constructor() {
		this.userService = new UserService();
		const typedSanitizer = createSanitizer(UserModel);
		this.sanitizer = (data: any, rules?: SanitizationRules<any>) => typedSanitizer(data, rules as SanitizationRules<UserModel>);
	}

	async login(request: FastifyRequest<{ Body: LoginUserDTO }>, reply: FastifyReply) {
		const data = await this.userService.login(request);
		if(data){
			const { user, accessToken, refreshToken } = data;
			let cleanUser = this.sanitizer(user, userUserSanitizationRules);
			return returnApiResponse({ user: cleanUser, accessToken, refreshToken }, request, reply);
		}
		return returnApiResponse(null, request, reply);
	}

	async refreshToken(request: FastifyRequest<{ Body: { refreshToken: string } }>, reply: FastifyReply) {
		const tokens = await this.userService.refreshToken(request);
		return returnApiResponse({ tokens }, request, reply);
	}

	async viewProfile(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		const requestedUserId = parseInt(request.params.id, 10);
		const user = await this.userService.getUserProfile(requestedUserId);
		return returnApiResponse({ user }, request, reply, 200);
	}

	async updateUser(request: FastifyRequest<{ Params: { id: string }, Body: UpdateUserDTO }>, reply: FastifyReply) {
		const userId = parseInt(request.params.id, 10);
		try {
			const user = await this.userService.updateUser(userId, request.body);
			return returnApiResponse({ user }, request, reply, 200);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply, 200);
	}

	async activateUser(request: FastifyRequest<{ Params: { hashCode: string }, Body: { firstName: string, lastName: string, password: string } }>, reply: FastifyReply) {
		const { hashCode } = request.params;
		//grab firstname and lastname and password from body hash password and store it in users object
		const { firstName, lastName, password } = request.body;
		try {
			const user = await this.userService.activateUser(hashCode, firstName, lastName, password);
			return returnApiResponse({ user }, request, reply, 200);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply, 200);
	}

	async initiatePasswordReset(request: FastifyRequest<{ Body: { email: string } }>, reply: FastifyReply) {
		try {
			await this.userService.initiatePasswordReset(request);
			return returnApiResponse(null, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	async resetPassword(request: FastifyRequest<{ Body: { email: string, resetToken: string, newPassword: string } }>, reply: FastifyReply) {
		try {
			const success = await this.userService.resetPassword(request);
			return returnApiResponse({ success }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	async updatePassword(request: FastifyRequest<{ Params: { id: string }, Body: UpdateUserPasswordDTO }>, reply: FastifyReply) {
		const userId = parseInt(request.params.id, 10);
		try {
			const success = await this.userService.updatePassword(userId, request);
			return returnApiResponse({ success }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

    async switchOrg(request: FastifyRequest<{ Body: { newOrgId: string }, Params: {id:string} }>, reply: FastifyReply) {
        try {
            let tokens = await this.userService.switchOrg(request);
            return returnApiResponse({ tokens }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
        }
        return returnApiResponse(null, request, reply);
    }

    async organizations(request: FastifyRequest<{ Params: {id:string} }>, reply: FastifyReply) {
        try {
            let organizations = await this.userService.organizations(request);
            return returnApiResponse({ organizations }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
        }
        return returnApiResponse(null, request, reply);
    }

    async organization(request: FastifyRequest<{ Params: {id:string} }>, reply: FastifyReply) {
        try {
            let organization = await this.userService.organization(request);
            return returnApiResponse({ organization }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
        }
        return returnApiResponse(null, request, reply);
    }
}
