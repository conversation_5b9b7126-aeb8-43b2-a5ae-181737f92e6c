import {FastifyReply, FastifyRequest} from "fastify";
import {createRegistrationDTO, verifyEmailDTO} from "@/interfaces/register.interface.js";
import {returnApiResponse} from "@/utils/response.util.js";
import RegisterService from "@/services/register.service.js";

export default class RegisterController {
	private registerService: RegisterService;

	constructor() {
		this.registerService = new RegisterService();
	}

	async register(request: FastifyRequest<{ Body: createRegistrationDTO }>, reply: FastifyReply) {
		const data = await this.registerService.register(request);
		if(!data) return returnApiResponse(null, request, reply);
		return returnApiResponse(data, request, reply);
	}

	async verifyEmail(request: FastifyRequest<{ Body: verifyEmailDTO }>, reply: FastifyReply) {
		const data = await this.registerService.verifyEmail(request);
		if(!data) return returnApiResponse(null, request, reply);
		return returnApiResponse({
			verified: data.isVerified,
			active: data.isActive,
			email: data.email.toLowerCase()
		}, request, reply);
	}

}