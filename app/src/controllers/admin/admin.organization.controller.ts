import AdminOrganizationService from "@/services/admin/admin.organization.service.js";
import {
	CreateOrganizationDTO,
	UpdateOrganizationDTO,
} from "@/interfaces/admin/admin.organization.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import OrganizationModel from "@/models/organization.model.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";
import { returnApiResponse } from '@/utils/response.util.js';

export default class AdminOrganizationController  extends GenericCrudController<OrganizationModel, CreateOrganizationDTO, UpdateOrganizationDTO> {
	private adminOrganizationService: AdminOrganizationService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminOrganizationService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'organization', plural: 'organizations' }, OrganizationModel);
		this.adminOrganizationService = service;
	}

	//getOrganizationAccessRules
	async getOrganizationAccessRules(request: any, reply: any) {
		try {
			const defaultUsersRules = request.requestingUser?.defaultRules;
			if (!defaultUsersRules) {
				request.messageCollector.addError("Rules not found");
				return returnApiResponse(null, request, reply);
			}
			return returnApiResponse({
				accessRules: defaultUsersRules,
				description: "Default rules for organization. User.accountType is used to determine access rules," +
					" User with 'admin' accountType is global admin and can access all resources." +
					" User with 'org_admin' accountType can access all resources in the organization." +
					" User with 'manager' accountType can access all resources in the organization with limited privileges." +
					" User with 'user' accountType can access all resources in the allowed by org_admin or manager." +
					" Plebs basically." +
					" all organizations."

			}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return null;
		}
	}

}