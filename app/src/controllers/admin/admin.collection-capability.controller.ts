import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminCollectionCapabilityService from "@/services/admin/admin.collection-capability.service.js";
import {
	CreateCollectionCapabilityDTO,
	UpdateCollectionCapabilityDTO,
	UpdateStatusDTO
} from "@/interfaces/admin/admin.collection-capability.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import CollectionCapabilityModel from '@/models/collection-capability.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminCollectionCapabilitySanitizationRules } from "@/acl/sanitization/admin/collection-capability.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminCollectionCapabilityController extends GenericCrudController<
	CollectionCapabilityModel,
	CreateCollectionCapabilityDTO,
	UpdateCollectionCapabilityDTO
> {
	private adminCollectionCapabilityService: AdminCollectionCapabilityService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminCollectionCapabilityService(messageCollector, requestingUser, organizationId);
		super(service, {
			singular: 'collectionCapability',
			plural: 'collectionCapabilities'
		}, CollectionCapabilityModel);
		this.adminCollectionCapabilityService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateCollectionCapabilityDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const capability = await this.adminCollectionCapabilityService.createWithUserId(
				request.body,
				request.requestingUser.id
			);

			if (!capability) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedCapability = this.sanitizer(capability, adminCollectionCapabilitySanitizationRules);
			return returnApiResponse({
				'collectionCapability': sanitizedCapability
			}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// Add updateStatus method
	async updateStatus(request: FastifyRequest<{
		Params: { id: string },
		Body: UpdateStatusDTO
	}>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const capability = await this.adminCollectionCapabilityService.updateStatus(
				request.params.id,
				request.body,
				request.requestingUser.id
			);

			if (!capability) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedCapability = this.sanitizer(capability, adminCollectionCapabilitySanitizationRules);
			return returnApiResponse({
				'collectionCapability': sanitizedCapability
			}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// // Override getAll to ensure proper loading of relationships
	// async getAll(request: FastifyRequest<{
	//     Querystring: {
	//         page?: number,
	//         perPage?: number,
	//         sortBy?: string,
	//         orderBy?: string,
	//         load?: string
	//     }
	// }>, reply: FastifyReply) {
	//     try {
	//         const {
	//             page = 1,
	//             perPage = 10,
	//             sortBy = 'desc',
	//             orderBy = 'id',
	//             load = 'operation,asset,createdByUser,pointOfContactUser'
	//         } = request.query;
	//
	//         const { items, count } = await this.adminCollectionCapabilityService.findAll({
	//             page,
	//             perPage,
	//             sortBy,
	//             orderBy,
	//             load
	//         });
	//
	//         const totalPages = Math.ceil(count / perPage);
	//         const sanitizedItems = items.map(item =>
	//             this.sanitizer(item, adminCollectionCapabilitySanitizationRules)
	//         );
	//
	//         return returnApiResponse({
	//             collectionCapabilities: sanitizedItems,
	//             pagination: {
	//                 page,
	//                 perPage,
	//                 total: count,
	//                 totalPages
	//             }
	//         }, request, reply);
	//     } catch (error) {
	//         request.messageCollector.addThrowableError(error);
	//         return returnApiResponse(null, request, reply);
	//     }
	// }
	//
	// // Override getById to ensure proper loading of relationships
	// async getById(request: FastifyRequest<{
	//     Params: { id: string },
	//     Querystring: { load?: string }
	// }>, reply: FastifyReply) {
	//     try {
	//         const { id } = request.params;
	//         const { load = 'operation,asset,createdByUser,pointOfContactUser' } = request.query;
	//
	//         const capability = await this.adminCollectionCapabilityService.findById(id, load);
	//         if (!capability) {
	//             request.messageCollector.addError("Collection Capability not found");
	//             return returnApiResponse(null, request, reply);
	//         }
	//
	//         const sanitizedCapability = this.sanitizer(capability, adminCollectionCapabilitySanitizationRules);
	//         return returnApiResponse({
	//             collectionCapability: sanitizedCapability
	//         }, request, reply);
	//     } catch (error) {
	//         request.messageCollector.addThrowableError(error);
	//         return returnApiResponse(null, request, reply);
	//     }
	// }
}