// generic-crud.controller.ts
import { FastifyReply, FastifyRequest } from 'fastify';
import { ICrudController, ICrudService, EntityLabels, IPaginationParams } from '@/interfaces/admin/admn.item-crud.interface.js';
import { returnApiResponse } from '@/utils/response.util.js';
import { SanitizationRules, createSanitizer } from '@/utils/sanitize.utils.js';
import {ObjectLiteral} from "typeorm";
import * as console from "node:console";

type FlexibleSanitizerFunction = (data: any, rules?: SanitizationRules<any>) => any;


export class GenericCrudController<T extends ObjectLiteral, CreateDTO, UpdateDTO> implements ICrudController<T, CreateDTO, UpdateDTO> {
	public sanitizer: FlexibleSanitizerFunction;

	constructor(
		protected service: ICrudService<T, CreateDTO, UpdateDTO>,
		protected labels: EntityLabels,
		protected entity: new () => T
	) {
		const typedSanitizer = createSanitizer(this.entity);
		this.sanitizer = (data: any, rules?: SanitizationRules<any>) => typedSanitizer(data, rules as SanitizationRules<T>);
	}

	async create(request: FastifyRequest<{ Body: CreateDTO }>, reply: FastifyReply) {
		const body = request.body as CreateDTO;
		const entity = await this.service.create(body);
		const sanitizationRules = (request as any).sanitizationRules;
		const sanitizedEntity = this.sanitizer(entity, sanitizationRules);
		return returnApiResponse({ [this.labels.singular]: sanitizedEntity }, request, reply);
	}

	// rules.item-crud.controller.ts
	async getAll(request: FastifyRequest<{ Querystring: IPaginationParams & { load?: string } }>, reply: FastifyReply) {
		const query = request.query;
		// Extract pagination params
		const paginationParams = {
			page: Number(query.page) || 1,
			perPage: Number(query.perPage) || 10,
			sortBy: query.sortBy || 'desc',
			orderBy: query.orderBy || 'id'
		};

		// Get all other params except pagination and load
		const { page, perPage, sortBy, orderBy, load, ...filterParams } = query;

		// Pass everything to service
		const { items, count } = await this.service.findAll({
			...paginationParams,
			load,
			...filterParams  // Include all additional filter parameters
		});

		const { page: currentPage, perPage: itemsPerPage } = paginationParams;
		const totalPages = Math.ceil(count / itemsPerPage);
		const sanitizationRules = (request as any).sanitizationRules;
		const sanitizedItems = items.map(item => this.sanitizer(item, sanitizationRules));

		return returnApiResponse({
			[this.labels.plural]: sanitizedItems,
			pagination: {
				page: currentPage,
				perPage: itemsPerPage,
				total: count,
				totalPages
			}
		}, request, reply);
	}

	async getById(request: FastifyRequest<{ Params: { id: string }, Querystring: { load?: string } }>, reply: FastifyReply) {
		const { id } = request.params;
		const { load } = request.query;
		const entity = await this.service.findById(id, load);
		const sanitizationRules = (request as any).sanitizationRules;
		const sanitizedEntity = this.sanitizer(entity, sanitizationRules);
		return returnApiResponse({ [this.labels.singular]: sanitizedEntity }, request, reply);
	}

	async update(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		const { id } = request.params;
		const body = request.body as UpdateDTO;
		const updatedEntity = await this.service.update(id, body);
		const sanitizationRules = (request as any).sanitizationRules;
		const sanitizedEntity = this.sanitizer(updatedEntity, sanitizationRules);
		return returnApiResponse({ [this.labels.singular]: sanitizedEntity }, request, reply);
	}

	async delete(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		const { id } = request.params;
		const deleted = await this.service.delete(id);
		return returnApiResponse({ deleted }, request, reply);
	}

	async search(request: FastifyRequest<{ Querystring: { searchTerm: string, fields?: string } }>, reply: FastifyReply) {
		const { searchTerm, fields } = request.query;
		const searchFields = fields ? fields.split(',') : undefined;
		const { items, count } = await this.service.search(searchTerm, searchFields);
		const sanitizationRules = (request as any).sanitizationRules;
		//sanitize each item in the array
		const sanitizedItems = items.map(item => this.sanitizer(item, sanitizationRules));
		return returnApiResponse({
			[this.labels.plural]: sanitizedItems,
			total: count
		}, request, reply);
	}

}