import AdminOriginatorService from "@/services/admin/admin.originator.service.js";
import {
	CreateOriginatorDTO,
	UpdateOriginatorDTO,
} from "@/interfaces/admin/admin.originator.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import OriginatorModel from "@/models/originator.model.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminOriginatorController  extends GenericCrudController<OriginatorModel, CreateOriginatorDTO, UpdateOriginatorDTO> {
	private adminOriginatorService: AdminOriginatorService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminOriginatorService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'originator', plural: 'originators' }, OriginatorModel);
		this.adminOriginatorService = service;
	}

}