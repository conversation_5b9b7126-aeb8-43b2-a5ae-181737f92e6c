// app/src/controllers/admin/admin.engaged-mission-asset.controller.ts
import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import EngagedMissionAssetService from "@/services/admin/admin.engaged-mission-asset.service.js";
import {
    CreateEngagedMissionAssetDTO,
    UpdateEngagedMissionAssetDTO
} from "@/interfaces/admin/admin.engaged-mission-asset.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import EngagedMissionAssetModel from '@/models/engaged-mission-asset.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { IRequestingUser } from "@/interfaces/database.interface.js";
import { adminEngagedMissionAssetSanitizationRules } from "@/acl/sanitization/admin/admin.engaged-mission-asset.sanitization.js";

export default class AdminEngagedMissionAssetController extends GenericCrudController<EngagedMissionAssetModel, CreateEngagedMissionAssetDTO, UpdateEngagedMissionAssetDTO> {
    private engagedMissionAssetService: EngagedMissionAssetService;

    constructor(messageCollector: MessageCollector, requestingUser: IRequestingUser | undefined, organizationId: number | undefined) {
        const service = new EngagedMissionAssetService(messageCollector, requestingUser, organizationId);
        super(service, { singular: 'engagedMissionAsset', plural: 'engagedMissionAssets' }, EngagedMissionAssetModel);
        this.engagedMissionAssetService = service;
    }

    // Override create method to handle userId from request
    async create(request: FastifyRequest<{ Body: CreateEngagedMissionAssetDTO }>, reply: FastifyReply) {
        try {
            if (!request.requestingUser) {
                request.messageCollector.addError("User ID not found in request");
                return returnApiResponse(null, request, reply);
            }

            const engagedMissionAsset = await this.engagedMissionAssetService.create(request.body);
            if (!engagedMissionAsset) {
                return returnApiResponse(null, request, reply);
            }

            const sanitizedEngagedMissionAsset = this.sanitizer(engagedMissionAsset, adminEngagedMissionAssetSanitizationRules);
            return returnApiResponse({ 'engagedMissionAsset': sanitizedEngagedMissionAsset }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
            return returnApiResponse(null, request, reply);
        }
    }

    // Override update method
    async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateEngagedMissionAssetDTO }>, reply: FastifyReply) {
        try {
            if (!request.requestingUser) {
                request.messageCollector.addError("User ID not found in request");
                return returnApiResponse(null, request, reply);
            }

            const engagedMissionAsset = await this.engagedMissionAssetService.update(request.params.id, request.body);
            if (!engagedMissionAsset) {
                return returnApiResponse(null, request, reply);
            }

            const sanitizedEngagedMissionAsset = this.sanitizer(engagedMissionAsset, adminEngagedMissionAssetSanitizationRules);
            return returnApiResponse({ 'engagedMissionAsset': sanitizedEngagedMissionAsset }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
            return returnApiResponse(null, request, reply);
        }
    }

    // Override delete method
    async delete(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
        try {
            const success = await this.engagedMissionAssetService.delete(request.params.id);
            if (!success) {
                return returnApiResponse(null, request, reply);
            }

            return returnApiResponse({ success: true }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
            return returnApiResponse(null, request, reply);
        }
    }
}