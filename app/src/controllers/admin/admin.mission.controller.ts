import AdminMissionService from "@/services/admin/admin.mission.service.js";
import {
	CreateMissionDTO,
	UpdateMissionDTO,
	addAssetDTO,
	addAssetSchema,
	removeAssetDTO,
	removeAssetSchema,
	addTaisDTO,
	addTaisSchema,
	removeTaiDTO,
	removeTaiSchema,
	InformationRequirementIdParams,
	putInformationRequirementSchema, CopyGlobalISRTOLocalISRDTO

} from "@/interfaces/admin/admin.mission.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import MissionModel from "@/models/mission.model.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import {FastifyReply, FastifyRequest} from "fastify";
import {returnApiResponse} from "@/utils/response.util.js";
import {adminMissionSanitizationRules} from "@/acl/sanitization/admin/mission.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";
import {IPaginationParams} from "@/interfaces/admin/admn.item-crud.interface.js";

export default class AdminMissionController  extends GenericCrudController<MissionModel, CreateMissionDTO, UpdateMissionDTO> {
	private adminMissionService: AdminMissionService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminMissionService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'mission', plural: 'missions' }, MissionModel);
		this.adminMissionService = service;
	}

	async copyGlobalISRTOLocalISR(request: FastifyRequest<{ Body: CopyGlobalISRTOLocalISRDTO, Params: { id: string } }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { globalIsrIds } = request.body;
			const mission = await this.adminMissionService.copyGlobalISRTOLocalISR(id, globalIsrIds);
			if (!mission) {
				request.messageCollector.addError("Mission not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async log(request: FastifyRequest, reply: FastifyReply) {
		try {
			const queryData = request.query as IPaginationParams;
			console.log("queryData", queryData);
			//call service log() function
			const { items, count } = await this.adminMissionService.log(queryData);
			if (!items) {
				request.messageCollector.addError("Log not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedItems = items.map(item => this.sanitizer(item, adminMissionSanitizationRules));
			return returnApiResponse({ 'missions': sanitizedItems, 'count': count }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateMissionDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const dataWithUserId = {
				...request.body,
				userId: request.requestingUser.id
			};

			const mission = await this.adminMissionService.create(dataWithUserId);
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// addTais(missionId: number, taiIds: number[]): Promise<MissionModel | null> {
	async addTais(request: FastifyRequest<{ Params: { id: string }, Body: addTaisDTO }>, reply: FastifyReply) {
		try {
			let { taiIds } = request.body;
			const missionId = request.params.id;
			//convert taiIds to number
			taiIds.map((taiId) => Number(taiId));
			const mission = await this.adminMissionService.addTais(Number(missionId), taiIds);
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}
	// removeTai(missionId: number, taiId: number): Promise<MissionModel | null> {
	async removeTai(request: FastifyRequest<{ Params: { id: string, taiId: string } }>, reply: FastifyReply) {
		try {
			const { taiId } = request.params;
			const missionId = request.params.id;
			const mission = await this.adminMissionService.removeTai(Number(missionId), Number(taiId));
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}
	// addAssets(missionId: number, assetIds: number[]): Promise<MissionModel | null> {
	async addAssets(request: FastifyRequest<{ Params: { id: string }, Body: addAssetDTO }>, reply: FastifyReply) {
		try {
			let { assetIds } = request.body;
			const missionId = request.params.id;
			//convert assetIds to number
			assetIds.map((assetId) => Number(assetId));
			const mission = await this.adminMissionService.addAssets(Number(missionId), assetIds);
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}
	// removeAsset(missionId: number, assetId: number): Promise<MissionModel | null> {
	async removeAsset(request: FastifyRequest<{ Params: { id: string, assetId: string } }>, reply: FastifyReply) {
		try {
			const missionId = request.params.id;
			const assetId = request.params.assetId;
			const mission = await this.adminMissionService.removeAsset(Number(missionId), Number(assetId));
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// put information requirements
	async putInformationRequirements(request: FastifyRequest<{ Params: { id: string }, Body: InformationRequirementIdParams }>, reply: FastifyReply) {
		try {
			const missionId = request.params.id;
			const irIds = request.body.irIds;
			const mission = await this.adminMissionService.putInformationRequirements(Number(missionId), irIds);
			if (!mission) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedMission = this.sanitizer(mission, adminMissionSanitizationRules);
			return returnApiResponse({ 'mission': sanitizedMission }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

}