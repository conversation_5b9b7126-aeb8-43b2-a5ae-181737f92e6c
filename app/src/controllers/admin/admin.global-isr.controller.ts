import { FastifyReply, FastifyRequest } from "fastify";
import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import { CreateGlobalISRDTO, UpdateGlobalISRDTO,    GeometryCollection } from "@/interfaces/admin/admin.global-isr.interface.js";
import GlobalIsrModel from "@/models/global-isr.model.js";
import AdminGlobalIsrService from "@/services/admin/admin.global-isr.service.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminGlobalISRSanitizationRules } from "@/acl/sanitization/admin/global-isr.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";



export default class AdminGlobalIsrController extends GenericCrudController<GlobalIsrModel, CreateGlobalISRDTO, UpdateGlobalISRDTO> {
	private adminGlobalIsrService: AdminGlobalIsrService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminGlobalIsrService(messageCollector, requestingUser, organizationId);
		super(service, {singular: 'globalIsr', plural: 'globalIsrs'}, GlobalIsrModel);
		this.adminGlobalIsrService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateGlobalISRDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const dataWithUserId = {
				...request.body,
				userId: request.requestingUser.id
			};

			const globalIsr = await this.adminGlobalIsrService.create(dataWithUserId);
			if (!globalIsr) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedIsr = this.sanitizer(globalIsr, adminGlobalISRSanitizationRules);
			return returnApiResponse({'globalIsr': sanitizedIsr}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}


	// rules.globalIsr.controller.ts


async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateGlobalISRDTO }>, reply: FastifyReply) {
	try {
		const { id } = request.params;
		const updateData = request.body;

		// First fetch the existing GlobalIsr to ensure it exists
		const existingIsr = await this.adminGlobalIsrService.findById(id);
		if (!existingIsr) {
			request.messageCollector.addError("GlobalIsr not found");
			return returnApiResponse(null, request, reply);
		}


		// Validate dates if provided
		if (updateData.commenceAt && updateData.concludeAt) {
			const commenceDate = new Date(updateData.commenceAt);
			const concludeDate = new Date(updateData.concludeAt);

			if (commenceDate >= concludeDate) {
				request.messageCollector.addError("Commence date must be before conclude date");
				return returnApiResponse(null, request, reply);
			}
		}

		// // Validate center coordinates if provided
		// if (updateData.centerCoordinates) {
		// 	const { coordinates } = updateData.centerCoordinates;
		// 	if (!coordinates ||
		// 		!Array.isArray(coordinates) ||
		// 		coordinates.length !== 2 ||
		// 		!coordinates.every(coord => typeof coord === 'number')) {
		// 		request.messageCollector.addError("Invalid center coordinates format");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		//
		// 	const [longitude, latitude] = coordinates;
		// 	if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
		// 		request.messageCollector.addError("Center coordinates out of valid range");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// // Validate zoom if provided
		// if (updateData.zoom !== undefined) {
		// 	if (updateData.zoom < 0 || updateData.zoom > 22) {
		// 		request.messageCollector.addError("Zoom level must be between 0 and 22");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// If type is changed, ensure coordinates match the new type
		// if (updateData.type && updateData.coordinates) {
		// 	const coordinates = updateData.coordinates as GeometryCollection; // Type assertion here
		// 	const geometryTypes = coordinates.geometries.map(g => g.type);
		//
		// 	const hasMatchingGeometry = geometryTypes.some(gType => {
		// 		switch(updateData.type) {
		// 			case 'point': return gType === 'Point';
		// 			case 'line': return gType === 'LineString';
		// 			case 'polygon': return gType === 'Polygon';
		// 			default: return false;
		// 		}
		// 	});
		//
		// 	if (!hasMatchingGeometry) {
		// 		request.messageCollector.addError(
		// 			`Geometry collection must contain at least one ${updateData.type} geometry`
		// 		);
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// Perform the update
		const updatedIsr = await this.adminGlobalIsrService.update(id, updateData);
		if (!updatedIsr) {
			return returnApiResponse(null, request, reply);
		}

		// Sanitize and return the response
		const sanitizedIsr = this.sanitizer(updatedIsr, adminGlobalISRSanitizationRules);

		request.messageCollector.addSuccess("GlobalIsr updated successfully");
		return returnApiResponse({ 'globalIsr': sanitizedIsr }, request, reply);

	} catch (error) {
		request.messageCollector.addThrowableError(error);
		return returnApiResponse(null, request, reply);
	}
}


}