import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminInformationRequirementService from "@/services/admin/admin.information-requirement.service.js";
import {
	CreateInformationRequirementDTO,
	UpdateInformationRequirementDTO,
} from "@/interfaces/admin/admin.information-requirement.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import InformationRequirementModel from '@/models/information-requirement.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminInformationRequirementSanitizationRules } from "@/acl/sanitization/admin/information-requirement.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminInformationRequirementController extends GenericCrudController<InformationRequirementModel, CreateInformationRequirementDTO, UpdateInformationRequirementDTO> {
	private adminInformationRequirementService: AdminInformationRequirementService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminInformationRequirementService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'informationRequirement', plural: 'informationRequirements' }, InformationRequirementModel);
		this.adminInformationRequirementService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateInformationRequirementDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User not found in request");
				return returnApiResponse(null, request, reply);
			}

			const dataWithUserId = {
				...request.body,
				userId: request.requestingUser?.id
			};

			const ir = await this.adminInformationRequirementService.create(dataWithUserId);
			if (!ir) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedIr = this.sanitizer(ir, adminInformationRequirementSanitizationRules);
			return returnApiResponse({ 'informationRequirement': sanitizedIr }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// async search(request: FastifyRequest<{ Querystring: { searchTerm: string, pirId: string } }>, reply: FastifyReply) {
	// 	try {
	// 		const { searchTerm, pirId } = request.query;
	// 		if (!searchTerm || !pirId) {
	// 			request.messageCollector.addError("Search term and PIR ID are required");
	// 			return returnApiResponse(null, request, reply);
	// 		}
	// 		const ir = await this.adminInformationRequirementService.search(pirId, searchTerm);
	// 		if (!ir) {
	// 			return returnApiResponse(null, request, reply);
	// 		}
	// 		const sanitizedIr = this.sanitizer(ir, adminInformationRequirementSanitizationRules);
	// 		return returnApiResponse({ 'informationRequirement': sanitizedIr }, request, reply);
	// 	} catch (error) {
	// 		request.messageCollector.addThrowableError(error);
	// 		return returnApiResponse(null, request, reply);
	// 	}
	// }
}