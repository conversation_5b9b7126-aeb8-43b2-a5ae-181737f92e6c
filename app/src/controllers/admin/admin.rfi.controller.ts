import AdminRFIService from "@/services/admin/admin.rfi.service.js";
import {
	CreateRFIDTO,
	UpdateRFIDTO,
} from "@/interfaces/admin/admin.rfi.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import RFIModel from "@/models/rfi.model.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminRFIController  extends GenericCrudController<RFIModel, CreateRFIDTO, UpdateRFIDTO> {
	private adminRFIService: AdminRFIService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminRFIService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'rfi', plural: 'rfis' }, RFIModel);
		this.adminRFIService = service;
	}

}