import AdminUserService from "@/services/admin/admin.user.service.js";
import {FastifyReply, FastifyRequest} from 'fastify'
import {
	CreateUserDTO,
	UpdateUserDTO,
	AssignRolesBodyDTO, AssignUserToOperationDTO, UpdateUserOperationsAccessDTO
} from "@/interfaces/admin/admin.user.interface.js";
import {
	returnApiResponse
} from '@/utils/response.util.js';
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import UserModel from "@/models/user.model.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import {adminUserSanitizationRules} from "@/acl/sanitization/admin/user.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminUserController  extends GenericCrudController<UserModel, CreateUserDTO, UpdateUserDTO> {

	private adminUserService: AdminUserService


	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminUserService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'user', plural: 'users' }, UserModel);
		this.adminUserService = service;
	}

	async assignRoles(request: FastifyRequest<{ Params: { id: string }, Body: AssignRolesBodyDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { roleIds } = request.body;
			const user = await this.adminUserService.assignRoles(id, roleIds);
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async addRoles(request: FastifyRequest<{ Params: { id: string }, Body: AssignRolesBodyDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { roleIds } = request.body;
			const user = await this.adminUserService.addRoles(id, roleIds);
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async removeRoleFromUser(request: FastifyRequest<{ Params: { id: string, roleId: string } }>, reply: FastifyReply) {
		try {
			const { id, roleId } = request.params;
			const user = await this.adminUserService.removeRoleFromUser(id, roleId);
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	//operations
	async assignUserToOperation(request: FastifyRequest<{ Params: { id: string }, Body: AssignUserToOperationDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { operationId, accessType } = request.body;
			const user = await this.adminUserService.assignUserToOperation(id, parseInt(operationId), accessType);
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			console.trace(error);
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	//removeUserFromOperation
	async removeUserFromOperation(request: FastifyRequest<{ Params: { id: string, operationId: string } }>, reply: FastifyReply) {
		try {
			const { id, operationId } = request.params;
			const user = await this.adminUserService.removeUserFromOperation(id, parseInt(operationId));
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async updateUserOperationsAccess(request: FastifyRequest<{ Params: { id: string, operationId:string }, Body: UpdateUserOperationsAccessDTO }>, reply: FastifyReply) {
		try {
			const { id,  operationId} = request.params;
			const { accessType } = request.body;
			const user = await this.adminUserService.updateUserOperationAccess(id, parseInt(operationId), accessType);
			return this._returnSanitizedUser(user, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	_returnSanitizedUser(user: UserModel | null, request: any, reply: FastifyReply) {
		const sanitizedUser = this.sanitizer(user, adminUserSanitizationRules);
		return returnApiResponse({ user: sanitizedUser }, request, reply);
	}



}