import AdminTaskService from "@/services/admin/admin.task.service.js";
import {
	CreateTaskDTO,
	UpdateTaskDTO,
	AssignMembersDTO
} from "@/interfaces/admin/admin.task.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import TaskModel from "@/models/task.model.js";
import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import { FastifyReply, FastifyRequest } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminTaskSanitizationRules } from "@/acl/sanitization/admin/task.sanitization.js";
import { IRequestingUser } from "@/interfaces/database.interface.js";
import { IPaginationParams } from "@/interfaces/admin/admn.item-crud.interface.js";

export default class AdminTaskController extends GenericCrudController<TaskModel, CreateTaskDTO, UpdateTaskDTO> {
	private adminTaskService: AdminTaskService;

	constructor(messageCollector: MessageCollector, requestingUser: IRequestingUser | undefined, organizationId: number | undefined) {
		const service = new AdminTaskService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'task', plural: 'tasks' }, TaskModel);
		this.adminTaskService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateTaskDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const dataWithUserId = {
				...request.body,
				userId: request.requestingUser.id
			};

			const task = await this.adminTaskService.create(dataWithUserId);
			if (!task) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedTask = this.sanitizer(task, adminTaskSanitizationRules);
			return returnApiResponse({ 'task': sanitizedTask }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// Assign members to a task
	async assignMembers(request: FastifyRequest<{ Params: { id: string }, Body: AssignMembersDTO }>, reply: FastifyReply) {
		try {
			const { userIds } = request.body;
			const taskId = request.params.id;

			// Convert userIds to numbers
			const numericUserIds = userIds.map(userId => Number(userId));

			const task = await this.adminTaskService.assignUsersToTask(Number(taskId), numericUserIds);
			if (!task) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedTask = this.sanitizer(task, adminTaskSanitizationRules);
			return returnApiResponse({ 'task': sanitizedTask }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// Remove a member from a task
	async removeMember(request: FastifyRequest<{ Params: { id: string, memberId: string } }>, reply: FastifyReply) {
		try {
			const { memberId } = request.params;
			const taskId = request.params.id;

			const task = await this.adminTaskService.removeUserFromTask(Number(taskId), Number(memberId));
			if (!task) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedTask = this.sanitizer(task, adminTaskSanitizationRules);
			return returnApiResponse({ 'task': sanitizedTask }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}
}