import { FastifyRequest, FastifyReply } from "fastify";
import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import { CreatePlatformDTO, UpdatePlatformDTO, GetPlatformDTO, PlatformType, SearchPlatformsDTO } from "@/interfaces/admin/admin.platform.interface.js";
import PlatformModel from "@/models/platform.model.js";
import AdminPlatformService from "@/services/admin/admin.platform.service.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminPlatformSanitizationRules } from "@/acl/sanitization/admin/platform.sanitization.js";
import { IRequestingUser } from "@/interfaces/database.interface.js";

export default class AdminPlatformController extends GenericCrudController<PlatformModel, CreatePlatformDTO, UpdatePlatformDTO> {
	private adminPlatformService: AdminPlatformService;

	constructor(messageCollector: MessageCollector, requestingUser: IRequestingUser | undefined, organizationId: number | undefined) {
		const service = new AdminPlatformService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'platform', plural: 'platforms' }, PlatformModel);
		this.adminPlatformService = service;
	}

	async searchPlatforms(request: FastifyRequest<{ Querystring: SearchPlatformsDTO }>, reply: FastifyReply) {
		try {
			const {searchTerm, countryIsoCode, type} = request.query;
			//convert type into array of items parsed by comma
			const typeArray = type ? (type.split(',') as PlatformType[]) : undefined;
			let response = await this.adminPlatformService.searchPlatforms(searchTerm, countryIsoCode, typeArray);
			if (response) {
				const sanitizedItem = this.sanitizer(response[0], adminPlatformSanitizationRules);
				return  returnApiResponse({platforms: sanitizedItem, total: response[1]}, request, reply);
			}

		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse({platforms: [], total: 0}, request, reply);
	}

	// Example of a custom method that might be useful:
	async getPlatformsByType(request: FastifyRequest<{ Querystring: { type: string } }>, reply: FastifyReply) {
		try {
			const { type } = request.query;
			const { items, count } = await this.adminPlatformService.findAll({ type });

			const sanitizedItems = items.map(item => this.sanitizer(item, adminPlatformSanitizationRules));

			return returnApiResponse({
				platforms: sanitizedItems,
				total: count
			}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

}
