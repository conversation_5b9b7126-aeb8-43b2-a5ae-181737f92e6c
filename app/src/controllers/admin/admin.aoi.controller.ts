import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminAoiService from "@/services/admin/admin.aoi.service.js";
import {
	CreateAoiDTO,
	UpdateAoiDTO,
	ApproveAoiDTO
} from "@/interfaces/admin/admin.aoi.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import AoiModel from '@/models/aoi.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminAoiSanitizationRules } from "@/acl/sanitization/admin/aoi.sanitization.js";
import { Position, Point, Polygon } from 'geojson';
import {IRequestingUser} from "@/interfaces/database.interface.js";


export default class AdminAoiController extends GenericCrudController<AoiModel, CreateAoiDTO, UpdateAoiDTO> {
	private adminAoiService: AdminAoiService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminAoiService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'aoi', plural: 'aois' }, AoiModel);
		this.adminAoiService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateAoiDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			// const coordinates = request.body.area.coordinates.map(coord =>
			// 	Array.isArray(coord[0]) ? [coord[0][0], coord[1][0]] : coord
			// ) as Position[];
			//
			// const formattedArea: Polygon = {
			// 	type: "Polygon",
			// 	coordinates: [coordinates]
			// };
			//
			// const formattedBody: CreateAoiDTO = {
			// 	...request.body,
			// 	area: formattedArea
			// };

			const aoi = await this.adminAoiService.createWithUserId(request.body, request.requestingUser?.id);
			if (!aoi) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedAoi = this.sanitizer(aoi, adminAoiSanitizationRules);
			return returnApiResponse({ 'aoi': sanitizedAoi }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateAoiDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			// console.log("REQUEST BODY COORDS: ", request.body?.area?.coordinates);
			//
			// const coordinates = request.body?.area?.coordinates.map(coord =>
			// 	Array.isArray(coord[0]) ? [coord[0][0], coord[1][0]] : coord
			// ) as Position[];
			//
			// const formattedArea: Polygon = {
			// 	type: "Polygon",
			// 	coordinates: [coordinates]
			// };
			//
			// const formattedBody: UpdateAoiDTO = {
			// 	...request.body,
			// 	area: formattedArea
			// };
			const aoi = await this.adminAoiService.update(request.params.id, request.body);
			if (!aoi) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedAoi = this.sanitizer(aoi, adminAoiSanitizationRules);
			return returnApiResponse({ 'aoi': sanitizedAoi }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}


	// Add approve method
	async approve(request: FastifyRequest<{ Params: { id: string }, Body: ApproveAoiDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const aoi = await this.adminAoiService.approve(request.params.id, request.body, request.requestingUser.id);
			if (!aoi) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedAoi = this.sanitizer(aoi, adminAoiSanitizationRules);
			return returnApiResponse({ 'aoi': sanitizedAoi }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	//toTAI
	async toTAI(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		try {

			const aoi = await this.adminAoiService.toTAI(request.params.id);
			if (!aoi) {
				return returnApiResponse(null, request, reply);
			}
			return returnApiResponse(aoi, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}

	}

	// Override getAll to ensure proper loading of relationships
	// async getAll(request: FastifyRequest<{ Querystring: { page?: number, perPage?: number, sortBy?: string, orderBy?: string, load?: string } }>, reply: FastifyReply) {
	// 	try {
	// 		const { page = 1, perPage = 10, sortBy = 'desc', orderBy = 'id', load = 'operation,requestedByUser,approvedByUser' } = request.query;
	//
	// 		const { items, count } = await this.adminAoiService.findAll({
	// 			page,
	// 			perPage,
	// 			sortBy,
	// 			orderBy,
	// 			load
	// 		});
	//
	// 		const totalPages = Math.ceil(count / perPage);
	// 		const sanitizedItems = items.map(item => this.sanitizer(item, adminAoiSanitizationRules));
	//
	// 		return returnApiResponse({
	// 			aois: sanitizedItems,
	// 			pagination: {
	// 				page,
	// 				perPage,
	// 				total: count,
	// 				totalPages
	// 			}
	// 		}, request, reply);
	// 	} catch (error) {
	// 		request.messageCollector.addThrowableError(error);
	// 		return returnApiResponse(null, request, reply);
	// 	}
	// }
	//
	// // Override getById to ensure proper loading of relationships
	// async getById(request: FastifyRequest<{ Params: { id: string }, Querystring: { load?: string } }>, reply: FastifyReply) {
	// 	try {
	// 		const { id } = request.params;
	// 		const { load = 'operation,requestedByUser,approvedByUser' } = request.query;
	//
	// 		const aoi = await this.adminAoiService.findById(id, load);
	// 		if (!aoi) {
	// 			request.messageCollector.addError("Area of Interest not found");
	// 			return returnApiResponse(null, request, reply);
	// 		}
	//
	// 		const sanitizedAoi = this.sanitizer(aoi, adminAoiSanitizationRules);
	// 		return returnApiResponse({ aoi: sanitizedAoi }, request, reply);
	// 	} catch (error) {
	// 		request.messageCollector.addThrowableError(error);
	// 		return returnApiResponse(null, request, reply);
	// 	}
	// }
}