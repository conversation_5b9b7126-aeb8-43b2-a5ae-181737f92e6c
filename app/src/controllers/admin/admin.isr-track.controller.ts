// src/controllers/rules/rules.rules.isr-track.controller.ts

import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminISRTrackService from "@/services/admin/admin.isr-track.service.js";
import {
	CreateISRTrackDTO,
	UpdateISRTrackDTO,
} from "@/interfaces/admin/admin.isr-track.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import ISRTrackModel from '@/models/isr-track.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminISRTrackSanitizationRules } from "@/acl/sanitization/admin/isr-track.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminISRTrackController extends GenericCrudController<ISRTrackModel, CreateISRTrackDTO, UpdateISRTrackDTO> {
	private adminISRTrackService: AdminISRTrackService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminISRTrackService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'isrTrack', plural: 'isrTracks' }, ISRTrackModel);
		this.adminISRTrackService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateISRTrackDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const isrTrack = await this.adminISRTrackService.createWithUserId(request.body, request.requestingUser.id);
			if (!isrTrack) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedISRTrack = this.sanitizer(isrTrack, adminISRTrackSanitizationRules);
			return returnApiResponse({ 'isrTrack': sanitizedISRTrack }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateISRTrackDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const isrTrack = await this.adminISRTrackService.update(request.params.id, request.body);
			if (!isrTrack) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedISRTrack = this.sanitizer(isrTrack, adminISRTrackSanitizationRules);
			return returnApiResponse({ 'isrTrack': sanitizedISRTrack }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	//assignAssetsByIds
	async assignAssetsByIds(request: FastifyRequest<{ Params: { id: string }, Body: { assetId: number, missionId: number } }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const isrTrack = await this.adminISRTrackService.assignAssetsByIds(request.params.id, request.body.assetId, request.body.missionId);
			if (!isrTrack) {
				return returnApiResponse(null, request, reply);
			}
			const sanitizedISRTrack = this.sanitizer(isrTrack, adminISRTrackSanitizationRules);
			return returnApiResponse({ 'isrTrack': sanitizedISRTrack }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

    async deleteAssetById(request: FastifyRequest<{ Params: { id: string, assetId: string } }>, reply: FastifyReply) {
        try {
            const isrTrack = await this.adminISRTrackService.deleteAssetById(request.params.id, request.params.assetId);
            if (!isrTrack) {
                return returnApiResponse(null, request, reply);
            }
            const sanitizedISRTrack = this.sanitizer(isrTrack, adminISRTrackSanitizationRules);
            return returnApiResponse({ 'isrTrack': sanitizedISRTrack }, request, reply);
        } catch (error) {
            request.messageCollector.addThrowableError(error);
            return returnApiResponse(null, request, reply);
        }
    }
}