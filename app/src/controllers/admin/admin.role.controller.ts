import { FastifyRequest, FastifyReply } from "fastify";
import AdminRoleService from "@/services/admin/admin.role.service.js";
import {
	returnApiResponse
} from "@/utils/response.util.js";
import {
	CreateRoleDTO,
	UpdateRoleDTO,
} from "@/interfaces/admin/admin.role.interface.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import RoleModel from "@/models/role.model.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminRoleController extends Generic<PERSON>rudController<RoleModel, CreateRoleDTO, UpdateRoleDTO> {
	private adminRoleService: AdminRoleService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminRoleService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'role', plural: 'roles' }, RoleModel);
		this.adminRoleService = service;
	}

	async replaceSuperiorRoles(request: FastifyRequest<{ Params: { id: string }, Body: { superiorRoleIds: number[] } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const updatedRole = await this.adminRoleService.addSuperiorRoles(id, request.body.superiorRoleIds, true);
			if (!updatedRole) {
				request.messageCollector.addError("Role not found");
				return null;
			}
			const sanitizationRules = (request as any).sanitizationRules;
			const sanitizedRole = this.sanitizer(updatedRole, sanitizationRules);
			return returnApiResponse({ role: sanitizedRole }, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}

	}

	async addSuperiorRoles(request: FastifyRequest<{ Params: { id: string }, Body: { superiorRoleIds: number[] } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const updatedRole = await this.adminRoleService.addSuperiorRoles(id, request.body.superiorRoleIds, false);
			return this._updateChildResponse(updatedRole, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async removeSuperiorRole(request: FastifyRequest<{ Params: { id: string, roleId: string } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const roleId = parseInt(request.params.roleId);
			const updatedRole = await this.adminRoleService.removeSuperiorRole(id, roleId);
			return this._updateChildResponse(updatedRole, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async replaceSubordinateRoles(request: FastifyRequest<{ Params: { id: string }, Body: { subordinateRoleIds: number[] } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const updatedRole = await this.adminRoleService.addSubordinateRoles(id, request.body.subordinateRoleIds, true);
			return this._updateChildResponse(updatedRole, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async addSubordinateRoles(request: FastifyRequest<{ Params: { id: string }, Body: { subordinateRoleIds: number[] } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const updatedRole = await this.adminRoleService.addSubordinateRoles(id, request.body.subordinateRoleIds, false);
			return this._updateChildResponse(updatedRole, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	async removeSubordinateRole(request: FastifyRequest<{ Params: { id: string, roleId: string } }>, reply: FastifyReply) {
		try{
			const id = parseInt(request.params.id);
			const roleId = parseInt(request.params.roleId);
			const updatedRole = await this.adminRoleService.removeSubordinateRole(id, roleId);
			return this._updateChildResponse(updatedRole, request, reply);
		} catch (error: unknown) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	 private async _updateChildResponse(updatedRole: RoleModel | null, request: any, reply: FastifyReply) {
		if (!updatedRole) {
			request.messageCollector.addError("Role not found");
			return null;
		}
		const sanitizationRules = (request as any).sanitizationRules;
		 const sanitizedRole = this.sanitizer(updatedRole, sanitizationRules);
		return returnApiResponse({ role: sanitizedRole }, request, reply);
	}
}