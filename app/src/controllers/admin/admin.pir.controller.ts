import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminPirService from "@/services/admin/admin.pir.service.js";
import {
	CreatePirDTO,
	UpdatePirDTO,
	SearchPirsDTO
} from "@/interfaces/admin/admin.pir.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import PIRModel from '@/models/pir.model.js';
import { FastifyRequest, FastifyReply } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminPirSanitizationRules } from "@/acl/sanitization/admin/pir.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminPirController extends GenericCrudController<PIRModel, CreatePirDTO, UpdatePirDTO> {
	private adminPirService: AdminPirService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminPirService(messageCollector,requestingUser, organizationId);
		super(service, { singular: 'pir', plural: 'pirs' }, PIRModel);
		this.adminPirService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreatePirDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const Pir = await this.adminPirService.create(request.body);
			if (!Pir) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedPir = this.sanitizer(Pir,adminPirSanitizationRules);
			return returnApiResponse({ 'pir': sanitizedPir }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}
}