import { FastifyReply, FastifyRequest } from "fastify";
import { returnApiResponse } from "@/utils/response.util.js";
import DangerService from "@/services/admin/danger.service.js";

import { fixOperationsDesignation, fixMissionDesignation }	 from '@/utils/fixOperationsDesignation.js';

export default class DangerController {
	private dangerService: DangerService;

	constructor() {
		this.dangerService = new DangerService();
	}

	async resetEverything(request: FastifyRequest, reply: FastifyReply) {
		const data = await this.dangerService.resetEverything(request);
		if (!data || !data.success) {
			return returnApiResponse(null, request, reply);
		}
		return returnApiResponse({
			tablesAffected: data.tablesAffected,
			tablesTotal: data.tablesTotal
		}, request, reply);
	}

	async fixDesignation(request: FastifyRequest, reply: FastifyReply) {
		console.log('Fixing designations...');
		await fixOperationsDesignation();
		console.log('Fixing mission designations...');
		await fixMissionDesignation();
	}
}