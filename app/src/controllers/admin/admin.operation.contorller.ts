import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import AdminOperationService from "@/services/admin/admin.operation.service.js";
import {
	CreateOperationDTO,
	UpdateOperationDTO,
	SearchOperationsDTO, AddUpdateUsersToOperationDTO, RemoveUsersFromOperationDTO, GetOpSyncDTO
} from "@/interfaces/admin/admin.operation.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import OperationModel from '@/models/operation.model.js';
import {returnApiResponse} from "@/utils/response.util.js";
import {FastifyReply, FastifyRequest} from "fastify";
import { adminOperationSanitizationRules } from "@/acl/sanitization/admin/operation.sanitization.js";
import { adminMissionSanitizationRules } from "@/acl/sanitization/admin/mission.sanitization.js";
import {userUserSanitizationRules} from "@/acl/sanitization/user/user.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminOperationController extends GenericCrudController<OperationModel, CreateOperationDTO, UpdateOperationDTO> {
	private adminOperationService: AdminOperationService;


	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminOperationService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'operation', plural: 'operations' }, OperationModel);
		this.adminOperationService = service;
	}

	async resetOperation(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		try {
			const {id} = request.params;
			const operation = await this.adminOperationService.resetOperation(id);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({operation: sanitizedOperation}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}


	async sync(request: FastifyRequest<{ Params: { id: string }, Querystring: GetOpSyncDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const response = await this.adminOperationService.sync(id, request.query);
			if (!response) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const { syncs, startDate, endDate, type } = response;
			const sanitizedSyncs = this.sanitizer(syncs, adminMissionSanitizationRules);
			return returnApiResponse({
				syncs: sanitizedSyncs,
				startDate,
				endDate,
				type
			}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}

	// addUsersToOperation(request, reply);
	async addUsersToOperation(request: FastifyRequest<{ Params: { id: string }, Body: AddUpdateUsersToOperationDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { users } = request.body;
			const operation = await this.adminOperationService.addUsersToOperation(id, users);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	// removeUserFromOperation(request, reply);
	async removeUserFromOperation(request: FastifyRequest<{ Params: { id: string, userId: string } }>, reply: FastifyReply) {
		try {
			const { id, userId } = request.params;
			const operation = await this.adminOperationService.removeUser(id, userId);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	// removeUsersFromOperation(request, reply);
	async removeUsersFromOperation(request: FastifyRequest<{ Params: { id: string }, Body: RemoveUsersFromOperationDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { userIds } = request.body;
			const operation = await this.adminOperationService.removeUsers(id, userIds);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}
	// patchUsersToOperation(request, reply);
	async patchUsersToOperation(request: FastifyRequest<{ Params: { id: string }, Body: AddUpdateUsersToOperationDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { users } = request.body;
			const operation = await this.adminOperationService.patchUsers(id, users);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	// updateUsersToOperation(request, reply);
	async updateUsersToOperation(request: FastifyRequest<{ Params: { id: string }, Body: AddUpdateUsersToOperationDTO }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const { users } = request.body;
			const operation = await this.adminOperationService.updateUsers(id, users);
			if (!operation) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	async getOperationUsers(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
		try {
			const { id } = request.params;
			const users = await this.adminOperationService.getOperationUsers(id);
			if (!users) {
				request.messageCollector.addError("Operation not found");
				return returnApiResponse(null, request, reply);
			}
			const sanitizedUsers = users.map(user => this.sanitizer(user, userUserSanitizationRules));
			return returnApiResponse({ users: sanitizedUsers }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}

	async toggleStatus(request: FastifyRequest<{ Params: { id: string }, Body: {
		isActive: boolean
		} }>, reply: FastifyReply) {
		try {
			const { isActive } = request.body;
			const id = request.params.id;
			const operation = await this.adminOperationService.toggleStatus(id, isActive);
			const sanitizedOperation = this.sanitizer(operation, adminOperationSanitizationRules);
			return returnApiResponse({ operation: sanitizedOperation }, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
		}
		return returnApiResponse(null, request, reply);
	}
}