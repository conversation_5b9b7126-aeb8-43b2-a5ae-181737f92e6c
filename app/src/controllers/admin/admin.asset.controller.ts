import AdminAssetService from "@/services/admin/admin.asset.service.js";
import {
	CreateAssetDTO,
	UpdateAssetDTO,
} from "@/interfaces/admin/admin.asset.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import AssetModel from "@/models/asset.model.js";
import {GenericCrudController} from "@/controllers/admin/admin.item-crud.controller.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";

export default class AdminAssetController  extends GenericCrudController<AssetModel, CreateAssetDTO, UpdateAssetDTO> {
	private adminAssetService: AdminAssetService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminAssetService(messageCollector, requestingUser, organizationId);
		super(service, { singular: 'asset', plural: 'assets' }, AssetModel);
		this.adminAssetService = service;
	}

}