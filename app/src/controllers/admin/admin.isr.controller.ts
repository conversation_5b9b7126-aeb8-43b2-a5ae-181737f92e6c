import { FastifyReply, FastifyRequest } from "fastify";
import { GenericCrudController } from "@/controllers/admin/admin.item-crud.controller.js";
import {
	CreateISRDTO,
	UpdateISRDTO
} from "@/interfaces/admin/admin.isr.interface.js";
import ISRModel from "@/models/isr.model.js";
import AdminISRService from "@/services/admin/admin.isr.service.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { returnApiResponse } from "@/utils/response.util.js";
import { adminISRSanitizationRules } from "@/acl/sanitization/admin/isr.sanitization.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";



export default class AdminISRController extends GenericCrudController<ISRModel, CreateISRDTO, UpdateISRDTO> {
	private adminISRService: AdminISRService;

	constructor(messageCollector: MessageCollector,  requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		const service = new AdminISRService(messageCollector, requestingUser, organizationId);
		super(service, {singular: 'isr', plural: 'isrs'}, ISRModel);
		this.adminISRService = service;
	}

	// Override create method to handle userId from request
	async create(request: FastifyRequest<{ Body: CreateISRDTO }>, reply: FastifyReply) {
		try {
			if (!request.requestingUser) {
				request.messageCollector.addError("User ID not found in request");
				return returnApiResponse(null, request, reply);
			}

			const dataWithUserId = {
				...request.body,
				userId: request.requestingUser.id
			};

			const isr = await this.adminISRService.create(dataWithUserId);
			if (!isr) {
				return returnApiResponse(null, request, reply);
			}

			const sanitizedIsr = this.sanitizer(isr, adminISRSanitizationRules);
			return returnApiResponse({'isr': sanitizedIsr}, request, reply);
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return returnApiResponse(null, request, reply);
		}
	}


	// rules.isr.controller.ts


async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateISRDTO }>, reply: FastifyReply) {
	try {
		const { id } = request.params;
		const updateData = request.body;

		// First fetch the existing ISR to ensure it exists
		const existingIsr = await this.adminISRService.findById(id);
		if (!existingIsr) {
			request.messageCollector.addError("ISR not found");
			return returnApiResponse(null, request, reply);
		}


		// Validate dates if provided
		if (updateData.commenceAt && updateData.concludeAt) {
			const commenceDate = new Date(updateData.commenceAt);
			const concludeDate = new Date(updateData.concludeAt);

			if (commenceDate >= concludeDate) {
				request.messageCollector.addError("Commence date must be before conclude date");
				return returnApiResponse(null, request, reply);
			}
		}

		// // Validate center coordinates if provided
		// if (updateData.centerCoordinates) {
		// 	const { coordinates } = updateData.centerCoordinates;
		// 	if (!coordinates ||
		// 		!Array.isArray(coordinates) ||
		// 		coordinates.length !== 2 ||
		// 		!coordinates.every(coord => typeof coord === 'number')) {
		// 		request.messageCollector.addError("Invalid center coordinates format");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		//
		// 	const [longitude, latitude] = coordinates;
		// 	if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
		// 		request.messageCollector.addError("Center coordinates out of valid range");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// // Validate zoom if provided
		// if (updateData.zoom !== undefined) {
		// 	if (updateData.zoom < 0 || updateData.zoom > 22) {
		// 		request.messageCollector.addError("Zoom level must be between 0 and 22");
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// If type is changed, ensure coordinates match the new type
		// if (updateData.type && updateData.coordinates) {
		// 	const coordinates = updateData.coordinates as GeometryCollection; // Type assertion here
		// 	const geometryTypes = coordinates.geometries.map(g => g.type);
		//
		// 	const hasMatchingGeometry = geometryTypes.some(gType => {
		// 		switch(updateData.type) {
		// 			case 'point': return gType === 'Point';
		// 			case 'line': return gType === 'LineString';
		// 			case 'polygon': return gType === 'Polygon';
		// 			default: return false;
		// 		}
		// 	});
		//
		// 	if (!hasMatchingGeometry) {
		// 		request.messageCollector.addError(
		// 			`Geometry collection must contain at least one ${updateData.type} geometry`
		// 		);
		// 		return returnApiResponse(null, request, reply);
		// 	}
		// }

		// Perform the update
		const updatedIsr = await this.adminISRService.update(id, updateData);
		if (!updatedIsr) {
			return returnApiResponse(null, request, reply);
		}

		// Sanitize and return the response
		const sanitizedIsr = this.sanitizer(updatedIsr, adminISRSanitizationRules);

		request.messageCollector.addSuccess("ISR updated successfully");
		return returnApiResponse({ 'isr': sanitizedIsr }, request, reply);

	} catch (error) {
		request.messageCollector.addThrowableError(error);
		return returnApiResponse(null, request, reply);
	}
}


}