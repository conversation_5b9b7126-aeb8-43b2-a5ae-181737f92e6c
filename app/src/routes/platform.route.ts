// import {FastifyInstance, preHand<PERSON><PERSON><PERSON><PERSON>and<PERSON>} from "fastify";
// import {routeErrorHandler} from "@/utils/response.util.js";
// import {
// 	GetPlatformDTO,
// 	getPlatformSchema,
// 	SearchPlatformsDTO,
// 	searchPlatformsSchema
// } from "@/interfaces/admin/admin.platform.interface.js";
// import AdminPlatformController from "@/controllers/admin/admin.platform.controller.js";
// import authMiddleware from "@/middleware/auth.middleware.js";
//
//
// export default async function (fastify: FastifyInstance) {
//
//
// 	const path = '/platforms';
// 	const crudMiddleware: preHandlerHookHandler[] = [
// 		authMiddleware as preHandlerHookHandler,
// 	];
//
// 	const preHandlerErrorHandlerOptions = {
// 		preHandler: crudMiddleware,
// 		errorHandler: routeErrorHandler,
// 	};
//
// 	fastify.get<{
// 		Querystring: SearchPlatformsDTO
// 	}>(`${path}/search`,
// 		{
// 			schema: searchPlatformsSchema,
// 			...preHandlerErrorHandlerOptions
// 		}, async (request, reply) => {
// 			return await new AdminPlatformController(request.messageCollector).searchPlatforms(request, reply);
// 		}
// 	);
//
// 	fastify.get<{
// 		Params: GetPlatformDTO
// 	}>(`${path}/:id`,
// 		{
// 			schema: getPlatformSchema,
// 			...preHandlerErrorHandlerOptions
// 		}, async (request, reply) => {
// 			return await new AdminPlatformController(request.messageCollector).getPlatformById(request, reply);
// 		}
// 	);
//
//
// }


import {FastifyInstance, preHandlerHookHandler} from "fastify";
import {routeErrorHandler} from "@/utils/response.util.js";
import {
	GetPlatformDTO,
	SearchPlatformsDTO,
	getPlatformSchema,
	searchPlatformsSchema,
	createPlatformSchema,
	updatePlatformSchema,
	CreatePlatformDTO,
	UpdatePlatformDTO
} from "@/interfaces/admin/admin.platform.interface.js";
import AdminPlatformController from "@/controllers/admin/admin.platform.controller.js";
import authMiddleware from "@/middleware/auth.middleware.js";
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import PlatformModel from "@/models/platform.model.js";
import { adminPlatformSanitizationRules } from "@/acl/sanitization/admin/platform.sanitization.js";


export default async function (fastify: FastifyInstance) {
	const path = '/platforms';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	// Create handlers for standard CRUD operations
	const handlers = createCrudRouteHandlers<PlatformModel, CreatePlatformDTO, UpdatePlatformDTO, AdminPlatformController>(AdminPlatformController);

	// Register existing routes
	fastify.get<{
		Querystring: SearchPlatformsDTO
	}>(`${path}/search-platforms`,
		{
			schema: searchPlatformsSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminPlatformController(request.messageCollector, request.requestingUser, request.organizationId)
				.searchPlatforms(request, reply);
		}
	);

	// Custom route for getting platforms by type
	fastify.get<{
		Querystring: { type: string }
	}>(`${path}/by-type`, {
		schema: {
			querystring: {
				type: 'object',
				properties: {
					type: { type: 'string', enum: ['space', 'air', 'land', 'sea', 'hybrid', 'other'] }
				},
				required: ['type']
			}
		},
		...preHandlerErrorHandlerOptions
	}, async (request, reply) => {
		return await new AdminPlatformController(request.messageCollector, request.requestingUser, request.organizationId)
			.getPlatformsByType(request, reply);
	});

	// Register standard CRUD routes (will handle any routes not already registered)
	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createPlatformSchema,
		updateSchema: updatePlatformSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminPlatformSanitizationRules
	});
}