import {FastifyInstance, preH<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} from "fastify";
import {
	AssignRolesBodyDTO,
	CreateUserD<PERSON>,
	assignRolesSchema,
	removeRoleSchema,
	createUserSchema,
	AssignUserToOperationDTO,
	assignUserToOperationSchema,
	updateUserOperationsAccessSchema,
	UpdateUserOperationsAccessDTO,
	LoginUserDTO,
	loginSchema,
    switchOrgSchema,
	refreshTokenSchema,
	activateSchema,
	ActivateUserParamsDTO,
	ActivateUserBodyDTO,
	RequestPasswordResetBodyDTO,
	requestPasswordResetSchema,
	ResetPasswordBodyDTO,
	resetPasswordSchema,
	getUserProfileParamsDTO,
	getUserProfileSchema,
	UpdateUserDTO,
	UpdateUserPasswordDTO,
	updateUserPasswordSchema,
	updateUserSchema,
    organizationSchemas
} from "@/interfaces/admin/admin.user.interface.js";
import AdminUserController from "@/controllers/admin/admin.user.controller.js";
import {routeError<PERSON>andler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import UserModel from "@/models/user.model.js";
import {adminUserSanitizationRules} from "@/acl/sanitization/admin/user.sanitization.js";
import UserController from '@/controllers/user/user.controller.js';
import authMiddleware from '@/middleware/auth.middleware.js';
import selfMiddleware from "@/middleware/self.middleware.js";
import validationMiddleware from '@/middleware/validation.middleware.js';

export default async function (fastify: FastifyInstance) {
	const path = '/users';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<UserModel, CreateUserDTO, UpdateUserDTO, AdminUserController>(AdminUserController);

	const userController = new UserController();
	// fastify.register(messageCollectorPlugin);

	fastify.post<{
		Body: LoginUserDTO
	}>('/users/login', {
		schema: loginSchema,
	}, userController.login.bind(userController));

	fastify.post('/users/refresh-token', {
        schema: refreshTokenSchema
    }, userController.refreshToken.bind(userController));

    fastify.post<{
        Params: { id: string },
        Body: { newOrgId: string }
    }>('/users/:id/switch-organizations', {
        schema: switchOrgSchema,
        ...preHandlerErrorHandlerOptions
    }, userController.switchOrg.bind(userController));

    fastify.get<{
        Params: { id: string }
    }>('/users/:id/organizations', {
        schema: organizationSchemas,
        ...preHandlerErrorHandlerOptions
    }, userController.organizations.bind(userController));

    fastify.get<{
        Params: { id: string }
    }>('/users/:id/organization', {
        schema: organizationSchemas,
        ...preHandlerErrorHandlerOptions
    }, userController.organization.bind(userController));

	// fastify.post<{
	// 	Params: ActivateUserParamsDTO,
	// 	Body: ActivateUserBodyDTO,
	// }>('/users/activate/:hashCode', {
	// 		errorHandler: validationMiddleware,
	// 		schema: activateSchema
	// 	},
	// 	userController.activateUser.bind(userController)
	// );

	fastify.post<{
		Body: RequestPasswordResetBodyDTO,
	}>('/users/reset-password-request',
		{
			errorHandler: validationMiddleware,
			schema: requestPasswordResetSchema
		},
		userController.initiatePasswordReset.bind(userController)
	);

	fastify.post<{
		Body: ResetPasswordBodyDTO,
	}>('/users/reset-password',
		{
			errorHandler: validationMiddleware,
			schema: resetPasswordSchema
		},
		userController.resetPassword.bind(userController)
	);

	fastify.patch<{
		Params: { id: string },
		Body: UpdateUserPasswordDTO
	}>('/users/:id/password',
		{
			preHandler: fastify.auth([authMiddleware, selfMiddleware]),
			errorHandler: validationMiddleware,
			schema: updateUserPasswordSchema
		},
		userController.updatePassword.bind(userController)
	);

	fastify.post<{
		Params: { id: string },
		Body: AssignUserToOperationDTO,
	}>('/users/:id/operations',
		{
			schema: assignUserToOperationSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).assignUserToOperation(request, reply);
		}
	);

	fastify.delete<{
		Params: { id: string, operationId: string },
	}>('/users/:id/operations/:operationId',
		{
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).removeUserFromOperation(request, reply);
		}
	);

	fastify.patch<{
		Params: { id: string, operationId: string },
		Body: UpdateUserOperationsAccessDTO,
	}>('/users/:id/operations/:operationId/update-access-type',
		{
			schema: updateUserOperationsAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).updateUserOperationsAccess(request, reply);
		}
	)
	fastify.put<{
		Params: { id: string },
		Body: AssignRolesBodyDTO
	}>('/users/:id/roles',
		{
			schema: assignRolesSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).assignRoles(request, reply);
		}
	);

	fastify.patch<{
		Params: { id: string },
		Body: AssignRolesBodyDTO
	}>('/users/:id/roles',
		{
			schema: assignRolesSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).addRoles(request, reply);
		}
	);

	fastify.delete<{
		Params: { id: string, roleId: string }
	}>('/users/:id/roles/:roleId',
		{
			schema: removeRoleSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminUserController(request.messageCollector, request.requestingUser, request.organizationId).removeRoleFromUser(request, reply);
		}
	);

	// fastify.patch<{
	// 	Params: { id: string },
	// 	Body: UpdateUserDTO
	// }>('/users/:id',
	// 	{
	// 		preHandler: fastify.auth([authMiddleware, selfMiddleware]),
	// 		errorHandler: validationMiddleware,
	// 		schema: updateUserSchema
	// 	},
	// 	userController.updateUser.bind(userController)
	// );

	// fastify.get<{
	// 	Params: getUserProfileParamsDTO,
	// }>('/users/:id',
	// 	{
	// 		preHandler: fastify.auth([authMiddleware, selfMiddleware]),
	// 		schema: getUserProfileSchema,
	// 		errorHandler: validationMiddleware,
	// 	},
	// 	async (request, reply) => {
	// 		return userController.viewProfile(request, reply);
	// 	}
	// );

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createUserSchema,
		updateSchema: updateUserSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminUserSanitizationRules
	});
}