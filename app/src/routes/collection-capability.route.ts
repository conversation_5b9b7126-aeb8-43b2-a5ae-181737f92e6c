import { FastifyInstance, preHandlerHookHandler } from "fastify";
import {
	CreateCollectionCapabilityDTO,
	UpdateCollectionCapabilityDTO,
	UpdateStatusDTO,
	UpdateStatusRequest,
	createCollectionCapabilitySchema,
	updateCollectionCapabilitySchema,
	updateStatusSchema,
} from "@/interfaces/admin/admin.collection-capability.interface.js";
import AdminCollectionCapabilityController from "@/controllers/admin/admin.collection-capability.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import { adminCollectionCapabilitySanitizationRules } from "@/acl/sanitization/admin/collection-capability.sanitization.js";
import { FastifyRequest, FastifyReply } from "fastify";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/collection-capabilities';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	// Create handlers for standard CRUD operations
	const handlers = createCrudRouteHandlers<
		CollectionCapabilityModel,
		CreateCollectionCapabilityDTO,
		UpdateCollectionCapabilityDTO,
		AdminCollectionCapabilityController
	>(AdminCollectionCapabilityController);

	// Register standard CRUD routes
	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createCollectionCapabilitySchema,
		updateSchema: updateCollectionCapabilitySchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminCollectionCapabilitySanitizationRules
	});

	// Register status update route with proper typing
	fastify.put<UpdateStatusRequest>(`${path}/:id/update-status`,
		{
			schema: updateStatusSchema,
			...preHandlerErrorHandlerOptions,
		},
		async (request: FastifyRequest<UpdateStatusRequest>, reply: FastifyReply) => {
			const controller = new AdminCollectionCapabilityController(request.messageCollector, request.requestingUser, request.organizationId);
			return controller.updateStatus(request, reply);
		}
	);
}