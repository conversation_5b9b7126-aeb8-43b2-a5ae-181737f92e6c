import {FastifyInstance, FastifyRequest, FastifyReply, preHandler<PERSON>ook<PERSON>and<PERSON>} from "fastify";
import {
	CreateOperationDTO,
	UpdateOperationDTO,
	SearchOperationsDTO,
	searchOperationsSchema,
	createOperationSchema,
	updateOperationSchema,
	getOperationUsersSchema,
	AddUpdateUsersToOperationDTO,
	addUpdateUsersAccessSchema,
	RemoveUsersFromOperationDTO,
	removeUserAccessSchema,
	getOpSyncSchema,
	ToggleOperationStatusDTO, toggleOperationStatusSchema, GetOpSyncDTO
} from '@/interfaces/admin/admin.operation.interface.js';
import AdminOperationController from "@/controllers/admin/admin.operation.contorller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import OperationModel from "@/models/operation.model.js";
import {adminOperationSanitizationRules} from "@/acl/sanitization/admin/operation.sanitization.js";
import {addTaisSchema} from "@/interfaces/admin/admin.mission.interface.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/operations';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<OperationModel, CreateOperationDTO, UpdateOperationDTO, AdminOperationController>(AdminOperationController);


	fastify.get<{
		Params: {
			id: string,
		},
		Querystring: GetOpSyncDTO,
	}>(`${path}/:id/sync`,
		{
			schema: getOpSyncSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).sync(request, reply);
		}
	);
	
	//reset operation
	

	//get operation users
	fastify.post<{
		Params: { id: string },
	}>(`${path}/:id/reset`,
		{
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).resetOperation(request, reply);
		}
	);
	fastify.get<{
		Params: { id: string },
	}>(`${path}/:id/users`,
		{
			schema: getOperationUsersSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).getOperationUsers(request, reply);
		}
	);

	//add users to operation
	fastify.post<{
		Params: { id: string },
		Body: AddUpdateUsersToOperationDTO,
	}>(`${path}/:id/add-users`,
		{
			schema: addUpdateUsersAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).addUsersToOperation(request, reply);
		}
	);

	//remove a single user from operation
	fastify.delete<{
		Params: { id: string, userId: string },
	}>(`${path}/:id/remove-user/:userId`,
		{
			schema: removeUserAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).removeUserFromOperation(request, reply);
		}
	);

	//remove users from operation
	fastify.post<{
		Params: { id: string },
		Body: RemoveUsersFromOperationDTO,
	}>(`${path}/:id/remove-users`,
		{
			schema: removeUserAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).removeUsersFromOperation(request, reply);
		}
	);

	//update operations users access via patch
	fastify.patch<{
		Params: { id: string },
		Body: AddUpdateUsersToOperationDTO,
	}>(`${path}/:id/update-users`,
		{
			schema: addUpdateUsersAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).patchUsersToOperation(request, reply);
		}
	);

	//toggle status of operation
	fastify.patch<{
		Params: { id: string },
		Body: ToggleOperationStatusDTO,
	}>(`${path}/:id/status`,
		{
			schema: toggleOperationStatusSchema,
			...preHandlerErrorHandlerOptions
		},async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).toggleStatus(request, reply);
		}
	);

	//update operations users via put
	fastify.put<{
		Params: { id: string },
		Body: AddUpdateUsersToOperationDTO,
	}>(`${path}/:id/update-users`,
		{
			schema: addUpdateUsersAccessSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOperationController(request.messageCollector, request.requestingUser, request.organizationId).updateUsersToOperation(request, reply);
		}
	);


	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createOperationSchema,
		updateSchema: updateOperationSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminOperationSanitizationRules
	});

}