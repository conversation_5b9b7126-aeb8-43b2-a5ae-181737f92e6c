import { FastifyInstance, preHandler<PERSON>ookHandler } from "fastify";
import {
	CreateAoiDTO,
	UpdateAoiDTO,
	ApproveAoiDTO,
	ApproveAoiRequest,
	createAoiSchema,
	updateAoiSchema,
	approveAoiSchema, ToTAIAoiRequest, toTAIAoiSchema,
} from "@/interfaces/admin/admin.aoi.interface.js";
import authMiddleware from "@/middleware/auth.middleware.js";

import AdminAoiController from "@/controllers/admin/admin.aoi.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import AoiModel from "@/models/aoi.model.js";
import { adminAoiSanitizationRules } from "@/acl/sanitization/admin/aoi.sanitization.js";
import { FastifyRequest, FastifyReply } from "fastify";

export default async function (fastify: FastifyInstance) {
	const path = '/aois';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	// Create handlers for standard CRUD operations
	const handlers = createCrudRouteHandlers<AoiModel, CreateAoiDTO, UpdateAoiDTO, AdminAoiController>(AdminAoiController);


	fastify.put<ApproveAoiRequest>(`${path}/:id/toggle-approval`,
		{
			schema: approveAoiSchema,
			...preHandlerErrorHandlerOptions,
		},
		async (request: FastifyRequest<ApproveAoiRequest>, reply: FastifyReply) => {
			const controller = new AdminAoiController(request.messageCollector, request.requestingUser, request.organizationId);
			return controller.approve(request, reply);
		}
	);


	fastify.put<ToTAIAoiRequest>(`${path}/:id/to-tai`,
		{
			schema: toTAIAoiSchema,
			...preHandlerErrorHandlerOptions,
		},
		async (request: FastifyRequest<ToTAIAoiRequest>, reply: FastifyReply) => {
			const controller = new AdminAoiController(request.messageCollector, request.requestingUser, request.organizationId);
			return controller.toTAI(request, reply);
		}
	);

	// Register standard CRUD routes
	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createAoiSchema,
		updateSchema: updateAoiSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminAoiSanitizationRules
	});

	// Register approval route with proper typing



}