import { FastifyInstance, preHandlerHookHandler } from "fastify";
import {
	CreatePirDTO,
	UpdatePirDTO,
	createPirSchema,
	updatePirSchema,
} from "@/interfaces/admin/admin.pir.interface.js";
import AdminPirController from "@/controllers/admin/admin.pir.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import PIRModel from "@/models/pir.model.js";
import { adminPirSanitizationRules } from "@/acl/sanitization/admin/pir.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/pirs';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<PIRModel, CreatePirDTO, UpdatePirDTO, AdminPirController>(AdminPirController);




	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createPirSchema,
		updateSchema: updatePirSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminPirSanitizationRules
	});
}
