import { FastifyInstance } from "fastify";

export default async function (fastify: FastifyInstance) {
	fastify.get('/', async (request, reply) => {
		const apiInfo = {
			name: 'Lumio API',
			version: process.env.npm_package_version || '1.0.0',
			status: 'online',
			environment: process.env.NODE_ENV || 'development',
			codeTimestamp: process.env.CODE_TIMESTAMP || 'NA',
			timestamp: new Date().toISOString(),
		};

		return reply.code(200).send(apiInfo);
	});
}