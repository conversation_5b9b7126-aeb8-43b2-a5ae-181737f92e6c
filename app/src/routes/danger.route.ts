import { FastifyInstance } from "fastify";
import <PERSON><PERSON><PERSON>roll<PERSON> from "@/controllers/admin/danger.controller.js";
import authMiddleware from "@/middleware/auth.middleware.js";
import validationMiddleware from "@/middleware/validation.middleware.js";

export const resetEverythingSchema = {
	response: {
		200: {
			type: 'object',
			properties: {
				success: { type: 'boolean' },
				tablesAffected: { type: 'number' },
				tablesTotal: { type: 'number' }
			}
		}
	}
};

export default async function (fastify: FastifyInstance) {
	const dangerController = new DangerController();
	const path = '/danger';

	// Reset everything endpoint
	fastify.post(`${path}/reset`, {
		schema: resetEverythingSchema,
		preHandler: fastify.auth([
			authMiddleware,
			async (request, reply) => {
				if (!request.requestingUser || request.requestingUser.accountType !== 'org_admin') {
					return reply.code(403).send({
						success: false,
						data: null,
						error: 'Administrator access required',
						messages: [{
							message: 'This operation requires administrator privileges',
							type: 'error'
						}]
					});
				}
			}
		]),
		errorHandler: validationMiddleware,
	}, dangerController.resetEverything.bind(dangerController));

	fastify.get(`${path}/fix-designation`, {
		preHandler: fastify.auth([
			authMiddleware,
			async (request, reply) => {
				if (!request.requestingUser || request.requestingUser.accountType !== 'org_admin') {
					return reply.code(403).send({
						success: false,
						data: null,
						error: 'Administrator access required',
						messages: [{
							message: 'This operation requires administrator privileges',
							type: 'error'
						}]
					});
				}
			}
		]),
		errorHandler: validationMiddleware,
	}, dangerController.fixDesignation.bind(dangerController));
}