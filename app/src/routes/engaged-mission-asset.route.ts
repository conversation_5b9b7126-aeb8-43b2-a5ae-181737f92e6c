import { FastifyInstance, preHandlerHookHandler } from "fastify";
import {
    CreateEngagedMissionAssetDTO,
    UpdateEngagedMissionAssetDTO,
    createEngagedMissionAssetSchema,
    updateEngagedMissionAssetSchema,
} from "@/interfaces/admin/admin.engaged-mission-asset.interface.js";
import authMiddleware from "@/middleware/auth.middleware.js";
import AdminEngagedMissionAssetController from "@/controllers/admin/admin.engaged-mission-asset.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";
import { adminEngagedMissionAssetSanitizationRules } from "@/acl/sanitization/admin/admin.engaged-mission-asset.sanitization.js";
import { FastifyRequest, FastifyReply } from "fastify";

export default async function (fastify: FastifyInstance) {
    const path = '/engaged-mission-assets';
    const crudMiddleware: preHandlerHookHandler[] = [
        authMiddleware as preHandlerHookHandler,
    ];

    const preHandlerErrorHandlerOptions = {
        preHandler: crudMiddleware,
        errorHandler: routeErrorHandler,
    };

    // Create handlers for standard CRUD operations
    const handlers = createCrudRouteHandlers<EngagedMissionAssetModel, CreateEngagedMissionAssetDTO, UpdateEngagedMissionAssetDTO, AdminEngagedMissionAssetController>(AdminEngagedMissionAssetController);

    // Register standard CRUD routes
    registerCrudRoutes(fastify, handlers, path, {
        createSchema: createEngagedMissionAssetSchema,
        updateSchema: updateEngagedMissionAssetSchema,
        preHandlerErrorHandlerOptions,
        sanitizationRules: adminEngagedMissionAssetSanitizationRules
    });
}