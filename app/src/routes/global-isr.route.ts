import { FastifyInstance, preHandler<PERSON>ook<PERSON>andler } from "fastify";
import { CreateGlobalISRDTO, UpdateGlobalISRDTO, createGlobalISRSchema, updateGlobalISRSchema } from "@/interfaces/admin/admin.global-isr.interface.js";
import AdminGlobalISRController from "@/controllers/admin/admin.global-isr.controller.js";
import { routeErrorHandler } from "@/utils/response.util.js";
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import GlobalISRModel from "@/models/global-isr.model.js";
import { adminGlobalISRSanitizationRules } from "@/acl/sanitization/admin/global-isr.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/global-isrs';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<GlobalISRModel, CreateGlobalISRDTO, UpdateGlobalISRDTO, AdminGlobalISRController>(
		AdminGlobalISRController
	);

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createGlobalISRSchema,
		updateSchema: updateGlobalISRSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminGlobalISRSanitizationRules
	});
};