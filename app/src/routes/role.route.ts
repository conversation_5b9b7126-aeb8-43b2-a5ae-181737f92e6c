import {FastifyInstance, FastifyRequest, FastifyReply, preHandler<PERSON>ook<PERSON>and<PERSON>} from "fastify";
import {
UpdateRoleDTO,
	CreateRoleDTO,
	UpdateSuperiorRoleSchema,
	UpdateSubordinateRoleSchema,
	RemoveSubordinateRoleSchema,
	RemoveSuperiorRoleSchema,
	CreateRoleBodySchema,
	UpdateRoleBodySchema,
} from "@/interfaces/admin/admin.role.interface.js";
import AdminRoleController from "@/controllers/admin/admin.role.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import RoleModel from "@/models/role.model.js";
import {adminRoleSanitizationRules} from "@/acl/sanitization/admin/role.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/roles';

	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<RoleModel, CreateRoleDTO, UpdateRoleDTO, AdminRoleController>(AdminRoleController);

	//SUPERIOR ROLES

	//deterministically add superior roles
	fastify.put<{
		Params: { id: string },
		Body: { superiorRoleIds: number[] }
	}>(`${path}/:id/superior-roles`, {
		...preHandlerErrorHandlerOptions,
		schema: UpdateSuperiorRoleSchema,
	}, async (request, reply) => {
		return await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).replaceSuperiorRoles(request, reply);
	});

	//add superior roles
	fastify.patch<{
		Params: { id: string },
		Body: { superiorRoleIds: number[] }
	}>(`${path}/:id/superior-roles`, {
		...preHandlerErrorHandlerOptions,
		schema: UpdateSuperiorRoleSchema,
	}, async (request, reply) => {
		return await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).addSuperiorRoles(request, reply);
	});

	//remove superior role
	fastify.delete<{
		Params: { id: string, roleId: string }
	}>(`${path}/:id/superior-roles/:roleId`, {
		...preHandlerErrorHandlerOptions,
		schema: RemoveSuperiorRoleSchema,
	}, async (request, reply) => {
		return await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).removeSuperiorRole(request, reply);
	});

	//SUBORDINATE ROLES

	//deterministically add subordinate roles
	fastify.put<{
		Params: { id: string },
		Body: { subordinateRoleIds: number[] }
	}>(`${path}/:id/subordinate-roles`, {
		...preHandlerErrorHandlerOptions,
		schema: UpdateSubordinateRoleSchema,
	}, async (request, reply) => {
		return  await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).replaceSubordinateRoles(request,reply)
	});

	//remove subordinate role
	fastify.delete<{
		Params: { id: string, roleId: string }
	}>(`${path}/:id/subordinate-roles/:roleId`, {
		...preHandlerErrorHandlerOptions,
		schema: RemoveSubordinateRoleSchema,
	}, async (request, reply) => {
		return await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).removeSubordinateRole(request, reply);
	});

	//add subordinate roles
	fastify.patch<{
		Params: { id: string },
		Body: { subordinateRoleIds: number[] }
	}>(`${path}/:id/subordinate-roles`, {
		...preHandlerErrorHandlerOptions,
		schema: UpdateSubordinateRoleSchema,
	}, async (request, reply) => {
		return await new AdminRoleController(request.messageCollector, request.requestingUser, request.organizationId).addSubordinateRoles(request, reply);
	});

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: CreateRoleBodySchema,
		updateSchema: UpdateRoleBodySchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminRoleSanitizationRules
	});

}