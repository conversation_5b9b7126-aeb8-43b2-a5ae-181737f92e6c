import rootRoute from "@/routes/root.route.js";

import adminUserRoutes from "@/routes/user.route.js";
import adminRoleRoutes from "@/routes/role.route.js";
import adminOrganizationRoutes from "@/routes/organization.route.js";
import adminOperationRoute from "@/routes/operation.route.js";
import adminPlatformRoutes from "@/routes/platform.route.js";
import adminAssetRoutes from "@/routes/asset.route.js";
import adminAoiRoutes from "@/routes/aoi.route.js";
import adminPirRoutes from "@/routes/pir.route.js";
import adminInformationRequirementRoutes from "@/routes/information-requirement.route.js";
import adminCollectionCapabilityRoutes from "@/routes/collection-capability.route.js";
import adminOriginatorRoute from "@/routes/originator.route.js";
import adminRfiRoute from "@/routes/rfi.route.js";
import adminMissionRoute from "@/routes/mission.route.js";
import adminIsrRoute from "@/routes/isr.route.js";
import adminIsrTrackRoute from "@/routes/isr-track.route.js";
import adminGlobalIsrRoute from "@/routes/global-isr.route.js";
import dangerRoute from "@/routes/danger.route.js";
import adminEngagedMissionAssetRoute from "@/routes/engaged-mission-asset.route.js";
import adminTaskRoutes from "@/routes/task.route.js";

export const adminRouterRegister = async (fastify: any) => {
	await fastify.register(rootRoute);


	await fastify.register(adminUserRoutes);
	await fastify.register(adminRoleRoutes);
	await fastify.register(adminOrganizationRoutes);
	await fastify.register(adminOperationRoute);
	await fastify.register(adminPlatformRoutes);
	await fastify.register(adminAssetRoutes);
	await fastify.register(adminAoiRoutes);
	await fastify.register(adminInformationRequirementRoutes);
	await fastify.register(adminCollectionCapabilityRoutes);
	await fastify.register(adminPirRoutes);
	await fastify.register(adminOriginatorRoute)
	await fastify.register(adminRfiRoute)
	await fastify.register(adminMissionRoute)
	await fastify.register(adminIsrRoute)
	await fastify.register(adminIsrTrackRoute)
	await fastify.register(adminGlobalIsrRoute)
	await fastify.register(dangerRoute)
    await fastify.register(adminEngagedMissionAssetRoute)
	await fastify.register(adminTaskRoutes)
	//
}