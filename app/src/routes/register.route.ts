import {FastifyInstance} from "fastify";
import RegisterController from "@/controllers/register.controller.js";
import {
	createRegistrationDTO,
	createRegistrationSchema,
	verifyEmailDTO,
	verifyEmailSchema
} from "@/interfaces/register.interface.js";
import registerController from "@/controllers/register.controller.js";
import guestMiddleware from "@/middleware/guest.middleware.js";
import validationMiddleware from "@/middleware/validation.middleware.js";

export default async function (fastify: FastifyInstance) {
	const registerController = new RegisterController();
	// fastify.register(messageCollectorPlugin);

	const path = '/register';

	fastify.post< {
		Body: createRegistrationDTO,
	}>(path, {
		schema: createRegistrationSchema,
		preHandler: fastify.auth([guestMiddleware]),
		errorHandler: validationMiddleware,
	}, registerController.register.bind(registerController));


	fastify.post< {
		Body: verifyEmailDTO,
	}>(`${path}/verify-email`, {
		schema: verifyEmailSchema,
		preHandler: fastify.auth([guestMiddleware]),
		errorHandler: validationMiddleware,
	}, registerController.verifyEmail.bind(registerController));
}