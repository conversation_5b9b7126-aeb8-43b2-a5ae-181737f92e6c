import {FastifyInstance, FastifyRequest, FastifyReply, preHand<PERSON><PERSON>ook<PERSON>andler} from "fastify";
import { CreateAssetDTO, UpdateAssetDTO, createAssetSchema, updateAssetSchema } from "@/interfaces/admin/admin.asset.interface.js";
import AdminAssetController from "@/controllers/admin/admin.asset.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import AssetModel from "@/models/asset.model.js";
import {adminAssetSanitizationRules} from "@/acl/sanitization/admin/asset.sanitiziation.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/assets';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};
	const handlers = createCrudRouteHandlers<AssetModel, CreateAssetDTO, UpdateAssetDTO, AdminAssetController>(AdminAssetController);


	//OTHER ASSET ROUTES



	/**
	 * @route GET /rules/pirs - Get all PIRs
	 * @route POST /rules/pirs - Create a new PIR
	 * @route GET /rules/pirs/:id - Get a PIR by ID
	 * @route PUT /rules/pirs/:id - Update a PIR
	 * @route DELETE /rules/pirs/:id - Delete a PIR
	 */
	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createAssetSchema,
		updateSchema: updateAssetSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminAssetSanitizationRules
	});

};