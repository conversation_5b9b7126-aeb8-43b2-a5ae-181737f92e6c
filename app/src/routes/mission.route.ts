import {FastifyInstance, FastifyRequest, FastifyReply, preHandler<PERSON>ook<PERSON>andler} from "fastify";
import {
	CreateMissionDTO,
	UpdateMissionDTO,
	createMissionSchema,
	updateMissionSchema,
	addTaisDTO, addTaisSchema,
	putInformationRequirementSchema,
	addAssetDTO, addAssetSchema,
	InformationRequirementIdParams,
	removeAssetSchema,
	removeTaiSchema,
	removeAssetDTO,
	removeTaiDTO,
	CopyGlobalISRTOLocalISRDTO,
	copyGlobalISRTOLocalISRSchema
} from "@/interfaces/admin/admin.mission.interface.js";
import AdminMissionController from "@/controllers/admin/admin.mission.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import MissionModel from "@/models/mission.model.js";
import {adminMissionSanitizationRules} from "@/acl/sanitization/admin/mission.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/missions';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};
	const handlers = createCrudRouteHandlers<MissionModel, CreateMissionDTO, UpdateMissionDTO, AdminMissionController>(AdminMissionController);


	fastify.post<{
		Params: { id: string },
		Body: addTaisDTO,
	}>(`${path}/:id/add-tais`,
		{
			schema: addTaisSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).addTais(request, reply);
		}
	);

	// copy global isr to local isr
	fastify.post<{
		Params: { id: string },
		Body: CopyGlobalISRTOLocalISRDTO,
	}>(`${path}/:id/add-global-isrs`,
		{
			schema: copyGlobalISRTOLocalISRSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).copyGlobalISRTOLocalISR(request, reply);
		}
	);


	// removeTai(request: FastifyRequest<{ Params: { id: string, taiId: string } }>, reply: FastifyReply) {
	fastify.delete<{
		Params: { id: string, taiId: string },
	}>(`${path}/:id/remove-tai/:taiId`,
		{
			schema: removeTaiSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).removeTai(request, reply);
		}
	);

	// addAssets(request: FastifyRequest<{ Params: { id: string }, Body: addAssetDTO }>, reply: FastifyReply) {
	fastify.post<{
		Params: { id: string },
		Body: addAssetDTO,
	}>(`${path}/:id/add-assets`,
		{
			schema: addAssetSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).addAssets(request, reply);
		}
	);

	// removeAsset(request: FastifyRequest<{ Params: { id: string, assetId: string } }>, reply: FastifyReply) {
	fastify.delete<{
		Params: { id: string, assetId: string },
	}>(`${path}/:id/remove-asset/:assetId`,
		{
			schema: removeAssetSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).removeAsset(request, reply);
		}
	);


	// putInformationRequirements(request: FastifyRequest<{ Params: { id: string }, Body: InformationRequirementIdParams }>, reply: FastifyReply) {
	fastify.put<{
		Params: { id: string },
		Body: InformationRequirementIdParams,
	}>(`${path}/:id/information-requirements`,
		{
			schema: putInformationRequirementSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).putInformationRequirements(request, reply);
		}
	);


	//get for /log
	fastify.get(`${path}/log`, {
		...preHandlerErrorHandlerOptions
	}, async (request, reply) => {
		return await new AdminMissionController(request.messageCollector, request.requestingUser, request.organizationId).log(request, reply);
	});

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createMissionSchema,
		updateSchema: updateMissionSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminMissionSanitizationRules
	});

};