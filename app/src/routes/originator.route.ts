import {FastifyInstance, FastifyRequest, FastifyReply, preHand<PERSON><PERSON>ook<PERSON>and<PERSON>} from "fastify";
import { CreateOriginatorDTO, UpdateOriginatorDTO, createOriginatorSchema, updateOriginatorSchema } from "@/interfaces/admin/admin.originator.interface.js";
import AdminOriginatorController from "@/controllers/admin/admin.originator.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import OriginatorModel from "@/models/originator.model.js";
import {adminOriginatorSanitizationRules} from "@/acl/sanitization/admin/originator.sanitiziation.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/originators';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};
	const handlers = createCrudRouteHandlers<OriginatorModel, CreateOriginatorDTO, UpdateOriginatorDTO, AdminOriginatorController>(AdminOriginatorController);



	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createOriginatorSchema,
		updateSchema: updateOriginatorSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminOriginatorSanitizationRules
	});

};