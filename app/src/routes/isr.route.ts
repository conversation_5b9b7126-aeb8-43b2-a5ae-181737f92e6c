import { FastifyInstance, preHandlerHookHandler } from "fastify";
import {
	CreateISRDTO,
	UpdateISRDTO,
	createISRSchema,
	updateISRSchema
} from "@/interfaces/admin/admin.isr.interface.js";
import AdminISRController from "@/controllers/admin/admin.isr.controller.js";
import { routeErrorHandler } from "@/utils/response.util.js";
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import ISRModel from "@/models/isr.model.js";
import { adminISRSanitizationRules } from "@/acl/sanitization/admin/isr.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/isrs';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<ISRModel, CreateISRDTO, UpdateISRDTO, AdminISRController>(
		AdminISRController
	);

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createISRSchema,
		updateSchema: updateISRSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminISRSanitizationRules
	});
};