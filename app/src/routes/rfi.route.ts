import {FastifyInstance, FastifyRequest, FastifyReply, preHand<PERSON><PERSON>ook<PERSON>andler} from "fastify";
import {  CreateRFIDTO, UpdateRFIDTO, createRFISchema, updateRFISchema } from "@/interfaces/admin/admin.rfi.interface.js";
import AdminRFIController from "@/controllers/admin/admin.rfi.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import RFIModel from "@/models/rfi.model.js";
import {adminRFISanitizationRules} from "@/acl/sanitization/admin/rfi.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/rfis';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};
	const handlers = createCrudRouteHandlers<RFIModel, CreateRFIDTO, UpdateRFIDTO, AdminRFIController>(AdminRFIController);



	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createRFISchema,
		updateSchema: updateRFISchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminRFISanitizationRules
	});

};