// src/routes/rules/isr-track.route.ts

import { FastifyInstance, preHandlerHookHandler } from "fastify";
import {
    CreateISRTrackDTO,
    UpdateISRTrackDTO,
    createISRTrackSchema,
    updateISRTrackSchema, assignAssetsToIsrTrackSchema, deleteAssetFromISRTrackSchema,
} from '@/interfaces/admin/admin.isr-track.interface.js';
import AdminISRTrackController from "@/controllers/admin/admin.isr-track.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import ISRTrackModel from "@/models/isr-track.model.js";
import { adminISRTrackSanitizationRules } from "@/acl/sanitization/admin/isr-track.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/isr-tracks';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	// Create handlers for standard CRUD operations
	const handlers = createCrudRouteHandlers<ISRTrackModel, CreateISRTrackDTO, UpdateISRTrackDTO, AdminISRTrackController>(AdminISRTrackController);


	fastify.post<{
		Params: { id: string },
		Body: {
            assetId: number,
            missionId: number,
        },
	}>(`${path}/:id/assign-assets`,
		{
			schema: assignAssetsToIsrTrackSchema,
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminISRTrackController(request.messageCollector, request.requestingUser, request.organizationId).assignAssetsByIds(request, reply);
		}
	);

    fastify.delete<{
        Params: { id: string, assetId: string, missionId: string },
    }>(`${path}/:id/assign-assets/:assetId/mission/:missionId`,
        {
            schema: deleteAssetFromISRTrackSchema,
            ...preHandlerErrorHandlerOptions
        }, async (request, reply) => {
            return await new AdminISRTrackController(request.messageCollector, request.requestingUser, request.organizationId).deleteAssetById(request, reply);
        }
    );

	// Register standard CRUD routes
	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createISRTrackSchema,
		updateSchema: updateISRTrackSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminISRTrackSanitizationRules
	});




}