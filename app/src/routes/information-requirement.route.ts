import { FastifyInstance, preHandlerHook<PERSON>andler } from "fastify";
import {
	CreateInformationRequirementDTO,
	UpdateInformationRequirementDTO,
	createInformationRequirementSchema,
	updateInformationRequirementSchema,
} from "@/interfaces/admin/admin.information-requirement.interface.js";
import AdminInformationRequirementController from "@/controllers/admin/admin.information-requirement.controller.js";
import { routeErrorHandler } from '@/utils/response.util.js';
import { createCrudRouteHandlers, registerCrudRoutes } from "@/utils/route.admin-crud-register.utils.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import { adminInformationRequirementSanitizationRules } from "@/acl/sanitization/admin/information-requirement.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	const path = '/information-requirements';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<InformationRequirementModel, CreateInformationRequirementDTO, UpdateInformationRequirementDTO, AdminInformationRequirementController>(AdminInformationRequirementController);

	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createInformationRequirementSchema,
		updateSchema: updateInformationRequirementSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminInformationRequirementSanitizationRules
	});
}
