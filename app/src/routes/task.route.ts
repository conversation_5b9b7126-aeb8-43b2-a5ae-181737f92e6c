
import {FastifyInstance, FastifyRequest, FastifyReply, preHandler<PERSON>ook<PERSON>and<PERSON>} from "fastify";
import {
    CreateTaskDTO,
    UpdateTaskDTO,
    createTaskSchema,
    updateTaskSchema,
    AssignMembersDTO,
    assignMembersSchema,
    RemoveMemberDTO,
    removeMemberSchema
} from "@/interfaces/admin/admin.task.interface.js";
import AdminTaskController from "@/controllers/admin/admin.task.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import TaskModel from "@/models/task.model.js";
import {adminTaskSanitizationRules} from "@/acl/sanitization/admin/task.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
    const path = '/tasks';
    const crudMiddleware: preHandlerHookHandler[] = [
        authMiddleware as preHandlerHookHandler,
    ];

    const preHandlerErrorHandlerOptions = {
        preHandler: crudMiddleware,
        errorHandler: routeErrorHandler,
    };
    const handlers = createCrudRouteHandlers<TaskModel, CreateTaskDTO, UpdateTaskDTO, AdminTaskController>(AdminTaskController);

    // Assign members to a task
    fastify.post<{
        Params: { id: string },
        Body: AssignMembersDTO,
    }>(`${path}/:id/assign-members`,
        {
            schema: assignMembersSchema,
            ...preHandlerErrorHandlerOptions
        }, async (request, reply) => {
            return await new AdminTaskController(request.messageCollector, request.requestingUser, request.organizationId).assignMembers(request, reply);
        }
    );

    // Remove a member from a task
    fastify.delete<{
        Params: { id: string, memberId: string },
    }>(`${path}/:id/remove-member/:memberId`,
        {
            schema: removeMemberSchema,
            ...preHandlerErrorHandlerOptions
        }, async (request, reply) => {
            return await new AdminTaskController(request.messageCollector, request.requestingUser, request.organizationId).removeMember(request, reply);
        }
    );

    // Register standard CRUD routes
    registerCrudRoutes(fastify, handlers, path, {
        createSchema: createTaskSchema,
        updateSchema: updateTaskSchema,
        preHandlerErrorHandlerOptions,
        sanitizationRules: adminTaskSanitizationRules
    });
}