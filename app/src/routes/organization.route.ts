import {FastifyInstance, preHandler<PERSON>ookHandler} from "fastify";

import {
	CreateOrganizationDTO,
	UpdateOrganizationDTO,
	createOrganizationSchema,
	updateOrganizationSchema,
} from "@/interfaces/admin/admin.organization.interface.js";

import AdminOrganizationController from "@/controllers/admin/admin.organization.controller.js";
import {routeErrorHandler} from "@/utils/response.util.js";
import {createCrudRouteHandlers, registerCrudRoutes} from "@/utils/route.admin-crud-register.utils.js";
import OrganizationModel from "@/models/organization.model.js";
import {adminOrganizationSanitizationRules } from "@/acl/sanitization/admin/organization.sanitization.js";
import authMiddleware from "@/middleware/auth.middleware.js";

export default async function (fastify: FastifyInstance) {
	// fastify.register(messageCollectorPlugin);

	const path = '/organizations';
	const crudMiddleware: preHandlerHookHandler[] = [
		authMiddleware as preHandlerHookHandler,
	];

	const preHandlerErrorHandlerOptions = {
		preHandler: crudMiddleware,
		errorHandler: routeErrorHandler,
	};

	const handlers = createCrudRouteHandlers<OrganizationModel, CreateOrganizationDTO, UpdateOrganizationDTO, AdminOrganizationController>(AdminOrganizationController);

	//OTHER ORGANIZATION ROUTES
	//GET route /organizations/access-rules
	fastify.get(`${path}/access-rules`,
		{
			...preHandlerErrorHandlerOptions
		}, async (request, reply) => {
			return await new AdminOrganizationController(request.messageCollector, request.requestingUser, request.organizationId).getOrganizationAccessRules(request, reply);
		}
	);



	registerCrudRoutes(fastify, handlers, path, {
		createSchema: createOrganizationSchema,
		updateSchema: updateOrganizationSchema,
		preHandlerErrorHandlerOptions,
		sanitizationRules: adminOrganizationSanitizationRules
	});


}