import AppDataSource from "@/config/database.js";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, In, <PERSON><PERSON>han} from 'typeorm';
import UserModel from "@/models/user.model.js";
import {generateRefreshToken, generateToken, verifyRefreshToken} from '@/utils/jwt.utils.js';
import {
	LoginUserDTO,
	RequestPasswordResetBodyDTO,
	ResetPasswordBodyDTO,
	RefreshTokenDTO, UpdateUserDTO, UpdateUserPasswordDTO,
} from '@/interfaces/admin/admin.user.interface.js';
import * as argon2 from 'argon2';
import {FastifyRequest} from "fastify";
import sendResetPassword from "@/events/user/notification/send-reset-password-email.event.js";
import RoleModel from "@/models/role.model.js";
import { userCanAccessOrganization } from '@/acl/access/membership/userCanAccessOrganization.acl.js'
import OrganizationModel from "@/models/organization.model.js";
export default class UserService {

	private userRepository: ReturnType<EntityManager['getRepository']>;
    private organizationRepository: ReturnType<EntityManager['getRepository']>;

	constructor() {
        this.organizationRepository = AppDataSource.getRepository(OrganizationModel);
		this.userRepository = AppDataSource.getRepository(UserModel);
	}

	async findByEmail(request: FastifyRequest<{ Body: LoginUserDTO }>): Promise<UserModel | null> {
		try{
			const { email } = request.body;
			const user = await this.userRepository.findOne({ where: { email } });
			return user as UserModel | null;
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async login(request: FastifyRequest<{ Body: LoginUserDTO }>): Promise<{ accessToken: string, refreshToken: string, user: UserModel } | null> {
		try{
			let { email, password } = request.body;
			let organizationId = request.organizationId;
			const user = await this.userRepository.findOne({ where: { email } }) as UserModel | null;
			if (!user) {
				request.messageCollector.addNotFound("Cannot find user");
				return null
			}
			const isPasswordValid = await user.comparePassword(password);
			if (!isPasswordValid) {
				request.messageCollector.addDenied("Invalid credentials");
				return null
			}
			if (!user.canLogin()) {
				console.error('user.canLogin()', user);
				request.messageCollector.addDenied("You cannot login with those credentials!");
				return null
			}
			if(!organizationId){
				//grab user's default organization from db
				const userWithRoles = await this.userRepository.findOne({
					where: { id: user.id },
					relations: ['roles']
				}) as UserModel | null;
				if(!userWithRoles || !userWithRoles.roles.length){
					request.messageCollector.addDenied("User does not have access to organization");
					return null
				}
				organizationId = (userWithRoles.roles[0].organizationId) ? userWithRoles.roles[0].organizationId : undefined;
				if(!organizationId){
					request.messageCollector.addDenied("User does not have access to organization");
					return null
				}
			}
			const organizationCorrect = await this._doesUserHaveAccessToOrganization(user.id, organizationId);
			if(!organizationCorrect){
				request.messageCollector.addDenied("User does not have access to organization");
				return null
			}
			user.refreshToken = generateRefreshToken(user, organizationId);
			const token = generateToken(user, organizationId);
			const freshUser = await this.userRepository.save(user) as UserModel;
			return {
				user: freshUser,
				accessToken: token,
				refreshToken: user.refreshToken
			};
		} catch (error) {
			console.error("Error refreshing token", request.messageCollector);
			request.messageCollector.addThrowableError(error);
			return null;
		}

	}

	async refreshToken(request: FastifyRequest<{ Body: RefreshTokenDTO }>): Promise<{ refreshToken:string, accessToken: string } | null> {
		try {
			const { refreshToken } = request.body;
			const payload = verifyRefreshToken(refreshToken);

			// Find the user by ID from the token payload
			const user = await this.userRepository.findOne({ where: { id: payload.userId } }) as UserModel | null;

			if (!user || user.refreshToken !== refreshToken) {
				request.messageCollector.addDenied("Refresh token not valid");
				return null
			}

			// Generate a new access token
			const newToken = generateToken(user, payload.organizationId);
			// Generate a new refresh token
			user.refreshToken = generateRefreshToken(user, payload.organizationId);
			await this.userRepository.save(user);

			return { accessToken: newToken, refreshToken: user.refreshToken };
		} catch (error) {
			console.error("REFRESH TOKEN ERROR", error);
			// Token verification failed
			return null;
		}
	}

    async switchOrg(request: FastifyRequest<{ Body: {newOrgId: string}, Params: {id:string} }>): Promise<{ refreshToken:string, accessToken: string } | null> {
        try{

            if(request.params.id.toString() !== request.requestingUser?.id?.toString()) {
                request.messageCollector.addDenied("User cannot switch other users organizations yet");
                return null
            }

            const user = await this.userRepository.findOne({
                where: { id: request.requestingUser.id }
            })  as UserModel | null
            if(!user){
                request.messageCollector.addDenied("User not found");
                return null
            }
            const newOrgId = request.body.newOrgId;
            const organizationCorrect = await this._doesUserHaveAccessToOrganization(user.id, newOrgId);
            if(!organizationCorrect){
                request.messageCollector.addDenied("User does not have access to organization");
                return null
            }

            // Generate a new access token
            const newToken = generateToken(user, newOrgId);
            // Generate a new refresh token
            user.refreshToken = generateRefreshToken(user, newOrgId);
            await this.userRepository.save(user);

            return { accessToken: newToken, refreshToken: user.refreshToken };

        } catch (error){
            return null;
        }
    }

    async organizations(request: FastifyRequest<{ Params: {id:string} }>): Promise<OrganizationModel[] | []> {
        try{

            if(request.params.id.toString() !== request.requestingUser?.id?.toString()) {
                request.messageCollector.addDenied("User cannot perform this action");
                return []
            }
            let organizationIds:number[] = [];
            const userWithRoles = await this.userRepository.findOne({
                where: { id: request.requestingUser.id },
                relations: ['roles']
            }) as UserModel | null;

            if(!userWithRoles || !userWithRoles.roles.length){
                request.messageCollector.addDenied("User does not have access to organization");
                return [];
            }
            //cycle thorugh each user role and get id
            userWithRoles.roles.forEach((role:RoleModel) => {
                if(role.organizationId){
                    organizationIds.push(role.organizationId);
                }
            })


            return  await this.organizationRepository.find({
                where: {
                    id: In(organizationIds)
                }
            }) as OrganizationModel[] | [];

        } catch (error){
            return [];
        }
    }

    async organization(request: FastifyRequest<{ Params: {id:string} }>): Promise<OrganizationModel | null> {
        try{
            if(request.params.id.toString() !== request.requestingUser?.id?.toString()) {
                request.messageCollector.addDenied("User cannot switch other users organizations yet");
                return null;
            }
            const currentOrgId = request.organizationId;
            const userWithRole = await this.userRepository.findOne({
                where: {
                    id: request.requestingUser?.id
                },
                relations: ['roles']
            }) as UserModel | null;

           //check if user.roles has currentOrgId
            const userRoles = userWithRole?.roles;
            const hasRoleWithOrgId = userRoles?.some((role:RoleModel) => {
                return role.id === currentOrgId;
            });
            if(!hasRoleWithOrgId){
                request.messageCollector.addDenied("User does not have access to organization");
                return null;
            }


            return  await this.organizationRepository.findOne({
                where: { id: currentOrgId }
            }) as OrganizationModel;

        } catch (error){
            return null;
        }
    }

	async getUserProfile(userId: number): Promise<UserModel | null> {
		return await this.userRepository.findOne({ where: { id: userId } }) as UserModel | null;
	}

	async updateUser(userId: number, updateData: UpdateUserDTO): Promise<UserModel | null> {
		const user = await this.userRepository.findOne({ where: { id: userId } });
		if (!user) {
			return null;
		}
		Object.assign(user, updateData);
		return await this.userRepository.save(user) as UserModel;
	}

	async activateUser(activateHash: string, firstName: string, lastName: string, password: string): Promise<UserModel | null> {
		const user = await this.userRepository.findOne({ where: { activateHash: activateHash } }) as UserModel | null;
		if (!user) {
			return null;
		}

		user.firstName = firstName;
		user.lastName = lastName;
		user.password = await argon2.hash(password);
		await this.userRepository.save(user);
		user.activate();
		return await this.userRepository.save(user);
	}

	async initiatePasswordReset(request : FastifyRequest<{ Body: { email: string } }>): Promise<boolean> {
		const user = await this.userRepository.findOne({
			where: {
				email: request.body.email.toLowerCase(),
				isActive: true
			} });
		if (!user) {
			request.messageCollector.addDenied("Invalid email or password");
			return false;
		}

		const resetToken = await user.setResetPasswordHash();
		await this.userRepository.save(user);
		await sendResetPassword(user.email, resetToken);
		return true;
	}

	async resetPassword(request: FastifyRequest<{ Body: { email: string, resetToken: string, newPassword: string } }>): Promise<boolean> {
		const { email, resetToken, newPassword } = request.body;
		const user = await this.userRepository.findOne({
			where: {
				email,
				resetPasswordExpires: MoreThan(new Date())
			}
		});

		if (!user || !user.resetPasswordHash) {
			request.messageCollector.addDenied("Invalid email or password");
			return false;
		}

		const isValidToken = await argon2.verify(user.resetPasswordHash, resetToken);
		if (!isValidToken) {
			request.messageCollector.addDenied("Invalid reset token");
			return false;
		}

		user.password = newPassword;
		user.clearResetPasswordHash();
		await this.userRepository.save(user);
		return true;
	}

	async updatePassword(userId:number, request: FastifyRequest<{ Body: UpdateUserPasswordDTO }>): Promise<boolean> {
		try{
			const { password, oldPassword } = request.body;
			//check complexity of password
			if(!password.match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)){
				request.messageCollector.addBadRequest("Password must be at least 8 characters long and contain at least" +
					" one uppercase letter, one lowercase letter, one number, and one special character");
				return false;
			}
			let user = await this.userRepository.findOne({ where: { id: userId } }) as UserModel | null;
			if(!user){
				request.messageCollector.addDenied("Invalid User");
				return false;
			}

			if(await user.comparePassword(oldPassword)){
				user.setPassword(password);
				await this.userRepository.save(user);
				//add success
				request.messageCollector.addSuccess("Password updated successfully");
				return true;
			} else {
				request.messageCollector.addDenied("Invalid old password");
				return false;
			}
		} catch (error){
			request.messageCollector.addThrowableError(error);
		}
		return false;
	}

	async _doesUserHaveAccessToOrganization(userId: number, organizationId: string|number): Promise<boolean> {
		try {
			//get user with roles
			const userWithRoles = await this.userRepository.findOne({
				where: {
					id: userId,
				},
				relations: ['roles'],
			});
			if (!userWithRoles) {
				throw new Error("User not found");
			}
			//check if user's roles include organization
			const userRoles = userWithRoles.roles;
			const organizationRole = userRoles.find((role:RoleModel) => role.organizationId && role.organizationId.toString() === organizationId.toString());
			if (!organizationRole) {
				throw new Error("User does not have access to organization");
			}
			return true;
		} catch (error) {
			console.error(error);
			throw error;
		}
	}
}
