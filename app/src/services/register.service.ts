import AppDataSource from "@/config/database.js";

import {FastifyRequest} from "fastify";
import {
	createOrganizationDTO,
	createRegistrationDTO,
	createUserDTO,
	verifyEmailDTO
} from "@/interfaces/register.interface.js";
import OrganizationModel from "@/models/organization.model.js";
import {EntityManager} from "typeorm";
import UserModel from "@/models/user.model.js";
import RoleModel from "@/models/role.model.js";
import sendVerificationEmail from "@/events/user/notification/send-verification-email.event.js";
import sendSlackVerificationEmail from "@/events/user/notification/send-slack-verification-email.event.js";
import {sendSlackNotification} from "@/utils/system-notices.utils.js";
import OriginatorModel from "@/models/originator.model.js";
import sendWelcomeEmail from "@/events/user/notification/send-welcome-email.event.js";


export default class RegisterService {

	private userRepository: ReturnType<EntityManager['getRepository']>;
	private organizationRepository: ReturnType<EntityManager['getRepository']>;
	private roleRepository: ReturnType<EntityManager['getRepository']>;
	private originatorRepository: ReturnType<EntityManager['getRepository']>;

	constructor() {
		this.userRepository = AppDataSource.getRepository(UserModel);
		this.organizationRepository = AppDataSource.getRepository(OrganizationModel);
		this.roleRepository = AppDataSource.getRepository(RoleModel);
		this.originatorRepository = AppDataSource.getRepository(OriginatorModel);
	}

	async register(request: FastifyRequest<{ Body: createRegistrationDTO }>) {
		let newUser:UserModel|null = null;
		let newOrganization:OrganizationModel|null = null;
		let newRole:RoleModel|null = null;
		let newOriginator:OriginatorModel|null = null;
		try{
			const { organization, user } = request.body;
			newOrganization = await this._createOrganization(organization);
			if(!newOrganization) throw new Error("Error creating organization");
			newRole = await this._createRoles(newOrganization.id);
			if(!newRole) throw new Error("Error creating role");
			newUser = await this._createUser(user, newRole);
			if(!newUser) throw new Error("Error creating user");
			newOriginator = await this._createOriginator(newOrganization);
			if(!newOriginator) throw new Error("Error creating originator");

			//send verification email
			if(!newUser.activateHash) {
				await newUser.generateActivateHash();
				newUser = await this.userRepository.save(newUser) as UserModel;
			}
			await sendVerificationEmail(newUser.email, newUser.activateHash as string);
			await sendSlackVerificationEmail(newUser.email, newUser.activateHash as string);
			request.messageCollector.addSuccess("User created successfully");
			request.messageCollector.addSuccess("Please verify your email address");

			request.messageCollector.addSuccess("User activated successfully (FOR TESTING)");
			request.messageCollector.addSuccess("Please login to your account (FOR TESTING)");
			await this.userRepository.save(newUser);



			return {user: {
					firstName: newUser.firstName,
					lastName: newUser.lastName,
					email: newUser.email.toLowerCase(),
					isVerified: newUser.isVerified,
					isActive: newUser.isActive,
				}}

		} catch (error) {
			console.log(error);
			request.messageCollector.addThrowableError(error);
			const userId = (newUser && newUser.id) ? newUser.id : null;
			const organizationId = (newOrganization && newOrganization.id) ? newOrganization.id : null;
			const roleId = (newRole && newRole.id) ? newRole.id : null;
			await this._rollBack(userId, organizationId, roleId);
			return null;
		}
	}

	async verifyEmail(request: FastifyRequest<{ Body: verifyEmailDTO }>) {
		try{
			const { email, verifyHash } = request.body;
			const user = await this.userRepository.findOne({ where: {
					email: email.toLowerCase(),
					activateHash: verifyHash,
				} }) as UserModel | null;
			if(!user) throw new Error("Non-verified user cannot be found. Could be activated already or does not" +
				" exist. Please check.");
			if(user.isActive) throw new Error("User is already active");
			if(user.isVerified) throw new Error("User is already verified");
			if(user.activateHash !== verifyHash) throw new Error("Invalid token");
			user.activate();


			request.messageCollector.addSuccess("User activated successfully");
			request.messageCollector.addSuccess("Please login to your account");
			const mess = `[FOR TESTING PURPOSES ONLY] User ${user.email} has been activated!\n\n` +
				"```ACTIVE: "+user.isActive.toString()+"```" +
				"```TYPE: "+user.accountType.toString()+"```" +
				"```VERIFIED: "+user.isVerified.toString()+"```";
			await sendSlackNotification(mess);
			await sendWelcomeEmail(user, "Lumio", user.email);
			return await this.userRepository.save(user) as UserModel;
		} catch (error) {
			request.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async _createUser(user: createUserDTO, role: RoleModel) {
		try{
			const newUser = new UserModel();
			newUser.firstName = user.firstName;
			newUser.lastName = user.lastName;
			newUser.email = user.email;
			newUser.password = user.password;
			newUser.roles = [role];
			newUser.accountType = "org_admin";
			newUser.isActive = false;
			newUser.isVerified = false;
			let u =  await this.userRepository.save(newUser) as UserModel;
			return u;

		} catch (error) {
			throw("User cannot be created, possibly duplicate entry, try another email")
			return null;
		}
	}

	async _createRoles(organizationId: number) {
		const adminRole = new RoleModel();
		try {
			adminRole.roleName = "Admin";
			adminRole.roleDescription = "Admin of the organization";
			adminRole.organizationId = organizationId;
			adminRole.unitLevel = "ORG";
            adminRole.roleType = "org_admin";
			adminRole.staffDesignation = "ORG_ADMIN";
			adminRole.isCommandRole = true;
			adminRole.isStaffRole = true;
			adminRole.canBeAdditionalDuty = true;
			adminRole.functionalArea = "ORG";
			adminRole.countryOrOrganization = "GLOBAL";
			adminRole.isSystemRole = true;
			adminRole.isActive = true;
			const createdAdminRole = await this.roleRepository.save(adminRole) as RoleModel;
			const userRole = new RoleModel();
			userRole.roleName = "User";
            userRole.roleType = "member";
			userRole.roleDescription = "User of the organization";
			userRole.organizationId = organizationId;
			userRole.unitLevel = "ORG";
			userRole.staffDesignation = "USER";
			userRole.isCommandRole = false;
			userRole.isStaffRole = true;
			userRole.canBeAdditionalDuty = true;
			userRole.functionalArea = "ORG";
			userRole.countryOrOrganization = "GLOBAL";
			userRole.superiorRoles = [createdAdminRole];
			userRole.isSystemRole = true;
			userRole.isActive = true;
			let createdUserRole = await this.roleRepository.save(userRole);
			const managerRole = new RoleModel();
			managerRole.roleName = "Manager";
            managerRole.roleType = "manager";
			managerRole.roleDescription = "Manager/Superuser of the organization";
			managerRole.organizationId = organizationId;
			managerRole.unitLevel = "ORG";
			managerRole.staffDesignation = "MANAGER";
			managerRole.isCommandRole = true;
			managerRole.isStaffRole = true;
			managerRole.canBeAdditionalDuty = true;
			managerRole.functionalArea = "ORG";
			managerRole.countryOrOrganization = "GLOBAL";
			managerRole.superiorRoles = [createdAdminRole];
			managerRole.subordinateRoles = [createdUserRole];
			managerRole.isActive = true;
			managerRole.isSystemRole = true;
			managerRole.isActive = true;
			let createdManagerRole = await this.roleRepository.save(managerRole);
			createdAdminRole.subordinateRoles = [createdManagerRole];
			return this.roleRepository.save(createdAdminRole);
		} catch (error) {
			throw("Could not assign user to organization. Rolled back. Try registration again.")
			return null;
		}

	}

	async _createOrganization(organization: createOrganizationDTO) {
		try {

			let newOrganization = new OrganizationModel();
			newOrganization.name = organization.name;
			newOrganization.description = organization.description;
			newOrganization.isMilitary = organization.isMilitary;
			newOrganization.logoUrl = '#';
			//designation should be a timestamp + countrycode uppercase
			const denomination = organization.isMilitary ? "MIL" : "GEN";

			//get highest designation for this organizations from database
			const orgs = await this.organizationRepository.find({
				order: {
					designation: 'DESC'
				},
				take: 1
			});
			const lastOrg = orgs.length > 0 ? orgs[0] : null;

			console.log("Last Org: ", lastOrg);


			newOrganization.designation = lastOrg ? (parseInt(lastOrg.designation) + 1).toString() : '1';
			newOrganization.isoCountryCode = organization.isoCountryCode;
			newOrganization.sovereignty = organization.sovereignty;
			newOrganization.config = {
				defaultRules: {
					aoi: {
						title: "Areas of Interests (TAI/NAI)",
						rules: {
							delete: ['admin', 'org_admin', 'manager'],
							create: ['admin', 'org_admin', 'manager'],
							update: ['admin', 'org_admin', 'manager'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					asset: {
						title: "Assets of Operation",
						rules: {
							delete: ['admin', 'org_admin'],
							create: ['admin', 'org_admin'],
							update: ['admin', 'org_admin', 'manager'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					collectionCapability: {
						title: "Collection Capabilities",
						rules: {
							delete: ['admin', 'org_admin', 'manager'],
							create: ['admin', 'org_admin', 'manager'],
							update: ['admin', 'org_admin', 'manager'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					globalIsr: {
						title: "Global ISRs",
						rules: {
							delete: ['admin', 'org_admin'],
							create: ['admin', 'org_admin'],
							update: ['admin', 'org_admin'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					informationRequirement: {
						title: "Information Requests (IR)",
						rules: {
							delete: ['admin', 'org_admin','manager'],
							create: ['admin', 'org_admin', 'manager','user'],
							update: ['admin', 'org_admin','manager','user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					isr:{
						title: "ISRs",
						rules: {
							delete: ['admin', 'org_admin', 'manager'],
							create: ['admin', 'org_admin', 'manager', 'user'],
							update: ['admin', 'org_admin', 'manager', 'user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					isrTrack: {
						title: "ISR Tracks",
						rules: {
							delete: ['admin', 'org_admin', 'manager'],
							create: ['admin', 'org_admin', 'manager', 'user'],
							update: ['admin', 'org_admin', 'manager', 'user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					mission:{
						title: "Missions",
						rules: {
							delete: ['admin', 'org_admin','manager'],
							create: ['admin', 'org_admin', 'manager','user'],
							update: ['admin', 'org_admin','manager','user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					operation:{
						title: "Operation",
						rules: {
							delete: ['admin', 'org_admin'],
							create: ['admin', 'org_admin', 'manager','user'],
							update: ['admin', 'org_admin','manager','user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					organization:{
						title: "Organization",
						rules: {
							delete: ['admin'],
							create: ['admin'],
							update: ['admin', 'org_admin'],
							list: ['admin'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					originator:{
						title: "Originators for IR",
						rules: {
							delete: ['admin', 'org_admin'],
							create: ['admin', 'org_admin'],
							update: ['admin', 'org_admin', 'manager'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					pir:{
						title: "PIRs",
						rules: {
							delete: ['admin', 'org_admin','manager'],
							create: ['admin', 'org_admin', 'manager'],
							update: ['admin', 'org_admin', 'manager','user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						},
					},
					platform:{
						title: "Platforms (Temp DB)",
						rules: {
							delete: ['admin'],
							create: ['admin'],
							update: ['admin'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					rfi:{
						title: "Request For Information (RFI)",
						rules: {
							delete: ['admin', 'org_admin', 'manager', 'user'],
							create: ['admin', 'org_admin', 'manager', 'user'],
							update: ['admin', 'org_admin', 'manager','user'],
							list: ['admin', 'org_admin', 'manager', 'user'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					role:{
						title: "Roles (Organization)",
						rules: {
							delete: ['admin', 'org_admin'],
							create: ['admin', 'org_admin'],
							update: ['admin', 'org_admin'],
							list: ['admin', 'org_admin', 'manager'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					},
					users:{
						title: "Users",
						rules: {
							delete: ['admin', 'org_admin', 'manager'],
							create: ['admin', 'org_admin', 'manager'],
							update: ['admin', 'org_admin', 'manager'],
							list: ['admin', 'org_admin', 'manager'],
							read: ['admin', 'org_admin', 'manager', 'user']
						}
					}
				}
			}

			return await this.organizationRepository.save(newOrganization) as OrganizationModel;
		} catch (error) {
			console.log(error);
			throw("Could not register organization. Try again!")
		}
	}

	async _createOriginator(organization: OrganizationModel) {
		try{
			const newOriginator = new OriginatorModel();
			newOriginator.description = "Organization";
			newOriginator.title = organization.name;
			newOriginator.organizationId = organization.id;
			newOriginator.contactDetails = "ORG ADMIN";
			return await this.originatorRepository.save(newOriginator) as OriginatorModel;

		} catch (error) {
			console.log(error);
			throw("Could not register organization. Try again!")
		}
	}

	async _rollBack(userId:number|null, organizationId:number|null, roleId:number|null) {
		try {
			if(userId) await this.userRepository.delete(userId);
			if(roleId) await this.roleRepository.delete(roleId);
			if(organizationId) await this.organizationRepository.delete(organizationId);
		} catch (error) {
			console.error("Error rolling back registration", error);
			return false;
		}
		return true;
	}

}