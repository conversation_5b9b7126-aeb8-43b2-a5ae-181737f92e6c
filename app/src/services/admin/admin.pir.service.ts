import PIRModel from "@/models/pir.model.js";
import AppDataSource from "@/config/database.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import {CreatePirDTO, UpdatePirDTO,} from "@/interfaces/admin/admin.pir.interface.js";
import {EntityManager, FindOptionsWhere, Not, QueryFailedError} from "typeorm";
import OperationModel from "@/models/operation.model.js";
import {userCanAccessOperation} from '@/acl/access/membership/userCanAccessOperation.acl.js'
import {AccessType, IRequestingUser, Priority} from '@/interfaces/database.interface.js';
import {pirAcl} from '@/acl/access/rules/pir.acl.js';

export default class AdminPirService
	extends GenericCrudService<PIRModel, CreatePirDTO, UpdatePirDTO>
	implements ICrudService<PIRModel, CreatePirDTO, UpdatePirDTO> {

	private operationRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(PIRModel, messageCollector, requestingUser, organizationId);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
	}

	override async findAll(params: any): Promise<{ items: PIRModel[], count: number }> {
		if(!await pirAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async findById(id: string, load?: string): Promise<PIRModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: PIRModel[], count: number }> {
		if(!await pirAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await pirAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}

	private async checkDuplicateQuestion(pirNumber: number, excludeId?: number, operationId?: number|null): Promise<PIRModel | null> {
		const whereClause: FindOptionsWhere<PIRModel> = {
			pirNumber: pirNumber
		};

		if (operationId !== undefined) {
			whereClause.operationId = operationId as number;
		}

		if (excludeId) {
			whereClause.id = Not(excludeId);
		}

		const pir = await this.repository.findOne({
			where: whereClause
		});

		return pir;
	}

	private async createWithOperation(data: CreatePirDTO): Promise<PIRModel | null> {
		try {
			if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}
			const operationId = parseInt(data.operationId);
			if(!await this.operationExistsAndActive(operationId)){
				return null;
			}

			const Pir = new PIRModel();
			Pir.question = data.question.trim();
			Pir.description = data.description ? data.description.trim() : '';
			Pir.isActive = data.isActive ?? true;
			Pir.operationId = operationId;
			Pir.priority = data.priority ?? Priority.MEDIUM

			const savedPir = await this.repository.save(Pir);
			this.messageCollector.addSuccess("PIR created successfully");
			return savedPir;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async update(id: string | number, data: UpdatePirDTO): Promise<PIRModel | null> {
		try {
			if(! await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
			// If name is being updated, check for duplicates
			const Pir = await this.findById(id.toString());
			if (!Pir) {
				this.messageCollector.addInfo("PIR not found");
				return null;
			}

			if (data.question) Pir.question = data.question;
			// if (data.pirNumber) Pir.pirNumber = data.pirNumber;
			if (data.description !== undefined) Pir.description = data.description;
			if (data.isActive !== undefined) Pir.isActive = data.isActive;

			const updatedPir = await this.repository.save(Pir);
			this.messageCollector.addSuccess("PIR updated successfully");
			return updatedPir;
		} catch (error) {
			// Fallback error handling for race conditions
			if (error instanceof QueryFailedError) {
				const errorMessage = error.message || '';
				if (errorMessage.includes('duplicate key') && errorMessage.toLowerCase().includes('question')) {
					this.messageCollector.addConflict(`A  PIR with the question "${data.question}" already exists`);
				} else {
					this.messageCollector.addThrowableError(error);
				}
			} else {
				this.messageCollector.addThrowableError(error);
			}
		}
		return null;
	}

	// Override the create method from the base class
	override async create(data: CreatePirDTO): Promise<PIRModel | null> {
		if (!data.operationId) {
			this.messageCollector.addBadRequest("Operation ID is required");
			return null;
		}
		return this.createWithOperation(data);
	}

	private async operationExistsAndActive(operationId: number): Promise<boolean> {

		if(!await userCanAccessOperation(this.requestingUser?.id, operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return false;
		}

		const operation = await this.operationRepository.findOne({ where: { id: operationId } });
		if (!operation) {
			this.messageCollector.addNotFound("Operation not found");
			return false;
		}
		if (!operation.isActive) {
			this.messageCollector.addNotFound("Operation is not active");
			return false;
		}
		return true;
	}
}