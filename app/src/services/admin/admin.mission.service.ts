import  MissionModel  from "@/models/mission.model.js";
import {
	CreateMissionDTO,
	UpdateMissionDTO,
} from "@/interfaces/admin/admin.mission.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import { ICrudService, IPaginationParams } from '@/interfaces/admin/admn.item-crud.interface.js';
import {MessageCollector} from "@/utils/message-collector.utils.js";
import { EntityManager, FindManyOptions, FindOptionsOrder, In, Not } from 'typeorm';
import AoiModel from "@/models/aoi.model.js";
import AppDataSource from "@/config/database.js";
import AssetModel from "@/models/asset.model.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import GlobalISRModel from "@/models/global-isr.model.js";
import ISRModel from "@/models/isr.model.js";
import {CreateAssetDTO} from "@/interfaces/admin/admin.asset.interface.js";
import {userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js'
import { AccessType, IRequestingUser, MissionStatus } from '@/interfaces/database.interface.js';
import { missionAcl } from '@/acl/access/rules/mission.acl.js';
import { FastifyRequest } from 'fastify';

export default class AdminMissionService extends GenericCrudService<MissionModel, CreateMissionDTO, UpdateMissionDTO>
	implements ICrudService<MissionModel, CreateMissionDTO, UpdateMissionDTO>
{
	private globalIsrRepository: ReturnType<EntityManager['getRepository']>;
	private aoiRepository: ReturnType<EntityManager['getRepository']>;
	private assetRepository: ReturnType<EntityManager['getRepository']>;
	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private irRepository: ReturnType<EntityManager['getRepository']>;
	private isrRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(MissionModel, messageCollector, requestingUser, organizationId);
		this.aoiRepository = AppDataSource.getRepository(AoiModel);
		this.assetRepository = AppDataSource.getRepository(AssetModel);
		this.irRepository = AppDataSource.getRepository(InformationRequirementModel);
		this.globalIsrRepository = AppDataSource.getRepository(GlobalISRModel);
		this.isrRepository = AppDataSource.getRepository(ISRModel);
		this.operationRepository = AppDataSource.getRepository(MissionModel);
	}

	override async findAll(params: any): Promise<{ items: MissionModel[], count: number }> {

		if(!await missionAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}

		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	async log(queryDataWithPagination: IPaginationParams): Promise<{ items: MissionModel[], count: number }> {
		if(!await missionAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		const { page, perPage, order, orderBy, operationId, load } = queryDataWithPagination;

		// Convert page and perPage to numbers to avoid TypeScript arithmetic operation error
		const pageNum = typeof page === 'string' ? parseInt(page, 10) : (page || 1);
		const perPageNum = typeof perPage === 'string' ? parseInt(perPage, 10) : (perPage || 10);

		// Convert operationId to number if it exists and is a string
		const operationIdNum = operationId ? (typeof operationId === 'string' ? parseInt(operationId, 10) : operationId) : undefined;

		// Prepare the where clause
		const whereClause: any = {
			status: Not(In([MissionStatus.PENDING_APPROVAL, MissionStatus.PLANNED, MissionStatus.ACTIVE]))
		};

		// Only add operationId to where clause if it exists
		if (operationIdNum !== undefined) {
			whereClause.operationId = operationIdNum;
		}

		const loadRelations = load ?  load.toString().split(',') : [];

		const query: FindManyOptions<MissionModel> = {
			where: whereClause,
			skip: (pageNum - 1) * perPageNum,
			take: perPageNum,
			order: orderBy ? { [orderBy]: order as FindOptionsOrder<MissionModel> } : undefined,
			relations: [
				...loadRelations
			],
		}
		console.log("query", query);
		const response = await this.repository.findAndCount(query);
		return {
			items: response[0],
			count: response[1]
		}

	}

	override async findById(id: string, load?: string): Promise<MissionModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<MissionModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		const currentMission = await super.findById(id);
		if(!currentMission) {
			this.messageCollector.addNotFound("Mission not found");
			return null;
		}
		if(data.assetIds){
			const assets = await this.assetRepository.find({
				where: {
					id: In(data.assetIds),
					operationId: currentMission.operationId
				}
			}) as AssetModel[];
			data.assets = [];
			for(const asset of assets){
				data.assets.push(asset);
			}
		}
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: MissionModel[], count: number }> {
		if(!await missionAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}

		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await missionAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}

	async create(data: CreateMissionDTO & { userId: number }): Promise<MissionModel | null> {
		try{
			if(!await missionAcl(null, this.requestingUser, AccessType.CREATE)){
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}
			if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}

			const newMission = new MissionModel();
			//assign data to newMission
			Object.assign(newMission, data);
			newMission.requestedByUserId = data.userId;
			newMission.approvedByUserId = null;

			// grab the operation from this mission
			const operation = await this.operationRepository.findOne({
				where: {
					id: data.operationId
				}
			});
			// now let's grab first letters of the operation name
			if(!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}
			const operationDesignation = operation.designation;





			const savedMission = await this.repository.save(newMission);
			this.messageCollector.addSuccess("Mission created successfully.");
			return this.findById(savedMission.id.toString());

		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;

	}

	async copyGlobalISRTOLocalISR(missionId: string, globalIsrIds: number[]): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.CREATE)) return null;
			const mission = await this.findById(missionId.toString(), 'isrs');
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			const globalIsrs = await this.globalIsrRepository.find({
				where: {
					id: In(globalIsrIds),
					operationId: mission.operationId
				}
			}) as GlobalISRModel[];
			if (!globalIsrs.length) {
				this.messageCollector.addNotFound("No global ISRs found");
				return null;
			}
			for (const globalIsr of globalIsrs) {
				//check if global isr operation id matches mission operation id
				if(globalIsr.operationId !== mission.operationId){
					this.messageCollector.addDenied("Global ISR operation ID does not match mission operation ID");
				} else {
					//see if assetId and misisonId already exist
					const isr = await this.isrRepository.findOne({
						where: {
							assetId: globalIsr.assetId,
							missionId: mission.id
						}
					});
					if(isr){
						this.messageCollector.addConflict("Global ISR already exists in this mission");
					} else {
						const isr = new ISRModel();
						isr.label = globalIsr.label;
						isr.type = globalIsr.type;
						isr.coordinates = globalIsr.coordinates;
						isr.zoom = globalIsr.zoom;
						isr.centerCoordinates = globalIsr.centerCoordinates;
						isr.status = globalIsr.status;
						isr.isEditable = false;
						isr.isGlobal = true;
						isr.priority = globalIsr.priority;
						isr.commenceAt = globalIsr.commenceAt;
						isr.concludeAt = globalIsr.concludeAt;
						isr.ltiovDate = globalIsr.ltiovDate;
						isr.missionId = mission.id;
						isr.assetId = globalIsr.assetId;
						isr.createdByUserId = globalIsr.createdByUserId;
						await this.isrRepository.save(isr);
						this.messageCollector.addSuccess(`ISR: ${isr.designation} added successfully to mission ${mission.designation}`);
					}
					//create isr

				}
			}
			this.messageCollector.addSuccess("Global ISRs added successfully");
			return this.findById(missionId.toString(), 'isrs');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async addTais(missionId: number, taiIds: number[]): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.UPDATE)) return null;
			const mission = await this.findById(missionId.toString(), 'tais');
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			//if tais are empty remove them
			if(! taiIds.length) {
				mission.tais = [];
				await this.repository.save(mission);
				this.messageCollector.addSuccess("Removed all TAIs");
				return this.findById(missionId.toString(), 'tais');
			}

			const tais = await this.aoiRepository.find({
				where: {
					id: In(taiIds),
					operationId: mission.operationId,
					isApproved: true
					// isTargetable: true
				}
			}) as AoiModel[];
			if (!tais.length) {
				this.messageCollector.addNotFound("No TAIs found");
				return null;
			}
			//this needs to be foreach and add to message collector
			//remove old
			mission.tais = [];
			for (const tai of tais) {
				//add to mission
				mission.tais.push(tai);
				this.messageCollector.addSuccess(`TAI: ${tai.name} added to mission`);
			}
			await this.repository.save(mission);
			this.messageCollector.addSuccess("TAIs added successfully");
			return this.findById(missionId.toString(), 'tais');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async removeTai(missionId: number, taiId: number): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.UPDATE)) return null;
			const mission = await this.findById(missionId.toString(), 'tais');
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			//rewrite this to foreach and add to message collector
			//check if tai is in mission
			if (!mission.tais.find((tai) => tai.id === taiId)) {
				this.messageCollector.addNotFound("TAI not found");
				return null;
			}

			mission.tais = mission.tais.filter((tai) => tai.id !== taiId);
			await this.repository.save(mission);
			this.messageCollector.addSuccess("TAI removed successfully");
			return this.findById(missionId.toString(), 'tais');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async addAssets(missionId: number, assetIds: number[]): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.UPDATE)) return null;
			const mission = await this.findById(missionId.toString(), 'assets');
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			const assets = await this.assetRepository.find({
				where: {
					id: In(assetIds),
					operationId: mission.operationId,
					status: In(['active'])
				}
			}) as AssetModel[];
			if (!assets.length) {
				this.messageCollector.addNotFound("No assets found");
				return null;
			}
			for (const asset of assets) {
				//add to mission
				mission.assets.push(asset);
				this.messageCollector.addSuccess(`Asset: ${asset.name} added to mission`);
			}
			await this.repository.save(mission);
			this.messageCollector.addSuccess("Assets added successfully");
			return this.findById(missionId.toString(), 'assets');
		} catch (error) {
			this.messageCollector.addError("Error", error);
			return null;
		}
	}

	async removeAsset(missionId: number, assetId: number): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.UPDATE)) return null;
			const mission = await this.findById(missionId.toString(), 'assets');
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			const asset = await this.assetRepository.findOne({
				where: {
					id: assetId,
					operationId: mission.operationId //check if asset belongs to mission
				}
			});
			if (!asset) {
				this.messageCollector.addNotFound("Asset not found");
				return null;
			}
			//remove from mission
			mission.assets = mission.assets.filter((asset) => asset.id !== assetId);
			await this.repository.save(mission);
			this.messageCollector.addSuccess(`Asset: ${asset.name} removed from mission`);
			return this.findById(missionId.toString(), 'assets');
		} catch (error) {
			this.messageCollector.addError("Error", error);
			return null;
		}
	}

	async putInformationRequirements(missionId: number, irIds: number[]): Promise<MissionModel | null> {
		try {
			if(! await this._canUserAccessThis(missionId.toString(), AccessType.UPDATE)) return null;
			const mission = await this.findById(missionId.toString(), 'informationRequirements,operation,operation.pirs');
			let cleanIds = [];
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}
			if(irIds.length === 0){
				mission.informationRequirements = [];
				await this.repository.save(mission);
				this.messageCollector.addSuccess("IRs removed successfully");
				return this.findById(missionId.toString(), 'informationRequirements, informationRequirements.pir');
			}
			for(const id of irIds){
				const ir = await this.irRepository.findOne({
					where: {
						id: id,
					},
					relations: ['pir']
				});
				if(!ir || !ir.pir || ir.pir.operationId.toString() !== mission?.operationId?.toString()){
					this.messageCollector.addWarning("IR not found");
				} else {
					cleanIds.push(ir.id);
				}
			}
			const irs = await this.irRepository.find({
				where: {
					id: In(cleanIds),
				}
			}) as InformationRequirementModel[]; //this should be an array of irs
			if (!irs.length) {
				this.messageCollector.addNotFound("No IRs found");
				return null;
			}
			//replace all irs
			mission.informationRequirements = irs;
			await this.repository.save(mission);
			this.messageCollector.addSuccess("IRs added successfully");
			return this.findById(missionId.toString(), 'informationRequirements, informationRequirements.pir');
		} catch (error) {
			console.error("FUCKIT FYUCK", error);
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}
}