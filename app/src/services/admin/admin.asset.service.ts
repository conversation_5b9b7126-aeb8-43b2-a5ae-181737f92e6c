import AssetModel from '@/models/asset.model.js';
import { CreateAssetDTO, UpdateAssetDTO } from '@/interfaces/admin/admin.asset.interface.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';

import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { assetAcl } from '@/acl/access/rules/asset.acl.js';

import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js'

export default class AdminAssetService extends GenericCrudService<AssetModel, CreateAssetDTO, UpdateAssetDTO>
	implements ICrudService<AssetModel, CreateAssetDTO, UpdateAssetDTO>
{
	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(AssetModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: AssetModel[], count: number }> {
		if(!await assetAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: CreateAssetDTO): Promise<AssetModel | null> {
		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		if(!await assetAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		//search asset by callSign
		const asset = await super.findAll({
			callSign: data.callSign,
			operationId: data.operationId
		});

		if(asset.items.length > 0){
			this.messageCollector.addConflict(`Asset with callSign ${data.callSign} already exists`);
			return null;
		}
		return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<AssetModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<AssetModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: AssetModel[], count: number }> {
		if(!await assetAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await assetAcl(id, this.requestingUser, requestingAccess)){
			return false;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}


}