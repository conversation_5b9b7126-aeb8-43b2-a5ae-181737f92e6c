import {FindOptionsWhere, <PERSON><PERSON>, In} from "typeorm";
import {PlatformType} from "@/interfaces/admin/admin.platform.interface.js";
import {AllowedPlatformType} from "@/interfaces/database.interface.js";

import PlatformModel from '@/models/platform.model.js';
import { CreatePlatformDTO, UpdatePlatformDTO } from '@/interfaces/admin/admin.platform.interface.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { platformAcl } from '@/acl/access/rules/platform.acl.js';

export default class AdminPlatformService
	extends GenericCrudService<PlatformModel, CreatePlatformDTO, UpdatePlatformDTO>
	implements ICrudService<PlatformModel, CreatePlatformDTO, UpdatePlatformDTO> {

	constructor(messageCollector: MessageCollector, requestingUser: IRequestingUser | undefined, organizationId: number | undefined) {
		super(PlatformModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: PlatformModel[], count: number }> {
		if (!await platformAcl(null, this.requestingUser, AccessType.LIST)) {
			this.messageCollector.addDenied("You do not have access to list platforms");
			return {
				items: [],
				count: 0
			};
		}
		return super.findAll(params);
	}

	override async create(data: CreatePlatformDTO): Promise<PlatformModel | null> {
		if (!await platformAcl(null, this.requestingUser, AccessType.CREATE)) {
			this.messageCollector.addDenied("You do not have access to create platforms");
			return null;
		}

		try {
			// Generate designation based on the specified pattern
			const nameParts = data.name.split(' ');
			const designationParts = nameParts.map(word =>
				isNaN(Number(word)) ? word.charAt(0).toUpperCase() : word
			);

			const randomSuffix = Math.floor(Math.random() * 900 + 100);
			const designation = `${designationParts.join('-')}-${data.countryIsoCode}-${randomSuffix}`;

			// Create a new platform with the generated designation
			const platformData: CreatePlatformDTO = {
				...data,
				designation
			};

			return super.create(platformData);
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	override async findById(id: string, load?: string): Promise<PlatformModel | null> {
		if (!await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id, load);
	}

	override async update(id: string, data: UpdatePlatformDTO): Promise<PlatformModel | null> {
		if (!await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if (!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: PlatformModel[], count: number }> {
		if (!await platformAcl(null, this.requestingUser, AccessType.LIST)) {
			this.messageCollector.addDenied("You do not have access to search platforms");
			return {
				items: [],
				count: 0
			};
		}
		return super.search(searchTerm, fields);
	}

	async searchPlatforms(   searchTerm?: string,
							 countryIsoCode?: string,
							 types?: PlatformType[]
	): Promise<[PlatformModel[], number] | null> {
		try {
			const whereClause: FindOptionsWhere<PlatformModel> = {};

			if (searchTerm && searchTerm.length > 0) {
				whereClause.name = ILike(`%${searchTerm}%`);
			}

			if (countryIsoCode && countryIsoCode.length > 0) {
				whereClause.countryIsoCode = countryIsoCode;
			}

			if (types && types.length > 0) {
				//make sure type array is part of enum
				const validTypes = types.filter(type => type in AllowedPlatformType)
				if (validTypes.length > 0) {
					whereClause.type = In(validTypes);
				}
				whereClause.type = In(types);
			}

			const [platforms, totalCount] = await this.repository.findAndCount({
				where: whereClause,
			}) as [PlatformModel[], number];

			if (totalCount === 0) {
				this.messageCollector.addWarning("No platforms found. Try to widen your search.");
			}

			return [platforms, totalCount];
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}


	async _canUserAccessThis(id: string, requestingAccess: AccessType = AccessType.READ): Promise<boolean> {
		if (!await platformAcl(id, this.requestingUser, requestingAccess)) {
			this.messageCollector.addDenied("You do not have access to this platform resource");
			return false;
		}
		return true;
	}
}