import {In} from "typeorm";
import RoleModel from "@/models/role.model.js";
import {
	UpdateRoleDTO,
	CreateRoleDTO,
} from "@/interfaces/admin/admin.role.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { userCanAccessOrganization } from '@/acl/access/membership/userCanAccessOrganization.acl.js'
import { roleAcl } from '@/acl/access/rules/role.acl.js';


export default class AdminRoleService
	extends GenericCrudService<RoleModel, CreateRoleDTO, UpdateRoleDTO>
	implements ICrudService<RoleModel, CreateRoleDTO, UpdateRoleDTO> {


	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(RoleModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: RoleModel[], count: number }> {
		if(!await roleAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		params.organizationId = this.organizationId;
		return super.findAll(params);
	}

	override async create(data: CreateRoleDTO): Promise<RoleModel | null> {
		if(!await roleAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		//TODO: in multi-org mode this should be set by the requesting user but needs to be checked if the user has access to the organization
		data.organizationId = this.organizationId; //set organizationId
		if(!await userCanAccessOrganization(this.requestingUser?.id, data.organizationId)){
			this.messageCollector.addDenied("User does not have access to organization");
			return null;
		}
		return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<RoleModel | null> {

		let role = await super.findById(id,load);
		if(!await this._canUserAccessThis(role, AccessType.READ, true, false)) return null;
		return role;
	}

	override async update(id: string, data: any): Promise<RoleModel | null> {
		const role = await this.findById(id);
		if(!await this._canUserAccessThis(role, AccessType.UPDATE, true, true)) return null;

		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		const role = await this.findById(id);
		if(!await this._canUserAccessThis(role, AccessType.DELETE, true, true)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: RoleModel[], count: number }> {

		this.messageCollector.addInfo("Role search not implemented");
		//TODO: no need to search for now
		return {
			items: [],
			count: 0
		}
		//return super.search(searchTerm, fields);
	}

	async _canUserAccessThis(role:RoleModel|null, requestingAccess: AccessType = AccessType.READ, withMessages:boolean = true, checkSystemRole:boolean = false){
		if(!role || !role.organizationId){
			if(withMessages) this.messageCollector.addNotFound("Role not found");
			return false;
		}

		if(!await roleAcl(role.id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOrganization(this.requestingUser?.id, role.organizationId)){
			if(withMessages) this.messageCollector.addDenied("User does not have access to organization");
			return false;
		}
		if(checkSystemRole && role.isSystemRole){
			if(withMessages) this.messageCollector.addDenied("Cannot modify system roles");
			return false;
		}
		return true;
	}


	async addSubordinateRoles(roleId: number, subordinateRoleIds: number[], overwrite: boolean): Promise<RoleModel | null> {
		try {
			const role = await this.repository.findOne({
				where: { id: roleId },
				relations: ['subordinateRoles']
			}) as RoleModel;

			if(!await this._canUserAccessThis(role, AccessType.UPDATE, true, true)) return null;

			if(overwrite){
				role.subordinateRoles = [];
			} else {
				const currentSubordinateRoleIds = role.subordinateRoles.map(role => role.id);
				subordinateRoleIds = currentSubordinateRoleIds.concat(subordinateRoleIds);
			}
			//remove roleId from subordinateRoleIds
			subordinateRoleIds = subordinateRoleIds.filter(roleId => roleId !== role.id);

			const subordinateRoles = await this.repository.find({
				where: { id: In(subordinateRoleIds) },
			}) as RoleModel[];
			if (!subordinateRoles.length) {
				this.messageCollector.addNotFound("No subordinate roles found");
				return null;
			}
			let allowedSubordinateRoles = []
			for (const subordinateRole of subordinateRoles) {
				if(await this._canUserAccessThis(subordinateRole, AccessType.UPDATE, false, true)){
					allowedSubordinateRoles.push(subordinateRole);
				}
			}
			role.subordinateRoles = [...subordinateRoles];
			await this.repository.save(role);
			return await this.findById(role.id.toString()) as RoleModel;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async addSuperiorRoles(roleId: number, superiorRoleIds: number[], overwrite: boolean): Promise<RoleModel | null> {
		try {
			const role = await this.repository.findOne({
				where: { id: roleId },
				relations: ['superiorRoles']
			}) as RoleModel;
			if(!await this._canUserAccessThis(role, AccessType.UPDATE, true, true)) return null;

			if(overwrite){
				role.superiorRoles = [];
			} else {
				const currentSuperiorRoleIds = role.superiorRoles.map(role => role.id);
				superiorRoleIds = currentSuperiorRoleIds.concat(superiorRoleIds);
			}

			//remove roleId from superiorRoleIds
			superiorRoleIds = superiorRoleIds.filter(roleId => roleId !== role.id);

			const superiorRoles = await this.repository.find({
				where: { id: In(superiorRoleIds) },
			}) as RoleModel[];
			if (!superiorRoles.length) {
				this.messageCollector.addNotFound("No superior roles found");
				return null;
			}
			let allowedSuperiorRoles = []
			for (const superiorRole of superiorRoles) {
				if(await this._canUserAccessThis(superiorRole, AccessType.UPDATE, false, true)){
					allowedSuperiorRoles.push(superiorRole);
				}
			}

			role.superiorRoles = [...allowedSuperiorRoles]

			await this.repository.save(role);
			return await this.findById(role.id.toString()) as RoleModel;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async removeSubordinateRole(roleId: number, subordinateRoleId: number): Promise<RoleModel | null> {
		try{
			const role = await this.repository.findOne({
				where: { id: roleId },
				relations: ['subordinateRoles']
			}) as RoleModel;
			if(!await this._canUserAccessThis(role, AccessType.UPDATE, true, true)) return null;

			const roleToRemove = await this.repository.find({
				where: { id: roleId },
				relations: ['subordinateRoles']
			})

			const currentSubordinateRoleIds = role.subordinateRoles.map(role => role.id);
			//does currentSubordinateRoleIds contain subordinateRoleId?
			const hasIdInSubordinateRoles = currentSubordinateRoleIds.includes(subordinateRoleId);
			if(!hasIdInSubordinateRoles){
				this.messageCollector.addNotFound("Role not found in subordinate roles");
				return null;
			}

			let subordinateRoleIds = currentSubordinateRoleIds.filter(roleId => roleId !== subordinateRoleId);
			let subordinateRoles = await this.repository.find({
				where: { id: In(subordinateRoleIds) },
			}) as RoleModel[];
			let allowedSubordinateRoles = []
			for (const subordinateRole of subordinateRoles) {
				if(await this._canUserAccessThis(subordinateRole, AccessType.UPDATE, false, true)){
					allowedSubordinateRoles.push(subordinateRole);
				}
			}


			role.subordinateRoles = [...allowedSubordinateRoles];
			await this.repository.save(role);
			return await this.findById(role.id.toString()) as RoleModel;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async removeSuperiorRole(roleId: number, superiorRoleId: number): Promise<RoleModel | null> {
		try{
			const role = await this.repository.findOne({
				where: { id: roleId },
				relations: ['superiorRoles']
			}) as RoleModel;
			if(!await this._canUserAccessThis(role, AccessType.UPDATE, true, true)) return null;

			const currentSuperiorRoleIds = role.superiorRoles.map(role => role.id);
			//does currentSuperiorRoleIds contain superiorRoleId?
			const hasIdInSuperiorRoles = currentSuperiorRoleIds.includes(superiorRoleId);
			if(!hasIdInSuperiorRoles){
				this.messageCollector.addNotFound("Role not found in superior roles");
				return null;
			}
			let superiorRoleIds = currentSuperiorRoleIds.filter(roleId => roleId !== superiorRoleId);
			let superiorRoles = await this.repository.find({
				where: { id: In(superiorRoleIds) },
			}) as RoleModel[];

			const allowedSuperiorRoles = []
			for (const superiorRole of superiorRoles) {
				if(await this._canUserAccessThis(superiorRole, AccessType.UPDATE, false, true)){
					allowedSuperiorRoles.push(superiorRole);
				}
			}

			role.superiorRoles = [...allowedSuperiorRoles];
			await this.repository.save(role);
			return await this.findById(role.id.toString()) as RoleModel;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}
}