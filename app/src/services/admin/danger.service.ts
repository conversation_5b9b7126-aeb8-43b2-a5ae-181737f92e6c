import AppDataSource from "@/config/database.js";
import { DataSource } from "typeorm";
import { FastifyRequest } from "fastify";
import { sendSlackNotification } from "@/utils/system-notices.utils.js";

interface ResetEverythingResponse {
	success: boolean;
	tablesAffected: number;
	tablesTotal?: number;
	error?: string;
}


export default class DangerService {
	private dataSource: DataSource;

	constructor() {
		this.dataSource = AppDataSource;
	}

	async resetEverything(request: FastifyRequest): Promise<ResetEverythingResponse> {

		const queryRunner = this.dataSource.createQueryRunner();
		const whitelist = ['platforms'];
		try {
			await queryRunner.connect();
			await queryRunner.startTransaction();

			// Get all entities metadata
			const entities = this.dataSource.entityMetadatas;

			// Disable all triggers in PostgreSQL (correct syntax)
			await queryRunner.query('SET session_replication_role = replica;');


			request.messageCollector.addInfo('Starting database reset...');
			await sendSlackNotification('🚨 Database reset operation started');

			let truncatedTables = 0;
			let truncatedTableStringList = '';
			// Truncate all tables and reset sequences
			for (const entity of entities) {
				const tableName = entity.tableName;
				truncatedTableStringList += tableName + '\n';
				//skip table in whitelist
				if(whitelist.includes(tableName)) {
					request.messageCollector.addWarning(`Skipping table ${tableName}`);
					await sendSlackNotification(`⚠️ Skipping table ${tableName}`);
					continue;
				}
				try {
					await queryRunner.query(`TRUNCATE TABLE "${tableName}" RESTART IDENTITY CASCADE`);
					truncatedTables++;

					// Add success message for each table
					request.messageCollector.addSuccess(`✓ Truncated table: ${tableName}`);

					// Send granular Slack notifications in batches to avoid rate limiting

					await sendSlackNotification(`..... 🔄 Progress: Truncated ${truncatedTables}/${entities.length} tables\n ${tableName}`);


				} catch (tableError) {
					// @ts-ignore
					request.messageCollector.addError(`Failed to truncate table ${tableName}: ${tableError.message}`);
					// @ts-ignore
					await sendSlackNotification(`⚠️ Failed to truncate table ${tableName}: ${tableError.message}`);
					throw tableError;
				}
			}

			// Re-enable all triggers in PostgreSQL (correct syntax)
			await queryRunner.query('SET session_replication_role = origin;');


			await queryRunner.commitTransaction();

			// Final success messages
			const successMessage = `Successfully reset database - ${truncatedTables} tables truncated`;
			request.messageCollector.addSuccess(successMessage);
			await sendSlackNotification(`✅ ${successMessage}`);

			return {
				success: true,
				tablesAffected: truncatedTables,
				tablesTotal: entities.length
			};

		} catch (error) {
			await queryRunner.rollbackTransaction();

			// @ts-ignore
			const errorMessage  = error.message || error;
			const slackMessage = "Database reset failed: ```" + errorMessage +"```";
			request.messageCollector.addThrowableError(error);
			await sendSlackNotification(`❌ ${slackMessage}`);

			return {
				success: false,
				error: "Bitching error happened",
				tablesAffected: 0
			};

		} finally {
			await queryRunner.release();
			request.messageCollector.addInfo('Database reset operation completed');
		}
	}
}