import  OrganizationModel  from "@/models/organization.model.js";
import {
	CreateOrganizationDTO,
	UpdateOrganizationDTO,
} from "@/interfaces/admin/admin.organization.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import { userCanAccessOrganization } from '@/acl/access/membership/userCanAccessOrganization.acl.js'
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';

import { organizationAcl } from '@/acl/access/rules/organization.acl.js';

export default class AdminOrganizationService extends GenericCrudService<OrganizationModel, CreateOrganizationDTO, UpdateOrganizationDTO>
	implements ICrudService<OrganizationModel, CreateOrganizationDTO, UpdateOrganizationDTO>
{
	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(OrganizationModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: OrganizationModel[], count: number }> {
		this.messageCollector.addInfo("Organization get not implemented");
		return {items: [], count: 0};
		// return super.findAll(params);
	}

	override async create(data: CreateOrganizationDTO): Promise<OrganizationModel | null> {

		this.messageCollector.addInfo("Organization creation not implemented");
		return null;
		// return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<OrganizationModel | null> {

		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<OrganizationModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: OrganizationModel[], count: number }> {
		//not implemented
		this.messageCollector.addInfo("Organization search not implemented");
		return {items: [], count: 0};
		// return super.search(searchTerm, fields);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		if(! await organizationAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(! await userCanAccessOrganization(this.requestingUser?.id, parseInt(id))){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}

}