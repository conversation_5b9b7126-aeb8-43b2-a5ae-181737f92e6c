import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { CreateEngagedMissionAssetDTO, UpdateEngagedMissionAssetDTO } from '@/interfaces/admin/admin.engaged-mission-asset.interface.js';
import EngagedMissionAssetModel from '@/models/engaged-mission-asset.model.js';
import AppDataSource from '@/config/database.js';
import { EntityManager } from 'typeorm';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import {
    IRequestingUser,
} from '@/interfaces/database.interface.js';
import MissionModel from '@/models/mission.model.js';

import AssetModel from "@/models/asset.model.js";
import ISRTrackModel from "@/models/isr-track.model.js";
import {userCanAccessMission} from "@/acl/access/membership/userCanAccessMission.acl.js";
import {isrAcl} from "@/acl/access/rules/isr.acl.js";

export default class EngagedMissionAssetService extends GenericCrudService<EngagedMissionAssetModel, CreateEngagedMissionAssetDTO, UpdateEngagedMissionAssetDTO>
    implements ICrudService<EngagedMissionAssetModel,CreateEngagedMissionAssetDTO, UpdateEngagedMissionAssetDTO> {

    private missionRepository: ReturnType<EntityManager['getRepository']>;
    private assetRepository: ReturnType<EntityManager['getRepository']>;
    private isrTrackRepository: ReturnType<EntityManager['getRepository']>;

    constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
        super(EngagedMissionAssetModel, messageCollector, requestingUser, organizationId);
        this.missionRepository = AppDataSource.getRepository(MissionModel);
        this.assetRepository = AppDataSource.getRepository(AssetModel);
        this.isrTrackRepository = AppDataSource.getRepository(ISRTrackModel);
    }

    //override create
    async create(data: CreateEngagedMissionAssetDTO): Promise<EngagedMissionAssetModel | null> {

         const { missionId, assetId, isrTrackId } = data;

         if(!await userCanAccessMission(this.requestingUser?.id, missionId)){
                this.messageCollector.addDenied("You do not have access to this resource");
                return null;
         }

        const mission = await this.missionRepository.findOne({
            where: {
                id: missionId,
            },
            relations: ['assets']
        }) as MissionModel;


        if(!mission) {
            this.messageCollector.addNotFound("Mission not found");
            return null;
        }

        const isrTrack = await this.isrTrackRepository.findOne({
            where: {
                id: isrTrackId,
                operationId: mission.operationId
            }
        }) as ISRTrackModel;

        if(!isrTrack) {
        	this.messageCollector.addNotFound("ISR Track not found");
        	return null;
        }

        const asset = await this.assetRepository.findOne({
        	where: {
        		id: assetId,
        		operationId: isrTrack?.operationId
        	}
        }) as AssetModel;

        //check if asset assigned to mission
        if(!mission.assets.find(a => a.id === asset.id)){
            this.messageCollector.addBadRequest("Asset not assigned to mission");
            return null;
        }

        //check if engagedMissionAsset exists
        const engagedMissionAsset = await this.repository.findOne({
            where: {
                assetId: asset.id,
                missionId: mission.id,
                isrTrackId: isrTrack.id
            }
        }) as EngagedMissionAssetModel;

        if(engagedMissionAsset){
            this.messageCollector.addConflict("Asset already assigned to ISR Track");
            return null;
        }

        const newEngagedMissionAsset = new EngagedMissionAssetModel();
        newEngagedMissionAsset.assetId = asset.id;
        newEngagedMissionAsset.missionId = mission.id;
        newEngagedMissionAsset.isrTrackId = isrTrack.id;
        newEngagedMissionAsset.startAt = mission.startAt;
        newEngagedMissionAsset.endAt = mission.endAt;

        const savedEngagedMissionAsset = await this.repository.save(newEngagedMissionAsset);
        return this.findById(savedEngagedMissionAsset.id.toString(), 'asset,mission,isrTrack');
    }


    override async delete(id: string | number): Promise<boolean> {
        try {
            const engagedMissionAsset = await this.findById(id.toString());
            if (!engagedMissionAsset) {
                this.messageCollector.addNotFound("Engaged Mission Asset not found");
                return false;
            }

            const missionId = engagedMissionAsset.missionId;
            if (!await userCanAccessMission(this.requestingUser?.id, missionId)) {
                this.messageCollector.addDenied("You do not have access to this resource");
                return false;
            }

            await this.repository.delete(id);
            this.messageCollector.addSuccess("Engaged Mission Asset deleted successfully");
            return true;
        } catch (error) {
            this.messageCollector.addThrowableError(error);
            return false;
        }
    }



}