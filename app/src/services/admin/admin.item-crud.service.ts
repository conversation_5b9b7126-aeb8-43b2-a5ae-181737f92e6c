// generic-crud.service.ts
import {Between, FindOptionsWhere, In, LessThanOrEqual, MoreThanOrEqual, FindManyOptions, FindOptionsOrder} from "typeorm";
import { Repository, EntityTarget, ObjectLiteral, DeepPartial, FindOptionsRelations, ILike} from 'typeorm';
import AppDataSource from '@/config/database.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import {CACHE_DURATIONS, IRequestingUser} from "@/interfaces/database.interface.js";
export interface extraFilter {
	parent: string;
	oneDeep: string;
	ids: number[];
	shallow?: boolean;
	relationship?: 'm2m' | 'm2o' | 'o2m' | null | undefined;
}

export class GenericCrudService<T extends ObjectLiteral, CreateDTO, UpdateDTO> implements ICrudService<T, CreateDTO, UpdateDTO> {
	protected repository: Repository<T>;
	protected messageCollector: MessageCollector;
	protected userId: number|undefined;
	protected organizationId: number|undefined;
	protected requestingUser: IRequestingUser|undefined;


	constructor(entity: EntityTarget<T>, messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		this.repository = AppDataSource.getRepository(entity);
		this.messageCollector = messageCollector;
		this.userId = requestingUser?.id || undefined;
		this.requestingUser = requestingUser;
		this.organizationId = organizationId;
	}

	isDateTimeColumn(column: any): boolean {
		return column?.type === Date ||
			column?.type === 'datetime' ||
			column?.type === 'timestamp' ||
			column?.type === 'timestamptz';
	}

	processDateRangeParam(key: string): { isDateRange: boolean; type: 'start' | 'end'; columnName: string } | null {
		const startMatch = key.match(/^start(.+)At$/);
		const endMatch = key.match(/^end(.+)At$/);

		if (startMatch) {
			return {
				isDateRange: true,
				type: 'start',
				columnName: startMatch[1].charAt(0).toLowerCase() + startMatch[1].slice(1) + 'At'
			};
		}

		if (endMatch) {
			return {
				isDateRange: true,
				type: 'end',
				columnName: endMatch[1].charAt(0).toLowerCase() + endMatch[1].slice(1) + 'At'
			};
		}

		return null;
	}

	buildWhereConditions(params: any, extraFilters?: extraFilter[]): FindOptionsWhere<T> {
		const entityColumns = this.repository.metadata.columns.map(col => col.propertyName);
		const whereConditions: Record<string, any> = {};
		const dateRanges: Record<string, { start?: Date; end?: Date }> = {};
		const filterDescriptions: string[] = [];

		const paginationParams = ['page', 'perPage', 'sortBy', 'orderBy', 'load'];

		Object.entries(params).forEach(([key, value]) => {
			if (!paginationParams.includes(key) && value !== undefined && value !== '') {
				// Check for date range parameters
				const dateRangeInfo = this.processDateRangeParam(key);

				if (dateRangeInfo) {
					const { type, columnName } = dateRangeInfo;
					const column = this.repository.metadata.findColumnWithPropertyName(columnName);

					if (column && this.isDateTimeColumn(column)) {
						try {
							const date = new Date(value as string);
							if (!isNaN(date.getTime())) {
								if (!dateRanges[columnName]) {
									dateRanges[columnName] = {};
								}
								dateRanges[columnName][type] = date;
							} else {
								this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
							}
						} catch (error) {
							this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
						}
					}
					return;
				}

				// Handle regular conditions
				if (entityColumns.includes(key)) {
					const column = this.repository.metadata.findColumnWithPropertyName(key);
					let processedValue = value;

					if (column?.type === Number || column?.type === 'int' || column?.type === 'integer') {
						processedValue = Number(value);
						filterDescriptions.push(`${key} is ${processedValue}`);
					} else if (this.isDateTimeColumn(column)) {
						try {
							processedValue = new Date(value as string);
							// @ts-ignore
							if (!processedValue && isNaN(processedValue.getTime())) {
								throw new Error('Invalid date');
							}
							filterDescriptions.push(`${key} is ${processedValue.toString()}`);
						} catch (error) {
							this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
							return;
						}
					} else {
						filterDescriptions.push(`${key} is "${value}"`);
					}

					whereConditions[key] = processedValue;
				} else {
					this.messageCollector.addWarning(`Ignoring invalid filter parameter: ${key}`);
				}
			}
		});

		// Process date ranges
		Object.entries(dateRanges).forEach(([columnName, range]) => {
			if (range.start && range.end) {
				whereConditions[columnName] = Between(range.start, range.end);
				filterDescriptions.push(`${columnName} is between ${range.start.toISOString()} and ${range.end.toISOString()}`);
			} else if (range.start) {
				whereConditions[columnName] = MoreThanOrEqual(range.start);
				filterDescriptions.push(`${columnName} is after ${range.start.toISOString()}`);
			} else if (range.end) {
				whereConditions[columnName] = LessThanOrEqual(range.end);
				filterDescriptions.push(`${columnName} is before ${range.end.toISOString()}`);
			}
		});

		// Add organizations filter for AoiModel
		if (extraFilters && extraFilters.length > 0) {
			// Using TypeORM relations to filter
			extraFilters.forEach(filter => {
				switch (filter.relationship) {
					case 'm2m':
						if (filter.shallow) {
							whereConditions[filter.parent] = {
								id: In(filter.ids)
							};
							filterDescriptions.push(`Shallow M2M ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						} else {
							whereConditions[filter.parent] = {
								[filter.oneDeep]: {
									id: In(filter.ids)
								}
							};
							filterDescriptions.push(`Deep M2M ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						}
						break;

					case 'm2o':
						if (filter.shallow) {
							whereConditions[`${filter.parent}Id`] = In(filter.ids);
							filterDescriptions.push(`Shallow M2O ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						} else {
							whereConditions[filter.parent] = {
								[filter.oneDeep]: {
									id: In(filter.ids)
								}
							};
							filterDescriptions.push(`Deep M2O ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						}
						break;

					case 'o2m':
						if (filter.shallow) {
							whereConditions.id = In(filter.ids);
							filterDescriptions.push(`Shallow O2M ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						} else {
							whereConditions[filter.parent] = {
								[filter.oneDeep]: {
									id: In(filter.ids)
								}
							};
							filterDescriptions.push(`Deep O2M ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						}
						break;

					default:
						if (filter.shallow) {
							whereConditions[filter.parent] = {
								id: In(filter.ids)
							};
							filterDescriptions.push(`Shallow ${filter.parent} ids are [${filter.ids.join(', ')}]`);
						} else {
							whereConditions[filter.parent] = {
								[filter.oneDeep]: {
									id: In(filter.ids)
								}
							};
							filterDescriptions.push(`${filter.parent} ids are [${filter.ids.join(', ')}]`);
						}
				}
			});
		}


		// Add the filter message if there are any conditions
		if (filterDescriptions.length > 0) {
			const filterMessage = `Applying filters where ${filterDescriptions.join(' and ')}`;
			this.messageCollector.addInfo(filterMessage);
		}
		return whereConditions as FindOptionsWhere<T>;
	}

	parseRelations(loadString?: string): string[] {
		if (!loadString) return [];

		const relations:string[] = [];
		loadString.split(',').forEach(relation => {
			relations.push(relation.trim());
		});

		return relations as string[];
	}

	async create(data: CreateDTO): Promise<T | null> {
		try {
			const entity = this.repository.create(data as DeepPartial<T>);
			const savedEntity = await this.repository.save(entity);

			this.messageCollector.addSuccess(`Entity created successfully`);
			return savedEntity as T;
		} catch (error) {
			console.trace(error);
			this.messageCollector.addError("Failed to create entity:", error);
			return null;
		}
	}

	async findAll(params: any, extraFilters?: extraFilter[]): Promise<{ items: T[], count: number }> {
		const { page = 1, perPage = 10, sortBy = 'desc', orderBy = 'id', load } = params;
		try {
			const relations = this.parseRelations(load);
			const where = this.buildWhereConditions(params, extraFilters);


			if (Object.keys(where).length > 0) {
				this.messageCollector.addInfo(`Applying filters: ${JSON.stringify(where)}`);
			}

			// Process comma-separated orderBy and sortBy values for sequential ordering
			const orderByFields: string[] = orderBy.split(',').map((field: string) => field.trim());
			const sortByDirections: string[] = sortBy.split(',').map((direction: string) => direction.trim().toUpperCase());

			// Build the order object for sequential sorting
			const orderObject: Record<string, string> = {};
			orderByFields.forEach((field: string, index: number) => {
				// If sortBy has fewer items than orderBy, use the first sortBy for all remaining fields
				// If sortBy has more items than needed for the current orderBy fields, extra values are ignored
				const direction: string = index < sortByDirections.length ?
					sortByDirections[index] :
					// If we've run out of sort directions but have more fields, use the last provided direction
					(sortByDirections.length > 0 ? sortByDirections[sortByDirections.length - 1] : 'DESC');

				orderObject[field] = direction;
			});

			// Create a cache key based on all query parameters
			const query: FindManyOptions<T> = {
				where,
				skip: (page - 1) * perPage,
				take: perPage,
				order: orderObject as FindOptionsOrder<T>,
				relations: relations,
				// cache: {
				// 	id: cacheKey,
				// 	milliseconds: CACHE_DURATIONS.QUICK// Cache for 1 minute
				// }
			}
			const [items, total] = await this.repository.findAndCount(query) as [T[], number];
			return { items, count: total };
		} catch (error) {
			this.messageCollector.addError("Failed to retrieve entities:", error);
			return { items: [], count: 0 };
		}
	}

	async findById(id: string, load?: string): Promise<T | null> {
		try {
			const relations = this.parseRelations(load);
			const cacheKey = `${this.repository.metadata.tableName}:${id}:${JSON.stringify(relations)}`;

			const query = {
				where: { id: parseInt(id) } as any,
				relations: relations,
				// cache: {
				// 	id: cacheKey,
				// 	milliseconds: CACHE_DURATIONS.QUICK  // Cache for 1 minute
				// }
			}
			const entity = await this.repository.findOne(query) as T | null;
			if (!entity) {
				this.messageCollector.addWarning(`Entity with id ${id} not found`);
			}
			return entity;
		} catch (error) {
			this.messageCollector.addError("Failed to get entity by id:", error);
			return null;
		}
	}

	async update(id: string, data: UpdateDTO): Promise<T | null> {
		try {
			// First fetch the existing entity
			const existingEntity = await this.repository.findOne({
				where: { id: parseInt(id) } as any
			});

			if (!existingEntity) {
				this.messageCollector.addWarning(`Entity with id ${id} not found`);
				return null;
			}

			// Update the entity properties
			Object.assign(existingEntity, data);

			// Save the updated entity
			const updatedEntity = await this.repository.save(existingEntity);
			return updatedEntity as T;

		} catch (error) {
			this.messageCollector.addError("Failed to update entity:", error);
			return null;
		}
	}

	async delete(id: string): Promise<boolean> {
		try {
			const result = await this.repository.delete(id);
			if (result.affected === 0) {
				this.messageCollector.addWarning(`Entity with id ${id} not found for deletion`);
				return false;
			}
			return true;
		} catch (error) {
			this.messageCollector.addError("Failed to delete entity:", error);
			return false;
		}
	}

	async search(searchTerm: string, fields?: string[], extraFilters?: extraFilter[]): Promise<{ items: T[], count: number }> {
		try {
			if (searchTerm.length < 2) {
				this.messageCollector.addWarning("Search term must be at least 2 characters long");
				return { items: [], count: 0 };
			}

			const entityMetadata = this.repository.metadata;
			const allFields = entityMetadata.columns.map(column => column.propertyName);

			const searchFields = fields || (this.repository.target as any).getSearchableFields?.() || [];

			if (searchFields.length === 0) {
				this.messageCollector.addWarning("No searchable fields found. Please provide a list of fields to search, (ex: ?fields=name,description,email). Check for documentation");
				return { items: [], count: 0 };
			}

			const invalidFields = searchFields.filter((field: string) => !allFields.includes(field));

			if (invalidFields.length > 0) {
				this.messageCollector.addWarning(`Invalid fields provided: ${invalidFields.join(', ')}`);
				return { items: [], count: 0 };
			}

			let whereClause = searchFields.reduce((acc: any, field: string) => {
				acc[field] = ILike(`%${searchTerm}%`);
				return acc;
			}, {});

			if (extraFilters && extraFilters.length > 0) {
				// Using TypeORM relations to filter
				extraFilters.forEach(filter => {
					switch (filter.relationship) {
						case 'm2m':
							if (filter.shallow) {
								whereClause[filter.parent] = {
									id: In(filter.ids)
								};
							} else {
								whereClause[filter.parent] = {
									[filter.oneDeep]: {
										id: In(filter.ids)
									}
								};
							}
							break;

						case 'm2o':
							if (filter.shallow) {
								whereClause[`${filter.parent}Id`] = In(filter.ids);
							} else {
								whereClause[filter.parent] = {
									[filter.oneDeep]: {
										id: In(filter.ids)
									}
								};
							}
							break;

						case 'o2m':
							if (filter.shallow) {
								whereClause.id = In(filter.ids);
							} else {
								whereClause[filter.parent] = {
									[filter.oneDeep]: {
										id: In(filter.ids)
									}
								};
							}
							break;

						default:
							if (filter.shallow) {
								whereClause[filter.parent] = {
									id: In(filter.ids)
								};
							} else {
								whereClause[filter.parent] = {
									[filter.oneDeep]: {
										id: In(filter.ids)
									}
								};
							}
					}
				});
			}

			const [items, total] = await this.repository.findAndCount({
				where: whereClause,
				take: 30
			});

			if (items.length === 0) {
				this.messageCollector.addWarning(`No entities found for search term "${searchTerm}"`);
			}

			return { items, count: total };
		} catch (error) {
			this.messageCollector.addError("Failed to search entities:", error);
			return { items: [], count: 0 };
		}
	}

	// async search(searchTerm: string, fields?: string[]): Promise<{ items: T[], count: number }> {
	// 	try {
	// 		if (searchTerm.length < 2) {
	// 			this.messageCollector.addWarning("Search term must be at least 2 characters long");
	// 			return { items: [], count: 0 };
	// 		}
	//
	//
	// 		const searchFields = fields || (this.repository.target as any).getSearchableFields?.() || [];
	// 		//if not searchable fields - tell user to provider them
	// 		if (searchFields.length === 0) {
	// 			this.messageCollector.addWarning("No searchable fields found. Please provide a list of fields to" +
	// 				" search, (ex: ?fields=name,description,email). Check for documentation");
	// 			return { items: [], count: 0 };
	// 		}
	// 		console.log("FIELDS ", searchFields, this.repository.target.hasOwnProperty("dddd"));
	// 		//check make sure fields are valid and exist on model
	// 		const invalidFields = searchFields.filter((field: string) => {
	// 			console.log("FIELD ", field);
	// 			console.log("HASOWNPROPERTY ", this.repository.target.hasOwnProperty(field));
	// 			return !this.repository.target.hasOwnProperty(field)
	// 		});
	// 		console.log("INV ", invalidFields);
	// 		if(invalidFields.length > 0){
	// 			this.messageCollector.addWarning(`Invalid fields provided: ${invalidFields.join(', ')}`);
	// 			return { items: [], count: 0 };
	// 		}
	// 		const whereClause = searchFields.reduce((acc: any, field: string) => {
	// 			acc[field] = ILike(`%${searchTerm}%`);
	// 			return acc;
	// 		}, {});
	//
	// 		const [items, total] = await this.repository.findAndCount({
	// 			where: whereClause,
	// 			take: 30
	// 		});
	// 		//if items is empty, add info eror
	// 		if (items.length === 0) {
	// 			this.messageCollector.addWarning(`No entities found for search term "${searchTerm}"`);
	// 		}
	// 		return { items, count: total };
	// 	} catch (error) {
	// 		this.messageCollector.addError("Failed to search entities:", error);
	// 		return { items: [], count: 0 };
	// 	}
	// }
}