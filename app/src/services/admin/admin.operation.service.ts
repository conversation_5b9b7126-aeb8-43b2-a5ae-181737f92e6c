import OperationModel from '@/models/operation.model.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import AppDataSource from '@/config/database.js';
import { Brackets, EntityManager, In } from 'typeorm';
import {
	CreateOperationDTO,
	GetOpSyncDTO,
	UpdateOperationDTO,
	UserAccess
} from '@/interfaces/admin/admin.operation.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import UserModel from '@/models/user.model.js';
import OperationsUsersModel from '@/models/operations-users.model.js';
import {
	AccessType,
	IRequestingUser,
	ITimezoneType,
	MissionStatus,
	OperationType
} from '@/interfaces/database.interface.js';
import MissionModel from '@/models/mission.model.js';
import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js';
import { userCanAccessOrganization } from '@/acl/access/membership/userCanAccessOrganization.acl.js';
import OrganizationModel from '@/models/organization.model.js';
import { operationAcl } from '@/acl/access/rules/operation.acl.js';
import PIRModel from "@/models/pir.model.js";

export default class AdminOperationService
	extends GenericCrudService<OperationModel, CreateOperationDTO, UpdateOperationDTO>
	implements ICrudService<OperationModel, CreateOperationDTO, UpdateOperationDTO> {
	private userRepository: ReturnType<EntityManager['getRepository']>;
	private operationUserRepository: ReturnType<EntityManager['getRepository']>;
	private missionRepository: ReturnType<EntityManager['getRepository']>;
	private organizationRepository: ReturnType<EntityManager['getRepository']>;
	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private pirRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(OperationModel, messageCollector, requestingUser, organizationId);
		this.userRepository = AppDataSource.getRepository(UserModel);
		this.operationUserRepository = AppDataSource.getRepository(OperationsUsersModel);
		this.missionRepository = AppDataSource.getRepository(MissionModel);
		this.organizationRepository = AppDataSource.getRepository(OrganizationModel);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
		this.pirRepository = AppDataSource.getRepository(PIRModel);
	}

	override async findAll(params: any): Promise<{ items: OperationModel[], count: number }> {

		if(!await operationAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'organizations',
				oneDeep: '',
				ids: this.organizationId ? [this.organizationId] : [],
				shallow: true,
				relationship: 'm2m'
			}
		]);
	}

	override async create(data: CreateOperationDTO): Promise<OperationModel | null> {

		try {
			if(!await operationAcl(null, this.requestingUser, AccessType.CREATE)){
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}

			let orgIds: number[] = [];
			// Check if we have organizationIds, if not use current organization
			if (!data.organizationIds || data.organizationIds.length === 0) {
				let orgId = this.organizationId ?? null;

				if (!orgId) {
					const userWithRoles = await this.userRepository.findOne({
						where: { id: this.requestingUser!.id },
						relations: ['roles']
					});
					orgId = userWithRoles?.roles[0]?.organizationId;
				}

				if (!orgId) {
					this.messageCollector.addNotFound("Organization not found");
					return null;
				}

				orgIds = [orgId];
			} else {
				// Filter allowed organizations
				for (const orgId of data.organizationIds) {
					const canAccess = await userCanAccessOrganization(this.requestingUser?.id, orgId);
					if (canAccess) {
						orgIds.push(orgId);
					}
				}
			}

			// Create operation without organizations first
			console.log("Creating operation with data: ", data);
			const opsData = {
				name: data.name,
				location: data.location,
				config: data.config,
				locationCoordinates: data.locationCoordinates,
				zoom: data.zoom,
				designation: data.designation,
				description: data.description,
				type: data.type,
				isActive: true
			}
			console.log("Operation data: ", opsData);


			const operation = await this.repository.save(opsData) as OperationModel;
			console.log("Operation created: ", operation);
			// Save operation first to get ID

			console.log("Saving pir...");
			//lets add PIRs
			if (data.pirs) {
				console.log("PIRs found: ", data.pirs);
				for (const pir of data.pirs) {
					console.log("Saving pir: ", pir);
					const newPir = new PIRModel();
					newPir.question = pir.question;
					newPir.isActive = pir.isActive ?? true;
					newPir.operationId = operation.id;
					console.log("New pir: ", newPir);
					let pp = await this.pirRepository.save(newPir) as PIRModel;
					console.log("Pir saved", pp);
				}
			}

			if (operation && operation.id) {
				// Fetch organizations and set the relationship
				operation.organizations = await this.organizationRepository.find({
					where: { id: In(orgIds) },
				}) as OrganizationModel[];
				this.messageCollector.addSuccess("Operation was added to organizations");
				// Save again with organizations
				await this.repository.save(operation);

				// Add current user to operation
				if (this.requestingUser) {
					await this.addUsersToOperation(operation.id.toString(), [{
						id: this.requestingUser.id,
						accessType: 'manage'
					}]);
					this.messageCollector.addSuccess("Current user added to this operation");
				} else {
					this.messageCollector.addInfo("User not found in request. Add user to operation manually");
				}

				return operation;
			}

			this.messageCollector.addError("Failed to create operation");
			return null;
		} catch (error) {
			console.log("Error creating operation: ", error);
			this.messageCollector.addThrowableError(error);
			return null;
		}

	}

	override async findById(id: string, load?: string): Promise<OperationModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<OperationModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: OperationModel[], count: number }> {

		if(!await operationAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [
			{
				parent: 'organizations',
				oneDeep: '',
				ids: this.organizationId ? [this.organizationId] : [],
				shallow: true,
				relationship: 'm2m'
			}
		]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){

		if(!await operationAcl(id, this.requestingUser,  requestingAccess)){
			this.messageCollector.addDenied("You do not have rights to this resource");
			return false;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, parseInt(id))){
			this.messageCollector.addDenied("You do not have access to this operation");
			return false;
		}
		return true;
	}

	async resetOperation(operationId: string): Promise<OperationModel|null> {
		try {

			if(! await this._canUserAccessThis(operationId, AccessType.DELETE)) return null;

			const tables = [
				'global_isrs',
				'areas_of_interest',
				'assets',
				'missions',
				'originators',
				'pirs',
				'rfis',
			];

			// Temporarily disable foreign key constraints
			await AppDataSource.manager.query('SET CONSTRAINTS ALL DEFERRED');

			// Remove entries for each table where operation_id matches
			for (const table of tables) {
				try {
					await AppDataSource.manager.query(
						`DELETE
                         FROM ${table}
                         WHERE operation_id = $1`,
						[operationId]
					);
					this.messageCollector.addSuccess(
						`Entries removed from table ${table} for operation_id: ${operationId}`
					);
				} catch (error) {
					this.messageCollector.addError(
						`Error deleting entries from table ${table}`
					);
					throw error; // Re-throw to trigger transaction rollback
				}
			}

			// Re-enable foreign key constraints
			await AppDataSource.manager.query('SET CONSTRAINTS ALL IMMEDIATE');

			this.messageCollector.addSuccess(
				`Entries successfully removed for operation_id: ${operationId}`
			);
			return this.findById(operationId);
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			throw error; // Re-throw the error to trigger proper error handling
		}
	}

	async sync(id: string, requestQuery:GetOpSyncDTO): Promise<{ syncs: MissionModel[], type: string, startDate: Date, endDate: Date } | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}

			const type = requestQuery.type ? requestQuery.type : '24';
			const endDate = requestQuery.endDate;
			const currentTimestamp = requestQuery.currentTimestamp;
			// Calculate the base timestamp
			const baseTimestamp = (currentTimestamp) ? parseInt(currentTimestamp) : Date.now();
			const baseDate = new Date(baseTimestamp);
			// Calculate offset in milliseconds
			let offset = 0;
			if(endDate){
				offset = Date.parse(endDate) - baseTimestamp;
			} else {
				offset = (type === '24') ? 86400000 :
					(type === '48') ? 172800000 :
						(type === '72') ? 259200000 : 0;
			}
			//if offset is less than 0, set it to 0
			if(offset < 0) offset = 0;

			// Create the offset date
			const offsetDate = new Date(baseTimestamp + offset);

			// Create a cache key based on operation, type, and timestamp
			//const cacheKey = `sync:operation:${id}:type:${type}:timestamp:${baseTimestamp}`;
            const response = await this.missionRepository
                .createQueryBuilder('missions')
                .leftJoinAndSelect('missions.assets', 'assets')
                .leftJoinAndSelect('assets.isrTracks', 'isr_tracks')
                .leftJoinAndSelect('isr_tracks.mapElement', 'map_elements')
                .leftJoinAndSelect('missions.tais', 'areas_of_interest')
                .leftJoinAndSelect('areas_of_interest.mapElement', 'aoi_map_element')
                .leftJoinAndSelect('missions.engagedMissionAssets', 'engaged_mission_assets')
                .leftJoinAndSelect('engaged_mission_assets.asset', 'engaged_assets')
                .leftJoinAndSelect('engaged_mission_assets.mission', 'engaged_missions')
                .leftJoinAndSelect('engaged_mission_assets.isrTrack', 'engaged_isr_tracks')
                .leftJoinAndSelect('engaged_isr_tracks.mapElement', 'engaged_tracks_map_elements')
                .where('missions.operationId = :operationId', { operationId: operation.id })
                .andWhere('missions.status IN (:...statuses)', { statuses: [MissionStatus.ACTIVE, MissionStatus.COMPLETED, MissionStatus.PLANNED] })
                .andWhere(new Brackets(qb => {
                    qb.where('missions.end_at > :baseDate', { baseDate })
                        .orWhere(new Brackets(qb2 => {
                            qb2.where('missions.start_at < :offsetDate', { offsetDate })
                                .andWhere('missions.start_at > :baseDate', { baseDate });
                        }));
                }))
                .andWhere(new Brackets(qb => {
                    qb.where('NOT (missions.start_at > :baseDate AND missions.end_at > :offsetDate)',
                        { baseDate, offsetDate });
                }))
                .getMany() as MissionModel[];

			return {
				syncs: response,
				type: type,
				startDate: baseDate,
				endDate: offsetDate,
			};

		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	// addUsersToOperation(id, users);
	async addUsersToOperation(id: string, users: UserAccess[]): Promise<OperationModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}

			const usersToAdd = await this.userRepository.find({
				where: {
					id: In(users.map(user => user.id))
				},
				relations: ['roles']
			}) as UserModel[];
			//foreach user, add to operationUsers ad add message to message collector
			for (const user of usersToAdd) {

				//skip user if they cannot access this operation
				if(!await userCanAccessOperation(user.id, operation.id)){
					this.messageCollector.addDenied(`User ${user.firstName} ${user.lastName} does not have access to this operation`);
					continue;
				}

				//do not add user if already in operation
				const operationUserExisting = await this.operationUserRepository.findOne({
					where: {
						userId: user.id,
						operationId: operation.id
					}
				});
				if(operationUserExisting){
					//check if accessType is different
					const existingAccessType = operationUserExisting.accessType;
					if(existingAccessType !== users.find(user => user.id === user.id)?.accessType){
						operationUserExisting.accessType = users.find(user => user.id === user.id)?.accessType || AccessType.READ;
						await this.operationUserRepository.save(operationUserExisting);
						this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} access Type updated successfully`);
					} else {
						this.messageCollector.addWarning(`User ${user.firstName} ${user.lastName} already in operation`);
					}
					continue;
				}

				//create operationUser
				const operationUser = new OperationsUsersModel();
				operationUser.userId = user.id;
				operationUser.operationId = operation.id;
				operationUser.accessType = users.find(user => user.id === user.id)?.accessType || AccessType.READ;
				await this.operationUserRepository.save(operationUser);
				this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} added to operation`);
			}
			this.messageCollector.addSuccess("Users added successfully");
			return this.findById(id, 'users');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	// removeUserFromOperation(id, userId);
	async removeUser(id: string, userId: string): Promise<OperationModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}
			const user = await this.userRepository.findOne({
				where: { id: userId },
			});

			if (!user) {
				this.messageCollector.addNotFound("User not found for this operation");
				return null;
			}

			//remove on OperationsUsersModel
			await this.operationUserRepository.delete({
				operationId: id,
				userId: userId
			});

			this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} removed successfully`);
			return this.findById(id, 'users');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	// removeUsersFromOperation(id, userIds);
	async removeUsers(id: string, userIds: number[]): Promise<OperationModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}

			const users = await this.userRepository.find({
				where: {
					id: In(userIds)
				}
			}) as UserModel[];
			//remove on OperationsUsersModel
			//go through each user and remove from operationUsers
			for (const user of users) {
				await this.operationUserRepository.delete({
					operationId: id,
					userId: user.id
				});
				this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} removed successfully`);
			}

			this.messageCollector.addSuccess("Users removed successfully");
			return this.findById(id, 'users');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	// patchUsersToOperation(id, users);
	async patchUsers(id: string, users: UserAccess[]): Promise<OperationModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}
			const usersToAdd = await this.userRepository.find({
				where: {
					id: In(users.map(user => user.id))
				},
				relations: ['roles']
			}) as UserModel[];
			//foreach user, add to operationUsers ad add message to message collector
			for (const user of usersToAdd) {
				if(!await userCanAccessOperation(user.id, operation.id)){
					this.messageCollector.addDenied(`User ${user.firstName} ${user.lastName} does not have access to this operation`);
					continue;
				}
				//check if user already exists in operationUsers
				const operationUserExisting = await this.operationUserRepository.findOne({
					where: {
						userId: user.id,
						operationId: id
					}
				});
				if (operationUserExisting) {
					//update accessType
					operationUserExisting.accessType = users.find(user => user.id === user.id)?.accessType || AccessType.READ;
					await this.operationUserRepository.save(operationUserExisting);
					this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} updated successfully`);
				} else {
					const operationUser = new OperationsUsersModel();
					operationUser.userId = user.id;
					operationUser.operationId = operation.id;
					operationUser.accessType = users.find(user => user.id === user.id)?.accessType || AccessType.READ;
					await this.operationUserRepository.save(operationUser);
					this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} added to operation`);
				}
			}
			this.messageCollector.addSuccess("Users added successfully");
			return this.findById(id, 'users');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	// updateUsersToOperation(id, users);
	async updateUsers(id: string, users: UserAccess[]): Promise<OperationModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const operation = await this.findById(id);
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}
			const usersToAdd = await this.userRepository.find({
				where: {
					id: In(users.map(user => user.id))
				}
			}) as UserModel[];

			//clear out all existing users
			await this.operationUserRepository.delete({
				operationId: id
			});

			//foreach user, add to operationUsers ad add message to message collector
			for (const user of usersToAdd) {
				//check if user already exists in operationUsers
				if(!await userCanAccessOperation(user.id, operation.id)){
					this.messageCollector.addWarning(`User ${user.firstName} ${user.lastName} does not have access to this operation`);
					continue;
				}
				const operationUser = new OperationsUsersModel();
				operationUser.userId = user.id;
				operationUser.operationId = operation.id;
				operationUser.accessType = users.find(user => user.id === user.id)?.accessType || AccessType.READ;
				await this.operationUserRepository.save(operationUser);
				this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} added to operation`);

			}
			this.messageCollector.addSuccess("Users added successfully");
			return this.findById(id, 'users');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async getOperationUsers(id: string): Promise<UserModel[] | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
			const opUsers = await this.operationUserRepository.find({
				where: { operationId: id },
				relations: ['user']
			}) as OperationsUsersModel[];
			const users = opUsers.map(opUser => opUser.user);
			return users;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async toggleStatus(id: string, isActive: boolean): Promise<OperationModel | null> {
		try {
			if(!await this._canUserAccessThis(id, AccessType.UPDATE)){
				this.messageCollector.addDenied(`You don't have permission to toggle this operation`);
				return null;
			}
			const operation = await this.operationRepository.findOne({
				where: { id: id },
			});
			if(!operation){
				this.messageCollector.addDenied(`Operation with id ${id} not found`);
				return null;
			}
			operation.isActive = isActive;
			return await this.operationRepository.save(operation) as OperationModel;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}

	}

	private _parseUserToCheck(userWithRoles:UserModel){

		return  {
			id: userWithRoles.id,
			email: userWithRoles.email.toLowerCase(),
			accountType: userWithRoles.accountType,
			roles: userWithRoles.roles.map(role => ({
				id: role.id,
				staffDesignation: role.staffDesignation,
				roleName: role.roleName,
				isCommandRole: role.isCommandRole,
				isStaffRole: role.isStaffRole,
				organizationId: role.organizationId,
				isActive: role.isActive
			}))
		}
	}

}