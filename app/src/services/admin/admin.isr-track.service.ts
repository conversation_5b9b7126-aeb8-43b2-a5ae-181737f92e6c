import ISRTrackModel from '@/models/isr-track.model.js';
import AppDataSource from '@/config/database.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import { CreateISRTrackDTO, UpdateISRTrackDTO } from '@/interfaces/admin/admin.isr-track.interface.js';
import {EntityManager, In, QueryFailedError} from 'typeorm';
import OperationModel from '@/models/operation.model.js';
import MapElementModel from '@/models/map-element.model.js';
import { AccessType, BorderType, IRequestingUser, RGBAColor } from '@/interfaces/database.interface.js';
import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js';
import { isrTrackAcl } from '@/acl/access/rules/isr-track.acl.js';
import AssetModel from "@/models/asset.model.js";
import MissionModel from "@/models/mission.model.js";
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";

export default class AdminISRTrackService
	extends GenericCrudService<ISRTrackModel, CreateISRTrackDTO, UpdateISRTrackDTO>
	implements ICrudService<ISRTrackModel, CreateISRTrackDTO, UpdateISRTrackDTO> {

	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private mapElementRepository: ReturnType<EntityManager['getRepository']>;
	private assetRepository: ReturnType<EntityManager['getRepository']>;
    private missionRepository: ReturnType<EntityManager['getRepository']>;
    private engagedMissionAssetRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(ISRTrackModel, messageCollector, requestingUser, organizationId);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
		this.mapElementRepository = AppDataSource.getRepository(MapElementModel);
		this.assetRepository = AppDataSource.getRepository(AssetModel);
        this.missionRepository = AppDataSource.getRepository(MissionModel);
        this.engagedMissionAssetRepository = AppDataSource.getRepository(EngagedMissionAssetModel);

	}

	override async findAll(params: any): Promise<{ items: ISRTrackModel[], count: number }> {
		if(!await isrTrackAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("No access allowed");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: any): Promise<ISRTrackModel | null> {

		if(!await isrTrackAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<ISRTrackModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async delete(id: string): Promise<boolean> {
		if(!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: ISRTrackModel[], count: number }> {
		if(!await isrTrackAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("No access allowed");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess:AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have accesss to this resource");
			return false;
		}
		if(!await isrTrackAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You don't have access to this")
			return false;
		}
		return true
	}


	private async operationExistsAndActive(operationId: number): Promise<boolean> {
		const operation = await this.operationRepository.findOne({ where: { id: operationId } });
		if (!operation) {
			this.messageCollector.addNotFound("Operation not found");
			return false;
		}
		if (!operation.isActive) {
			this.messageCollector.addDenied("Operation is not active");
			return false;
		}
		return true;
	}

	async createWithUserId(data: CreateISRTrackDTO, userId: number): Promise<ISRTrackModel | null> {
		if (!userId) {
			this.messageCollector.addBadRequest("User ID is required");
			return null;
		}
		if(!await isrTrackAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("User does not have access to this operation");
			return null;
		}
		try {
			if (!await this.operationExistsAndActive(data.operationId)) {
				return null;
			}

			// Create map element if provided
			let mapElementId: number | undefined;
			if (data.mapElement) {
				const mapElement = new MapElementModel();
				Object.assign(mapElement, {
					element: data.mapElement.element,
					elementType: data.mapElement.elementType,
					elementColor: data.mapElement.elementColor,
					borderType: data.mapElement.borderType,
					borderThickness: data.mapElement.borderThickness,
					borderColor: data.mapElement.borderColor
				});
				const savedMapElement = await this.mapElementRepository.save(mapElement);
				mapElementId = savedMapElement.id;
			}

			// Create ISR Track
			const isrTrack = new ISRTrackModel();
			Object.assign(isrTrack, {
				createdByUserId: userId,
				label: data.label,
				operationId: data.operationId,
				status: data.status,
				priority: data.priority,
				commenceAt: data.commenceAt,
				concludeAt: data.concludeAt,
				ltiovDate: data.ltiovDate,
				mapElementId
			});

			const savedISRTrack = await this.repository.save(isrTrack);

			//yeah we are not doing this
			// Add collection capabilities if provided
			// if (data.collectionCapabilityIds?.length) {
			// 	savedISRTrack.collectionCapabilities = await this.collectionCapabilityRepository
			// 		.createQueryBuilder('cc')
			// 		.where('cc.id IN (:...ids)', { ids: data.collectionCapabilityIds })
			// 		.getMany() as CollectionCapabilityModel[];
			// 	await this.repository.save(savedISRTrack);
			// }

			return savedISRTrack;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async update(id: string | number, data: UpdateISRTrackDTO): Promise<ISRTrackModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
			const isrTrack = await this.findById(id.toString());
			if (!isrTrack) {
				this.messageCollector.addNotFound("ISR Track not found");
				return null;
			}

			// Update basic fields
			if (data.label !== undefined) isrTrack.label = data.label;
			if (data.status !== undefined) isrTrack.status = data.status;
			if (data.priority !== undefined) isrTrack.priority = data.priority;
			if (data.commenceAt !== undefined) isrTrack.commenceAt = data.commenceAt;
			if (data.concludeAt !== undefined) isrTrack.concludeAt = data.concludeAt;
			if (data.ltiovDate !== undefined) isrTrack.ltiovDate = data.ltiovDate;

			// Update map element if provided
			if (data.mapElement) {
				const mapElement = isrTrack.mapElementId
					? await this.mapElementRepository.findOne({ where: { id: isrTrack.mapElementId }})
					: new MapElementModel();

				if (mapElement) {
					if (data.mapElement.element) mapElement.element = data.mapElement.element;
					if (data.mapElement.elementType) mapElement.elementType = data.mapElement.elementType;
					if (data.mapElement.elementColor) mapElement.elementColor = data.mapElement.elementColor;
					if (data.mapElement.borderType) mapElement.borderType = data.mapElement.borderType as BorderType;
					if (data.mapElement.borderThickness) mapElement.borderThickness = data.mapElement.borderThickness;
					if (data.mapElement.borderColor) mapElement.borderColor = data.mapElement.borderColor as RGBAColor;

					const savedMapElement = await this.mapElementRepository.save(mapElement);
					isrTrack.mapElementId = savedMapElement.id;
				}
			}

			const updatedISRTrack = await this.repository.save(isrTrack);
			this.messageCollector.addSuccess("ISR Track updated successfully");
			return updatedISRTrack;
		} catch (error) {
			if (error instanceof QueryFailedError) {
				this.messageCollector.addThrowableError(error);
			} else {
				this.messageCollector.addThrowableError(error);
			}
			return null;
		}
	}

	async assignAssetsByIds(id: string | number, assetId: number, missionId :number): Promise<ISRTrackModel | null> {
		try{

			// if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
			// const isrTrack = await super.findById(id.toString());
			// if(!isrTrack) {
			// 	this.messageCollector.addNotFound("ISR Track not found");
			// 	return null;
			// }
            //
            //
            // const mission = await this.missionRepository.findOne({
            //     where: {
            //         id: missionId,
            //         operationId: isrTrack?.operationId
            //     },
            //     relations: ['assets']
            // }) as MissionModel;
            //
            //
            // if(!mission) {
            //     this.messageCollector.addNotFound("Mission not found");
            //     return null;
            // }
            //
            //
			// const asset = await this.assetRepository.findOne({
			// 	where: {
			// 		id: assetId,
			// 		operationId: isrTrack?.operationId
			// 	}
			// }) as AssetModel;
            //
            // //check if asset assigned to mission
            // if(!mission.assets.find(a => a.id === asset.id)){
            //     this.messageCollector.addBadRequest("Asset not assigned to mission");
            //     return null;
            // }
            //
            // //check if engagedMissionAsset exists
            // const engagedMissionAsset = await this.engagedMissionAssetRepository.findOne({
            //     where: {
            //         assetId: asset.id,
            //         missionId: mission.id,
            //         isrTrackId: isrTrack.id
            //     }
            // }) as EngagedMissionAssetModel;
            //
            // if(engagedMissionAsset){
            //     this.messageCollector.addBadRequest("Asset already assigned to ISR Track");
            //     return null;
            // }
            //
            // const newEngagedMissionAsset = new EngagedMissionAssetModel();
            // newEngagedMissionAsset.assetId = asset.id;
            // newEngagedMissionAsset.missionId = mission.id;
            // newEngagedMissionAsset.isrTrackId = isrTrack.id;
            // newEngagedMissionAsset.startAt = mission.startAt;
            // newEngagedMissionAsset.endAt = mission.endAt;
            //
            // await this.engagedMissionAssetRepository.save(engagedMissionAsset);
            //
            // const updatedIsrTrack =
			return null;

		} catch (error) {
			console.log(error);
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

    async deleteAssetById(id: string | number, assetId: string): Promise<ISRTrackModel | null> {
        try {
            if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
            const isrTrack = await super.findById(id.toString(), 'assets');
            if(!isrTrack) {
                this.messageCollector.addNotFound("ISR Track not found");
                return null;
            }
            const assets = await this.assetRepository.find({
                where: {
                    id: Number(assetId),
                    operationId: isrTrack?.operationId
                }
            }) as AssetModel[];
            if(!assets || assets.length === 0) {
                this.messageCollector.addNotFound("Asset not found");
                return null;
            }

            isrTrack.assets = isrTrack.assets.filter(asset => asset.id !== assets[0].id);
            return await this.repository.save(isrTrack) as ISRTrackModel;
        } catch (error) {
            console.log(error);
            this.messageCollector.addThrowableError(error);
            return null;
        }
    }

	// async assignCollectionCapabilityById(id: string | number, collectionCapabilityId: number): Promise<ISRTrackModel | null> {
	// 	try {
	// 		if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
	// 		const isrTrack = await this.repository.findOne({
	// 			where: { id: Number(id) },
	// 			relations: ['collectionCapabilities']
	// 		});
	//
	// 		if (!isrTrack) {
	// 			this.messageCollector.addNotFound("ISR Track not found");
	// 			return null;
	// 		}
	//
	// 		const capability = await this.collectionCapabilityRepository.findOne({
	// 			where: {
	// 				id: collectionCapabilityId,
	// 				operationId: isrTrack.operationId
	// 			}
	// 		}) as CollectionCapabilityModel;
	//
	// 		if (!capability) {
	// 			this.messageCollector.addNotFound("Collection Capability not found");
	// 			return null;
	// 		}
	//
	// 		if (!isrTrack.collectionCapabilities) {
	// 			isrTrack.collectionCapabilities = [];
	// 		}
	//
	// 		// Check if capability is already assigned
	// 		if (isrTrack.collectionCapabilities.some(cc => cc.id === collectionCapabilityId)) {
	// 			this.messageCollector.addConflict("Collection Capability already assigned to this ISR Track");
	// 			return null;
	// 		}
	//
	// 		isrTrack.collectionCapabilities.push(capability);
	// 		const updatedISRTrack = await this.repository.save(isrTrack);
	// 		this.messageCollector.addSuccess("Collection Capability assigned successfully");
	// 		return updatedISRTrack;
	// 	} catch (error) {
	// 		this.messageCollector.addThrowableError(error);
	// 		return null;
	// 	}
	// }
	//
	// async removeCollectionCapabilityById(id: string | number, collectionCapabilityId: number): Promise<ISRTrackModel | null> {
	// 	try {
	// 		if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
	// 		const isrTrack = await this.repository.findOne({
	// 			where: { id: Number(id) },
	// 			relations: ['collectionCapabilities']
	// 		});
	//
	// 		if (!isrTrack) {
	// 			this.messageCollector.addNotFound("ISR Track not found");
	// 			return null;
	// 		}
	//
	// 		if (!isrTrack.collectionCapabilities?.some(cc => cc.id === collectionCapabilityId)) {
	// 			this.messageCollector.addBadRequest("Collection Capability not assigned to this ISR Track");
	// 			return null;
	// 		}
	//
	// 		isrTrack.collectionCapabilities = isrTrack.collectionCapabilities.filter(
	// 			cc => cc.id !== collectionCapabilityId
	// 		);
	//
	// 		const updatedISRTrack = await this.repository.save(isrTrack);
	// 		this.messageCollector.addSuccess("Collection Capability removed successfully");
	// 		return updatedISRTrack;
	// 	} catch (error) {
	// 		this.messageCollector.addThrowableError(error);
	// 		return null;
	// 	}
	// }
}