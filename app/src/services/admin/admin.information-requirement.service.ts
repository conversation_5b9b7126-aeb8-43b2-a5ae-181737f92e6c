import {Entity<PERSON>anager} from "typeorm";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import { GenericCrudService } from "@/services/admin/admin.item-crud.service.js";
import { ICrudService } from "@/interfaces/admin/admn.item-crud.interface.js";
import AppDataSource from "@/config/database.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";

import {
	CreateInformationRequirementDTO,
	UpdateInformationRequirementDTO,
} from "@/interfaces/admin/admin.information-requirement.interface.js";
import PIRModel from "@/models/pir.model.js";

import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { informationRequirementAcl } from '@/acl/access/rules/information-requirement.acl.js'
import { userCanAccessPir } from '@/acl/access/membership/userCanAccessPir.acl.js'

export default class AdminInformationRequirementService
	extends GenericCrudService<InformationRequirementModel, CreateInformationRequirementDTO, UpdateInformationRequirementDTO>
	implements ICrudService<InformationRequirementModel, CreateInformationRequirementDTO, UpdateInformationRequirementDTO> {

	private pirRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(InformationRequirementModel, messageCollector, requestingUser, organizationId);
		this.pirRepository = AppDataSource.getRepository(PIRModel);
	}

	override async findAll(params: any): Promise<{ items: InformationRequirementModel[], count: number }> {
		//not implemented
		if(!params.pirId) {
			this.messageCollector.addBadRequest("PIR ID is required");
			return {items: [], count: 0};
		}
		if(!await userCanAccessPir(this.requestingUser?.id, params.pirId)){
			this.messageCollector.addDenied("You do not have access to those IRs with PIR ID provided");
			return {items: [], count: 0};
		}

		return super.findAll(params);
	}

	override async findById(id: string, load?: string): Promise<InformationRequirementModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<InformationRequirementModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: InformationRequirementModel[], count: number }> {
		return {items: [], count: 0};
		// if(!pirId) {
		// 	this.messageCollector.addBadRequest("PIR ID is required");
		// 	return {items: [], count: 0};
		// }
		// if(!await userCanAccessPir(this.requestingUser?.id, pirId)){
		// 	this.messageCollector.addDenied("You do not have access to those IRs with PIR ID provided");
		// 	return {items: [], count: 0};
		// }
		// return super.search(searchTerm, fields, [
		// 	{
		// 		parent: 'pir',
		// 		oneDeep: '',
		// 		ids: [parseInt(pirId.toString())],
		// 		shallow: true
		// 	}
		// ]);
	}

	async _canUserAccessThis(id:string, requestingAccess:AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.pirId) return false;

		if(!await informationRequirementAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessPir(this.userId, item.pirId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}


		return true
	}


	private async getNextIrNumber(pirId: number): Promise<number> {
		const result = await this.repository
			.createQueryBuilder('ir')
			.where('ir.pirId = :pirId', { pirId })
			.orderBy('ir.irNumber', 'DESC')
			.getOne();

		return result ? result.irNumber + 1 : 1;
	}

	async create(data: CreateInformationRequirementDTO & { userId?: number }): Promise<InformationRequirementModel | null> {
		try {

			if(!await informationRequirementAcl(null, this.requestingUser, AccessType.CREATE)){
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}

			if (!data.userId) {
				data.userId = this.requestingUser?.id;
			}

			if(!await userCanAccessPir(data.userId, data.pirId)){
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}
			if(!data.userId){
				this.messageCollector.addBadRequest("User ID is required");
				return null;
			}

			// Check if PIR exists and is active
			const pir = await this.pirRepository.findOne({
				where: { id: data.pirId }
			});

			if (!pir) {
				this.messageCollector.addNotFound("PIR not found");
				return null;
			}

			if (!pir.isActive) {
				this.messageCollector.addDenied("Cannot create IR for inactive PIR");
				return null;
			}

			const pirId = parseInt(data.pirId.toString());
			const irNumber = await this.getNextIrNumber(pirId);

			const ir = new InformationRequirementModel();
			ir.pirId = pirId;
			ir.designation = irNumber.toString();
			ir.originator = data.originator;
			ir.informationRequirement = data.informationRequirement;
			ir.ltiovDate = data.ltiovDate;
			ir.priority = data.priority;
			ir.createdByUserId = parseInt(data.userId.toString());

			const savedIr = await this.repository.save(ir);
			this.messageCollector.addSuccess("Information Requirement created successfully");
			return await this.findById(savedIr.id.toString(), 'pir');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}
}