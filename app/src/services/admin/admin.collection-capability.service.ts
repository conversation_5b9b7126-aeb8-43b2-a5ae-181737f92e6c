import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import {ApprovalStatus, AvailabilityStatus, IRequestingUser, AccessType} from "@/interfaces/database.interface.js"
import AppDataSource from "@/config/database.js";
import { GenericCrudService } from "@/services/admin/admin.item-crud.service.js";
import { ICrudService } from "@/interfaces/admin/admn.item-crud.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import {
	CreateCollectionCapabilityDTO,
	UpdateCollectionCapabilityDTO,
	UpdateStatusDTO,
} from "@/interfaces/admin/admin.collection-capability.interface.js";
import { EntityManager, QueryFailedError } from "typeorm";
import OperationModel from "@/models/operation.model.js";
import AssetModel from "@/models/asset.model.js";
import { Point, Polygon, LineString } from 'geojson';
import UserModel from "@/models/user.model.js";
import {userCanAccessOperation } from "@/acl/access/membership/userCanAccessOperation.acl.js";
import { collectionCapabilityAcl } from '@/acl/access/rules/collection-capability.acl.js';

export default class AdminCollectionCapabilityService
	extends GenericCrudService<CollectionCapabilityModel, CreateCollectionCapabilityDTO, UpdateCollectionCapabilityDTO>
	implements ICrudService<CollectionCapabilityModel, CreateCollectionCapabilityDTO, UpdateCollectionCapabilityDTO> {

	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private assetRepository: ReturnType<EntityManager['getRepository']>;
	private userRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(CollectionCapabilityModel, messageCollector, requestingUser, organizationId);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
		this.assetRepository = AppDataSource.getRepository(AssetModel);
		this.userRepository = AppDataSource.getRepository(UserModel);
	}

	override async delete(id: string): Promise<boolean> {

		if(!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: CollectionCapabilityModel[], count: number }> {

		if(!await collectionCapabilityAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("Cannot list items");
			return {
				items: [],
				count: 0,
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await collectionCapabilityAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}


	private validateGeometries(point?: Point, area?: Polygon, track?: LineString): boolean {
		if (point && (
			!Array.isArray(point.coordinates) ||
			point.coordinates.length !== 2 ||
			typeof point.coordinates[0] !== 'number' ||
			typeof point.coordinates[1] !== 'number'
		)) {
			this.messageCollector.addBadRequest("Invalid point coordinates");
			return false;
		}

		if (area && (
			!Array.isArray(area.coordinates) ||
			!Array.isArray(area.coordinates[0]) ||
			area.coordinates[0].length < 4 ||
			area.coordinates[0][0][0] !== area.coordinates[0][area.coordinates[0].length - 1][0] ||
			area.coordinates[0][0][1] !== area.coordinates[0][area.coordinates[0].length - 1][1]
		)) {
			this.messageCollector.addBadRequest("Invalid area polygon coordinates");
			return false;
		}

		if (track && (
			!Array.isArray(track.coordinates) ||
			track.coordinates.length < 2 ||
			!track.coordinates.every(coord =>
				Array.isArray(coord) &&
				coord.length === 2 &&
				typeof coord[0] === 'number' &&
				typeof coord[1] === 'number'
			)
		)) {
			this.messageCollector.addBadRequest("Invalid track line coordinates");
			return false;
		}

		return true;
	}

	private async validateAsset(data: CreateCollectionCapabilityDTO, assetId: number): Promise<AssetModel | null> {
		const asset = await this.assetRepository.findOne({
			where: { id: assetId }
		}) as AssetModel;
		if (!asset) {
			this.messageCollector.addNotFound("Asset not found");
			return null;
		}
		return asset;
	}

	private async validateOperation(data: CreateCollectionCapabilityDTO, operationId: number|null): Promise<OperationModel | null> {
		const operation = await this.operationRepository.findOne({
			where: { id: operationId }
		}) as OperationModel;
		if (!operation) {
			this.messageCollector.addNotFound("Operation not found");
			return null;
		}
		if (!operation.isActive) {
			this.messageCollector.addDenied("Operation is not active");
			return null;
		}
		if(!data.operationId || parseInt(data.operationId) !== operation.id){
			this.messageCollector.addDenied("Operation ID does not match operation ID in request");
			return null;
		}
		return operation;
	}

	private async validatePointOfContact(data: CreateCollectionCapabilityDTO, userId: number): Promise<boolean> {
		const pointOfContactUserId = data.pointOfContactUserId ? parseInt(data.pointOfContactUserId) : undefined;
		if (!pointOfContactUserId) {
			return true;
		}
		// Check point of contact exists
		const pointOfContactUser = await this.userRepository.findOne({
			where: { id: pointOfContactUserId }
		});
		if (!pointOfContactUser) {
			this.messageCollector.addNotFound("Point of contact user not found");
			return false;
		}
		data.pointOfContactUserId = pointOfContactUser.id;
		return true;
	}

	async createWithUserId(data: CreateCollectionCapabilityDTO, userId: number): Promise<CollectionCapabilityModel | null> {

		if(!await collectionCapabilityAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}

		if (!userId) {
			this.messageCollector.addBadRequest("User ID is required");
			return null;
		}

		try {
			const assetId = parseInt(data.assetId);
			const pointOfContactUserId = data.pointOfContactUserId ? parseInt(data.pointOfContactUserId) : undefined;

			// Validate relations
			const asset = await this.validateAsset(data, assetId);
			if (!asset) {
				return null;
			}


			const operation = await this.validateOperation(data, asset.operationId);
			if (!operation) {
				return null;
			}

			if(pointOfContactUserId && !await this.validatePointOfContact(data, userId)){
				return null;
			}


			// Prepare the data with user ID
			const createData = {
				...data,
				assetDesignation: asset.designation,
				createdByUserId: userId,
				status: ApprovalStatus.PENDING
			} as CreateCollectionCapabilityDTO & {
				createdByUserId: number,
				status: ApprovalStatus
			};

			return await super.create(createData);
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async updateStatus(id: string | number, data: UpdateStatusDTO, userId: number): Promise<CollectionCapabilityModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;

			const capability = await this.findById(id.toString());
			if (!capability) {
				this.messageCollector.addNotFound("Collection Capability not found");
				return null;
			}

			capability.status = data.status;

			const updatedCapability = await this.repository.save(capability);
			this.messageCollector.addSuccess(`Collection Capability status updated to ${data.status} successfully`);
			return updatedCapability;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async update(id: string | number, data: UpdateCollectionCapabilityDTO): Promise<CollectionCapabilityModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;

			const capability = await this.findById(id.toString());
			if (!capability) {
				this.messageCollector.addNotFound("Collection Capability not found");
				return null;
			}


			// Update fields
			if (data.description !== undefined) capability.description = data.description;
			if (data.assigned !== undefined) capability.assigned = data.assigned;
			if (data.pointOfContactUserId !== undefined) {
				capability.pointOfContactUserId = data.pointOfContactUserId ? parseInt(data.pointOfContactUserId) : null;
			}
			if (data.contactDetails !== undefined) capability.contactDetails = data.contactDetails;
			if (data.availability !== undefined) capability.availability = data.availability;

			const updatedCapability = await this.repository.save(capability);
			this.messageCollector.addSuccess("Collection Capability updated successfully");
			return updatedCapability;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	override async findAll(params: any): Promise<{ items: CollectionCapabilityModel[], count: number }> {
		if(!await collectionCapabilityAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("Cannot list items");
			return {
				items: [],
				count: 0,
			}
		}
		const {
			load = 'asset,createdByUser,pointOfContactUser',
			...restParams
		} = params;
		return super.findAll({ ...restParams, load }, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async findById(
		id: string,
		load: string = 'asset,createdByUser,pointOfContactUser'
	): Promise<CollectionCapabilityModel | null> {
		if(!await this._canUserAccessThis(id.toString(), AccessType.READ)) return null;

		return super.findById(id, load);
	}
}