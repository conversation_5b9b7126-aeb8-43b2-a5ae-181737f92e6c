import ISRModel from "@/models/isr.model.js";
import { CreateISRDTO, UpdateISRDTO } from "@/interfaces/admin/admin.isr.interface.js";
import { GenericCrudService } from "@/services/admin/admin.item-crud.service.js";
import { ICrudService } from "@/interfaces/admin/admn.item-crud.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { EntityManager } from "typeorm";
import AppDataSource from "@/config/database.js";
import MissionModel from "@/models/mission.model.js";
import AssetModel from "@/models/asset.model.js";
import OperationModel from "@/models/operation.model.js";
import {CreateAssetDTO} from "@/interfaces/admin/admin.asset.interface.js";
import AoiModel from "@/models/aoi.model.js";
import {userCanAccessOperation } from "@/acl/access/membership/userCanAccessOperation.acl.js";
import { userCanAccessMission } from "@/acl/access/membership/userCanAccessMission.acl.js";
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { isrAcl } from '@/acl/access/rules/isr.acl.js'

export default class AdminISRService extends GenericCrudService<ISRModel, CreateISRDTO, UpdateISRDTO>
	implements ICrudService<ISRModel, CreateISRDTO, UpdateISRDTO> {

	private missionRepository: ReturnType<EntityManager['getRepository']>;
	private assetRepository: ReturnType<EntityManager['getRepository']>;
	private operationRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(ISRModel, messageCollector, requestingUser, organizationId);
		this.missionRepository = AppDataSource.getRepository(MissionModel);
		this.assetRepository = AppDataSource.getRepository(AssetModel);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
	}

	override async findAll(params: any): Promise<{ items: ISRModel[], count: number }> {
		//not implemented
		this.messageCollector.addBadRequest("Method not implemented.");
		return {items: [], count: 0};
	}

	override async findById(id: string, load?: string): Promise<ISRModel | null> {

		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: ISRModel[], count: number }> {
		//not implemented
		return {items: [], count: 0};
	}

	async _canUserAccessThis(id:string, requestingAccess:AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.missionId) return false;

		if(!await userCanAccessMission(this.requestingUser?.id, item.missionId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await isrAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		return true
	}

	async create(data: CreateISRDTO & { userId: number }): Promise<ISRModel | null> {
		try {
			if(!await isrAcl(null, this.requestingUser, AccessType.CREATE)){
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}
			// Get mission and asset details
			const [mission, asset] = await Promise.all([
				this.missionRepository.findOneBy({ id: data.missionId }),
				this.assetRepository.findOneBy({ id: data.assetId })
			]);

			// Validate mission exists
			if (!mission) {
				this.messageCollector.addNotFound("Mission not found");
				return null;
			}

			// Validate asset exists
			if (!asset) {
				this.messageCollector.addNotFound("Asset not found");
				return null;
			}

			// Validate asset belongs to the same operation as the mission
			if (asset.operationId !== mission.operationId) {
				this.messageCollector.addDenied("Asset does not belong to the same operation as the mission");
				return null;
			}

			if(!await userCanAccessOperation(this.requestingUser?.id, mission.operationId)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}

			//lets grab operation location coordinates
			const operation = await this.operationRepository.findOneBy({ id: asset.operationId });
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}

			// check if ISR already exist with this combination
			const isr = await this.repository.findOne({
				where: {
					assetId: data.assetId,
					missionId: data.missionId,
				}
			});
			if (isr) {
				this.messageCollector.addConflict("ISR already exists. Please modify the existing ISR");
				return null;
			}

			const newISR = new ISRModel();
			newISR.centerCoordinates = operation.locationCoordinates;
			Object.assign(newISR, data);
			newISR.createdByUserId = data.userId;
			const savedISR = await this.repository.save(newISR);
			this.messageCollector.addSuccess("ISR created successfully");

			return this.findById(savedISR.id.toString(), 'asset,mission,createdByUser');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async update(id: string, updateData: UpdateISRDTO): Promise<ISRModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const isr = await this.repository.findOne({
				where: { id: parseInt(id) },
				relations: ['asset', 'mission', 'createdByUser']
			});

			if (!isr) {
				this.messageCollector.addNotFound("ISR not found");
				return null;
			}

			if(!isr.isEditable){
				this.messageCollector.addBadRequest("ISR is not editable");
				return null;
			}

			// Merge the updates
			Object.assign(isr, updateData);

			// Save the updated entity
			const updatedIsr = await this.repository.save(isr);
			this.messageCollector.addSuccess("ISR updated successfully");

			return updatedIsr;

		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}
}