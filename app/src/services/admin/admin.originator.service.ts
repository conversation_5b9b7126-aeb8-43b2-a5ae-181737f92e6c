import  OriginatorModel  from "@/models/originator.model.js";
import {
	CreateOriginatorDTO,
	UpdateOriginatorDTO,
} from "@/interfaces/admin/admin.originator.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { originatorAcl } from '@/acl/access/rules/originator.acl.js';
import {userCanAccessOrganization} from "@/acl/access/membership/userCanAccessOrganization.acl.js";


export default class AdminOriginatorService extends GenericCrudService<OriginatorModel, CreateOriginatorDTO, UpdateOriginatorDTO>
	implements ICrudService<OriginatorModel, CreateOriginatorDTO, UpdateOriginatorDTO>
{
	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(OriginatorModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: OriginatorModel[], count: number }> {
		if(!await originatorAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}

		return super.findAll(params, [
			{
				parent: 'organization',
				oneDeep: '',
				shallow: true,
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: CreateOriginatorDTO): Promise<OriginatorModel | null> {
		if(!await originatorAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		const orgId = this.organizationId ? this.organizationId.toString() : null;
		if(!orgId){
			this.messageCollector.addDenied("You're not a member of any organizations.");
			return null;
		}

		return super.create({
			title: data.title,
			organizationId: orgId,
			contactDetails: data.contactDetails,
			description: data.description,
		});
	}

	override async findById(id: string, load?: string): Promise<OriginatorModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<OriginatorModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: OriginatorModel[], count: number }> {

		if(!await originatorAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}

		return super.search(searchTerm, fields, [{
			parent: 'organization',
			oneDeep: '',
			shallow: true,
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.organizationId) return false;
		if(!await originatorAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOrganization(this.requestingUser?.id, item.organizationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}

	//go
}