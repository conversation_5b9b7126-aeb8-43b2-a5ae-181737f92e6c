import  RFIModel  from "@/models/rfi.model.js";
import {
	CreateRFIDTO,
	UpdateRFIDTO,
} from "@/interfaces/admin/admin.rfi.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import {userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js'
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { rfiAcl } from '@/acl/access/rules/rfi.acl.js';

export default class AdminRFIService extends GenericCrudService<RFIModel, CreateRFIDTO, UpdateRFIDTO>
	implements ICrudService<RFIModel, CreateRFIDTO, UpdateRFIDTO>
{
	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(RFIModel, messageCollector, requestingUser, organizationId);
	}

	override async findAll(params: any): Promise<{ items: RFIModel[], count: number }> {

		if(!await rfiAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: CreateRFIDTO): Promise<RFIModel | null> {
		if(!await rfiAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<RFIModel | null> {
		if(!await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<RFIModel | null> {
		if(!await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {
		if(!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: RFIModel[], count: number }> {
		if(!await rfiAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;

		if(!await rfiAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}

}