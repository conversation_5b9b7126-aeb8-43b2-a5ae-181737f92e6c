
import TaskModel from "@/models/task.model.js";
import {
  CreateTaskDTO,
  UpdateTaskDTO,
} from "@/interfaces/admin/admin.task.interface.js";
import { GenericCrudService } from "@/services/admin/admin.item-crud.service.js";
import { ICrudService } from "@/interfaces/admin/admn.item-crud.interface.js";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { Entity<PERSON>anager, In } from "typeorm";
import AppDataSource from "@/config/database.js";
import UserModel from "@/models/user.model.js";
import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js';
import { AccessType, IRequestingUser, TaskStatus } from '@/interfaces/database.interface.js';

export default class AdminTaskService extends GenericCrudService<TaskModel, CreateTaskDTO, UpdateTaskDTO>
  implements ICrudService<TaskModel, CreateTaskDTO, UpdateTaskDTO>
{
  private userRepository: ReturnType<EntityManager['getRepository']>;

  constructor(messageCollector: MessageCollector, requestingUser: IRequestingUser | undefined, organizationId: number | undefined) {
    super(TaskModel, messageCollector, requestingUser, organizationId);
    this.userRepository = AppDataSource.getRepository(UserModel);
  }

  override async findAll(params: any): Promise<{ items: TaskModel[], count: number }> {
    return super.findAll(params, [
      {
        parent: 'operation',
        oneDeep: 'organizations',
        ids: this.organizationId ? [this.organizationId] : []
      }
    ]);
  }

  override async findById(id: string, load?: string): Promise<TaskModel | null> {
    if (!await this._canUserAccessThis(id, AccessType.READ)) return null;
    return super.findById(id, load);
  }

  override async update(id: string, data: UpdateTaskDTO): Promise<TaskModel | null> {
    if (!await this._canUserAccessThis(id, AccessType.UPDATE)) return null;

    // If task is being completed, set completedAt
    if (data.status === TaskStatus.COMPLETED && !data.completedAt) {
      data.completedAt = new Date().toISOString();
    }

    return super.update(id, data);
  }

  override async delete(id: string): Promise<boolean> {
    if (!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
    return super.delete(id);
  }

  override async search(searchTerm: string, fields?: string[]): Promise<{ items: TaskModel[], count: number }> {
    return super.search(searchTerm, fields, [
      {
        parent: 'operation',
        oneDeep: 'organizations',
        ids: this.organizationId ? [this.organizationId] : []
      }
    ]);
  }

  async _canUserAccessThis(id: string, requestingAccess: AccessType = AccessType.READ): Promise<boolean> {
    let item = await super.findById(id);
    if (!item || !item.operationId) return false;

    if (!await userCanAccessOperation(this.requestingUser?.id, item.operationId)) {
      this.messageCollector.addDenied("You do not have access to this resource");
      return false;
    }
    return true;
  }

  async create(data: CreateTaskDTO & { userId: number }): Promise<TaskModel | null> {
    try {
      if (!await userCanAccessOperation(this.requestingUser?.id, data.operationId)) {
        this.messageCollector.addDenied("You do not have access to this operation");
        return null;
      }

      const newTask = new TaskModel();
      Object.assign(newTask, data);
      newTask.createdByUserId = data.userId;
      newTask.status = TaskStatus.CREATED;

      const savedTask = await this.repository.save(newTask);
      this.messageCollector.addSuccess("Task created successfully.");
      return this.findById(savedTask.id.toString());
    } catch (error) {
      this.messageCollector.addThrowableError(error);
    }
    return null;
  }

  async assignUsersToTask(taskId: number, userIds: number[]): Promise<TaskModel | null> {
    try {
      if (!await this._canUserAccessThis(taskId.toString(), AccessType.UPDATE)) return null;

      const task = await this.findById(taskId.toString(), 'members,operation');
      if (!task) {
        this.messageCollector.addNotFound("Task not found");
        return null;
      }

      const users = await this.userRepository.find({
        where: {
          id: In(userIds)
        }
      }) as UserModel[];

      if (!users.length) {
        this.messageCollector.addNotFound("No users found");
        return null;
      }

      // Check if users have access to the operation
      for (const user of users) {
        if (!await userCanAccessOperation(user.id, task.operationId)) {
          this.messageCollector.addWarning(`User ${user.email} does not have access to this operation`);
        } else {
          // Only add users that aren't already members
          if (!task.members.some(member => member.id === user.id)) {
            task.members.push(user);
            this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} added to task`);
          }
        }
      }

      await this.repository.save(task);
      this.messageCollector.addSuccess("Users assigned successfully");
      return this.findById(taskId.toString(), 'members');
    } catch (error) {
      this.messageCollector.addThrowableError(error);
    }
    return null;
  }

  async removeUserFromTask(taskId: number, userId: number): Promise<TaskModel | null> {
    try {
      if (!await this._canUserAccessThis(taskId.toString(), AccessType.UPDATE)) return null;

      const task = await this.findById(taskId.toString(), 'members');
      if (!task) {
        this.messageCollector.addNotFound("Task not found");
        return null;
      }

      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        this.messageCollector.addNotFound("User not found");
        return null;
      }

      // Check if user is a member of the task
      if (!task.members.some(member => member.id === userId)) {
        this.messageCollector.addNotFound("User is not assigned to this task");
        return null;
      }

      task.members = task.members.filter(member => member.id !== userId);
      await this.repository.save(task);
      this.messageCollector.addSuccess(`User ${user.firstName} ${user.lastName} removed from task`);
      return this.findById(taskId.toString(), 'members');
    } catch (error) {
      this.messageCollector.addThrowableError(error);
    }
    return null;
  }
}