import GlobalISRModel from '@/models/global-isr.model.js';
import { CreateGlobalISRDTO, UpdateGlobalISRDTO } from '@/interfaces/admin/admin.global-isr.interface.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import { EntityManager } from 'typeorm';
import AppDataSource from '@/config/database.js';
import OperationModel from '@/models/operation.model.js';
import AssetModel from '@/models/asset.model.js';
import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js';
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { globalIsrAcl } from '@/acl/access/rules/global-isr.acl.js';

export default class AdminGlobalISRService extends GenericCrudService<GlobalISRModel, CreateGlobalISRDTO, UpdateGlobalISRDTO>
	implements ICrudService<GlobalISRModel, CreateGlobalISRDTO, UpdateGlobalISRDTO> {

	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private assetRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(GlobalISRModel, messageCollector, requestingUser, organizationId);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
		this.assetRepository = AppDataSource.getRepository(AssetModel);
	}

	override async findAll(params: any): Promise<{ items: GlobalISRModel[], count: number }> {
		if(!await globalIsrAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async findById(id: string, load?: string): Promise<GlobalISRModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return super.findById(id,load);
	}

	override async delete(id: string): Promise<boolean> {
		if(! await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: GlobalISRModel[], count: number }> {
		if(!await globalIsrAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id:string, requestingAccess: AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item || !item.operationId) return false;
		if(!await globalIsrAcl(id, this.requestingUser, requestingAccess)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}

		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}


	async create(data: CreateGlobalISRDTO & { userId: number }): Promise<GlobalISRModel | null> {
		try {
			if(!await globalIsrAcl(null, this.requestingUser, AccessType.CREATE)) {
				this.messageCollector.addDenied("You do not have access to this resource");
				return null;
			}
			// Get operation and asset details
			const asset = await this.assetRepository.findOneBy({ id: data.assetId });

			// Validate asset exists
			if (!asset) {
				this.messageCollector.addNotFound("Asset not found");
				return null;
			}

			if(!await userCanAccessOperation(this.requestingUser?.id, asset.operationId)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}

			const operation = await this.operationRepository.findOneBy({ id: asset.operationId });
			if (!operation) {
				this.messageCollector.addNotFound("Operation not found");
				return null;
			}

			if(!await userCanAccessOperation(this.requestingUser?.id, operation.id)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}


			// check if GlobalISR already exist with this combination
			const isr = await this.repository.findOne({
				where: {
					assetId: data.assetId,
				}
			});
			if (isr) {
				this.messageCollector.addConflict("GlobalISR already exists. Please modify the existing GlobalISR");
				return null;
			}

			const newGlobalISR = new GlobalISRModel();
			newGlobalISR.operationId = operation.id;
			newGlobalISR.centerCoordinates = operation.locationCoordinates;
			Object.assign(newGlobalISR, data);
			newGlobalISR.createdByUserId = data.userId;
			const savedGlobalISR = await this.repository.save(newGlobalISR);
			this.messageCollector.addSuccess("GlobalISR created successfully");

			return this.findById(savedGlobalISR.id.toString(), 'asset,operation,createdByUser');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async update(id: string, updateData: UpdateGlobalISRDTO): Promise<GlobalISRModel | null> {
		try {
			if(! await this._canUserAccessThis(id, AccessType.UPDATE)) return null;
			const isr = await this.repository.findOne({
				where: { id: parseInt(id) },
				relations: ['asset', 'operation', 'createdByUser']
			});

			if (!isr) {
				this.messageCollector.addNotFound("GlobalISR not found");
				return null;
			}

			// Merge the updates
			Object.assign(isr, updateData);

			// Save the updated entity
			const updatedIsr = await this.repository.save(isr);
			this.messageCollector.addSuccess("GlobalISR updated successfully");

			return updatedIsr;

		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}
}