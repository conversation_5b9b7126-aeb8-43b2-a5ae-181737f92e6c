import UserModel from "@/models/user.model.js";
import RoleModel from "@/models/role.model.js";
import AppDataSource from "@/config/database.js";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, In} from "typeorm";

import {
	CreateUserDTO,
	UpdateUserDTO,
	AssignUserToOperationDTO
} from "@/interfaces/admin/admin.user.interface.js";
import {GenericCrudService} from "@/services/admin/admin.item-crud.service.js";
import {ICrudService} from "@/interfaces/admin/admn.item-crud.interface.js";
import {MessageCollector} from "@/utils/message-collector.utils.js";
import OperationsUsersModel from "@/models/operations-users.model.js";
import { AccessType, IRequestingUser, UserType } from '@/interfaces/database.interface.js';
import OperationModel from "@/models/operation.model.js";
import { userAcl } from '@/acl/access/rules/user.acl.js';
import sendSlackVerificationEmail from '@/events/user/notification/send-slack-verification-email.event.js';
import sendSlackOrganizationUserCreated from '@/events/user/notification/send-slack-organization-user-created.event.js';
import OrganizationModel from "@/models/organization.model.js";
import { sendSlackNotification } from '@/utils/system-notices.utils.js';
import sendCreatePassword from "@/events/user/notification/send-create-password-email.event.js";
import {userCanAccessOrganization} from "@/acl/access/membership/userCanAccessOrganization.acl.js";


export default class AdminUserService extends GenericCrudService<UserModel, CreateUserDTO, UpdateUserDTO>
implements ICrudService<UserModel, CreateUserDTO, UpdateUserDTO>
{
	private roleRepository: ReturnType<EntityManager['getRepository']>;
	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private operationsUsersRepository:ReturnType<EntityManager['getRepository']>;
	private userRepository: ReturnType<EntityManager['getRepository']>;
	private organizationRepository: ReturnType<EntityManager['getRepository']>;

	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(UserModel, messageCollector, requestingUser, organizationId);
		this.roleRepository = AppDataSource.getRepository(RoleModel)
		this.operationRepository = AppDataSource.getRepository(OperationModel)
		this.operationsUsersRepository  = AppDataSource.getRepository(OperationsUsersModel);
		this.userRepository = AppDataSource.getRepository(UserModel);
		this.organizationRepository = AppDataSource.getRepository(OrganizationModel);
	}

	override async findAll(params: any): Promise<{ items: UserModel[], count: number }> {


		return super.findAll(params, [
			{
				parent: 'roles',
				oneDeep: 'organization',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: CreateUserDTO): Promise<UserModel | null> {

		//can user access this route
		// if(!await userAcl(null, this.requestingUser, AccessType.CREATE)){
		// 	this.messageCollector.addDenied("You do not have access create users");
		// 	return null;
		// }
        const constRequestingUser = this.requestingUser;
        if(!constRequestingUser){
            this.messageCollector.addDenied("You do not have access to this resource");
            return null;
        }

        if(!await userCanAccessOrganization(constRequestingUser?.id, this.organizationId, ['org_admin', 'manager'])) {
			this.messageCollector.addDenied("User cannot modify organization");
            return null;
		}

        const orgAdmin = await this.userRepository.findOne({
            where: {
                id: this.requestingUser?.id,
        }}) as UserModel;

		const role = await this.roleRepository.findOne({
			where: {
				id: data.roleId,
				organizationId: this.organizationId
			}
		}) as RoleModel;

        if(!role){
            this.messageCollector.addError(`Role with id ${data.roleId} does not exist`);
            return null;
        }

		const organization = await this.organizationRepository.findOne({
			where: {
				id: this.organizationId
			}
		}) as OrganizationModel;


		let userType:UserType = 'user';
		// if(role.isSystemRole){
		// // 	staffDesignation = "ORG_ADMIN";
		// 	// staffDesignation = "USER";
		// 	// staffDesignation = "MANAGER";
		// 	if(role.staffDesignation === 'USER'){
		// 		userType = 'user';
		// 	} else if(role.staffDesignation === 'ORG_ADMIN'){
		// 		userType = 'user';
		// 	} else if(role.staffDesignation === 'MANAGER'){
		// 		userType = 'user';
		// 	}
		// }

		const userData =  new UserModel();
		userData.firstName = data.firstName;
		userData.lastName = data.lastName;
		userData.email = data.email;
        //create random passowrd
		userData.password = new Date().getMilliseconds() + '2!!!2g113'+Math.random()+"3a!!!34";
		userData.roles = [role];
		userData.accountType = userType;
		userData.isActive = true;
		userData.isVerified = true;
		let createdU = await this.repository.save(userData) as UserModel;
		const adminEmail = this.requestingUser?.email ?? "n/a";
		createdU.activate();
        const resetToken = await createdU.setResetPasswordHash();
        await this.userRepository.save(createdU);
        if(!createdU.resetPasswordHash){
            this.messageCollector.addError("Could not set password create token");
            return null;
        }
        //send user email to reset password:
        await sendCreatePassword(createdU.email.toLowerCase(), createdU.resetPasswordHash, orgAdmin, organization.name);
		await sendSlackOrganizationUserCreated(createdU, organization.name, adminEmail);

		return this.repository.save(createdU);
	}

	override async findById(id: string, load?: string): Promise<UserModel | null> {

		return super.findById(id,load);
	}

	override async update(id: string, data: any): Promise<UserModel | null> {

		return super.update(id, data);
	}

	override async delete(id: string): Promise<boolean> {

		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: UserModel[], count: number }> {

		return super.search(searchTerm, fields);
	}

	async _canUserAccessThis(){

	}


	async assignRoles(userId: string, roleIds: string[]): Promise<UserModel | null> {
		try {
			let user = await this.findById(userId, 'roles');
			const roles = await this.roleRepository.find({
				where: {
					id: In(roleIds)
				}
			}) as RoleModel[];
			if(!user) {
				this.messageCollector.addNotFound("No such role found");
				return null;
			}
			user.roles = roles;
			await this.repository.save(user);
			//add success message
			this.messageCollector.addSuccess("Roles added successfully");
			return await this.findById(userId, 'roles');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async addRoles(userId: string, roleIds: string[]): Promise<UserModel | null> {
		try {
			let user = await this.findById(userId, 'roles');
			const newRoles = await this.roleRepository.find({
				where: {
					id: In(roleIds)
				}
			}) as RoleModel[];
			if(!user) {
				this.messageCollector.addNotFound("No such role found");
				return null;
			}
			user.roles = [...user.roles, ...newRoles];
			await this.repository.save(user);
			//add success message
			this.messageCollector.addSuccess("Roles added successfully");
			return await this.findById(userId, 'roles');
		} catch (error) {
			this.messageCollector.addThrowableError(error);
		}
		return null;
	}

	async removeRoleFromUser(userId: string, roleId: string): Promise<UserModel | null> {
		let user = await this.findById(userId, 'roles');

		if (!user) {
			this.messageCollector.addNotFound("User not found");
			return null;
		}

		const roleToRemove = user.roles.find((role: {id: number}) => role.id === parseInt(roleId));

		if (!roleToRemove) {
			this.messageCollector.addWarning("No roles to remove");
			return user;
		}

		user.roles = user.roles.filter((role: {id: number}) => role.id !== parseInt(roleId));
		await this.repository.save(user);
		//add success message
		this.messageCollector.addSuccess("Role removed successfully");
		return await this.findById(userId, 'roles');
	}

	async assignUserToOperation(id:string, operationId:number, accessType:string|undefined): Promise<UserModel | null> {
		const {user, operation, existingUserOperation} = await this._getUserAndOperations(id, operationId);
		if(!user || !operation){
			return null;
		}
		if (existingUserOperation) {
			this.messageCollector.addWarning("User operation already assigned to operations, use Update User" +
				" Operations Access to change accessType");
			return null;
		}

		const operationUser = new OperationsUsersModel();
		operationUser.userId = user.id;
		operationUser.operationId = operation.id;
		operationUser.accessType = accessType || AccessType.READ;
		await this.operationsUsersRepository.save(operationUser);
		//add success message
		this.messageCollector.addSuccess("User assigned to operation successfully");
		let u =  await this.findById(id, 'operations');
		return u;
	}

	async removeUserFromOperation(id:string, operationId:number): Promise<UserModel | null> {
		const {user, operation, existingUserOperation} = await this._getUserAndOperations(id, operationId);
		if(!user || !operation){
			return null;
		}
		if (!existingUserOperation) {
			this.messageCollector.addNotFound("User is not assigned to operation");
			return null;
		}

		await this.operationsUsersRepository.delete(existingUserOperation.id);
		//add success message
		this.messageCollector.addSuccess("User removed from operation successfully");
		return await this.findById(id, 'operations');
	}

	async updateUserOperationAccess(id:string, operationId:number, accessType:string|undefined): Promise<UserModel | null> {
		const {user, operation, existingUserOperation} = await this._getUserAndOperations(id, operationId);
		if(!user || !operation){
			return null;
		}
		if (!existingUserOperation) {
			this.messageCollector.addNotFound("User is not assigned to operation");
			return null;
		}

		existingUserOperation.accessType = accessType || AccessType.READ;
		await this.operationsUsersRepository.save(existingUserOperation);
		//add success message
		this.messageCollector.addSuccess("User access type updated to "+existingUserOperation.accessType+" successfully");
		return await this.findById(id, 'operations');
	}

	private async _getUserAndOperations(userId:string, operationId:number): Promise<{user: UserModel, operation: OperationModel, existingUserOperation: OperationsUsersModel | null}> {
		let user = await this.findById(userId) as UserModel;
		const operation = await this.operationRepository.findOne({ where: { id: operationId } }) as OperationModel;
		if (!user) {
			this.messageCollector.addNotFound("User not found");
			throw new Error("User not found");
		}
		if (!operation) {
			this.messageCollector.addError("Operation not found");
			throw new Error("Operation not found");
		}
		//check if user is already assigned to operation
		const existingUserOperation = (user && operation) ? await this.operationsUsersRepository.findOne({
			where: {
				userId: user.id,
				operationId: operation.id
			}
		}) as OperationsUsersModel : null;
		return {user, operation, existingUserOperation};
	}
}