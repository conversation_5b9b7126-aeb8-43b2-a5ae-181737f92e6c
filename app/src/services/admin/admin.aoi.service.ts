import AoiModel from '@/models/aoi.model.js';
import AppDataSource from '@/config/database.js';
import { GenericCrudService } from '@/services/admin/admin.item-crud.service.js';
import { ICrudService } from '@/interfaces/admin/admn.item-crud.interface.js';
import { MessageCollector } from '@/utils/message-collector.utils.js';
import { operationAcl } from '@/acl/access/rules/operation.acl.js';
import { userCanAccessOperation } from '@/acl/access/membership/userCanAccessOperation.acl.js';
import { ApproveAoiDTO, CreateAoiDTO, UpdateAoiDTO } from '@/interfaces/admin/admin.aoi.interface.js';
import { EntityManager, QueryFailedError } from 'typeorm';
import OperationModel from '@/models/operation.model.js';
import { Point, Polygon } from 'geojson';
import MapElementModel from '@/models/map-element.model.js';
import { AccessType, BorderType, IRequestingUser, RGBAColor } from '@/interfaces/database.interface.js';
import { aoiAcl } from '@/acl/access/rules/aoi.acl.js';


export default class AdminAoiService
	extends GenericCrudService<AoiModel, CreateAoiDTO, UpdateAoiDTO>
	implements ICrudService<AoiModel, CreateAoiDTO, UpdateAoiDTO> {

	private operationRepository: ReturnType<EntityManager['getRepository']>;
	private mapElementRepository: ReturnType<EntityManager['getRepository']>;
	
	constructor(messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) {
		super(AoiModel, messageCollector, requestingUser, organizationId);
		this.operationRepository = AppDataSource.getRepository(OperationModel);
		this.mapElementRepository = AppDataSource.getRepository(MapElementModel);
	}


	override async findAll(params: any): Promise<{ items: AoiModel[], count: number }> {
		if(!await aoiAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			}
		}

		return super.findAll(params, [
			{
				parent: 'operation',
				oneDeep: 'organizations',
				ids: this.organizationId ? [this.organizationId] : []
			}
		]);
	}

	override async create(data: CreateAoiDTO): Promise<AoiModel | null> {

		if(!await aoiAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, data.operationId)){
			this.messageCollector.addDenied("You do not have access to this operation");
			return null;
		}
		return super.create(data);
	}

	override async findById(id: string, load?: string): Promise<AoiModel | null> {
		if(! await this._canUserAccessThis(id, AccessType.READ)) return null;
		return await super.findById(id,load);
	}

	override async delete(id: string): Promise<boolean> {
		if(!await this._canUserAccessThis(id, AccessType.DELETE)) return false;
		return super.delete(id);
	}

	override async search(searchTerm: string, fields?: string[]): Promise<{ items: AoiModel[], count: number }> {
		if(!await aoiAcl(null, this.requestingUser, AccessType.LIST)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return {
				items: [],
				count: 0
			};
		}
		return super.search(searchTerm, fields, [{
			parent: 'operation',
			oneDeep: 'organizations',
			ids: this.organizationId ? [this.organizationId] : []
		}]);
	}

	async _canUserAccessThis(id: string, accessRequested:AccessType = AccessType.READ){
		let item = await super.findById(id);
		if(!item) return false;
		if(!await aoiAcl(id, this.requestingUser, accessRequested)){
			this.messageCollector.addDenied("You do rights to this resource");
			return false;
		}
		if(!await userCanAccessOperation(this.requestingUser?.id, item.operationId)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return false;
		}
		return true
	}



	private async checkDuplicateDesignation(designation: string, operationId: number, excludeId?: number): Promise<AoiModel | null> {
		const queryBuilder = this.repository
			.createQueryBuilder('areas_of_interest')
			.where('areas_of_interest.designation = :designation', { designation })
			.andWhere('areas_of_interest.operation_id = :operationId', { operationId });

		if (excludeId) {
			queryBuilder.andWhere('areas_of_interest.id != :id', { id: excludeId });
		}

		return queryBuilder.getOne();
	}

	private validateGeometry(point: Point | undefined, polygon: Polygon | undefined): boolean {
		if (point && (
			!Array.isArray(point.coordinates) ||
			point.coordinates.length !== 2 ||
			typeof point.coordinates[0] !== 'number' ||
			typeof point.coordinates[1] !== 'number'
		)) {
			this.messageCollector.addBadRequest("Invalid location point coordinates");
			return false;
		}

		if (polygon && (
			!Array.isArray(polygon.coordinates) ||
			!Array.isArray(polygon.coordinates[0]) ||
			polygon.coordinates[0].length < 4 || // Minimum 4 points for a closed polygon
			polygon.coordinates[0][0][0] !== polygon.coordinates[0][polygon.coordinates[0].length - 1][0] || // First and last points should match
			polygon.coordinates[0][0][1] !== polygon.coordinates[0][polygon.coordinates[0].length - 1][1]
		)) {

			this.messageCollector.addBadRequest("Invalid area polygon coordinates");
			// return false;
		}

		return true;
	}

	private async operationExistsAndActive(operationId: number): Promise<boolean> {
		const operation = await this.operationRepository.findOne({ where: { id: operationId } });
		if (!operation) {
			this.messageCollector.addNotFound("Operation not found");
			return false;
		}
		if (!operation.isActive) {
			this.messageCollector.addDenied("Operation is not active");
			return false;
		}
		return true;
	}

	async createWithUserId(data: CreateAoiDTO, userId: number): Promise<AoiModel | null> {
		if (!userId) {
			this.messageCollector.addBadRequest("User ID is required");
			return null;
		}
		if(!await aoiAcl(null, this.requestingUser, AccessType.CREATE)){
			this.messageCollector.addDenied("You do not have access to this resource");
			return null;
		}
		try {
			const operationId = parseInt(data.operationId);
			if(!await userCanAccessOperation(this.requestingUser?.id, operationId)){
				this.messageCollector.addDenied("You do not have access to this operation");
				return null;
			}
			if (!await this.operationExistsAndActive(operationId)) {
				//add message collector
				this.messageCollector.addDenied("Operation is not active");
				return null;
			}
			if(!data.isApproved) {
				this.messageCollector.addInfo("TAI must be approved. Reverted to NAI");
				data.isTargetable = false;
			} //if not approved, set isTargetable to false
			const NAIorTAI = (data.isTargetable && data.isApproved) ? 'TAI' : 'NAI';
			const nextDesignationNumber = await this._getNextDesignationNumber(operationId, data.isTargetable as boolean);
			const prettyDesignationNumber = this._formatDesignationAsFourDigitNumber(nextDesignationNumber.toString());
			data.name = `${NAIorTAI}-${prettyDesignationNumber}`;

			//lets create mapElement
			const createMapElementObject = {
				element: data.mapElement?.element,
				elementType: data.mapElement.elementType,
				elementColor: data.mapElement.elementColor,
				borderType: data.mapElement.borderType,
				borderThickness: data.mapElement.borderThickness,
				borderColor: data.mapElement.borderColor
			};
			const mapElement =new MapElementModel();
			Object.assign(mapElement, createMapElementObject);
			const savedMapElement = await this.mapElementRepository.save(mapElement);
			if(!savedMapElement.id) {
				throw new Error("Failed to create geographic area. Abort");
			}
			// Use the parent class's create method
			const aoi = new AoiModel();
			aoi.designation = nextDesignationNumber;
			aoi.description = data.description || 'N/A';
			aoi.name = data.name || 'N/A';
			aoi.operationId = operationId;
			aoi.isTargetable = data.isTargetable || false;
			aoi.requestedByUserId = userId;
			aoi.isApproved = data.isApproved || false;
			aoi.mapElementId = savedMapElement.id;
			const savedAoi = await this.repository.save(aoi);
			return savedAoi;
		} catch (error) {
			console.error("ERROR CREATING AOI ", error);
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async approve(id: string | number, data: ApproveAoiDTO, userId: number): Promise<AoiModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
			const aoi = await this.findById(id.toString());
			if (!aoi) {
				this.messageCollector.addNotFound("An Area of Interest not found");
				return null;
			}

			aoi.isApproved = data.isApproved;
			aoi.approvedByUserId = data.isApproved ? userId : null;

			const updatedAoi = await this.repository.save(aoi);
			this.messageCollector.addSuccess(data.isApproved ? "An Area of Interest approved successfully" : "An Area of Interest unapproved successfully");
			return updatedAoi;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	async toTAI(id: string | number): Promise<AoiModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;
			const aoi = await this.findById(id.toString());
			if (!aoi) {
				this.messageCollector.addNotFound("An Area of Interest not found");
				return null;
			}
			if(!aoi.isApproved) {
				this.messageCollector.addBadRequest("NAI must be approved before it can be targetable");
				return null;
			}
			aoi.isTargetable = true;

			//lets change name and designation
			//get the next TAI designation
			const nextDesignationNumber = await this._getNextDesignationNumber(aoi.operationId, true);
			const prettyDesignationNumber = this._formatDesignationAsFourDigitNumber(nextDesignationNumber.toString());
			aoi.name = `TAI-${prettyDesignationNumber}`;
			aoi.designation = nextDesignationNumber;


			const updatedAoi = await this.repository.save(aoi);
			this.messageCollector.addSuccess("An Area of Interest updated successfully");
			return updatedAoi;
		} catch (error) {
			this.messageCollector.addThrowableError(error);
			return null;
		}
	}

	override async update(id: string | number, data: UpdateAoiDTO): Promise<AoiModel | null> {
		try {
			if(!await this._canUserAccessThis(id.toString(), AccessType.UPDATE)) return null;

			const aoi = await this.findById(id.toString());
			if (!aoi) {
				this.messageCollector.addNotFound("An Area of Interest not found");
				return null;
			}
			
			// Update fields
			if (data.name) aoi.name = data.name.trim();
			if (data.description !== undefined) aoi.description = data.description;

			//update map Elements
			if(data.mapElement) {
				const mapElement = await this.mapElementRepository.findOne({
					where: { id: aoi.mapElementId}
				}) as MapElementModel;
				if(mapElement) {
					if(data.mapElement.element) mapElement.element = data.mapElement.element;
					if(data.mapElement.elementType) mapElement.elementType = data.mapElement.elementType;
					if(data.mapElement.elementColor) mapElement.elementColor = data.mapElement.elementColor;
					if(data.mapElement.borderType) mapElement.borderType = data.mapElement.borderType as BorderType;
					if(data.mapElement.borderThickness) mapElement.borderThickness = data.mapElement.borderThickness;
					if(data.mapElement.borderColor) mapElement.borderColor = data.mapElement.borderColor as RGBAColor;
					await this.mapElementRepository.save(mapElement);
				}
			}

			const updatedAoi = await this.repository.save(aoi);
			this.messageCollector.addSuccess("An Area of Interest updated successfully");
			return updatedAoi;
		} catch (error) {
			if (error instanceof QueryFailedError) {
				const errorMessage = error.message || '';
				if (errorMessage.includes('duplicate key') && errorMessage.toLowerCase().includes('designation')) {
					this.messageCollector.addConflict(`An Area of Interest with the designation already exists`);
				} else {
					this.messageCollector.addThrowableError(error);
				}
			} else {
				this.messageCollector.addThrowableError(error);
			}
			return null;
		}
	}

	async _getNextDesignationNumber(operationId: number, isTargetable: boolean): Promise<number> {
		let allAOIs = await this.findAll({
			isTargetable: isTargetable,
			orderBy: 'designation',
			sortBy: 'DESC',
			perPage: 1,
			operationId: operationId
		}) as { items: AoiModel[]; count: number; };

		if(allAOIs.count === 0){
			return 1;
		}
		const lastDesignation = allAOIs.items[0].designation;
		const lastDesignationNumber = lastDesignation
		return lastDesignationNumber + 1;
	}

	_formatDesignationAsFourDigitNumber(designation: string): string {
		const designationNumber = designation.trim();
		return designationNumber.toString().padStart(4, '0');
	}
}