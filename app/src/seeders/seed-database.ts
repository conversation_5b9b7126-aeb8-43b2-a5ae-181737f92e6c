import { DataSource } from "typeorm";
import { UserSeeder } from "@/seeders/users.seeder.js";
import { OrganizationSeeder } from "@/seeders/organizations.seeder.js";
import { RolesSeeder } from "@/seeders/roles.seeder.js";

// Import your DataSource configuration
import AppDataSource from "@/config/database.js";
import {PlatformSeeder} from "@/seeders/platform-seeder.js";
import {OperationSeeder} from "@/seeders/operations.seeder.js";
import {OriginatorSeeder} from "@/seeders/originators.seeder.js";
import {TruncateSeeder} from "@/seeders/truncate.seeder.js";

export default async function seedDatabase() {
	try {
	//	console.log("Starting database seeding...");

		// Start a transaction
		await AppDataSource.transaction(async (transactionalEntityManager) => {
			// Create a new QueryRunner
			const queryRunner = transactionalEntityManager.queryRunner;

			if (!queryRunner) {
				throw new Error("QueryRunner is undefined");
			}

			try {
				//reset database
				//clean up before seeding
				// await TruncateSeeder.run(queryRunner);
				// Run seeders
				// await UserSeeder.run(queryRunner);
				// await OrganizationSeeder.run(queryRunner);
				// await RolesSeeder.run(queryRunner);
				await PlatformSeeder.run(queryRunner);
				// await OperationSeeder.run(queryRunner);
				// await OriginatorSeeder.run(queryRunner);
				// Add other seeders here
			} catch (error) {
	//			console.error("Error during seeding:", error);
				throw error; // Re-throw the error to trigger transaction rollback
			}
		});

	//	console.log("Database seeding completed successfully");
	} catch (error) {
	//	console.error("Error during seeding:", error);
		throw error; // Re-throw the error so the calling function knows seeding failed
	}
}