import { QueryRunner } from "typeorm";
import {platformData} from '@/seeders/data/platforms-data.js';

interface Platform {
    id: string;
    name: string;
    cost: number | null;
    category: 'system' | 'platform' | 'weapon';
    combat_radius: number | null;
    country_iso_code: string;
    type: 'space' | 'air' | 'land' | 'sea' | 'hybrid' | 'other';
}

export class PlatformSeeder {
    private static platforms: any = platformData.data;

    public static async run(queryRunner: QueryRunner): Promise<void> {
        for (const platform of this.platforms) {
            // Check if the platform already exists
            //create designation, first 4 letters of name + random number + random letter
            //@ts-ignore
            const designation = platform.name.split(' ').map(word => isNaN(word) ? word.charAt(0) : word).join('-') + '-' + platform.country_iso_code + '-' + Math.floor(Math.random() * 900 + 100);

            const squareFootPrint = (platform.combat_radius && platform.combat_radius > 0) ? Math.PI * Math.pow(platform.combat_radius, 2) : 0;
            const existingAsset = await queryRunner.query(`
                SELECT id FROM platforms
                WHERE name = $1 AND country_iso_code = $2
                LIMIT 1
            `, [platform.name, platform.country_iso_code]);

            if (existingAsset.length === 0) {
                // Asset doesn't exist, insert it
                await queryRunner.query(`
                    INSERT INTO platforms (
                        name,
                        aliases,
                        country_iso_code,
                        designation,
                        cost,
                        cost_currency,
                        type,
                        combat_radius,
                        footprint_area,
                        configuration
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                `, [
                    platform.name,
                    JSON.stringify([platform.id]), // Using id as an alias
                    platform.country_iso_code,
                    designation,
                    platform.cost,
                    'AUD',
                    platform.type,
                    platform.combat_radius,
                    squareFootPrint,
                    JSON.stringify({}) // platform_details as empty object for now
                ]);

                console.log(`Asset '${platform.name}' inserted successfully.`);
            } else {
                console.log(`Asset '${platform.name}' already exists. Skipping.`);
            }
        }

        console.log('Assets seeding completed.');
    }

}
