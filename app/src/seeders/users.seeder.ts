import { QueryRunner } from "typeorm";
import * as argon2 from "argon2";

export class UserSeeder {
	public static async run(queryRunner: QueryRunner): Promise<void> {
		// Admin user seeding
		const hashedPassword = await argon2.hash('adm!nPassword123'); // Replace with a secure password

		await queryRunner.query(`
            INSERT INTO users (
                email,
                password,
                account_type,
                first_name,
                last_name,
                is_active,
                is_verified,
                created_at,
                updated_at
            ) VALUES (
                '<EMAIL>',
                $1,
                'rules',
                'Admin',
                'User',
                true,
                true,
                NOW(),
                NOW()
            )
            ON CONFLICT (email) DO NOTHING;
        `, [hashedPassword]);

		// Generate 100 incremental users
		// const userPassword = await argon2.hash('userPassword123');
		//
		// for (let i = 1; i <= 100; i++) {
		// 	await queryRunner.query(`
        //         INSERT INTO users (
        //             email,
        //             password,
        //             account_type,
        //             first_name,
        //             last_name,
        //             is_active,
        //             is_verified,
        //             created_at,
        //             updated_at
        //         ) VALUES (
        //             $1,
        //             $2,
        //             'user',
        //             $3,
        //             $4,
        //             true,
        //             true,
        //             NOW(),
        //             NOW()
        //         )
        //         ON CONFLICT (email) DO NOTHING;
        //     `, [
		// 		`user${i}@email.com`,
		// 		userPassword,
		// 		`First${i}`,
		// 		`Last${i}`
		// 	]);
		// }
	}
}