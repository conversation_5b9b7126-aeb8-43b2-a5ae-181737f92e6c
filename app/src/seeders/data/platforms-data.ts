export const platformData = {data :[
  {
    "id": "F-35A Lightning II",
    "name": "F-35A Lightning II",
    "cost": 170000000,
    "category": "platform",
    "combat_radius": 1093,
    "country_iso_code": "AU",
    "type": "air"
  },
  // {
  //   "id": "AIM-120 AMRAAM",
  //   "name": "AIM-120 AMRAAM",
  //   "cost": 590429.46,
  //   "category": "weapon",
  //   "combat_radius": null,
  //   "country_iso_code": "AU",
  //   "type": "other"
  // },
  // {
  //   "id": "AIM-9 Sidewinder",
  //   "name": "AIM-9 Sidewinder",
  //   "cost": 581874.44,
  //   "category": "weapon",
  //   "combat_radius": null,
  //   "country_iso_code": "AU",
  //   "type": "other"
  // },
  // {
  //   "id": "GBU-31 JDAM Guided Bomb",
  //   "name": "GBU-31 Joint Direct Attack Munitions (JDAM) Guided Bomb",
  //   "cost": 24431.20,
  //   "category": "weapon",
  //   "combat_radius": null,
  //   "country_iso_code": "AU",
  //   "type": "other"
  // },
  // {
  //   "id": "GAU-22/A",
  //   "name": "Internal 25 mm GAU-22/A Cannon",
  //   "cost": null,
  //   "category": "weapon",
  //   "combat_radius": null,
  //   "country_iso_code": "AU",
  //   "type": "other"
  // },
  {
    "id": "ARH Tiger",
    "name": "Eurocopter Tiger ARH",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "MRH-90",
    "name": "NHIndustries MRH-90 Taipan",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "UH-60M",
    "name": "Sikorsky UH-60M Black Hawk",
    "cost": null,
    "category": "platform",
    "combat_radius": 1080,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "AW139",
    "name": "Augusta Westland AW139",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "CH-47F",
    "name": "Boeing CH-47F Chinook",
    "cost": null,
    "category": "platform",
    "combat_radius": 3600,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "EC135 T2+",
    "name": "Eurocopter EC135 T2+",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "RQ-7B",
    "name": "RQ-7B Shadow 200",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "RQ-12A",
    "name": "RQ-12A Wasp AE",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "PD-100",
    "name": "PD-100 Black Hornet",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Phantom 4",
    "name": "DJI Phantom 4",
    "cost": null,
    "category": "platform",
    "combat_radius": 5,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Lockheed Martin F-35A Lightning II",
    "name": "F-35A Lightning II",
    "cost": null,
    "category": "platform",
    "combat_radius": 1277.88,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing F/A-18F Super Hornet",
    "name": "F/A-18F Super Hornet",
    "cost": null,
    "category": "platform",
    "combat_radius": 1296.4,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing P-8A Poseidon",
    "name": "P-8A Poseidon",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing E-7A Wedgetail",
    "name": "E-7A Wedgetail",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing EA-18G Growler",
    "name": "EA-18G Growler",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Lockheed AP-3C Orion",
    "name": "AP-3C Orion",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "MC-55A Peregrine",
    "name": "MC-55A Peregrine",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Alenia C-27J Spartan",
    "name": "C-27J Spartan",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Lockheed C-130J Hercules",
    "name": "C-130J Hercules",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing C-17 Globemaster",
    "name": "C-17 Globemaster III",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Airbus KC-30A Multi-Role Tanker",
    "name": "KC-30A Multi-Role Tanker Transport",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Boeing BBJ (Boeing Business Jet)",
    "name": "BBJ (Boeing Business Jet)",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Dassault Falcon 7X",
    "name": "Falcon 7X",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Beechcraft King Air 350",
    "name": "King Air 350",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "AgustaWestland AW139",
    "name": "AW139",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Diamond DA40NG",
    "name": "DA40NG",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Pilatus PC-21",
    "name": "PC-21",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "BAE Hawk 127",
    "name": "Hawk 127",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "MQ-4C Triton",
    "name": "MQ-4C Triton",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Loyal Wingman",
    "name": "Loyal Wingman (Boeing Airpower Teaming System)",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "MH-60 Seahawk",
    "name": "MH-60 Seahawk",
    "cost": null,
    "category": "platform",
    "combat_radius": 222.24,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "NHI MRH 90",
    "name": "MRH 90",
    "cost": null,
    "category": "platform",
    "combat_radius": 333.36,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Eurocopter EC135",
    "name": "EC135",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "AU",
    "type": "air"
  },
  {
    "id": "Eurofighter Typhoon",
    "name": "Eurofighter Typhoon",
    "cost": null,
    "category": "platform",
    "combat_radius": 555.6,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Lockheed Martin C-130J",
    "name": "C-130J Hercules",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airbus A400M Atlas",
    "name": "A400M Atlas",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Boeing E-3D Sentry",
    "name": "E-3D Sentry (AWACS)",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Eurocopter H135",
    "name": "H135 (Juno)",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Lockheed Martin F-35B",
    "name": "F-35B Lightning II",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Boeing Chinook HC6",
    "name": "Chinook HC6",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airbus Voyager KC3",
    "name": "Voyager KC3",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "General Atomics MQ-9",
    "name": "MQ-9 Reaper",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Bombardier Sentinel R1",
    "name": "Sentinel R1",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "AgustaWestland AW159 Wildcat",
    "name": "AW159 Wildcat",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Westland Lynx",
    "name": "Lynx HMA8",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Boeing Chinook HC4",
    "name": "Chinook HC4",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Boeing Apache AH1",
    "name": "Apache AH1",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airbus H135 Juno",
    "name": "H135 Juno",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "AW101 Merlin",
    "name": "Merlin HM2",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Boeing AH-64E Apache Guardian",
    "name": "AH-64E Apache Guardian",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airbus H145 Jupiter",
    "name": "H145 Jupiter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airbus A330 MRTT",
    "name": "A330 MRTT",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "MQ-9 Reaper",
    "name": "MQ-9 Reaper",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Watchkeeper",
    "name": "Unmanned Aerial Vehicle",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Desert Hawk III",
    "name": "Unmanned Aerial Vehicle",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Switchblade",
    "name": "Loitering Munition",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Dauphin II",
    "name": "Light Utility Helicopter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "AW109SP GrandNew",
    "name": "Light Utility Helicopter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Chinook HC5",
    "name": "Heavy Lift Helicopter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Chinook HC6A",
    "name": "Heavy Lift Helicopter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Envoy IV CC Mk1",
    "name": "VIP Transport",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Hawk T1",
    "name": "Trainer/Light Combat Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 833.4,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Hawk T2",
    "name": "Trainer/Light Combat Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 833.4,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Phenom 100",
    "name": "Light Business Jet",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "120TP Prefect",
    "name": "Trainer",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Puma HC2",
    "name": "Tactical Transport Helicopter",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "RC-135W Rivet Joint",
    "name": "SIGINT Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Shadow R1",
    "name": "ISR Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Texan T1",
    "name": "Trainer Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Tutor T1",
    "name": "Trainer Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "G 103A Twin II Acro",
    "name": "Glider",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Voyager KC2",
    "name": "Tanker/Transport Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Airseeker R1",
    "name": "SIGINT Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Protector RG1",
    "name": "Unmanned Aerial Vehicle",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Viking T1",
    "name": "Trainer Aircraft",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "air"
  },
  {
    "id": "Skynet 5",
    "name": "Skynet 5",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Skynet 6",
    "name": "Skynet 6",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Tyche",
    "name": "Tyche",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Juno",
    "name": "Juno",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Titania",
    "name": "Titania",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Oberon",
    "name": "Oberon",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "GeoNet",
    "name": "GeoNet",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "GPS",
    "name": "GPS",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "GLONASS",
    "name": "GLONASS",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "Galileo",
    "name": "Galileo",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  },
  {
    "id": "BeiDou",
    "name": "BeiDou",
    "cost": null,
    "category": "platform",
    "combat_radius": 0,
    "country_iso_code": "GB",
    "type": "space"
  }
]};
