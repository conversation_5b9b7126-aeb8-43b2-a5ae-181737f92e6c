import { QueryRunner } from "typeorm";

interface Role {
	role_name: string;
	role_description: string;
	unit_level: string;
	staff_designation: string;
	functional_area: string;
	rank: string;
	is_command_role: boolean;
	is_staff_role: boolean;
	typical_unit_size: number | null;
	service_branch: string;
	country_or_organization: string;
}

export class RolesSeeder {
	private static roles: Role[] = [
		// {
		// 	role_name: 'Commander',
		// 	role_description: 'Overall commander of the unit',
		// 	unit_level: 'Division',
		// 	staff_designation: 'J1',
		// 	functional_area: 'Command',
		// 	rank: 'Major General',
		// 	is_command_role: true,
		// 	is_staff_role: false,
		// 	typical_unit_size: 10000,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'Chief of Staff',
		// 	role_description: 'Principal staff officer and coordinator',
		// 	unit_level: 'Division',
		// 	staff_designation: 'J23',
		// 	functional_area: 'Staff',
		// 	rank: 'Colonel',
		// 	is_command_role: false,
		// 	is_staff_role: true,
		// 	typical_unit_size: null,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'G1',
		// 	role_description: 'Personnel officer',
		// 	unit_level: 'Division',
		// 	staff_designation: 'G1',
		// 	functional_area: 'Personnel',
		// 	rank: 'Lieutenant Colonel',
		// 	is_command_role: false,
		// 	is_staff_role: true,
		// 	typical_unit_size: null,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'G2',
		// 	role_description: 'Intelligence officer',
		// 	unit_level: 'Division',
		// 	staff_designation: 'G2',
		// 	functional_area: 'Intelligence',
		// 	rank: 'Lieutenant Colonel',
		// 	is_command_role: false,
		// 	is_staff_role: true,
		// 	typical_unit_size: null,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'G3',
		// 	role_description: 'Operations officer',
		// 	unit_level: 'Division',
		// 	staff_designation: 'G3',
		// 	functional_area: 'Operations',
		// 	rank: 'Lieutenant Colonel',
		// 	is_command_role: false,
		// 	is_staff_role: true,
		// 	typical_unit_size: null,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'G4',
		// 	role_description: 'Logistics officer',
		// 	unit_level: 'Division',
		// 	staff_designation: 'G4',
		// 	functional_area: 'Logistics',
		// 	rank: 'Lieutenant Colonel',
		// 	is_command_role: false,
		// 	is_staff_role: true,
		// 	typical_unit_size: null,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'Brigade Commander',
		// 	role_description: 'Commander of a brigade',
		// 	unit_level: 'Brigade',
		// 	staff_designation: 'S2',
		// 	functional_area: 'Command',
		// 	rank: 'Colonel',
		// 	is_command_role: true,
		// 	is_staff_role: false,
		// 	typical_unit_size: 3000,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// },
		// {
		// 	role_name: 'Battalion Commander',
		// 	role_description: 'Commander of a battalion',
		// 	unit_level: 'Battalion',
		// 	staff_designation: 'S23',
		// 	functional_area: 'Command',
		// 	rank: 'Lieutenant Colonel',
		// 	is_command_role: true,
		// 	is_staff_role: false,
		// 	typical_unit_size: 500,
		// 	service_branch: 'Army',
		// 	country_or_organization: 'US'
		// }
	];

	public static async run(queryRunner: QueryRunner): Promise<void> {
		// Fetch the organization_id for the US Army
		const organizationResult = await queryRunner.query(`
            SELECT id FROM organizations 
            WHERE designation = 'Army' AND alpha_2_country_code = 'US'
            LIMIT 1
        `);

		if (organizationResult.length === 0) {
		//	console.error("US Army organization not found. Please seed organizations first.");
			return;
		}

		const organizationId = organizationResult[0].id;

		for (const role of this.roles) {
			// Check if the role already exists
			const existingRole = await queryRunner.query(`
                SELECT id FROM roles 
                WHERE staff_designation = $1 AND organization_id = $2
                LIMIT 1
            `, [role.staff_designation, organizationId]);

			if (existingRole.length === 0) {
				// Role doesn't exist, insert it
				await queryRunner.query(`
                    INSERT INTO roles (
                        role_name, 
                        role_description, 
                        unit_level, 
                        staff_designation, 
                        functional_area, 
                        rank, 
                        is_command_role, 
                        is_staff_role, 
                        typical_unit_size, 
                        service_branch, 
                        country_or_organization,
                        organization_id
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                `, [
					role.role_name,
					role.role_description,
					role.unit_level,
					role.staff_designation,
					role.functional_area,
					role.rank,
					role.is_command_role,
					role.is_staff_role,
					role.typical_unit_size,
					role.service_branch,
					role.country_or_organization,
					organizationId
				]);

			//	console.log(`Role '${role.role_name}' inserted successfully.`);
			} else {
			//	console.log(`Role with staff_designation '${role.staff_designation}' already exists. Skipping.`);
			}
		}

	//	console.log('Roles seeding completed.');
	}
}