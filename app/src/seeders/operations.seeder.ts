import { QueryRunner } from "typeorm";
import { Point } from 'geojson';

export class OperationSeeder {
	public static async run(queryRunner: QueryRunner): Promise<void> {
		const operations:any = [];

		// Insert each operation
		for (const operation of operations) {
			try {
				await queryRunner.query(`
                    INSERT INTO operations (
                        name,
                        location,
                        location_coordinates,
                        designation,
                        description,
                        is_active,
                        created_at,
                        updated_at
                    ) VALUES (
                        $1, $2, ST_SetSRID(ST_GeomFromGeoJSON($3), 4326), $4, $5, $6, $7, $8
                    ) ON CONFLICT (designation) DO NOTHING
                `, [
					operation.name,
					operation.location,
					JSON.stringify(operation.location_coordinates),
					operation.designation,
					operation.description,
					operation.is_active,
					operation.created_at,
					operation.updated_at
				]);

			} catch (error) {
				console.error(`Error processing operation ${operation.designation}:`, error);
			}
		}

		console.log('Operations seeding completed');
	}

	/**
	 * Helper method to clean up operations table
	 * Useful for testing and resetting the database
	 */
	public static async cleanup(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`TRUNCATE TABLE operations CASCADE;`);
		console.log('Operations table cleaned up');
	}
}