import { QueryRunner } from "typeorm";
import {sendSlackNotification} from "@/utils/system-notices.utils.js";

export class TruncateSeeder {
	/**
	 * Truncates all specified tables with proper constraint handling
	 */
	public static async run(queryRunner: QueryRunner): Promise<void> {
		// List of tables to truncate
		const tables:any[] = [
			// 'global_isrs',
			// 'areas_of_interest',
			// 'assets',
			// 'missions',
			// 'originators',
			// 'pirs',
			// 'rfis'
		];

		try {
			// Temporarily disable foreign key constraints
			await queryRunner.query('SET CONSTRAINTS ALL DEFERRED');

			// Truncate each table
			for (const table of tables) {
				try {
					await queryRunner.query(`TRUNCATE TABLE ${table} CASCADE`);
					console.log(`Table ${table} truncated successfully`);
					const mess = `Table ${table} truncated successfully`;
					await sendSlackNotification(mess);
				} catch (error) {
					console.error(`Error truncating table ${table}:`, error);
					const mess = `Error truncating table ${table}: ${error}`;
					await sendSlackNotification(mess);
					throw error; // Re-throw to trigger transaction rollback
				}
			}

			// Re-enable foreign key constraints
			await queryRunner.query('SET CONSTRAINTS ALL IMMEDIATE');

			console.log('All tables truncated successfully');
		} catch (error) {
			console.error('Error during truncation:', error);
			throw error; // Re-throw the error to trigger transaction rollback
		}
	}
}