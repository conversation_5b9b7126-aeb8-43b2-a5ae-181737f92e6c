import { QueryRunner } from "typeorm";

interface Originator {
	title: string;
	operationId: number;  // Changed to match model's operationId
	designation: string;
	description: string;
	contactDetails: string | null;  // Added as per model
	created_at: Date;
	updated_at: Date;
}

export class OriginatorSeeder {
	private static slugify(text: string): string {
		return text
			.toLowerCase()
			.replace(/\s+/g, '-')           // Replace spaces with -
			.replace(/[^\w\-]+/g, '')       // Remove all non-word chars
			.replace(/^-+/, '')             // Trim - from start of text
			.replace(/-+$/, '');            // Trim - from end of text
	}

	public static async run(queryRunner: QueryRunner): Promise<void> {
		try {
			// Get all operations first
			const operations = await queryRunner.query(`
                SELECT id, designation
                FROM operations
                ORDER BY id;
			`);

			if (!operations || operations.length === 0) {
				console.log('No operations found. Please seed operations first.');
				return;
			}

			const originatorTemplates = [
				"CIMIC Team",
				"Engineering Task Group",
				"Maritime Task Group",
				"Air Task Group",
				"Logistics HQ"
			];

			// Prepare all originators data first
			const originatorsToInsert: Originator[] = [];

			for (const operation of operations) {
				for (const originatorTitle of originatorTemplates) {
					const slugifiedTitle = this.slugify(originatorTitle);
					originatorsToInsert.push({
						title: originatorTitle + " @ " + operation.designation,
						operationId: operation.id,
						designation: `${operation.designation}-${slugifiedTitle}`, // Changed to match model's format
						description: `${originatorTitle} for ${operation.designation}`,
						contactDetails: null,
						created_at: new Date(),
						updated_at: new Date()
					});
				}
			}

			// Insert all originators in a single transaction
			for (const originator of originatorsToInsert) {
				await queryRunner.query(`
                    INSERT INTO originators (
                        title,
                        operation_id,
                        designation,
                        description,
                        contact_details,
                        created_at,
                        updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                        ON CONFLICT (designation) 
                    DO UPDATE SET
                        title = EXCLUDED.title,
                                                   operation_id = EXCLUDED.operation_id,
                                                   description = EXCLUDED.description,
                                                   contact_details = EXCLUDED.contact_details,
                                                   updated_at = EXCLUDED.updated_at
				`, [
					originator.title,
					originator.operationId,
					originator.designation,
					originator.description,
					originator.contactDetails,
					originator.created_at,
					originator.updated_at
				]);

			}

			console.log('Originators seeding completed successfully');
		} catch (error) {
			console.error('Error in OriginatorSeeder:', error);
			throw error; // Re-throw to ensure transaction rollback
		}
	}

	/**
	 * Helper method to clean up originators table
	 * Useful for testing and resetting the database
	 */
	public static async cleanup(queryRunner: QueryRunner): Promise<void> {
		try {
			await queryRunner.query(`TRUNCATE TABLE originators CASCADE;`);
			console.log('Originators table cleaned up');
		} catch (error) {
			console.error('Error cleaning up originators:', error);
			throw error;
		}
	}
}