import { QueryRunner } from "typeorm";

export class OrganizationSeeder {
    public static async run(queryRunner: QueryRunner): Promise<void> {
        const organizations:any[] = [

        ];

        // Insert each organization
        for (const org of organizations) {
            try {
                await queryRunner.query(`
                    INSERT INTO organizations (
                        name,
                        description,
                        is_military,
                        designation,
                        logo_url,
                        alpha_2_country_code,
                        sovereignty,
                        created_at,
                        updated_at
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9
                    ) ON CONFLICT (designation) DO NOTHING
                `, [
                    org.name,
                    org.description,
                    org.is_military,
                    org.designation,
                    org.logo_url,
                    org.alpha_2_country_code,
                    org.sovereignty,
                    org.created_at,
                    org.updated_at
                ]);
            } catch (error) {
                console.error(`Error processing organization ${org.designation}:`, error);
            }
        }

        console.log('Organizations seeding completed');
    }

    /**
     * Helper method to clean up organizations table
     * Useful for testing and resetting the database
     */
    public static async cleanup(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`TRUNCATE TABLE organizations CASCADE;`);
        console.log('Organizations table cleaned up');
    }
}