import { fixMissionDesignations } from "@/utils/fix-mission-designations.js";

/**
 * This script can be run directly to fix mission designations
 * Usage: npx ts-node -r tsconfig-paths/register src/scripts/fix-mission-designations.ts
 */
async function main() {
  console.log("Starting mission designation fix script...");
  
  try {
    await fixMissionDesignations();
    console.log("Mission designation fix script completed successfully");
  } catch (error) {
    console.error("Error running mission designation fix script:", error);
    process.exit(1);
  }
  
  process.exit(0);
}

main();
