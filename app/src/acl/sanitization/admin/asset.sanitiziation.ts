import { SanitizationRules } from '@/utils/sanitize.utils.js';
import AssetModel from '@/models/asset.model.js';

import {
    minimalISRSanitizationRules, minimalISRTrackSanitizationRules,
    minimalMissionSanitizationRules, minimalOperationSanitizationRules, minimalPlatformSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminAssetSanitizationRules: SanitizationRules<AssetModel> = {
	id: true,
	assetDetails: true,
	title: true,
	name: true,
	hasCollectionCapability: true,
	createdAt: true,
	designation: true,
	updatedAt: true,
	status: true,
	callSign: true,
	operationId: true,
	platformId: true,
    color:true,
	//relationships
	platform: minimalPlatformSanitizationRules,
	missions: minimalMissionSanitizationRules,
	operation: minimalOperationSanitizationRules,
    engagedMissions: minimalMissionSanitizationRules,
    engagedIsrTracks: minimalISRTrackSanitizationRules
	// isrTracks: minimalISRSanitizationRules,
};