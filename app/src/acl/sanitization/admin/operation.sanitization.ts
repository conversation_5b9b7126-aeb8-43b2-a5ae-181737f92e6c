import { SanitizationRules } from '@/utils/sanitize.utils.js';
import  OperationModel  from '@/models/operation.model.js';

import {
	minimalAssetsRules,
	minimalMissionsRules,
	minimalPirsRules,
	minimalUserSanitizationRules,
	minimalGlobalIsrsRules,
	minimalMapElementSanitizationRules,
	minimalCollectionRequirementSanitizationRules,
	minimalOriginatorSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminOperationSanitizationRules: SanitizationRules<OperationModel> = {
	id: true,
	name: true,
	description: true,
	designation: true,
	location: true,
	isActive: true,
	createdAt: true,
	updatedAt: true,
	locationCoordinates: true,
	zoom: true,
	areaOfOperation: true,
	timezones: true,
	config: true,
	tacticalAreaOfResponsibility: true,
	type: true,
	missions: minimalMissionsRules,
	users: minimalUserSanitizationRules,
	assets: minimalAssetsRules,
	pirs: minimalPirsRules,
	isrTracks: true,
	collectionCapabilities: minimalCollectionRequirementSanitizationRules,
	globalIsrs: minimalGlobalIsrsRules,
	mapElement: minimalMapElementSanitizationRules,
	mapElementId: true,
};
