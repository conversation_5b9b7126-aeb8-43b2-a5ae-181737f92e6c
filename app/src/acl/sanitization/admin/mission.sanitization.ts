

import { SanitizationRules } from '@/utils/sanitize.utils.js';
import OperationModel from "@/models/operation.model.js";
import MissionModel from "@/models/mission.model.js";
import AoiModel from "@/models/aoi.model.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import ISRModel from "@/models/isr.model.js";
import {
    minimalAoiSanitizationRules, minimalAssetSanitizationRules,
    minimalIrSanitizationRules,
    minimalISRSanitizationRules, minimalISRTrackSanitizationRules, minimalOperationSanitizationRules,
    minimalEngagedMissionAssetsSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminMissionSanitizationRules: SanitizationRules<MissionModel> = {
	id: true,
	name: true,
	description: true,
	designation: true,
	type: true,
	classification: true,
	operationId: true,
	priority: true,
	reportTypeConfiguration: true,
	startAt: true,
	endAt: true,
	status: true,
	createdAt: true,
	updatedAt: true,
	indicatorsDescription: true,
	warningsDescription: true,
	requestedByUserId: true,
	approvedByUserId: true,
	requestedByUser: true,
	approvedByUser: true,
	isrs: minimalISRSanitizationRules,
	informationRequirements: minimalIrSanitizationRules,
	tais: minimalAoiSanitizationRules,
	operation: minimalOperationSanitizationRules,
	assets: minimalAssetSanitizationRules,
    engagedAssets: minimalAssetSanitizationRules,
    engagedIsrTracks: minimalISRTrackSanitizationRules,
    engagedMissionAssets: minimalEngagedMissionAssetsSanitizationRules,
};