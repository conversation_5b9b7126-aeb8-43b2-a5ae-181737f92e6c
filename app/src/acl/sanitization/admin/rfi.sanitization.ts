import RFIModel from "@/models/rfi.model.js";
import { SanitizationRules} from "@/utils/sanitize.utils.js";
import {
	minimalOperationSanitizationRules,
	minimalOriginatorSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminRFISanitizationRules: SanitizationRules<RFIModel> = {
	id: true,
	title: true,
	originatorId: true,
	ltiovDate: true,
	originator: minimalOriginatorSanitizationRules,
	originatorLabel: true,
	operationId: true,
	operation: minimalOperationSanitizationRules,
	priority: true,
	checkSource: true,
	createdAt: true,
	updatedAt: true,
	status: true,
};