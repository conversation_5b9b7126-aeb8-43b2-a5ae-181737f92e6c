import { SanitizationRules } from '@/utils/sanitize.utils.js';
import AoiModel from '@/models/aoi.model.js';
import {
	minimalMapElementSanitizationRules,
	minimalMissionSanitizationRules,
	minimalOperationSanitizationRules, minimalUserSanitizationRules
} from '@/acl/sanitization/admin/minimal-rules.sanitization.js';

export const adminAoiSanitizationRules: SanitizationRules<AoiModel> = {
	id: true,
	designation: true,
	name: true,
	description: true,
	isApproved: true,
	isTargetable: true,
	operationId: true,
	requestedByUserId: true,
	approvedByUserId: true,
	createdAt: true,
	updatedAt: true,
	mapElement: minimalMapElementSanitizationRules,
	mapElementId: true,
	missions: minimalMissionSanitizationRules,
	operation: minimalOperationSanitizationRules,
	requestedByUser: minimalUserSanitizationRules,
	approvedByUser: minimalUserSanitizationRules,
};