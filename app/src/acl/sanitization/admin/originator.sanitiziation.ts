import { SanitizationRules } from '@/utils/sanitize.utils.js';
import OriginatorModel from "@/models/originator.model.js";
import {minimalOperationSanitizationRules } from "@/acl/sanitization/admin/minimal-rules.sanitization.js";

export const adminOriginatorSanitizationRules: SanitizationRules<OriginatorModel> = {
	id: true,
	title: true,
	designation: true,
	description: true,
	organizationId: true,
	contactDetails: true,
};