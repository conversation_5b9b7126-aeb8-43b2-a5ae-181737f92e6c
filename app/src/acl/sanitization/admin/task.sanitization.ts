import { SanitizationRules } from '@/utils/sanitize.utils.js';
import TaskModel from "@/models/task.model.js";
import {
    minimalUserSanitizationRules,
    minimalOperationSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";

// Define sanitization rules for the Task model
export const adminTaskSanitizationRules: SanitizationRules<TaskModel> = {
    // Basic properties
    id: true,
	title: true,
    description: true,
    shortDescription: true,
    dueAt: true,
    status: true,
    completedAt: true,
	priority: true,
    // Foreign keys
    operationId: true,
    createdByUserId: true,
    
    // Metadata
    createdAt: true,
    updatedAt: true,
    
    // Relations
    createdByUser: minimalUserSanitizationRules,
    members: minimalUserSanitizationRules,
    operation: minimalOperationSanitizationRules,

    labels: true,
	archivedAt: true,

};
