import { SanitizationRules } from '@/utils/sanitize.utils.js';
import UserModel from '@/models/user.model.js';
import {
	minimalCollectionRequirementSanitizationRules, minimalISRSanitizationRules,
	minimalMissionSanitizationRules, minimalOperationSanitizationRules, minimalRoleSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";

export const adminUserSanitizationRules: SanitizationRules<UserModel> = {
	id: true,
	firstName: true,
	lastName: true,
	email: true,
	accountType: true,
	isOnline: true,
	isActive: true,
	isVerified: true,
	createdAt: true,
	updatedAt: true,
	roles: minimalRoleSanitizationRules,
	createdInformationRequirements: minimalCollectionRequirementSanitizationRules,
	requestedMissions: minimalMissionSanitizationRules,
	approvedMissions: minimalMissionSanitizationRules,
	isrs: minimalISRSanitizationRules,
	operations: {
		operation: minimalOperationSanitizationRules,
		accessType: true
	},
};