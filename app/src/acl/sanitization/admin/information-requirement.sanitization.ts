import { SanitizationRules } from '@/utils/sanitize.utils.js';
import InformationRequirementModel from '@/models/information-requirement.model.js';

import {
	minimalMissionSanitizationRules,
	minimalPirSanitizationRules,
	minimalUserSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminInformationRequirementSanitizationRules: SanitizationRules<InformationRequirementModel> = {
	id: true,
	irNumber: true,
	designation: true,
	originator: true,
	isAnswered: true,
	informationRequirement: true,
	ltiovDate: true,
	priority: true,
	pirId: true,
	createdByUserId: true,
	createdAt: true,
	updatedAt: true,
	// Relationship sanitization rules
	createdByUser: minimalUserSanitizationRules,
	pir: minimalPirSanitizationRules,
	missions: minimalMissionSanitizationRules,
};