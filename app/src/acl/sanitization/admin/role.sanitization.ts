import { SanitizationRules } from '@/utils/sanitize.utils.js';
import RoleModel from '@/models/role.model.js';
import {minimalRoleSanitizationRules} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminRoleSanitizationRules: SanitizationRules<RoleModel> = {
	id: true,
	roleName: true,
	roleDescription: true,
	unitLevel: true,
	staffDesignation: true,
	functionalArea: true,
	rank: true,
	isCommandRole: true,
	isStaffRole: true,
	typicalUnitSize: true,
	serviceBranch: true,
	countryOrOrganization: true,
	organizationId: true,
    roleType: true,
	createdAt: true,
	updatedAt: true,
	isSystemRole: true,
	superiorRoles: minimalRoleSanitizationRules,
	subordinateRoles: minimalRoleSanitizationRules,
};