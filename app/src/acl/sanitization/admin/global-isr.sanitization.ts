import { SanitizationRules } from '@/utils/sanitize.utils.js';
import GlobalISRModel from '@/models/global-isr.model.js';
import {
	minimalAssetSanitizationRules,
	 minimalOperationSanitizationRules,
	minimalUserSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";

export const adminGlobalISRSanitizationRules: SanitizationRules<GlobalISRModel> = {
	id: true,
	designation: true,
	label: true,
	type: true,
	coordinates: true,
	zoom: true,
	centerCoordinates: true,
	status: true,
	priority: true,
	commenceAt: true,
	concludeAt: true,
	ltiovDate: true,
	createdAt: true,
	updatedAt: true,
	assetId: true,
	operationId: true,
	createdByUserId: true,
	// Relationships
	asset: minimalAssetSanitizationRules,
	operation: minimalOperationSanitizationRules,
	createdByUser: minimalUserSanitizationRules,
};