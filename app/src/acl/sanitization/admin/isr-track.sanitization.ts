import { SanitizationRules } from '@/utils/sanitize.utils.js';
import ISRTrackModel from '@/models/isr-track.model.js';
import {
    minimalUserSanitizationRules,
    minimalOperationSanitizationRules,
    minimalMapElementSanitizationRules,
    minimalCollectionCapabilitySanitizationRules,
    minimalAssetSanitizationRules,
    minimalMissionSanitizationRules,
    minimalISRTrackSanitizationRules
} from '@/acl/sanitization/admin/minimal-rules.sanitization.js';

export const adminISRTrackSanitizationRules: SanitizationRules<ISRTrackModel> = {
	id: true,
	createdByUserId: true,
	designation: true,
	label: true,
	operationId: true,
	status: true,
	priority: true,
	commenceAt: true,
	concludeAt: true,
	ltiovDate: true,
	mapElementId: true,
	createdAt: true,
	updatedAt: true,

	// Relationships
	mapElement: minimalMapElementSanitizationRules,
	createdByUser: minimalUserSanitizationRules,
	operation: minimalOperationSanitizationRules,
	assets: minimalAssetSanitizationRules,
    engagedMissions: minimalMissionSanitizationRules,
    engagedAssets: minimalAssetSanitizationRules,
};