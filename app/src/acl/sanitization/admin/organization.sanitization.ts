import { SanitizationRules } from '@/utils/sanitize.utils.js';
import  OrganizationModel  from '@/models/organization.model.js';
import {adminUserSanitizationRules} from "@/acl/sanitization/admin/user.sanitization.js";

export const adminOrganizationSanitizationRules: SanitizationRules<OrganizationModel> = {
	id: true,
	name: true,
	description: true,
	isMilitary: true,
	designation: true,
	logoUrl: true,
	isoCountryCode: true,
	sovereignty: true,
	createdAt: true,
	updatedAt: true,
	config: true,
	// users: adminUserSanitizationRules,
};
