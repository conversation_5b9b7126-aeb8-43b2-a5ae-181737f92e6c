// app/src/acl/sanitization/admin/admin.engaged-mission-asset.sanitization.ts
import { SanitizationRules } from '@/utils/sanitize.utils.js';
import EngagedMissionAssetModel from '@/models/engaged-mission-asset.model.js';
import { minimalAssetSanitizationRules } from '@/acl/sanitization/admin/minimal-rules.sanitization.js';
import { minimalMissionSanitizationRules } from '@/acl/sanitization/admin/minimal-rules.sanitization.js';
import { minimalISRTrackSanitizationRules } from '@/acl/sanitization/admin/minimal-rules.sanitization.js';

export const adminEngagedMissionAssetSanitizationRules: SanitizationRules<EngagedMissionAssetModel> = {
    id: true,
    assetId: true,
    missionId: true,
    isrTrackId: true,
    startAt: true,
    endAt: true,
    asset: minimalAssetSanitizationRules,
    mission: minimalMissionSanitizationRules,
    isrTrack: minimalISRTrackSanitizationRules,
};