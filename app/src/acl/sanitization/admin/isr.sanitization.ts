import { SanitizationRules } from '@/utils/sanitize.utils.js';
import ISRModel from '@/models/isr.model.js';
import {
	minimalAssetSanitizationRules,
	minimalMissionSanitizationRules,
	minimalUserSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";

export const adminISRSanitizationRules: SanitizationRules<ISRModel> = {
	id: true,
	designation: true,
	label: true,
	type: true,
	coordinates: true,
	zoom: true,
	isEditable: true,
	isGlobal: true,
	centerCoordinates: true,
	status: true,
	priority: true,
	commenceAt: true,
	concludeAt: true,
	ltiovDate: true,
	createdAt: true,
	updatedAt: true,
	assetId: true,
	missionId: true,
	createdByUserId: true,
	// Relationships
	asset: minimalAssetSanitizationRules,
	mission: minimalMissionSanitizationRules,
	createdByUser: minimalUserSanitizationRules,
};