import { SanitizationRules } from '@/utils/sanitize.utils.js';
import WeatherForecastModel from '@/models/weather-forecast.model.js';
import WeatherForecastHourlyModel from '@/models/weather-forecast-hourly.model.js';
import WeatherForecastPeriodModel from '@/models/weather-forecast-period.model.js';
import WeatherGridDataModel from '@/models/weather-grid-data.model.js';

// Hourly Forecast sanitization rules
export const adminWeatherForecastHourlySanitizationRules: SanitizationRules<WeatherForecastHourlyModel> = {
	id: true,
	number: true,
	startTime: true,
	endTime: true,
	temperature: true,
	temperatureUnit: true,
	temperatureTrend: true,
	dewpoint: true,
	relativeHumidity: true,
	windSpeed: true,
	windDirection: true,
	icon: true,
	shortForecast: true,
	isDaytime: true,
	probabilityOfPrecipitation: true,
	detailedForecast: true,
	rawData: true,
	weatherForecastId: true,
	createdAt: true,
	updatedAt: true
};

// Period Forecast sanitization rules
export const adminWeatherForecastPeriodSanitizationRules: SanitizationRules<WeatherForecastPeriodModel> = {
	id: true,
	number: true,
	startTime: true,
	endTime: true,
	name: true,
	isDaytime: true,
	temperature: true,
	temperatureUnit: true,
	temperatureTrend: true,
	probabilityOfPrecipitation: true,
	windSpeed: true,
	windDirection: true,
	icon: true,
	shortForecast: true,
	detailedForecast: true,
	rawData: true,
	weatherForecastId: true,
	createdAt: true,
	updatedAt: true
};

// Grid Data sanitization rules
export const adminWeatherGridDataSanitizationRules: SanitizationRules<WeatherGridDataModel> = {
	id: true,
	updateTime: true,
	validTimes: true,
	elevation: true,
	forecastOffice: true,
	gridId: true,
	gridX: true,
	gridY: true,
	temperature: true,
	dewpoint: true,
	maxTemperature: true,
	minTemperature: true,
	relativeHumidity: true,
	apparentTemperature: true,
	heatIndex: true,
	windChill: true,
	skyCover: true,
	windDirection: true,
	windSpeed: true,
	windGust: true,
	weather: true,
	hazards: true,
	probabilityOfPrecipitation: true,
	quantitativePrecipitation: true,
	iceAccumulation: true,
	snowfallAmount: true,
	ceilingHeight: true,
	visibility: true,
	transportWindSpeed: true,
	transportWindDirection: true,
	mixingHeight: true,
	hainesIndex: true,
	lightningActivityLevel: true,
	grasslandFireDangerIndex: true,
	probabilityOfThunder: true,
	redFlagThreatIndex: true,
	geometry: true,
	weatherForecastId: true,
	createdAt: true,
	updatedAt: true
};

// Main Weather Forecast sanitization rules
export const adminWeatherForecastSanitizationRules: SanitizationRules<WeatherForecastModel> = {
	id: true,
	provider: true,
	lastUpdatedAt: true,
	timeZone: true,
	radarStation: true,
	forecastOffice: true,
	gridId: true,
	gridX: true,
	gridY: true,
	location: true,
	forecastArea: true,
	units: true,
	forecastGenerator: true,
	generatedAt: true,
	updateTime: true,
	validTimes: true,
	elevationValue: true,
	elevationUnit: true,
	locationProperties: true,
	createdAt: true,
	updatedAt: true,
	// Add relationships using their respective sanitization rules
	forecasts: adminWeatherForecastPeriodSanitizationRules,
	hourlyForecasts: adminWeatherForecastHourlySanitizationRules,
	gridData: adminWeatherGridDataSanitizationRules
};