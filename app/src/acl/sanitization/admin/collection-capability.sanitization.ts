import { SanitizationRules } from '@/utils/sanitize.utils.js';
import CollectionCapabilityModel from '@/models/collection-capability.model.js';


import {
	minimalAssetSanitizationRules, minimalISRTrackSanitizationRules,
	minimalUserSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminCollectionCapabilitySanitizationRules: SanitizationRules<CollectionCapabilityModel> = {
	id: true,
	description: true,
	assetDesignation: true,
	assigned: true,
	contactDetails: true,
	availability: true,
	status: true,
	createdByUserId: true,
	assetId: true,
	pointOfContactUserId: true,
	createdAt: true,
	updatedAt: true,
	// Relationships
	asset: minimalAssetSanitizationRules,
	createdByUser: minimalUserSanitizationRules,
	pointOfContactUser: minimalUserSanitizationRules,
	// isrTracks: minimalISRTrackSanitizationRules
};