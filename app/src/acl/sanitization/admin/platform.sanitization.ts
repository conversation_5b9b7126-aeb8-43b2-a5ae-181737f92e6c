import {SanitizationRules} from "@/utils/sanitize.utils.js";
import PlatformModel from "@/models/platform.model.js";
import {minimalAssetsRules} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";



export const adminPlatformSanitizationRules: SanitizationRules<PlatformModel> = {
	id: true,
	name: true,
	designation: true,
	description: true,
	countryIsoCode: true,
	type: true,
	combatRadius: true,
	footprintArea: true,
	configuration: true,
	createdAt: true,
	updatedAt: true,
	hasCollectionCapability: true,
	assets: minimalAssetsRules,
	//assets: adminAssetSanitizationRules
};
