import { SanitizationRules } from '@/utils/sanitize.utils.js';
import OperationModel from '@/models/operation.model.js';
import UserModel from '@/models/user.model.js';
import MissionModel from '@/models/mission.model.js';
import ISRModel from '@/models/isr.model.js';
import PlatformModel from '@/models/platform.model.js';
import AssetModel from '@/models/asset.model.js';
import AoiModel from '@/models/aoi.model.js';
import InformationRequirementModel from '@/models/information-requirement.model.js';
import RoleModel from '@/models/role.model.js';
import OperationsUsersModel from '@/models/operations-users.model.js';
import PIRModel from '@/models/pir.model.js';
import OriginatorModel from '@/models/originator.model.js';
import GlobalISRModel from "@/models/global-isr.model.js";
import WeatherForecastModel from "@/models/weather-forecast.model.js";
import WeatherGridDataModel from "@/models/weather-grid-data.model.js";
import MapElementModel from "@/models/map-element.model.js";
import ISRTrackModel from "@/models/isr-track.model.js";
import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";
import TaskModel from '@/models/task.model.js';


// Basic Rules
export const minimalTaskSanitizationRules: SanitizationRules<TaskModel> = {
	id: true,
	name: true,
	shortDescription: true,
	status: true,
	priority: true,
	dueAt: true,
	operationId: true,
	createdByUserId: true,
	labels: true,
	createdAt: true,
	updatedAt: true
};

export const minimalMapElementSanitizationRules:SanitizationRules<MapElementModel> = {
	id: true,
	element: true,
	elementType: true,
	elementColor: true,
	borderType: true,
	borderThickness: true,
	borderColor: true,
};

export const minimalUserSanitizationRules: SanitizationRules<UserModel> = {
	id: true,
	firstName: true,
	lastName: true,
	email: true,
	avatarPath: true,
	accountType: true,
	isOnline: true,
	isActive: true,
	isVerified: true,
	createdAt: true,
	updatedAt: true,
};

export const minimalRoleSanitizationRules: SanitizationRules<RoleModel> = {
	id: true,
	roleName: true,
	rank: true,
	roleDescription: true,
	unitLevel: true,
	staffDesignation: true,
	functionalArea: true,
	isCommandRole: true,
	isStaffRole: true,
	typicalUnitSize: true,
    roleType: true,
	serviceBranch: true,
	countryOrOrganization: true,
	organizationId: true,
	createdAt: true,
	updatedAt: true,
	isSystemRole: true,
};

export const minimalOperationSanitizationRules: SanitizationRules<OperationModel> = {
	id: true,
	name: true,
	description: true,
	designation: true,
	location: true,
	locationCoordinates: true,
	zoom: true,
	areaOfOperation: true,
	timezones: true,
	config: true,
	tacticalAreaOfResponsibility: true,
	type: true,
	isActive: true,
	createdAt: true,
	updatedAt: true,
};

export const minimalPlatformSanitizationRules: SanitizationRules<PlatformModel> = {
	id: true,
	name: true,
	designation: true,
	countryIsoCode: true,
	type: true,
	combatRadius: true,
	footprintArea: true,
	configuration: true,
	quantity: true,
	createdAt: true,
	updatedAt: true,
};

export const minimalMissionSanitizationRules: SanitizationRules<MissionModel> = {
	id: true,
	name: true,
	description: true,
	designation: true,
	type: true,
	classification: true,
	priority: true,
	reportTypeConfiguration: true,
	startAt: true,
	endAt: true,
	status: true,
	createdAt: true,
	updatedAt: true,
	indicatorsDescription: true,
	warningsDescription: true,
	requestedByUserId: true,
	approvedByUserId: true,
	requestedByUser: minimalUserSanitizationRules,
	approvedByUser: minimalUserSanitizationRules,
};

export const minimalISRSanitizationRules: SanitizationRules<ISRModel> = {
	id: true,
	createdByUserId: true,
	assetId: true,
	isEditable: true,
	isGlobal: true,
	label: true,
	missionId: true,
	type: true,
	coordinates: true,
	centerCoordinates: true,
	zoom: true,
	commenceAt: true,
	concludeAt: true,
	ltiovDate: true,
	status: true,
	priority: true,
};

export const minimalISRTrackSanitizationRules: SanitizationRules<ISRTrackModel> = {
	id: true,
	designation: true,
	label: true,
	commenceAt: true,
	concludeAt: true,
	status: true,
	operationId: true,
	ltiovDate: true,
	updatedAt: true,
	priority: true,
	mapElement: minimalMapElementSanitizationRules,
	createdAt: true,
	createdByUserId: true,
};

export const minimalAssetSanitizationRules: SanitizationRules<AssetModel> = {
	id: true,
	name: true,
	title: true,
	designation: true,
	status: true,
	platformId: true,
	callSign: true,
	operationId: true,
    color:true,
	isrTracks: minimalISRTrackSanitizationRules,
	hasCollectionCapability: true,
	collectionCapabilities: true,
	assetDetails: true,
	createdAt: true,
	updatedAt: true,
	platform: minimalPlatformSanitizationRules,
    missions: minimalMissionSanitizationRules,
};

export const minimalGlobalIsrsRules: SanitizationRules<GlobalISRModel> = {
	id: true,
	designation: true,
	label: true,
	type: true,
	coordinates: true,
	zoom: true,
	centerCoordinates: true,
	status: true,
	priority: true,
	commenceAt: true,
	concludeAt: true,
	ltiovDate: true,
	createdAt: true,
	updatedAt: true,
	assetId: true,
	operationId: true,
	createdByUserId: true,
};

export const minimalAoiSanitizationRules: SanitizationRules<AoiModel> = {
	id: true,
	designation: true,
	name: true,
	description: true,
	isApproved: true,
	isTargetable: true,
	operationId: true,
	requestedByUserId: true,
	approvedByUserId: true,
	mapElementId: true,
	mapElement: minimalMapElementSanitizationRules,
};

export const minimalPirSanitizationRules: SanitizationRules<PIRModel> = {
	id: true,
	pirNumber: true,
	designation: true,
	question: true,
	originator: true,
	description: true,
	isActive: true,
	operationId: true,
	createdAt: true,
	updatedAt: true,
	priority: true,
};

export const minimalInformationRequirementSanitizationRules: SanitizationRules<InformationRequirementModel> = {
	id: true,
	irNumber: true,
	designation: true,
	originator: true,
	informationRequirement: true,
	ltiovDate: true,
	priority: true,
	pirId: true,
	isAnswered: true,
	createdByUserId: true,
};

export const minimalOriginatorSanitizationRules: SanitizationRules<OriginatorModel> = {
	id: true,
	title: true,
	designation: true,
	description: true,
	organizationId: true,
	contactDetails: true,
};

export const minimalOperationsUsersSanitizationRules: SanitizationRules<OperationsUsersModel> = {
	user: {
		...minimalUserSanitizationRules,
		roles: minimalRoleSanitizationRules
	},
	accessType: true,
};

export const minimalWeatherForecastSanitizationRules: SanitizationRules<WeatherForecastModel> = {
	id: true,
	provider: true,
	lastUpdatedAt: true,
	timeZone: true,
	radarStation: true,
	forecastOffice: true,
	gridId: true,
	gridX: true,
	gridY: true,
	location: true,
	forecastArea: true,
	units: true,
	forecastGenerator: true,
	generatedAt: true,
	updateTime: true,
	validTimes: true,
	elevationValue: true,
	elevationUnit: true,
	locationProperties: true,
	createdAt: true,
	updatedAt: true,
};

export const minimalWeatherGridDataSanitizationRules: SanitizationRules<WeatherGridDataModel> = {
	id: true,
	updateTime: true,
	validTimes: true,
	elevation: true,
	forecastOffice: true,
	gridId: true,
	gridX: true,
	gridY: true,
	temperature: true,
	dewpoint: true,
	maxTemperature: true,
	minTemperature: true,
	relativeHumidity: true,
	apparentTemperature: true,
	heatIndex: true,
	windChill: true,
	skyCover: true,
	windDirection: true,
	windSpeed: true,
	windGust: true,
	weather: true,
	hazards: true,
	probabilityOfPrecipitation: true,
	quantitativePrecipitation: true,
	iceAccumulation: true,
	snowfallAmount: true,
	ceilingHeight: true,
	visibility: true,
	transportWindSpeed: true,
	transportWindDirection: true,
	mixingHeight: true,
	hainesIndex: true,
	lightningActivityLevel: true,
	grasslandFireDangerIndex: true,
	probabilityOfThunder: true,
	redFlagThreatIndex: true,
	geometry: true,
	weatherForecastId: true,
	weatherForecast: minimalWeatherForecastSanitizationRules,
	createdAt: true,
	updatedAt: true,
};

export const minimalCollectionCapabilitySanitizationRules: SanitizationRules<CollectionCapabilityModel> = {
	id: true,
	description: true,
	assetDesignation: true,
	assigned: true,
	contactDetails: true,
	availability: true,
	status: true,
	createdByUserId: true,
	assetId: true,
	pointOfContactUserId: true,
	createdAt: true,
	updatedAt: true,
	operationId: true,
};


// Aliases for backwards compatibility
export const minimalCollectionRequirementSanitizationRules = minimalInformationRequirementSanitizationRules;
export const minimalIrSanitizationRules = minimalInformationRequirementSanitizationRules;
export const minimalAssetsRules = minimalAssetSanitizationRules;
export const minimalMissionsRules = minimalMissionSanitizationRules;
export const minimalPirsRules = minimalPirSanitizationRules;
export const minimalWeatherForecastsRules = minimalWeatherForecastSanitizationRules;
export const minimalWeatherGridDataRules = minimalWeatherGridDataSanitizationRules;

export const minimalEngagedMissionAssetsSanitizationRules: SanitizationRules<EngagedMissionAssetModel> = {
    id: true,
    assetId: true,
    missionId: true,
    isrTrackId: true,
    asset: minimalAssetSanitizationRules,
    isrTrack: minimalISRTrackSanitizationRules,
    mission: minimalMissionSanitizationRules,
}