import { SanitizationRules } from '@/utils/sanitize.utils.js';
import PIRModel from '@/models/pir.model.js';
import {
	minimalCollectionRequirementSanitizationRules,
	minimalOperationSanitizationRules
} from "@/acl/sanitization/admin/minimal-rules.sanitization.js";


export const adminPirSanitizationRules: SanitizationRules<PIRModel> = {
	id: true,
	question: true,
	description: true,
	isActive: true,
	pirNumber: true,
	originator: true,
	designation: true,
	operationId: true,
	createdAt: true,
	updatedAt: true,
	priority: true,
	operation: minimalOperationSanitizationRules,
	informationRequirements: minimalCollectionRequirementSanitizationRules,
};