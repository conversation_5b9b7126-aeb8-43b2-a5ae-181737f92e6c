import { SanitizationRules } from '@/utils/sanitize.utils.js';
import UserModel from '@/models/user.model.js';
import { userRoleSanitizationRules } from '@/acl/sanitization/user/role.sanitization.js';
// import { teamSanitizationRules } from '@/models/team.sanitization.js';
// import { boardSanitizationRules } from '@/models/board.sanitization.js';

export const userUserSanitizationRules: SanitizationRules<UserModel> = {
	id: true,
	firstName: true,
	lastName: true,
	email: true,
	accountType: true,
	isOnline: true,
	isActive: true,
	isVerified: true,
	createdAt: true,
	updatedAt: true,
	roles: userRoleSanitizationRules,
};