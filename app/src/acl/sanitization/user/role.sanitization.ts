import { SanitizationRules } from '@/utils/sanitize.utils.js';
import RoleModel from '@/models/role.model.js';


const minimalRoleSanitizationRules: SanitizationRules<RoleModel> = {
	id: true,
	roleName: true,
	rank: true,
	roleDescription: true,
	unitLevel: true,
	staffDesignation: true,
	functionalArea: true,
	isCommandRole: true,
	isStaffRole: true,
	typicalUnitSize: true,
	serviceBranch: true,
	countryOrOrganization: true,
	organizationId: true,
    roleType: true,
	createdAt: true,
	updatedAt: true,
};

export const userRoleSanitizationRules: SanitizationRules<RoleModel> = {
	id: true,
	roleName: true,
	roleDescription: true,
	unitLevel: true,
	staffDesignation: true,
	functionalArea: true,
	rank: true,
	isCommandRole: true,
	isStaffRole: true,
	typicalUnitSize: true,
	serviceBranch: true,
	countryOrOrganization: true,
	organizationId: true,
	createdAt: true,
	updatedAt: true,
	superiorRoles: minimalRoleSanitizationRules,
	subordinateRoles: minimalRoleSanitizationRules,
};