import {AccessType, IRequestingUser} from '@/interfaces/database.interface.js';
import { checkACLByRule } from '@/utils/acl.utils.js';

/*
	ACL for Collection Capabilities
 */
export const operationAcl = async (id:number|null|string, userToCheck:IRequestingUser|null|undefined, accessRequested: AccessType = AccessType.READ) => {
	try {
		return checkACLByRule('operation', userToCheck, accessRequested);
	} catch (err) {
		console.log("ACL ACCESS ERROR", err);
		return false;
	}
}