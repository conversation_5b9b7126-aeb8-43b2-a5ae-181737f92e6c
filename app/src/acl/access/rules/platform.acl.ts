import {AccessType, IRequestingUser} from '@/interfaces/database.interface.js';
import { checkACLByRule } from '@/utils/acl.utils.js';

export const platformAcl = async (id:number|null|string, userToCheck:IRequestingUser|null|undefined, accessRequested: AccessType = AccessType.READ) => {
	try {
		//request to read allow all
		if (accessRequested === AccessType.READ || accessRequested === AccessType.LIST) {
			return true;
		}
		//allow only org to acdsess platform
		return userToCheck?.accountType === 'org_admin' && (accessRequested === AccessType.CREATE || accessRequested === AccessType.UPDATE || accessRequested === AccessType.DELETE) ? true : false;

		return checkACLByRule('platform', userToCheck, accessRequested);
	} catch (err) {
		console.log("ACL ACCESS ERROR", err);
		return false;
	}
}



//import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
// import { checkACLByRule } from '@/acl/acl.utils.js';
//
// export const platformAcl = async (itemId: string | null, userToCheck: IRequestingUser | null | undefined, accessRequested: AccessType = AccessType.READ) => {
//     // Special case for CREATE, UPDATE, DELETE - only org_admin users can perform these operations
//     if (accessRequested === AccessType.CREATE ||
//         accessRequested === AccessType.UPDATE ||
//         accessRequested === AccessType.DELETE) {
//         return userToCheck?.accountType === 'org_admin';
//     }
//
//     // For READ and LIST, use the standard ACL rules
//     return checkACLByRule('platform', userToCheck, accessRequested);
// };