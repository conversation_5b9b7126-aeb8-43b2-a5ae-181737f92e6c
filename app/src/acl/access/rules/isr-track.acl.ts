import {AccessType, IRequestingUser} from '@/interfaces/database.interface.js';
import { checkACLByRule } from '@/utils/acl.utils.js';

export const isrTrackAcl = async (id:number|null|string, userToCheck:IRequestingUser|null|undefined, accessRequested: AccessType = AccessType.READ) => {
	try {
		return checkACLByRule('isrTrack', userToCheck, accessRequested);
	} catch (err) {
		console.log("ACL ACCESS ERROR", err);
		return false;
	}
}