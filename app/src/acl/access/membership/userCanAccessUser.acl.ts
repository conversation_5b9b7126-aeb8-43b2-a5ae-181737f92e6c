import AppDataSource from "@/config/database.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';
import { userCanAccessManyOrganizations } from '@/acl/access/membership/userCanAccessManyOrganizations.acl.js';

const operationRepository =  AppDataSource.getRepository(OperationModel);
const userRepository =  AppDataSource.getRepository(UserModel);

export const userCanAccessUserAcl = async (userId:undefined|number|string|null, targetUserId:number|string|undefined|null) => {
	try{

		if(!userId || !targetUserId) return false;

		const targetUser = await userRepository.findOne({
			where: { id: parseInt(targetUserId.toString()) },
			relations: ['roles']
		}) as UserModel | null;
		if(!targetUser) return false;

		const user = await userRepository.findOne({
			where: { id: parseInt(userId.toString()) },
			relations: ['roles']
		}) as UserModel | null;
		if(!user) return false;

		const targetUserOrgIds = targetUser.roles.map(role => role.organizationId);
		const userOrgIds = user.roles.map(role => role.organizationId);
		return targetUserOrgIds.some(id => userOrgIds.includes(id));
	} catch(err){
		console.log("ERROR", err);
		return false;
	}

}
