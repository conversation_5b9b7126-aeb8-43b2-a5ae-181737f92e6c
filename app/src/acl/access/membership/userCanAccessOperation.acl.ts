import AppDataSource from "@/config/database.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import { userCanAccessManyOrganizations } from '@/acl/access/membership/userCanAccessManyOrganizations.acl.js';

const operationRepository =  AppDataSource.getRepository(OperationModel);
const userRepository =  AppDataSource.getRepository(UserModel);

export const userCanAccessOperation = async (userId:undefined|number|string|null, operationId:string|number|undefined|null) => {
	try{
		if(!userId || !operationId) return false;

		const operation = await operationRepository.findOne({
			where: { id: parseInt(operationId.toString()) },
			relations: ['organizations']
		}) as OperationModel | null;
		if(!operation) return false;

		const operationOrgIds = operation.organizations.map(org => org.id);

		return await userCanAccessManyOrganizations(userId, operationOrgIds);

	} catch(err){
		console.log("ERROR", err);
		return false;
	}

}
