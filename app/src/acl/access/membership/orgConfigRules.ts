
const orgConfigRules = {
	aoi: {
		title: "Areas of Interests (TAI/NAI)",
		rules: {
			delete: ['admin', 'org_admin', 'manager'],
			create: ['admin', 'org_admin', 'manager'],
			update: ['admin', 'org_admin', 'manager'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	asset: {
		title: "Assets of Operation",
		rules: {
			delete: ['admin', 'org_admin'],
			create: ['admin', 'org_admin'],
			update: ['admin', 'org_admin', 'manager'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	collectionCapability: {
		title: "Collection Capabilities",
		rules: {
			delete: ['admin', 'org_admin', 'manager'],
			create: ['admin', 'org_admin', 'manager'],
			update: ['admin', 'org_admin', 'manager'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	globalIsr: {
		title: "Global ISRs",
		rules: {
			delete: ['admin', 'org_admin'],
			create: ['admin', 'org_admin'],
			update: ['admin', 'org_admin'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	informationRequirement: {
		title: "Information Requests (IR)",
		rules: {
			delete: ['admin', 'org_admin','manager'],
			create: ['admin', 'org_admin', 'manager','user'],
			update: ['admin', 'org_admin','manager','user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	isr:{
		title: "ISRs",
		rules: {
			delete: ['admin', 'org_admin', 'manager'],
			create: ['admin', 'org_admin', 'manager', 'user'],
			update: ['admin', 'org_admin', 'manager', 'user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	isrTrack: {
		title: "ISR Tracks",
		rules: {
			delete: ['admin', 'org_admin', 'manager'],
			create: ['admin', 'org_admin', 'manager', 'user'],
			update: ['admin', 'org_admin', 'manager', 'user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	mission:{
		title: "Missions",
		rules: {
			delete: ['admin', 'org_admin','manager'],
			create: ['admin', 'org_admin', 'manager','user'],
			update: ['admin', 'org_admin','manager','user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	operation:{
		title: "Operation",
		rules: {
			delete: ['admin', 'org_admin'],
			create: ['admin', 'org_admin', 'manager','user'],
			update: ['admin', 'org_admin','manager','user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	organization:{
		title: "Organization",
		rules: {
			delete: ['admin'],
			create: ['admin'],
			update: ['admin', 'org_admin'],
			list: ['admin'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	originator:{
		title: "Originators for IR",
		rules: {
			delete: ['admin', 'org_admin'],
			create: ['admin', 'org_admin'],
			update: ['admin', 'org_admin', 'manager'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	pir:{
		title: "PIRs",
		rules: {
			delete: ['admin', 'org_admin','manager'],
			create: ['admin', 'org_admin', 'manager'],
			update: ['admin', 'org_admin', 'manager','user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		},
	},
	platform:{
		title: "Platforms (Temp DB)",
		rules: {
			delete: ['admin'],
			create: ['admin'],
			update: ['admin'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	rfi:{
		title: "Request For Information (RFI)",
		rules: {
			delete: ['admin', 'org_admin', 'manager', 'user'],
			create: ['admin', 'org_admin', 'manager', 'user'],
			update: ['admin', 'org_admin', 'manager','user'],
			list: ['admin', 'org_admin', 'manager', 'user'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	role:{
		title: "Roles (Organization)",
		rules: {
			delete: ['admin', 'org_admin'],
			create: ['admin', 'org_admin'],
			update: ['admin', 'org_admin'],
			list: ['admin', 'org_admin', 'manager'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	},
	users:{
		title: "Users",
		rules: {
			delete: ['admin', 'org_admin', 'manager'],
			create: ['admin', 'org_admin', 'manager'],
			update: ['admin', 'org_admin', 'manager'],
			list: ['admin', 'org_admin', 'manager'],
			read: ['admin', 'org_admin', 'manager', 'user']
		}
	}
}