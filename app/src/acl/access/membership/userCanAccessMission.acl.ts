import AppDataSource from "@/config/database.js";
import MissionModel from "@/models/mission.model.js";
import UserModel from "@/models/user.model.js";
import { userCanAccessManyOrganizations } from '@/acl/access/membership/userCanAccessManyOrganizations.acl.js';

const missionRepository = AppDataSource.getRepository(MissionModel);
const userRepository =  AppDataSource.getRepository(UserModel);

export const userCanAccessMission = async (userId:number|string|undefined, missionId:number|string|undefined):Promise<boolean> => {
	try{
		if(!userId || !missionId) return false;

		const mission = await missionRepository.findOne({
			where: { id: parseInt(missionId.toString()) },
			relations: ['operation', 'operation.organizations']
		}) as MissionModel | null;
		if(!mission) return false;
		const missionOrgId = mission.operation.organizations.map(org => org.id);
		return await userCanAccessManyOrganizations(userId, missionOrgId);

	} catch(err){
		console.log("ERROR", err);
		return false;
	}

}
