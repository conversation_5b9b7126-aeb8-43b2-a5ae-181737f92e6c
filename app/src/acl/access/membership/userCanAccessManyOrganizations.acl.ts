import UserModel from '@/models/user.model.js';
import AppDataSource from "@/config/database.js";
import OrganizationModel from "@/models/organization.model.js";
import { In } from 'typeorm';

const organizationRepository =  AppDataSource.getRepository(OrganizationModel);
const userRepository =  AppDataSource.getRepository(UserModel);

export const userCanAccessManyOrganizations = async (userId:number|string|undefined, organizationIds:number[]|undefined, refetchOrgs: boolean = false) => {
	try {
		if (!userId || !organizationIds) return false;

		const user = await userRepository.findOne({
			where: { id: parseInt(userId.toString()) },
			relations: ['roles']
		}) as UserModel | null;
		if(!user) return false;
		const userOrgIds = user.roles.map(role => role.organizationId);
		if(refetchOrgs){
			const organizations = await organizationRepository.find({
				where: { id: In(organizationIds) }
			}) as OrganizationModel[] | null;
			if(!organizations) return false;
			const allowedOrganizationIds = organizations.map(org => org.id);
			return allowedOrganizationIds.some(id => userOrgIds.includes(id));
		} else {
			return organizationIds.some(id => userOrgIds.includes(id));
		}

	} catch (err) {
		console.log("ERROR", err);
		return false;
	}
}