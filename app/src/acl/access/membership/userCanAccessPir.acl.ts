import UserModel from "@/models/user.model.js";
import PIRModel from "@/models/pir.model.js";
import AppDataSource from "@/config/database.js";
import { userCanAccessManyOrganizations } from '@/acl/access/membership/userCanAccessManyOrganizations.acl.js';
const pirRepository = AppDataSource.getRepository(PIRModel);
const userRepository = AppDataSource.getRepository(UserModel);

export const userCanAccessPir = async (userId:number|string|undefined, pirId:number|string|undefined):Promise<boolean> => {

	try {
		if (!userId || !pirId) return false;

		const pir = await pirRepository.findOne({
			where: {id: parseInt(pirId.toString())},
			relations: ['operation', 'operation.organizations']
		}) as PIRModel | null;
		if (!pir) return false;
		const operationOrgIds = pir.operation.organizations.map(org => org.id);
		return await userCanAccessManyOrganizations(userId, operationOrgIds);

	} catch (err) {
		console.log("ERROR", err);
		return false;
	}


}