import AppDataSource from "@/config/database.js";
import OrganizationModel from "@/models/organization.model.js";
import UserModel from "@/models/user.model.js";

const organizationRepository =  AppDataSource.getRepository(OrganizationModel);
const userRepository =  AppDataSource.getRepository(UserModel);

export const userCanAccessOrganization = async (userId:number|string|undefined, organizationId:number|string|undefined, neededRight:string[]|null = null) => {
	try {
		if (!userId || !organizationId) return false;

		const organization = await organizationRepository.findOne({
			where: {id: parseInt(organizationId.toString())}
		}) as OrganizationModel | null;
		if (!organization) return false;


		const user = await userRepository.findOne({
			where: { id: parseInt(userId.toString()) },
			relations: ['roles']
		}) as UserModel | null;
		if(!user) return false;

        if(neededRight){
            //fetch user.roles that have needed roleType in neededRight arra
            return user.roles.some(role => role.organizationId === organizationId  && neededRight.includes(role.roleType));
        }

		return user.roles.some(role => role.organizationId === organizationId);

	} catch (err) {
		console.log("ERROR", err);
		return false;
	}
}