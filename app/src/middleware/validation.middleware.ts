import {FastifyReply, FastifyRequest, FastifyError} from 'fastify';

export default async function validationMiddleware(error: FastifyError,request: FastifyRequest, reply: FastifyReply) {
	if(error.validation){
		let messages = error.validation.map((err) => ({
			message: `${err.message}`,
			type: 'error'
		}))
		return reply.code(422).send({
			success: false,
			data: null,
			error: error.message,
			messages: messages
		});
	} else {
		return reply.code(500).send({
			success: false,
			data: null,
			error: error.message,
			messages: []
		});
	}
}