import { FastifyReply, FastifyRequest } from 'fastify';

interface RequestWithIdParam extends FastifyRequest {
	params: {
		id: string;
	};
}

export default async function selfMiddleware(request: RequestWithIdParam, reply: FastifyReply) {
	try {
		const userId = request.requestingUser?.id;
		const parameterUserId = parseInt(request.params.id);
		if (userId !== parameterUserId) {
			return reply.code(403).send({
				success: false,
				data: null,
				error: 'Forbidden',
				messages: [{
					message: 'You are not authorized to access this resource',
					type: 'error'
				}]
			});
		}
	} catch (error) {
		return reply.code(500).send({
			success: false,
			data: null,
			error: 'Internal Server Error',
			messages: [{
				message: 'An unexpected error occurred',
				type: 'error'
			}]
		});
	}
}

// Extend FastifyRequest to include the userId property
declare module 'fastify' {
	interface FastifyRequest {
		userId?: number;
		organizationId?: number;
	}
}