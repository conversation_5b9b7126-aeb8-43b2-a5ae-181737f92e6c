import {FastifyReply, FastifyRequest} from 'fastify';

export default async function guestMiddleware(request: FastifyRequest, reply: FastifyReply) {
	try {
		const authHeader = request.headers.authorization;
		//token must not be here to proceed as a guest
		if (authHeader && authHeader.startsWith('Bearer ')) {
			return reply.code(401).send({
				success: false,
				data: null,
				error: 'Forbidden',
				messages: [{
					message: 'Cannot be logged in to access registration',
					type: 'error'
				}]
			});
		}
	} catch (error) {
		console.trace("ERROR", error);
		return reply.code(500).send({
			success: false,
			data: null,
			error: 'Server ERROR',
			messages: [{
				message: 'some random error not for your eyes.',
			}]
		});
	}
}