import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

export const setupErrorHandlers = (fastify: FastifyInstance) => {
	fastify.setNotFoundHandler((request: FastifyRequest, reply: FastifyReply) => {
		reply.code(404).send({
			success: false,
			data: null,
			error: {
				code: 404,
				message: 'Route not found',
				details: `The requested URL ${request.url} was not found on this server.`
			},
			messages: []
		});
	});
}