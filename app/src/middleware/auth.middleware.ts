import AppDataSource from "@/config/database.js";
import {FastifyReply, FastifyRequest} from 'fastify';
import {verifyToken} from '@/utils/jwt.utils.js';
import UserModel from "@/models/user.model.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";
import OrganizationModel from '@/models/organization.model.js';


export default async function authMiddleware(request: FastifyRequest, reply: FastifyReply) {
	try {
		const userRepository =  AppDataSource.getRepository(UserModel);
		const organizationRepository =  AppDataSource.getRepository(OrganizationModel);
		const authHeader = request.headers.authorization;
		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			return reply.code(401).send({
				success: false,
				data: null,
				error: 'No token provided',
				messages: [{
					message: 'No token provided for this user.',
					type: 'error'
				}]
			});
		}
		const token = authHeader.split(' ')[1];
		try {
			const parsedToken = verifyToken(token);
			if(!request.organizationId){
				request.organizationId = parseInt(parsedToken.organizationId);
			}
			const organizationId = parseInt(parsedToken.organizationId);
			//lets fetch user with roles with specific organizationId
			
			const userWithRoles = await userRepository.findOne({
				where: {
					id: parsedToken.userId,
					isActive: true,
					isVerified: true,
					roles: {
						organizationId: organizationId,
						isActive: true
					}
				},
				relations: ['roles']
			});

			const organization = await organizationRepository.findOne({
				where: {
					id: organizationId
				}
			}) as OrganizationModel;
			if(!organization){
				return reply.code(403).send({
					success: false,
					data: null,
					error: 'User not authorized for the organization',
					messages: [{
						message: 'User does not exist or lacks roles in the specified organization.',
						type: 'error'
					}]
				});
			}

			if (!userWithRoles) {
				return reply.code(403).send({
					success: false,
					data: null,
					error: 'User not authorized for the organization',
					messages: [{
						message: 'User does not exist or lacks roles in the specified organization.',
						type: 'error'
					}]
				});
			}

// If user exists and has matching roles, proceed. 
			request.requestingUser = {
				id: userWithRoles.id,
				email: userWithRoles.email.toLowerCase(),
				accountType: userWithRoles.accountType,
				defaultRules: organization.config.defaultRules,
				roles: userWithRoles.roles.map(role => ({
					id: role.id,
					staffDesignation: role.staffDesignation,
					roleName: role.roleName,
					isCommandRole: role.isCommandRole,
					isStaffRole: role.isStaffRole,
					organizationId: role.organizationId,
					isActive: role.isActive
				}))
			}
			
			
		} catch (error) {
			console.trace("ERROR", error);
			//token expired or invalid
			return reply.code(401).send({
				success: false,
				data: null,
				error: 'Invalid or expired token',
				messages: [{
					message: 'Invalid or expired token',
					type: 'error'
				}]
			});
		}
	} catch (error) {
		console.trace("AUTH ERROR", error);
		reply.code(401).send({
			success: false,
			data: null,
			error: 'Invalid or expired token',
			messages: [{
				message: 'Invalid or expired token',
				type: 'error'
			}]
		});
	}
}

// Extend FastifyRequest to include the user property
declare module 'fastify' {
	interface FastifyRequest {
		requestingUser?: IRequestingUser
		organizationId?: number;
	}
}