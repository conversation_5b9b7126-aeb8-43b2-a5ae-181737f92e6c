import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>o<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	ManyToMany,
	<PERSON><PERSON><PERSON>able, OneToMany
} from "typeorm";
import RoleModel from "@/models/role.model.js";
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";


@Entity('organizations')
export default class OrganizationModel extends BaseEntity {

	@Column({
		name: "name",
		length: 255,
		type: 'varchar'
	})
	name!: string;

	@Column({
		name: "description",
		length: 255,
		type: 'varchar'
	})
	description!: string;

	@Column({
		name: "is_military",
		type: 'boolean'
	})
	isMilitary!: boolean;

	@Column({
		name: "designation",
		type: 'int',
		unique: true
	})
	designation!: number;

	@Column({
		name: "logo_url",
		length: 255,
		type: 'varchar',
	})
	logoUrl!: string|null;

	@Column({
		name: "alpha_2_country_code",
		length: 2,
		type: 'varchar',
		nullable: true
	})
	isoCountryCode!: string | null;

	@Column({
		name: "sovereignty",
		length: 255,
		type: 'varchar'
	})
	sovereignty!: string;

	//add config column
	@Column({
		name: "config",
		type: 'jsonb',
		nullable: true
	})
	config!: any;

	//add roles relationship one organization has many roles but one role can only belong to one organization
	@OneToMany(() => RoleModel, role => role.organization)
	roles!: RoleModel[];

	@ManyToMany(() => OperationModel)
	operations?: OperationModel[];

	getSearchableFields(): string[] {
		return ['name', 'description'];
	}

}
