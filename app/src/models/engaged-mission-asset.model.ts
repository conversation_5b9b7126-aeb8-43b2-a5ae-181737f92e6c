import {
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>um<PERSON>
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import AssetModel from "@/models/asset.model.js";
import MissionModel from "@/models/mission.model.js";
import ISRTrackModel from "@/models/isr-track.model.js";

@Entity("engaged_mission_assets")
export default class EngagedMissionAssetModel extends BaseEntity {
    // Remove the redundant id declaration - it's already in BaseEntity

    @Column({
        name: "asset_id",
        type: "int"
    })
    assetId!: number;

    @Column({
        name: "mission_id",
        type: "int"
    })
    missionId!: number;

    @Column({
        name: "isr_track_id",
        type: "int",
        nullable: true
    })
    isrTrackId!: number | null;

    @Column({
        name: "start_at",
        type: 'timestamp',
        nullable: true
    })
    startAt!: Date | null;

    @Column({
        name: "end_at",
        type: 'timestamp',
        nullable: true
    })
    endAt!: Date | null;

    @ManyToOne(() => AssetModel, asset => asset.engagedMissionAssets, { eager: true })
    @JoinColumn({ name: "asset_id" })
    asset!: AssetModel;

    @ManyToOne(() => MissionModel, mission => mission.engagedMissionAssets, { eager: true })
    @JoinColumn({ name: "mission_id" })
    mission!: MissionModel;

    @ManyToOne(() => ISRTrackModel, isrTrack => isrTrack.engagedMissionAssets, { eager: true })
    @JoinColumn({ name: "isr_track_id" })
    isrTrack!: ISRTrackModel;
}