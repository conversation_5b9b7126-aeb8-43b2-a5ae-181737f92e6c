import {BaseHardDeleteEntity} from "@/models/base-hard-delete.entity.js";
import {BeforeInsert, Column, Entity, Index, JoinColumn, ManyToOne} from "typeorm";
import AssetModel from "@/models/asset.model.js";
import UserModel from "@/models/user.model.js";
import MissionModel from "@/models/mission.model.js";
import {ISRStatus, Priority} from "@/interfaces/database.interface.js";
import AppDataSource from "@/config/database.js";


@Entity("isrs")
@Index("IDX_ISR_DESIGNATION_UNIQUE", ["designation", "missionId", "assetId"], { unique: true })

export default class ISRModel extends BaseHardDeleteEntity {

	@Column({
		name: "created_by_user_id",
		type: "int"
	})
	createdByUserId!: number;

	@Column({
		name: "asset_id",
		type: "int"
	})
	assetId!: number;

	@Column({
		name: "designation",
		type: "varchar",
		length: 255,
	})
	designation!: string;

	@Column({
		name: "label",
		type: "varchar",
		length: 255,
		nullable: true
	})
	label!: string | null;

	@Column({
		name: "mission_id",
		type: "int",
	})
	missionId!: number;

	@Column({
		name: "type",
		type: "enum",
		enum: ["line", "point", "polygon"],
		default: "line"
	})
	type!: "line" | "point" | "polygon";

	@Column({
		name: "coordinates",
		type: "geometry",
		spatialFeatureType: 'GeometryCollection',
		srid: 4326,
		nullable: true
	})
	coordinates!: {
		type: "GeometryCollection",
		geometries: Array<{
			type: "Point" | "LineString" | "Polygon",
			coordinates: number[][] | number[][][] | number[][][][]
		}>
	} | null;

	@Column({
		name: "zoom",
		type: "integer",
		nullable: true
	})
	zoom!: number | null;

	@Column({
		name: "center_coordinates",
		type: "geometry",
		spatialFeatureType: 'Point',
		srid: 4326,
		nullable: true
	})
	centerCoordinates!: any | null;

	//status
	@Column({
		name: "status",
		type: "enum",
		enum: ISRStatus,
		default: ISRStatus.CREATED,
		nullable: true
	})
	status!: ISRStatus;

	//priority
	@Column({
		name: "priority",
		type: "enum",
		enum: Priority,
		default: Priority.NONE,
		nullable: true
	})
	priority!: Priority;

	//commence_at
	@Column({
		name: "commence_at",
		type: "timestamp",
		nullable: true
	})
	commenceAt!: Date | null;

	//conclude_at
	@Column({
		name: "conclude_at",
		type: "timestamp",
		nullable: true
	})
	concludeAt!: Date | null;

	//ltiov_date
	@Column({
		name: "ltiov_date",
		type: "timestamp",
		nullable: true
	})
	ltiovDate!: Date | null;

	@Column({
		name: "is_editable",
		type: "boolean",
		default: true
	})
	isEditable!: boolean;

	@Column({
		name: "is_global",
		type: "boolean",
		default: false
	})
	isGlobal!: boolean;

	getSearchableFields(): string[] {
		return ['designation', 'label', 'type', 'coordinates'];
	}

	@ManyToOne(() => AssetModel, asset => asset.collectionCapabilities)
	@JoinColumn({ name: "asset_id" })
	asset!: AssetModel;

	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "created_by_user_id" })
	createdByUser!: UserModel;

	@ManyToOne(() => MissionModel)
	@JoinColumn({ name: "mission_id" })
	mission!: MissionModel;

	@BeforeInsert()
	async generateDesignation() {
		if (!this.assetId || !this.missionId) {
			throw new Error('Asset ID and Mission ID are required to generate designation');
		}

		try {
			const entityManager = AppDataSource.manager;

			// Load asset and mission
			type EntityWithDesignation = { designation: string | null };

			// Type the promises explicitly
			const assetPromise: Promise<EntityWithDesignation | null> = entityManager.findOne<AssetModel>(AssetModel, {
				where: { id: this.assetId },
				select: { designation: true }
			});

			const missionPromise: Promise<EntityWithDesignation | null> = entityManager.findOne<MissionModel>(MissionModel, {
				where: { id: this.missionId },
				select: { designation: true }
			});

			//@ts-ignore
			const [asset, mission] = await Promise.all<[EntityWithDesignation | null, EntityWithDesignation | null]>([assetPromise, missionPromise]);

			if (asset && mission && asset.designation && mission.designation) {

				// Find the highest N number for this asset-mission combination
				const lastISR = await entityManager.createQueryBuilder(ISRModel, "isr")
					.where("isr.assetId = :assetId AND isr.missionId = :missionId", {
						assetId: this.assetId,
						missionId: this.missionId
					})
					.orderBy("isr.id", "DESC")
					.getOne();

				let nextNumber = 1;
				if (lastISR?.designation) {
					const parts = lastISR.designation.split('-');
					const lastNumber = parseInt(parts[parts.length - 1]);
					nextNumber = isNaN(lastNumber) ? 1 : lastNumber + 1;
				}

				// Generate the new designation
				this.designation = `${asset.designation}-${mission.designation}-${nextNumber}`;
			}





		} catch (error: any) {
			console.error('Error generating ISR designation:', error);
			throw new Error(`Failed to generate ISR designation: ${error.message}`);
		}
	}
}

