import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	ManyToOne,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index,
	OneToMany, JoinTable, ManyToMany, BeforeInsert
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import { MissionStatus, Priority} from "@/interfaces/database.interface.js";
import AoiModel from "@/models/aoi.model.js";
import AssetModel from "@/models/asset.model.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import ISRModel from "@/models/isr.model.js";
import AppDataSource from "@/config/database.js";
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";
import ISRTrackModel from "@/models/isr-track.model.js";

@Entity("missions")
export default class MissionModel extends BaseEntity {

	@Column({
		name: "name", //First Letters of operation Name + number of missions incremented + designation on ops
		length: 255,
		type: 'varchar'
	})
	name!: string | null;

	@Column({
		name: "description",
		length: 255,
		type: 'varchar'
	})
	description!: string | null;

	@Column({
		name: "designation",
		type: 'varchar',
		// unique: true,
		nullable: true
	})
	designation!: string | null;

	@Column({
		name: "type",
		length: 255,
		type: 'varchar',
		nullable: true
	})
	type!: string | null;

	@Column({
		name: "classification",
		length: 255,
		type: 'varchar',
		nullable: true
	})
	classification!: string | null;

	@Column({
		name: "priority",
		type: 'enum',
		enum: Priority,
		default: Priority.NONE
	})
	priority!: Priority;

	@Column({
		name: "report_type_configuration",
		type: 'jsonb',
		nullable: true
	})
	reportTypeConfiguration!: any | null;

	@Column({
		name: "start_at",
		type: 'timestamp',
		nullable: true
	})
	startAt!: Date | null;

	@Column({
		name: "end_at",
		type: 'timestamp',
		nullable: true
	})
	endAt!: Date | null;

	@Column({
		name: "status",
		type: 'enum',
		enum: MissionStatus,
		default: MissionStatus.PENDING_APPROVAL
	})
	status!: MissionStatus;

	@Column({
		name: "indicators_description",
		type: 'text',
		nullable: true
	})
	indicatorsDescription!: string | null;

	@Column({
		name: "warnings_description",
		type: 'text',
		nullable: true
	})
	warningsDescription!: string | null;

	@Column({
		name: "requested_by_user_id",
		type: "int"
	})
	requestedByUserId!: number;

	@Column({
		name: "approved_by_user_id",
		type: "int",
		nullable: true
	})
	approvedByUserId!: number | null;

	@Column({
		name: "operation_id",
		type: "int",
		nullable: false
	})
	operationId!: number | null;

	@ManyToOne(() => OperationModel, operation => operation.missions)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	@ManyToOne(() => UserModel, user => user.requestedMissions)
	@JoinColumn({ name: "requested_by_user_id" })
	requestedByUser!: UserModel;

	@ManyToOne(() => UserModel, user => user.approvedMissions)
	@JoinColumn({ name: "approved_by_user_id" })
	approvedByUser!: UserModel;

	@OneToMany(() => ISRModel, isr => isr.mission)
	isrs!: ISRModel[];

	@ManyToMany(() => AoiModel, tai => tai.missions)
	@JoinTable({
		name: "areas_of_interest_missions",
		joinColumn: {
			name: "area_of_interest_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "mission_id",
			referencedColumnName: "id"
		}
	})
	tais!: AoiModel[];

	@ManyToMany(() => AssetModel, (asset) => asset.missions)
	@JoinTable({
		name: "assets_missions",
		joinColumn: {
			name: "mission_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "asset_id",
			referencedColumnName: "id"
		}
	})
	assets!: AssetModel[];

	@ManyToMany(() => InformationRequirementModel, (ir) => ir.missions)
	@JoinTable({
		name: "information_requirements_missions",
		joinColumn: {
			name: "mission_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "ir_id",
			referencedColumnName: "id"
		}
	})
	informationRequirements!: InformationRequirementModel[];

    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.mission)
    engagedMissionAssets!: EngagedMissionAssetModel[];


    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.asset)
    engagedAssets!: AssetModel[];

    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.isrTrack)
    engagedIsrTracks!: ISRTrackModel[];


	getSearchableFields(): string[] {
		return ['name', 'description'];
	}


	@BeforeInsert()
	async generateDesignation() {
		if (!this.operationId) {
			throw new Error('Need operation ID to generate designation');
		}

		try {
			const entityManager = AppDataSource.manager;

			// Load asset and mission
			type EntityWithDesignation = { designation: string | null };

			// Type the promises explicitly
			const operation = await entityManager.findOne<OperationModel>(OperationModel, {
				where: { id: this.operationId }
			});

			if (operation && operation.designation) {

				// Find the highest N number for this operation-mission combination
				// const lastMission = await entityManager.createQueryBuilder(MissionModel, "missions")
				// 	.where("missions.operationId = :operationId AND missions.missionId = :missionId", {
				// 		operationId: this.operationId,
				// 	})
				// 	.orderBy("missions.id", "DESC")
				// 	.getOne();
				const lastMission = await entityManager.findOne<MissionModel>(MissionModel, {
					where: {
						operationId: this.operationId,
					},
					order: {
						id: 'DESC'
					}
				});

				let nextNumber = 1;
				if (lastMission?.designation) {
					const parts = lastMission.designation.split('-');
					const lastNumber = parseInt(parts[parts.length - 1]);
					nextNumber = isNaN(lastNumber) ? 1 : lastNumber + 1;
				}

				// Generate the new designation
				this.designation = `${operation.designation}-${nextNumber}`;
			} else {
				throw new Error('Operation is required to generate designation');
			}




		} catch (error) {
			console.error('Error generating Mission designation:', error);
			throw new Error(`Failed to generate Mission designation`);
		}
	}
}