import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>o<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index, BeforeInsert, ManyToMany, Join<PERSON><PERSON>,
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import PIRModel from "@/models/pir.model.js";
import UserModel from "@/models/user.model.js";

import {Priority} from "@/interfaces/database.interface.js";
import AppDataSource from "@/config/database.js";
import MissionModel from "@/models/mission.model.js";

@Entity("information_requirements")
@Index("IDX_IR_NUMBER_PIR_UNIQUE", ["designation", "pirId"], { unique: true })
export default class InformationRequirementModel extends BaseEntity {
	@Column({
		name: "ir_number",
		type: 'integer',
	})
	irNumber!: number;

	@Column({
		name: "designation",
		type: "varchar",
		length: 255,
		nullable: true
	})
	designation!: string | null;

	@Column({
		name: "originator",
		length: 255,
		type: 'varchar',
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	originator!: string;

	@Column({
		name: "information_requirement",
		length: 1200,
		type: 'varchar',
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	informationRequirement!: string;

	@Column({
		name: "ltiov_date",
		type: 'timestamp'
	})
	ltiovDate!: Date;

	@Column({
		name: "priority",
		type: 'enum',
		enum: Priority,
		default: Priority.NONE
	})
	priority!: Priority;

	@Column({
		name: "pir_id",
		type: "int"
	})
	pirId!: number;

	@Column({
		name: "created_by_user_id",
		type: "int"
	})
	createdByUserId!: number;

	@Column({
		name: "is_answered",
		type: 'boolean',
		default: false
	})
	isAnswered!: boolean;

	@ManyToOne(() => PIRModel, pir => pir.informationRequirements)
	@JoinColumn({ name: "pir_id" })
	pir!: PIRModel;

	@ManyToOne(() => UserModel, user => user.createdInformationRequirements)
	@JoinColumn({ name: "created_by_user_id" })
	createdByUser!: UserModel;

	@ManyToMany(() => MissionModel , mission => mission.informationRequirements)
	// @JoinTable({
	// 	name: "information_requirements_missions",
	// 	joinColumn: {
	// 		name: "information_requirement_id",
	// 		referencedColumnName: "id"
	// 	},
	// 	inverseJoinColumn: {
	// 		name: "mission_id",
	// 		referencedColumnName: "id"
	// 	}
	// })
	missions!: MissionModel[];

	getSearchableFields(): string[] {
		return ['originator', 'informationRequirement'];
	}

	@BeforeInsert()
	async setIrNumber() {
		const entityManager = AppDataSource.manager;
		const pir = await entityManager.createQueryBuilder(PIRModel, "pir")
			.where("pir.id = :pirId", { pirId: this.pirId })
			.getOne();
		if(!pir) {
			throw new Error("PIR not found");
		}
		const ir = await entityManager.createQueryBuilder(InformationRequirementModel, "ir")
			.where("ir.pirId = :pirId", { pirId: this.pirId })
			.orderBy("ir.irNumber", "DESC")
			.getOne();
		const nextSequenceNumber = ir?.irNumber ? ir.irNumber + 1 : 1;
		this.irNumber = nextSequenceNumber;
		this.designation = `${pir.pirNumber}.${nextSequenceNumber}`;



	}
}