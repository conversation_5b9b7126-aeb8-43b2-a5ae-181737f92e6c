import { BaseEntity } from "@/models/base.entity.js";
import {BeforeInsert, BeforeUpdate, Column, Entity, Index} from "typeorm";
import { Point, Polygon, LineString } from 'geojson';
import {BorderType, GeometryType, RGBAColor, SupportedGeometry} from "@/interfaces/database.interface.js";

// // Define geometry types
// export type GeometryType = 'Point' | 'Polygon' | 'LineString';
//
// // Define supported geometry union type
// export type SupportedGeometry = Point | Polygon | LineString;

@Entity("map_elements")
@Index('idx_map_element_spatial', ['element'], { spatial: true })  // Add the column name here
export default class MapElementModel extends BaseEntity {
	@Column({
		name: "element",
		type: "geometry",
		spatialFeatureType: "geometry",
		srid: 4326,
		nullable: true
	})
	element!: SupportedGeometry | null;

	@Column({
		name: "element_type",
		type: "varchar",
		length: 20,
		nullable: false
	})
	elementType!: GeometryType;

	@Column({
		name: "element_color",
		type: "int",
		array: true,
		default: [0, 0, 0, 255]
	})
	elementColor!: RGBAColor;

	@Column({
		name: "border_type",
		type: "enum",
		enum: BorderType,
		default: BorderType.SOLID
	})
	borderType!: BorderType;

	@Column({
		name: "border_thickness",
		type: "integer",
		default: 2
	})
	borderThickness!: number;

	@Column({
		name: "border_color",
		type: "int",
		array: true,
		default: [0, 0, 0, 255]
	})
	borderColor!: RGBAColor;

	@BeforeInsert()
	@BeforeUpdate()
	validateGeometry() {
		if (this.element) {
			if (this.element.type !== this.elementType) {
				throw new Error('Element type mismatch');
			}
		}
	}

	@BeforeInsert()
	@BeforeUpdate()
	validateColors() {
		if (!this.isValidRGBA(this.elementColor)) {
			throw new Error('Invalid element color format');
		}
		if (!this.isValidRGBA(this.borderColor)) {
			throw new Error('Invalid border color format');
		}
	}

	@BeforeInsert()
	@BeforeUpdate()
	validateBorderThickness() {
		if (this.borderThickness < 0) {
			throw new Error('Border thickness must be non-negative');
		}
	}

	private isValidRGBA(color: RGBAColor): boolean {
		if (!Array.isArray(color) || color.length !== 4) {
			return false;
		}
		return color.every(value =>
			Number.isInteger(value) &&
			value >= 0 &&
			value <= 255
		);
	}
}

// // Type guard helper functions
// export function isPoint(geometry: SupportedGeometry): geometry is Point {
// 	return geometry.type === 'Point';
// }
//
// export function isPolygon(geometry: SupportedGeometry): geometry is Polygon {
// 	return geometry.type === 'Polygon';
// }
//
// export function isLineString(geometry: SupportedGeometry): geometry is LineString {
// 	return geometry.type === 'LineString';
// }