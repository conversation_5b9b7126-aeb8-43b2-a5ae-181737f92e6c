import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>To<PERSON>any, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, ManyToMany, <PERSON>in<PERSON>able, Index,
} from "typeorm";
import {Point, Polygon } from 'geojson';  // Add this import
import { BaseEntity } from "@/models/base.entity.js";
import OperationsUsersModel from "@/models/operations-users.model.js";
import { OperationType, ITimezoneType} from "@/interfaces/database.interface.js";
import AssetModel from "@/models/asset.model.js";
import PIRModel from "@/models/pir.model.js";
import AoiModel from "@/models/aoi.model.js";
import MissionModel from "@/models/mission.model.js";
import GlobalISRModel from "@/models/global-isr.model.js";
import MapElementModel from "@/models/map-element.model.js";
import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import OrganizationModel from "@/models/organization.model.js";
import ISRTrackModel from "@/models/isr-track.model.js";
import TaskModel from "@/models/task.model.js";

@Entity("operations")
@Index("IDX_DESIGNATION_ORG_ID_UNIQUE", ["designation", "locationCoordinates", "location"], { unique: true })
export default class OperationModel extends BaseEntity {
	@Column({
		name: "name",
		length: 255,
		type: 'varchar'
	})
	name!: string | null;

	@Column({
		name: "location_coordinates",
		type: "geometry",
		spatialFeatureType: "Point",
		srid: 4326,
		nullable: true
	})
	locationCoordinates!: Point | null;

	@Column({
		name: "zoom",
		type: "float",
		default: 5,
	})
	zoom!: number;

	//area of operation
	@Column({
		name: "area_of_operation",
		type: "geometry",
		spatialFeatureType: "Polygon",
		srid: 4326,
		nullable: true
	})
	areaOfOperation!: Polygon | null;

	//tactical_area_of_responsibility
	@Column({
		name: "tactical_area_of_responsibility",
		type: "geometry",
		spatialFeatureType: "Polygon",
		srid: 4326,
		nullable: true
	})
	tacticalAreaOfResponsibility!: Polygon | null;

	@Column({
		name: "location",
		length: 1000,
		type: 'varchar',
		nullable: true
	})
	location!: string | null;

	@Column({ name: "map_element_id", nullable: true, type: "int" })
	mapElementId?: number;

	@ManyToOne(() => MapElementModel, { eager: true})
	@JoinColumn({ name: "map_element_id" })
	mapElement?: MapElementModel;

	@Column({
		name: "designation",
		length: 255,
		type: 'varchar'
	})
	designation!: string;

	@Column({
		name: "description",
		length: 1200,
		type: 'varchar'
	})
	description!: string | null;

	@Column({
		name: "is_active",
		type: 'boolean',
		default: false,
	})
	isActive!: boolean;

	//Operation Type
	// Conventional
	// Unconventional
	// Hybrid
	// HADR
	@Column({
		name: "type",
		type: 'enum',
		enum: OperationType,
		default: 'conventional'
	})
	type!: OperationType;

	//timezones jsonb
	@Column({
		name: "timezones",
		type: 'jsonb',
		nullable: true
	})
	timezones!: ITimezoneType[] | null;

	// config jsonb
	@Column({
		name: "config",
		type: 'jsonb',
		nullable: true
	})
	config!: any | null;

	@ManyToMany(() => OrganizationModel)
	@JoinTable({
		name: "operation_organization",
		joinColumn: {
			name: "operation_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "organization_id",
			referencedColumnName: "id"
		}
	})
	organizations!: OrganizationModel[];

	@OneToMany(() => OperationsUsersModel, operationUser => operationUser.operation)
	users!: OperationsUsersModel[];

	@OneToMany(() => AssetModel, asset => asset.operation)
	assets!: AssetModel[];

	@OneToMany(() => PIRModel, pir => pir.operation, {eager: true})
	pirs!: PIRModel[];

	//Collection Capabilities
	@ManyToOne(() => CollectionCapabilityModel, collectionCapability => collectionCapability.operation)
	collectionCapabilities!: CollectionCapabilityModel[];

	@OneToMany(() => AoiModel, areasOfInterest => areasOfInterest.operation, {eager: true})
	areasOfInterest!: AoiModel[];

	@OneToMany(() => MissionModel, mission => mission.operation, {eager: true})
	missions!: MissionModel[];

	@OneToMany(() => GlobalISRModel, globalIsr => globalIsr.operation, {eager: true})
	globalIsrs!: GlobalISRModel[];

	//IsrTracks
	@OneToMany(() => ISRTrackModel, isrTrack => isrTrack.operation)
	isrTracks!: ISRTrackModel[];

	@OneToMany(() => TaskModel, task => task.operation)
	tasks!: TaskModel[];

	getSearchableFields(): string[] {
		return ['name', 'locationText', 'designation', 'description'];
	}
}