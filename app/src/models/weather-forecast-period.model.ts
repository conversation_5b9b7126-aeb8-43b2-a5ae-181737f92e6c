import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} from "typeorm";
import { WeatherForecastBaseModel } from "@/models/weather-forecast-base.model.js";
import { PrecipitationProbability } from 'interfaces/admin/admin.weather.interface.js'
import WeatherForecastModel from "@/models/weather-forecast.model.js";


@Entity("weather_forecast_periods")
export default class WeatherForecastPeriodModel extends WeatherForecastBaseModel {
	@Column({
		name: "name",
		type: "varchar",
		length: 100
	})
	name!: string;

	@Column({
		name: "is_daytime",
		type: "boolean"
	})
	isDaytime!: boolean;

	@Column({
		name: "temperature",
		type: "int"
	})
	temperature!: number;

	@Column({
		name: "temperature_unit",
		type: "varchar",
		length: 10
	})
	temperatureUnit!: string;

	@Column({
		name: "temperature_trend",
		type: "varchar",
		length: 50,
		nullable: true
	})
	temperatureTrend!: string | null;

	@Column({
		name: "probability_of_precipitation",
		type: "jsonb",
		nullable: true
	})
	probabilityOfPrecipitation!: PrecipitationProbability | null;

	@Column({
		name: "wind_speed",
		type: "varchar",
		length: 50
	})
	windSpeed!: string;

	@Column({
		name: "wind_direction",
		type: "varchar",
		length: 50
	})
	windDirection!: string;

	@Column({
		name: "icon",
		type: "varchar",
		length: 255
	})
	icon!: string;

	@Column({
		name: "short_forecast",
		type: "text"
	})
	shortForecast!: string;

	@Column({
		name: "detailed_forecast",
		type: "text"
	})
	detailedForecast!: string;

	@Column({
		name: "weather_forecast_id",
		type: "int"
	})
	weatherForecastId!: number;

	@ManyToOne(() => WeatherForecastModel, weatherForecast => weatherForecast.forecasts)
	@JoinColumn({ name: "weather_forecast_id" })
	weatherForecast!: WeatherForecastModel;

	getSearchableFields(): string[] {
		return ['name', 'shortForecast', 'detailedForecast'];
	}
}