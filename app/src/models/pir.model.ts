import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index, OneToMany, BeforeInsert,
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import AppDataSource from "@/config/database.js";
import {Priority} from "@/interfaces/database.interface.js";

@Entity("pirs")
@Index("IDX_DESIGNATION_UNIQUE", ["designation", "question"], { unique: true })
export default class PIRModel extends BaseEntity {


	@Column({
		name: "question",
		length: 1200,
		type: 'varchar',
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	question!: string | null;

	@Column({
		name: "pir_number",
		type: 'integer',
	})
	pirNumber!: number | null;

	@Column({
		name: "description",
		length: 1200,
		type: 'varchar'
	})
	description!: string | null;

	@Column({
		name: "designation",
		length: 255,
		type: 'varchar',
	})
	designation!: string | null;

	@Column({
		name: "originator",
		length: 255,
		type: 'varchar',
		default: '',
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	originator!: string;

	@Column({
		name: "is_active",
		type: 'boolean',
		default: false,
	})
	isActive!: boolean;

	//user id
	@Column({
		name: "operation_id",
		type: "int"
	})
	operationId!: number | null;

	//priority
	@Column({
		name: "priority",
		type: 'enum',
		enum: Priority,
		default: Priority.MEDIUM
	})
	priority!: Priority;

	@ManyToOne(() => OperationModel)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	@OneToMany(() => InformationRequirementModel, ir => ir.pir)
	informationRequirements!: InformationRequirementModel[];


	@BeforeInsert()
	async generateDesignation() {

		try {
			if (!this.operationId) {
				console.error('Operation ID is required to generate designation');
				throw new Error('Operation ID are required to generate designation');
			}
			// Get the EntityManager instance
			const entityManager = AppDataSource.manager;
			console.log('EntityManager:', entityManager);
			// Load related entities using the EntityManager
			const [ operation] = await Promise.all([
				entityManager.findOneBy(OperationModel, { id: this.operationId })
			]);

			console.log('Operation:', operation);
			//sort by pir number and grab the last one
			const lastPir = await entityManager.find(PIRModel, {
				where: {
					operationId: this.operationId
				},
				order: {
					pirNumber: "DESC"
				},
				take: 1
			});
			console.log('Last PIR:', lastPir);
			const lastPirNumber = (lastPir.length > 0 && lastPir[0].pirNumber) ? lastPir[0].pirNumber : 0;
			console.log('Last PIR Number:', lastPirNumber);
			this.pirNumber = lastPirNumber + 1;
			console.log('New PIR Number:', this.pirNumber);
			if (!operation?.designation) {
				throw new Error('Operation designation is required');
			}
			console.log('Operation Designation:', operation.designation);
			// Generate the designation
			this.designation = `${operation.designation}-PIR:${this.pirNumber}`;
			this.description = this.question?.substring(0, 50) + '...';
			console.log('New Designation:', this.designation);
		} catch (error) {
			console.trace(error);
			throw new Error(`Failed to generate designation`);
		}
	}

	getSearchableFields(): string[] {
		return [];
	}
}