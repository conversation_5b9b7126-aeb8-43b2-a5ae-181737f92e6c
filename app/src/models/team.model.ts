import {
	<PERSON><PERSON>ty,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	Index,
	BeforeInsert,
	BeforeUpdate,
	ManyToMany,
	JoinTable,
	DeleteDateColumn
} from "typeorm";
import UserModel from "@/models/user.model.js";
import {BaseEntity} from "@/models/base.entity.js";

@Entity("teams")
export default class TeamModel extends BaseEntity  {

	@Column({
		name: "name",
		length: 255,
		type: 'varchar'
	})
	name!: string;

	@Column({
		name: "description",
		length: 255,
		type: 'varchar'
	})
	description!: string;

	@ManyToMany(() => UserModel, user => user.teams)
	@JoinTable({
		name: "team_users", // This will be the name of the junction table
		joinColumn: {
			name: "team_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "user_id",
			referencedColumnName: "id"
		}
	})
	users!: UserModel[];

	getSearchableFields(): string[] {
		return ['name', 'description'];
	}

}
