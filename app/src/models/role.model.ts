// app/src/models/role.model.ts

import {
	En<PERSON><PERSON>,
	Column,
	ManyToMany, JoinTable, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import UserModel from "@/models/user.model.js";
import OrganizationModel from "@/models/organization.model.js";

@Entity("roles")
export default class RoleModel extends BaseEntity {

	@Column({
		name: "role_name",
		length: 100,
		type: 'varchar'
	})
	roleName!: string;

	@Column({
		name: "role_description",
		type: 'text',
		nullable: true
	})
	roleDescription!: string | null;

	@Column({
		name: "unit_level",
		length: 50,
		type: 'varchar',
		nullable: true
	})
	unitLevel!: string | null;

	@Column({
		name: "staff_designation",
		length: 10,
		type: 'varchar',
		nullable: true
	})
	staffDesignation!: string | null;

	@Column({
		name: "functional_area",
		length: 50,
		type: 'varchar',
		nullable: true
	})
	functionalArea!: string | null;

	@Column({
		length: 50,
		type: 'varchar',
		nullable: true
	})
	rank!: string | null;

	@Column({
		name: "is_command_role",
		type: 'boolean',
		default: false
	})
	isCommandRole!: boolean;

	@Column({
		name: "is_staff_role",
		type: 'boolean',
		default: false
	})
	isStaffRole!: boolean;

	@Column({
		name: "typical_unit_size",
		type: 'int',
		nullable: true
	})
	typicalUnitSize!: number | null;

	@Column({
		name: "minimum_qualification",
		type: 'text',
		nullable: true
	})
	minimumQualification!: string | null;

	@Column({
		name: "special_skills_required",
		type: 'text',
		nullable: true
	})
	specialSkillsRequired!: string | null;

	@Column({
		name: "can_be_additional_duty",
		type: 'boolean',
		default: false
	})
	canBeAdditionalDuty!: boolean;

	@Column({
		name: "service_branch",
		length: 50,
		type: 'varchar',
		nullable: true
	})
	serviceBranch!: string | null;

	@Column({
		name: "country_or_organization",
		length: 100,
		type: 'varchar',
		nullable: true
	})
	countryOrOrganization!: string | null;

	//isSystemRole
	@Column({
		name: "is_system_role",
		type: 'boolean',
		default: false
	})
	isSystemRole!: boolean;

    @Column({
        name: "role_type",
        nullable: false,
        default: 'member',
        type: 'enum',
        enum: ['org_admin', 'manager', 'member', 'analyst', 'external', 'vendor']
    })
    roleType!: 'org_admin'| 'manager'| 'member'| 'analyst'| 'external'| 'vendor';

	@ManyToOne(() => OrganizationModel, organization => organization.roles)
	@JoinColumn({ name: "organization_id" })
	organization!: OrganizationModel | null;

	@Column({
		name: "organization_id",
		type: 'uuid',
	})
	organizationId!: number | null;

	@Column({
		name: "is_active",
		type: 'boolean',
		default: true
	})
	isActive!: boolean;

	@ManyToMany(() => RoleModel, role => role.subordinateRoles)
	@JoinTable({
		name: "role_hierarchies",
		joinColumn: {
			name: "superior_role_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "subordinate_role_id",
			referencedColumnName: "id"
		}
	})
	superiorRoles!: RoleModel[];

	@ManyToMany(() => RoleModel, role => role.superiorRoles)
	subordinateRoles!: RoleModel[];

	@ManyToMany(() => UserModel, user => user.roles)
	users!: UserModel[];

	getSearchableFields(): string[] {
		return ['role_name', 'staff_designation', 'role_description'];
	}

}