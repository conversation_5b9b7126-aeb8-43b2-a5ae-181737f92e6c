import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>To<PERSON>ne,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index,
	OneToMany, ManyToMany
} from "typeorm";
import { Point, Polygon } from 'geo<PERSON><PERSON>';
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import MissionModel from "@/models/mission.model.js";
import MapElementModel from "@/models/map-element.model.js";

@Entity("areas_of_interest")
@Index("IDX_AOI_DESIGNATION_OPERATION_UNIQUE", ["designation", "operationId", "isTargetable"], { unique: true })
export default class AoiModel extends BaseEntity {
	@Column({
		name: "designation",
		type: "int",
		transformer: {
			to: (value: number) => Number(value),
			from: (value: number) => value,
		}
	})
	designation!: number;

	@Column({
		name: "name",
		type: "varchar",
		length: 200,
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	name!: string;

	@Column({
		name: "description",
		type: "varchar",
		length: 1000,
		nullable: true
	})
	description!: string | null;

	@Column({
		name: "map_element_id",
		type: "int",
		nullable: true })
	mapElementId?: number;

	@ManyToOne(() => MapElementModel, { eager: true})
	@JoinColumn({ name: "map_element_id" })
	mapElement?: MapElementModel;

	@Column({
		name: "is_approved",
		type: "boolean",
		default: false
	})
	isApproved!: boolean;

	@Column({
		name: "is_targetable",
		type: "boolean",
		default: false
	})
	isTargetable!: boolean;

	@Column({
		name: "operation_id",
		type: "int"
	})
	operationId!: number;

	@Column({
		name: "requested_by_user_id",
		type: "int"
	})
	requestedByUserId!: number;

	@Column({
		name: "approved_by_user_id",
		type: "int",
		nullable: true
	})
	approvedByUserId!: number | null;

	@ManyToMany(() => MissionModel, mission => mission.tais)
	// @JoinTable({
	// 	name: "areas_of_interest_missions",
	// 	joinColumn: {
	// 		name: "area_of_interest_id",
	// 		referencedColumnName: "id"
	// 	},
	// 	inverseJoinColumn: {
	// 		name: "mission_id",
	// 		referencedColumnName: "id"
	// 	}
	// })
	missions!: MissionModel[];

	@ManyToOne(() => OperationModel, operation => operation.areasOfInterest)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "requested_by_user_id" })
	requestedByUser!: UserModel;

	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "approved_by_user_id" })
	approvedByUser!: UserModel;

	getSearchableFields(): string[] {
		return ['name', 'designation', 'description'];
	}
}
