import {BaseHardDeleteEntity} from "@/models/base-hard-delete.entity.js";
import {BeforeInsert, Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany} from "typeorm";
import AssetModel from "@/models/asset.model.js";
import UserModel from "@/models/user.model.js";
import OperationModel from "@/models/operation.model.js";
import {ISRStatus, Priority} from "@/interfaces/database.interface.js";
import AppDataSource from "@/config/database.js";
import MapElementModel from "@/models/map-element.model.js";
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";
import MissionModel from "@/models/mission.model.js";


@Entity("isr_tracks")
@Index("IDX_ISR_TRACK_MISSION_ID_UNIQUE", ["designation", "operationId"], { unique: true })

export default class ISRTrackModel extends BaseHardDeleteEntity {

	@Column({
		name: "created_by_user_id",
		type: "int"
	})
	createdByUserId!: number;

	@Column({
		name: "designation",
		type: "int",
	})
	designation!: number;

	@Column({
		name: "label",
		type: "varchar",
		length: 255,
		nullable: true
	})
	label!: string | null;

	@Column({
		name: "operation_id",
		type: "int",
	})
	operationId!: number;

	//status
	@Column({
		name: "status",
		type: "enum",
		enum: ISRStatus,
		default: ISRStatus.CREATED,
		nullable: true
	})
	status!: ISRStatus;

	//priority
	@Column({
		name: "priority",
		type: "enum",
		enum: Priority,
		default: Priority.NONE,
		nullable: true
	})
	priority!: Priority;

	//commence_at
	@Column({
		name: "commence_at",
		type: "timestamp",
		nullable: true
	})
	commenceAt!: Date | null;

	//conclude_at
	@Column({
		name: "conclude_at",
		type: "timestamp",
		nullable: true
	})
	concludeAt!: Date | null;

	//ltiov_date
	@Column({
		name: "ltiov_date",
		type: "timestamp",
		nullable: true
	})
	ltiovDate!: Date | null;

	@Column({
		name: "map_element_id",
		type: "int",
		nullable: true })
	mapElementId?: number;

	@ManyToOne(() => MapElementModel, { eager: true})
	@JoinColumn({ name: "map_element_id" })
	mapElement?: MapElementModel;

	@ManyToMany(() => AssetModel, (asset) => asset.isrTracks)
	assets!: AssetModel[];

	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "created_by_user_id" })
	createdByUser!: UserModel;

	@ManyToOne(() => OperationModel)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

    //relationship to engagedMissionAsset
    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.isrTrack)
    engagedMissionAssets!: EngagedMissionAssetModel[];


    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.mission)
    engagedMissions!: MissionModel[];

    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.asset)
    engagedAssets!: AssetModel[];


	@BeforeInsert()
	async generateDesignation() {
		if (!this.operationId) {
			throw new Error('Asset ID and Ops ID are required to generate designation');
		}

		try {
			const entityManager = AppDataSource.manager;
			// Type the promises explicitly

			const operation = await entityManager.findOne<OperationModel>(OperationModel, {
				where: { id: this.operationId },
			});

			if (operation && operation.designation) {

				// Find the highest N number for this asset-operation combination
				const lastISR = await entityManager.findOne<ISRTrackModel>(ISRTrackModel, {
					where: {
						operationId: this.operationId
					},
					order: {
						designation: 'DESC'
					},
				});

				let nextNumber = 1;
				if (lastISR?.designation) {
					nextNumber = lastISR.designation + 1;
				}
				// Generate the new designation
				this.designation = nextNumber;
			}

		} catch (error: any) {
			console.error('Error generating ISR designation:', error);
			throw new Error(`Failed to generate ISR designation: ${error.message}`);
		}
	}
}

