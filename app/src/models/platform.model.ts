import {Entity, Column, <PERSON>To<PERSON>any} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import AssetModel from "@/models/asset.model.js";

@Entity("platforms")
export default class PlatformModel extends BaseEntity {
	@Column({
		name: "name",
		length: 255,
		type: 'varchar'
	})
	name!: string;

	@Column({
		name: "aliases",
		type: 'jsonb',
		nullable: true
	})
	aliases!: string[] | null;

	@Column({
		name: "designation",
		length: 255,
		type: 'varchar',
		nullable: true
	})
	designation!: string | null;

	@Column({
		name: "quantity",
		type: 'integer',
		nullable: true,
		default: 1
	})
	quantity!: number | null;

	@Column({
		name: "country_iso_code",
		length: 3,
		type: 'varchar'
	})
	countryIsoCode!: string;

	@Column({
		name: "description",
		type: 'text',
		nullable: true
	})
	description!: string | null;

	@Column({
		name: "cost",
		type: 'decimal',
		precision: 15,
		scale: 2,
		nullable: true
	})
	cost!: number | null;

	@Column({
		name: "cost_currency",
		length: 3,
		type: 'varchar',
		nullable: true
	})
	costCurrency!: string | null;

	@Column({
		name: "type",
		type: 'enum',
		enum: ['space', 'air', 'land', 'sea', 'hybrid', 'other']
	})
	type!: 'space' | 'air' | 'land' | 'sea' | 'hybrid' | 'other';

	@Column({
		name: "combat_radius",
		type: 'decimal',
		scale: 2,
		precision: 15,
		nullable: true
	})
	combatRadius!: number | null;

	@Column({
		name: "has_collection_capability",
		type: 'boolean',
		default: false
	})
	hasCollectionCapability!: boolean;

	@Column({
		name: "footprint_area",
		type: 'decimal',
		precision: 10,
		scale: 2,
		nullable: true
	})
	footprintArea!: number | null;

	@Column({
		name: "configuration",
		type: 'jsonb',
		nullable: true
	})
	configuration!: any | null;

	@OneToMany(() => AssetModel, asset => asset.platform)
	assets!: AssetModel[];
}