import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import WeatherForecastModel from "./weather-forecast.model.js";

export abstract class WeatherForecastBaseModel extends BaseEntity {
	@Column({
		name: "number",
		type: "int",
		nullable: true
	})
	number!: number | null;

	@Column({
		name: "start_time",
		type: "timestamp with time zone"
	})
	startTime!: Date;

	@Column({
		name: "end_time",
		type: "timestamp with time zone"
	})
	endTime!: Date;

	@Column({
		name: "raw_data",
		type: "jsonb"
	})
	rawData!: any;

}