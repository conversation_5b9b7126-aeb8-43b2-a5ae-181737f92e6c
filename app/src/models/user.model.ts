import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	Index,
	BeforeInsert,
	BeforeUpdate,
	ManyToMany,
	JoinTable, DeleteDateColumn, OneToMany, RelationId
} from "typeorm";
import * as argon2 from 'argon2';
import TeamModel from "@/models/team.model.js";
import RoleModel from "@/models/role.model.js";
import {BaseEntity} from "@/models/base.entity.js";
import OperationsUsersModel from "@/models/operations-users.model.js";
import OperationModel from "@/models/operation.model.js";
import AssetModel from "@/models/asset.model.js";
import InformationRequirementModel from "@/models/information-requirement.model.js";
import AoiModel from "@/models/aoi.model.js";
import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import MissionModel from "@/models/mission.model.js";
import ISRModel from "@/models/isr.model.js";
import OrganizationModel from "@/models/organization.model.js";
import TaskModel from "@/models/task.model.js";

@Entity("users")
export default class UserModel extends BaseEntity  {
	private isPasswordModified: boolean = false;

	// @Column({
	// 	name: "position_id",
	// 	length: 100,
	// 	type: 'varchar',
	// 	nullable: true  // Allow null values
	// })
	// positionId!: string | null;

	@Column({
		name: "first_name",
		length: 100,
		type: 'varchar'
	})
	firstName!: string | null;

	@Column({
		name: "last_name",
		length: 100,
		type: 'varchar'
	})
	lastName!: string | null;

	@Column({
		unique: true,
		length: 255,
		type: 'varchar',
        transformer: {
            to: (value: string) => value?.toLowerCase(),
            from: (value: string) => value
        }
	})
	@Index()
	email!: string;

	@Column({
		length: 255,
		type: 'varchar'
	})
	password!: string;

	@Column({
		name: "avatar_path",
		nullable: true,
		length: 255,
		type: 'varchar'
	})
	avatarPath!: string | null;

	@Column({
		name: "refresh_token",
		nullable: true,
		length: 255,
		type: 'varchar'
	})
	@Index()
	refreshToken!: string | null;

	@Column({
		name: "activate_hash",
		nullable: true,
		length: 255,  // Increased length to accommodate argon2 hash
		type: 'varchar'
	})
	@Index()
	activateHash!: string | null;

	@Column({
		name: "account_type",
		nullable: false,
		default: 'user',
		type: 'enum',
		enum: ['admin', 'org_admin', 'user', 'vendor', 'manager']
	})
	accountType!: 'admin' |'org_admin' | 'manager'  | 'user' | 'vendor';

	@Column({
		name: "reset_password_hash",
		nullable: true,
		length: 255,
		type: 'varchar'
	})
	@Index()
	resetPasswordHash!: string | null;

	@Column({
		name: "reset_password_expires",
		nullable: true,
		type: 'timestamp'
	})
	resetPasswordExpires!: Date | null;

	@Column({
		name: "is_online",
		default: false,
		type: 'boolean'
	})
	isOnline!: boolean;

	@Column({
		name: "is_active",
		default: true,
		type: 'boolean'
	})
	isActive!: boolean;

	@Column({
		name: "is_verified",
		default: false,
		type: 'boolean'
	})
	isVerified!: boolean;

	@ManyToMany(() => TeamModel, team => team.users)
	teams!: TeamModel[];

	@ManyToMany(() => RoleModel, role => role.users, {eager: true})
	@JoinTable({
		name: "user_roles",
		joinColumn: {
			name: "user_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "role_id",
			referencedColumnName: "id"
		},
	})
	roles!: RoleModel[];

	@OneToMany(() => OperationsUsersModel, operationUser => operationUser.user)
	operations!: OperationsUsersModel[];

	@OneToMany(() => InformationRequirementModel, ir => ir.createdByUser)
	createdInformationRequirements!: InformationRequirementModel[];

	// Add to UserModel.ts:
	@OneToMany(() => AoiModel, areasOfInterest => areasOfInterest.requestedByUser)
	requestedAreasOfInterest!: AoiModel[];
	@OneToMany(() => AoiModel, areasOfInterest => areasOfInterest.approvedByUser)
	approvedAreasOfInterest!: AoiModel[];

	@OneToMany(() => CollectionCapabilityModel, capability => capability.createdByUser)
	createdCollectionCapabilities!: CollectionCapabilityModel[];

	@OneToMany(() => CollectionCapabilityModel, capability => capability.pointOfContactUser)
	pointOfContactCollectionCapabilities!: CollectionCapabilityModel[];

	@OneToMany(() => MissionModel, mission => mission.requestedByUser)
	requestedMissions!: MissionModel[];

	@OneToMany(() => MissionModel, mission => mission.approvedByUser)
	approvedMissions!: MissionModel[];

	@OneToMany(() => ISRModel, isr => isr.createdByUser)
	isrs!: ISRModel[];

	@OneToMany(() => TaskModel, task => task.createdByUser)
	createdTasks!: TaskModel[];

	@ManyToMany(() => TaskModel, task => task.members)
	assignedTasks!: TaskModel[];

	public setPassword(password: string) {
		this.password = password;
		this.isPasswordModified = true;
	}


	@BeforeInsert()
	async hashPasswordOnInsert() {
		if (this.password) {
			this.password = await argon2.hash(this.password);
		}
	}

	@BeforeUpdate()
	async hashPasswordOnUpdate() {
		if (this.isPasswordModified) {
			this.password = await argon2.hash(this.password);
			this.isPasswordModified = false;
		}
	}


	@BeforeInsert()
	async generateActivateHash() {
		if (!this.activateHash) {
			this.activateHash = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
			this.isActive = false;
			this.isVerified = false;
		}
	}

    @BeforeInsert()
    @BeforeUpdate()
    normalizeEmail() {
        if (this.email) {
            this.email = this.email.toLowerCase().trim();
        }
    }

	activate() {
		this.isActive = true;
		this.isVerified = true;
		this.activateHash = null;
	}

	getSearchableFields(): string[] {
		return ['email', 'firstName', 'lastName'];
	}
	// Custom methods
	getFullName(): string {
		return `${this.firstName} ${this.lastName}`;
	}

	hasAvatar(): boolean {
		return this.avatarPath !== null && this.avatarPath.length > 0;
	}

	setOffline(): void {
		this.isOnline = false;
	}

	setOnline(): void {
		this.isOnline = true;
	}

	deactivate(): void {
		this.isActive = false;
	}

	verify(): void {
		this.isVerified = true;
	}

	canLogin(): boolean {
		return this.isActive && this.isVerified;
	}

	isAdmin(): boolean {
		return this.accountType === 'admin';
	}

	setRefreshToken(token: string | null): void {
		this.refreshToken = token;
	}

	async comparePassword(candidatePassword: string): Promise<boolean> {
		return await argon2.verify(this.password, candidatePassword);
	}


	async setResetPasswordHash() {
		const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
		this.resetPasswordHash = await argon2.hash(resetToken);
		//set token to expire in 6 hours
		this.resetPasswordExpires = new Date(Date.now() + ********);
		return resetToken;
	}

	clearResetPasswordHash() {
		this.resetPasswordHash = null;
		this.resetPasswordExpires = null;
	}

	sanitizedUser() {
		const { password: _, activateHash: __, resetPasswordHash: ____, resetPasswordExpires: _____, ...safeUser } = this;
		return safeUser;
	}

	hasRole(roleName: string): boolean {
		return this.roles.some(role => role.roleName === roleName);
	}

	// @OneToMany(() => AssetModel, asset => asset.addedByUser)
	// addedAssets!: AssetModel[];
}