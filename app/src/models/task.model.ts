
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Index,
  ManyToMany,
  Join<PERSON><PERSON>
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import OperationModel from "@/models/operation.model.js";
import UserModel from "@/models/user.model.js";
import { TaskStatus, Priority } from "@/interfaces/database.interface.js";

@Entity("tasks")
export default class TaskModel extends BaseEntity {
  @Column({
    name: "name",
    length: 255,
    type: 'varchar'
  })
  title!: string;

  @Column({
    name: "category",
    length: 255,
    type: 'varchar',
    nullable: true
  })
  category!: string | null;

	@Column({
		name: "priority",
		type: 'enum',
		enum: Priority,
		default: Priority.MEDIUM
	})
	priority!: Priority;

  @Column({
    name: "status",
    type: 'enum',
    enum: TaskStatus,
    default: TaskStatus.CREATED
  })
  status!: TaskStatus;

  @Column({
    name: "short_description",
    length: 255,
    type: 'varchar',
    nullable: true
  })
  shortDescription!: string | null;

  @Column({
    name: "labels",
    type: 'jsonb',
    nullable: true
  })
  labels!: string[] | null;

  @Column({
    name: "description",
    type: 'text',
    nullable: true
  })
  description!: string | null;

  @Column({
    name: "is_past_due",
    type: 'boolean',
    default: false
  })
  isPastDue!: boolean;

  @Column({
    name: "archived_at",
    type: 'timestamp',
    nullable: true
  })
  archivedAt!: Date | null;

  @Column({
    name: "completed_at",
    type: 'timestamp',
    nullable: true
  })
  completedAt!: Date | null;

  @Column({
    name: "started_at",
    type: 'timestamp',
    nullable: true
  })
  startedAt!: Date | null;

  @Column({
    name: "ended_at",
    type: 'timestamp',
    nullable: true
  })
  endedAt!: Date | null;

  @Column({
    name: "due_at",
    type: 'timestamp',
    nullable: true
  })
  dueAt!: Date | null;

  @Column({
    name: "created_by_user_id",
    type: "int"
  })
  createdByUserId!: number;

  @Column({
    name: "operation_id",
    type: "int",
    nullable: false
  })
  operationId!: number;

  @ManyToOne(() => OperationModel, operation => operation.tasks)
  @JoinColumn({ name: "operation_id" })
  operation!: OperationModel;

  @ManyToOne(() => UserModel, user => user.createdTasks)
  @JoinColumn({ name: "created_by_user_id" })
  createdByUser!: UserModel;

  @ManyToMany(() => UserModel)
  @JoinTable({
    name: "tasks_users",
    joinColumn: {
      name: "task_id",
      referencedColumnName: "id"
    },
    inverseJoinColumn: {
      name: "user_id",
      referencedColumnName: "id"
    }
  })
  members!: UserModel[];

  getSearchableFields(): string[] {
    return ['name', 'shortDescription', 'description', 'category'];
  }
}