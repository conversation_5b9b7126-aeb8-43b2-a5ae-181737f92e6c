import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Jo<PERSON><PERSON><PERSON>um<PERSON> } from "typeorm";
import { AccessType, UserOpsAccessType } from '@/interfaces/database.interface.js';

import {BaseHardDeleteEntity} from "@/models/base-hard-delete.entity.js";
import UserModel from "@/models/user.model.js";
import OperationModel from "@/models/operation.model.js";

@Entity('operations_users')
export default class OperationsUsersModel extends BaseHardDeleteEntity {
	@Column({
		name: "user_id",
		type: "number"
	})
	userId!: number;

	@Column({
		name: "operation_id",
		type: "number"
	})
	operationId!: number;

	// @ManyToOne(() => UserModel, user => user.id)
	// @JoinColumn({ name: "user_id" })
	// user!: UserModel;
	//
	// @ManyToOne(() => OperationModel, operation => operation.id)
	// @JoinColumn({ name: "operation_id" })
	// operation!: OperationModel;

	@ManyToOne(() => UserModel, user => user.operations, { eager: true})
	@JoinColumn({ name: "user_id" })
	user!: UserModel;

	@ManyToOne(() => OperationModel, operation => operation.users, {eager: true})
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	@Column({
		name: "access_type",
		type: "enum",
		enum: UserOpsAccessType,
		default: UserOpsAccessType.READ
	})
	accessType!: string;
}