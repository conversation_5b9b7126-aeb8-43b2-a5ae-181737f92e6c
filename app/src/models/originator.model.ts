import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>To<PERSON>ne,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index,
	OneToMany, BeforeInsert
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import OrganizationModel from "@/models/organization.model.js";
import UserModel from "@/models/user.model.js";
import AppDataSource from "@/config/database.js";
import PlatformModel from "@/models/platform.model.js";

@Entity("originators")
export default class OriginatorModel extends BaseEntity {

	@Column({
		name: "title",
		length: 255,
		type: 'varchar'
	})
	title!: string | null;

	@Column({
		name: "designation",
		length: 255,
		type: 'varchar',
		nullable: true,
	})
	designation!: string | null;

	@Column({
		name: "description",
		length: 255,
		type: 'varchar',
		nullable: true
	})
	description!: string | null;

	@Column({
		name: "organization_id",
		type: "int"
	})
	organizationId!: number | null;

	@Column({
		name: "contact_details",
		type: 'text',
		nullable: true
	})
	contactDetails!: string | null;

	@ManyToOne(() => OrganizationModel)
	@JoinColumn({ name: "organization_id" })
	organization!: OrganizationModel;

	getSearchableFields(): string[] {
		return ['title', 'designation', 'description'];
	}

	@BeforeInsert()
	async generateDesignation() {
		if (!this.organizationId) {
			throw new Error('Platform ID and Operation ID are required to generate designation');
		}
		try {
			// Get the EntityManager instance
			const entityManager = AppDataSource.manager;

			// Load related entities using the EntityManager
			const organization = await entityManager.findOneBy(OrganizationModel, { id: this.organizationId }) as OrganizationModel;


			if (!organization || !organization.designation) {
				throw new Error('Platform or Operation not found');
			}

			if(!this.title){
				throw new Error('Title is required');
			}
			// Generate the designation
			const slugifyTitle = this.title.toLowerCase()
				.replace(/\s+/g, '-')           // Replace spaces with -
				.replace(/[^\w\-]+/g, '')       // Remove all non-word chars
				.replace(/^-+/, '')             // Trim - from start of text
				.replace(/-+$/, '');
			this.designation = `${organization.designation}-${slugifyTitle}`;

		} catch (error) {
			console.trace(error);
			throw new Error(`Failed to generate designation`);
		}
	}

}
