import {
	<PERSON><PERSON><PERSON>,
	Column,
	<PERSON>To<PERSON><PERSON>,
} from "typeorm";
import {Point, Polygon} from 'geojson';
import { BaseEntity } from "@/models/base.entity.js";
import WeatherForecastPeriodModel from "@/models/weather-forecast-period.model.js";
import WeatherForecastHourlyModel from "@/models/weather-forecast-hourly.model.js";
import WeatherGridDataModel from "@/models/weather-grid-data.model.js";
import {LocationProperties} from 'interfaces/admin/admin.weather.interface.js'


@Entity("weather_forecasts")
export default class WeatherForecastModel extends BaseEntity {
	@Column({
		name: "provider",
		type: "varchar",
		length: 100,
		default: "weather.gov"
	})
	provider!: string;

	@Column({
		name: "last_updated_at",
		type: "timestamp with time zone"
	})
	lastUpdatedAt!: Date;

	@Column({
		name: "time_zone",
		type: "varchar",
		length: 100
	})
	timeZone!: string;

	@Column({
		name: "radar_station",
		type: "varchar",
		length: 50
	})
	radarStation!: string;

	@Column({
		name: "forecast_office",
		type: "varchar",
		length: 255
	})
	forecastOffice!: string;

	@Column({
		name: "grid_id",
		type: "varchar",
		length: 50
	})
	gridId!: string;

	@Column({
		name: "grid_x",
		type: "int"
	})
	gridX!: number;

	@Column({
		name: "grid_y",
		type: "int"
	})
	gridY!: number;

	@Column({
		name: "location",
		type: "geometry",
		spatialFeatureType: "Point",
		srid: 4326
	})
	location!: Point;

	@Column({
		name: "forecast_area",
		type: "geometry",
		spatialFeatureType: "Polygon",
		srid: 4326
	})
	forecastArea!: Polygon;

	@Column({
		name: "units",
		type: "varchar",
		length: 10
	})
	units!: string;

	@Column({
		name: "forecast_generator",
		type: "varchar",
		length: 100
	})
	forecastGenerator!: string;

	@Column({
		name: "generated_at",
		type: "timestamp with time zone"
	})
	generatedAt!: Date;

	@Column({
		name: "update_time",
		type: "timestamp with time zone"
	})
	updateTime!: Date;

	@Column({
		name: "valid_times",
		type: "varchar",
		length: 100
	})
	validTimes!: string;

	@Column({
		name: "elevation_value",
		type: "decimal",
		precision: 10,
		scale: 2
	})
	elevationValue!: number;

	@Column({
		name: "elevation_unit",
		type: "varchar",
		length: 20
	})
	elevationUnit!: string;

	@Column({
		name: "location_properties",
		type: "jsonb"
	})
	locationProperties!: LocationProperties;

	@OneToMany(() => WeatherForecastPeriodModel, forecast => forecast.weatherForecast)
	forecasts!: WeatherForecastPeriodModel[];

	@OneToMany(() => WeatherForecastHourlyModel, hourlyForecast => hourlyForecast.weatherForecast)
	hourlyForecasts!: WeatherForecastHourlyModel[];

	@OneToMany(() => WeatherGridDataModel, gridData => gridData.weatherForecast)
	gridData!: WeatherGridDataModel[];

	getSearchableFields(): string[] {
		return ['provider', 'timeZone', 'radarStation', 'gridId'];
	}
}