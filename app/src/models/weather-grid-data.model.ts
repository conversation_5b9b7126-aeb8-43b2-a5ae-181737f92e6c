import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from "typeorm";
import { BaseEntity } from "@/models/base.entity.js";
import WeatherForecastModel from "@/models/weather-forecast.model.js";
import { Polygon } from 'geojson';
import { WeatherValue, WeatherValueArray, WeatherHazard } from 'interfaces/admin/admin.weather.interface.js'


@Entity("weather_grid_data")
export default class WeatherGridDataModel extends BaseEntity {
	@Column({
		name: "update_time",
		type: "timestamp with time zone"
	})
	updateTime!: Date;

	@Column({
		name: "valid_times",
		type: "varchar",
		length: 100
	})
	validTimes!: string;

	@Column({
		name: "elevation",
		type: "jsonb"
	})
	elevation!: {
		unitCode: string;
		value: number;
	};

	@Column({
		name: "forecast_office",
		type: "varchar",
		length: 255
	})
	forecastOffice!: string;

	@Column({
		name: "grid_id",
		type: "varchar",
		length: 10
	})
	gridId!: string;

	@<PERSON><PERSON><PERSON>({
		name: "grid_x",
		type: "varchar",
		length: 10
	})
	gridX!: string;

	@Column({
		name: "grid_y",
		type: "varchar",
		length: 10
	})
	gridY!: string;

	@Column({
		name: "temperature",
		type: "jsonb"
	})
	temperature!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "dewpoint",
		type: "jsonb"
	})
	dewpoint!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "max_temperature",
		type: "jsonb"
	})
	maxTemperature!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "min_temperature",
		type: "jsonb"
	})
	minTemperature!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "relative_humidity",
		type: "jsonb"
	})
	relativeHumidity!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "apparent_temperature",
		type: "jsonb"
	})
	apparentTemperature!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "heat_index",
		type: "jsonb"
	})
	heatIndex!: {
		uom: string;
		values: WeatherValue<number | null>[];
	};

	@Column({
		name: "wind_chill",
		type: "jsonb"
	})
	windChill!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "sky_cover",
		type: "jsonb"
	})
	skyCover!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "wind_direction",
		type: "jsonb"
	})
	windDirection!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "wind_speed",
		type: "jsonb"
	})
	windSpeed!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "wind_gust",
		type: "jsonb"
	})
	windGust!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "weather",
		type: "jsonb"
	})
	weather!: {
		values: WeatherValueArray<{
			coverage: string | null;
			weather: string | null;
			intensity: string | null;
			visibility: {
				unitCode: string;
				value: number | null;
			};
			attributes: string[];
		}>[];
	};

	@Column({
		name: "hazards",
		type: "jsonb"
	})
	hazards!: {
		values: WeatherValue<WeatherHazard[]>[];
	};

	@Column({
		name: "probability_of_precipitation",
		type: "jsonb"
	})
	probabilityOfPrecipitation!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "quantitative_precipitation",
		type: "jsonb"
	})
	quantitativePrecipitation!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "ice_accumulation",
		type: "jsonb"
	})
	iceAccumulation!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "snowfall_amount",
		type: "jsonb"
	})
	snowfallAmount!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "ceiling_height",
		type: "jsonb"
	})
	ceilingHeight!: {
		uom: string;
		values: WeatherValue<number | null>[];
	};

	@Column({
		name: "visibility",
		type: "jsonb"
	})
	visibility!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "transport_wind_speed",
		type: "jsonb"
	})
	transportWindSpeed!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "transport_wind_direction",
		type: "jsonb"
	})
	transportWindDirection!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "mixing_height",
		type: "jsonb"
	})
	mixingHeight!: {
		uom: string;
		values: WeatherValue<number>[];
	};

	@Column({
		name: "haines_index",
		type: "jsonb"
	})
	hainesIndex!: {
		values: WeatherValue<number>[];
	};

	@Column({
		name: "lightning_activity_level",
		type: "jsonb"
	})
	lightningActivityLevel!: {
		values: WeatherValue<number>[];
	};

	@Column({
		name: "grassland_fire_danger_index",
		type: "jsonb"
	})
	grasslandFireDangerIndex!: {
		values: WeatherValue<number>[];
	};

	@Column({
		name: "probability_of_thunder",
		type: "jsonb"
	})
	probabilityOfThunder!: {
		values: WeatherValue<number>[];
	};

	@Column({
		name: "red_flag_threat_index",
		type: "jsonb"
	})
	redFlagThreatIndex!: {
		values: WeatherValue<number>[];
	};

	@Column({
		name: "geometry",
		type: "geometry",
		spatialFeatureType: "Polygon",
		srid: 4326
	})
	geometry!: Polygon;

	@Column({
		name: "weather_forecast_id",
		type: "int"
	})
	weatherForecastId!: number;

	@ManyToOne(() => WeatherForecastModel, forecast => forecast.gridData)
	@JoinColumn({ name: "weather_forecast_id" })
	weatherForecast!: WeatherForecastModel;

	getSearchableFields(): string[] {
		return ['gridId'];
	}
}