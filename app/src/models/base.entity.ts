import { PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from "typeorm";

export abstract class BaseEntity {
	@PrimaryGeneratedColumn()
	id!: number;

	@CreateDateColumn({
		name: "created_at",
		type: 'timestamp'
	})
	createdAt!: Date;

	@UpdateDateColumn({
		name: "updated_at",
		type: 'timestamp'
	})
	updatedAt!: Date;

	@DeleteDateColumn({
		name: "deleted_at",
		type: 'timestamp'
	})
	deletedAt!: Date;
}