// app/src/models/board.model.ts
import AppDataSource from "@/config/database.js";
import {
	<PERSON><PERSON><PERSON>,
	Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, BeforeInsert, OneToMany, JoinTable, ManyToMany, Index
} from 'typeorm';
import { BaseEntity } from "@/models/base.entity.js";
import PlatformModel from "@/models/platform.model.js";
import OperationModel from "@/models/operation.model.js";
import {AssetStatus} from "@/interfaces/database.interface.js";
import CollectionCapabilityModel from "@/models/collection-capability.model.js";
import MissionModel from "@/models/mission.model.js";
import ISRTrackModel from '@/models/isr-track.model.js';
import EngagedMissionAssetModel from "@/models/engaged-mission-asset.model.js";

@Entity("assets")
@Index("IDX_ASSET_CALLSIGN_OPS_UNIQUE", ["callSign", "operationId"], { unique: true })
export default class AssetModel extends BaseEntity {


	@Column({
		name: "name",
		length: 255,
		type: 'varchar'
	})
	name!: string | null;

	@Column({
		name: "title",
		length: 255,
		type: 'varchar'
	})
	title!: string | null;

	@Column({
		name: "has_collection_capability",
		type: 'boolean',
		default: false
	})
	hasCollectionCapability!: boolean;

	@Column({
		name: "designation",
		length: 255,
		type: 'varchar',
	})
	designation!: string | null;

	@Column({
		name: "call_sign",
		type: 'varchar',
		length: 100,
	})
	callSign!: string | null;

	//platfrom id
	@Column({
		name: "platform_id",
		type: "int"
	})
	platformId!: number | null;

	@ManyToOne(() => PlatformModel, platform => platform.assets)
	@JoinColumn({ name: "platform_id" })
	platform!: PlatformModel;

	//operation id
	@Column({
		name: "operation_id",
		type: "int"
	})
	operationId!: number | null;

	@ManyToOne(() => OperationModel)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;

	//asset configuration
	@Column({
		name: "asset_details",
		type: 'jsonb',
		nullable: true
	})
	assetDetails!: any | null;

    //color string
    @Column({
        name: "color",
        type: 'varchar',
        nullable: true
    })
    color!: string | null;

	//status
	@Column({
		name: "status",
		type: 'enum',
		enum: AssetStatus,
		default: AssetStatus.PENDING_APPROVAL
	})
	status!: AssetStatus;

	@ManyToMany(() => MissionModel, (mission) => mission.assets)
	missions!: MissionModel[];

	@ManyToMany(() => ISRTrackModel, (isrTrack) => isrTrack.assets)
	@JoinTable({
		name: "assets_isr_tracks",
		joinColumn: {
			name: "asset_id",
			referencedColumnName: "id"
		},
		inverseJoinColumn: {
			name: "isr_track_id",
			referencedColumnName: "id"
		}
	})
	isrTracks!: ISRTrackModel[];

    //relationship to engagedMissionAsset
    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.asset)
    engagedMissionAssets!: EngagedMissionAssetModel[];


    //@OneToMany(() => OperationsUsersModel, operationUser => operationUser.user)
    // 	operations!: OperationsUsersModel[];
    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.mission)
    engagedMissions!: MissionModel[];

    @OneToMany(() => EngagedMissionAssetModel, engagedMissionAsset => engagedMissionAsset.isrTrack)
    engagedIsrTracks!: ISRTrackModel[];


	@BeforeInsert()
	async generateDesignation() {
		if (!this.platformId || !this.operationId) {
			throw new Error('Platform ID and Operation ID are required to generate designation');
		}
		try {
			// Get the EntityManager instance
			const entityManager = AppDataSource.manager;

			const platform = await entityManager.findOneBy(PlatformModel, { id: this.platformId });
			const operation = await entityManager.findOneBy(OperationModel, { id: this.operationId });

			console.log("BEFORE INSTRET", platform, operation);
			if (!platform || !operation) {
				throw new Error('Platform or Operation not found');
			}

			if (!operation || !operation.designation) {
				throw new Error('Operation designation is required');
			}

			// Use platform name if designation is null
			const platformIdentifier = platform.designation;

			if (!platformIdentifier) {
				throw new Error('Platform must have either designation or name');
			}

			if(platform){
				this.name = platform.name;
				this.title = platform.name;
				this.designation = `${operation.designation}-${platformIdentifier}`;
				this.hasCollectionCapability = platform?.hasCollectionCapability ?? false;
			} else {
				this.name = "PLATFORM N/A "+ this.platformId;
				this.title= "PLATFORM N/A "+ this.platformId;
				this.designation = `${operation.designation}-${platformIdentifier}`;
				this.hasCollectionCapability = false;
			}
			// Generate the designation

			
			//check if title is null
			if(!this.title){
				this.title = platform.name;
			}
		} catch (error) {
			console.trace(error);
			throw new Error(`Failed to generate designation`);
		}
	}


	@OneToMany(() => CollectionCapabilityModel, capability => capability.asset)
	collectionCapabilities!: CollectionCapabilityModel[];



}