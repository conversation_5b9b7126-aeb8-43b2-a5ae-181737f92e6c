import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Index, ManyToMany
} from "typeorm";
import { Point, Polygon, LineString } from 'geo<PERSON><PERSON>';
import { BaseEntity } from "@/models/base.entity.js";
import UserModel from "@/models/user.model.js";
import AssetModel from "@/models/asset.model.js";
import OperationModel from "@/models/operation.model.js";
import {AvailabilityStatus, ApprovalStatus} from "@/interfaces/database.interface.js";
import ISRTrackModel from "@/models/isr-track.model.js";
import MapElementModel from "@/models/map-element.model.js";

@Entity("collection_capabilities")
//@Index("IDX_COLLECTION_CAPABILITY_ASSET_OPERATION", ["assetId", "operationId"])
export default class CollectionCapabilityModel extends BaseEntity {
	@Column({
		name: "description",
		type: "varchar",
		length: 1000,
		transformer: {
			to: (value: string) => value?.trim(),
			from: (value: string) => value,
		}
	})
	description!: string;

	@Column({
		name: "asset_designation",
		type: "varchar",
		length: 100
	})
	assetDesignation!: string;

	@Column({
		name: "assigned",
		type: "varchar",
		length: 200,
		nullable: true
	})
	assigned?: string;

	@Column({
		name: "contact_details",
		type: "varchar",
		length: 500,
		nullable: true
	})
	contactDetails?: string;
	//
	// @Column({
	// 	name: "track",
	// 	type: "geometry",
	// 	spatialFeatureType: "LineString",
	// 	srid: 4326,
	// 	nullable: true
	// })
	// track?: LineString;

	// @Column({
	// 	name: "area",
	// 	type: "geometry",
	// 	spatialFeatureType: "Polygon",
	// 	srid: 4326,
	// 	nullable: true
	// })
	// area?: Polygon;

	// @Column({
	// 	name: "point",
	// 	type: "geometry",
	// 	spatialFeatureType: "Point",
	// 	srid: 4326,
	// 	nullable: true
	// })
	// point?: Point;

	// @Column({
	// 	name: "radius_meters",
	// 	type: "float",
	// 	nullable: false
	// })
	// radiusMeters!: number;

	@Column({
		name: "availability",
		type: "enum",
		enum: AvailabilityStatus,
		default: AvailabilityStatus.AVAILABLE
	})
	availability!: AvailabilityStatus;

	@Column({
		name: "status",
		type: "enum",
		enum: ApprovalStatus,
		default: ApprovalStatus.PENDING
	})
	status!: ApprovalStatus;

	// Foreign Keys
	@Column({
		name: "created_by_user_id",
		type: "int"
	})
	createdByUserId!: number;

	@Column({
		name: "asset_id",
		type: "int"
	})
	assetId!: number;

	//operation id
	@Column({
		name: "operation_id",
		type: "int"
	})
	operationId!: number | null;

	@ManyToOne(() => OperationModel)
	@JoinColumn({ name: "operation_id" })
	operation!: OperationModel;
	
	@Column({
		name: "point_of_contact_user_id",
		type: "int",
		nullable: true
	})
	pointOfContactUserId?: number | null;

	// @ManyToMany(() => ISRTrackModel, isrTrack => isrTrack.)
	// isrTracks!: ISRTrackModel[];

	// Relationships
	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "created_by_user_id" })
	createdByUser!: UserModel;

	@ManyToOne(() => UserModel)
	@JoinColumn({ name: "point_of_contact_user_id" })
	pointOfContactUser?: UserModel;

	@ManyToOne(() => AssetModel, asset => asset.collectionCapabilities)
	@JoinColumn({ name: "asset_id" })
	asset!: AssetModel;

	getSearchableFields(): string[] {
		return ['description', 'assetDesignation', 'assigned', 'contactDetails'];
	}
}