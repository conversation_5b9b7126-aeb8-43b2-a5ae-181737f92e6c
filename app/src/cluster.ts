// src/cluster.ts
import cluster from 'node:cluster';
import { cpus } from 'node:os';
import process from 'node:process';
import { schedule } from 'node-cron';
import { activatePlannedMissions } from '@/events/system/activate-planned-missions.event.js';

const numCPUs = cpus().length;
const MEMORY_THRESHOLD = 2048; // MB - restart if memory exceeds this
const CHECK_INTERVAL = 30000; // Check every 30 seconds

if (cluster.isPrimary) {
	console.log(`Primary ${process.pid} is running`);
	console.log(`Starting ${numCPUs} workers...`);

	// Fork workers
	for (let i = 0; i < numCPUs; i++) {
		cluster.fork();
	}

	// Monitor and restart workers if they die
	cluster.on('exit', (worker, code, signal) => {
		console.log(`Worker ${worker.process.pid} died. Code: ${code}, Signal: ${signal}`);
		console.log('Starting a new worker...');
		cluster.fork();
	});

	// Monitor memory usage
	const monitorMemory = () => {
		for (const id in cluster.workers) {
			const worker = cluster.workers[id];
			if (worker) {
				worker.send('memory_check');
			}
		}
	};

	// Start memory monitoring
	setInterval(monitorMemory, CHECK_INTERVAL);

	// Schedule a job to run every minute
	schedule('* * * * *', async () => {
		console.log('Running activatePlannedMissions job');
		await activatePlannedMissions();
	});

} else {
	// Worker process
	console.log(`Worker ${process.pid} started`);

	// Monitor memory usage in worker
	process.on('message', (msg) => {
		if (msg === 'memory_check') {
			const memoryUsage = process.memoryUsage();
			const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;

			if (memoryUsageMB > MEMORY_THRESHOLD) {
				console.log(`Worker ${process.pid} memory usage too high (${memoryUsageMB.toFixed(2)}MB). Restarting...`);
				process.exit(1);
			}
		}
	});

	// Start your application
	import('./index.js');
}