// import { Point } from 'geojson';
// import { DataSource } from 'typeorm';
// import WeatherForecastModel from '@/models/weather-forecast.model.js';
// import { MessageCollector } from '@/utils/message-collector.utils.js';
//
// // ... previous interfaces remain the same ...
//
// async function findExistingForecast(
// 	repository: typeof weatherForecastRepository,
// 	longitude: number,
// 	latitude: number,
// 	distanceThreshold: number = 100 // meters
// ): Promise<WeatherForecastModel | null> {
// 	return await repository
// 		.createQueryBuilder('forecast')
// 		.where(
// 			'ST_DWithin(forecast.location::geography, ST_SetSRID(ST_MakePoint(:longitude, :latitude), 4326)::geography, :threshold)',
// 			{
// 				longitude,
// 				latitude,
// 				threshold: distanceThreshold
// 			}
// 		)
// 		.getOne();
// }
//
// export async function syncWeatherForecast(
// 	dataSource: DataSource,
// 	messageCollector: MessageCollector,
// 	coordinates: { latitude: number; longitude: number }[]
// ): Promise<void> {
// 	const weatherForecastRepository = dataSource.getRepository(WeatherForecastModel);
//
// 	try {
// 		for (const coord of coordinates) {
// 			try {
// 				// Check if we already have a forecast for these coordinates
// 				const existingForecast = await findExistingForecast(
// 					weatherForecastRepository,
// 					coord.longitude,
// 					coord.latitude
// 				);
//
// 				// Fetch new data from weather.gov
// 				const response = await fetch(
// 					`https://api.weather.gov/points/${coord.latitude},${coord.longitude}`,
// 					{
// 						headers: {
// 							'User-Agent': '(weather-sync-job, <EMAIL>)',
// 							'Accept': 'application/geo+json'
// 						}
// 					}
// 				);
//
// 				if (!response.ok) {
// 					throw new Error(`Weather.gov API responded with status: ${response.status}`);
// 				}
//
// 				const rawData = await response.json();
//
// 				if (!isWeatherGovPointResponse(rawData)) {
// 					throw new Error('Invalid response format from Weather.gov API');
// 				}
//
// 				const pointData = rawData;
//
// 				// Determine if we're updating or creating
// 				let weatherForecast: WeatherForecastModel;
//
// 				if (existingForecast) {
// 					weatherForecast = existingForecast;
// 					messageCollector.addInfo(
// 						`Updating existing forecast for ${coord.latitude},${coord.longitude}`
// 					);
// 				} else {
// 					weatherForecast = new WeatherForecastModel();
// 					messageCollector.addInfo(
// 						`Creating new forecast for ${coord.latitude},${coord.longitude}`
// 					);
// 				}
//
// 				// Update forecast data
// 				weatherForecast.provider = 'weather.gov';
// 				weatherForecast.lastUpdatedAt = new Date();
// 				weatherForecast.timeZone = pointData.properties.timeZone;
// 				weatherForecast.radarStation = pointData.properties.radarStation;
// 				weatherForecast.forecastOffice = pointData.properties.forecastOffice;
// 				weatherForecast.gridId = pointData.properties.gridId;
// 				weatherForecast.gridX = pointData.properties.gridX;
// 				weatherForecast.gridY = pointData.properties.gridY;
//
// 				// Set location
// 				weatherForecast.location = {
// 					type: 'Point',
// 					coordinates: pointData.geometry.coordinates
// 				} as Point;
//
// 				// Set forecast area
// 				const [lon, lat] = pointData.geometry.coordinates;
// 				const boxSize = 0.1;
// 				weatherForecast.forecastArea = {
// 					type: 'Polygon',
// 					coordinates: [[
// 						[lon - boxSize, lat - boxSize],
// 						[lon + boxSize, lat - boxSize],
// 						[lon + boxSize, lat + boxSize],
// 						[lon - boxSize, lat + boxSize],
// 						[lon - boxSize, lat - boxSize]
// 					]]
// 				};
//
// 				weatherForecast.units = 'us';
// 				weatherForecast.forecastGenerator = 'BaselineForecastGenerator';
// 				weatherForecast.generatedAt = new Date();
// 				weatherForecast.updateTime = new Date();
//
// 				// Set valid times (24 hours)
// 				const now = new Date();
// 				const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
// 				weatherForecast.validTimes = `${now.toISOString()}/${tomorrow.toISOString()}`;
//
// 				weatherForecast.elevationValue = 0;
// 				weatherForecast.elevationUnit = 'meters';
//
// 				// Set location properties
// 				weatherForecast.locationProperties = {
// 					city: pointData.properties.relativeLocation.properties.city,
// 					state: pointData.properties.relativeLocation.properties.state,
// 					distance: {
// 						unitCode: pointData.properties.relativeLocation.properties.distance.unitCode,
// 						value: pointData.properties.relativeLocation.properties.distance.value
// 					},
// 					bearing: {
// 						unitCode: pointData.properties.relativeLocation.properties.bearing.unitCode,
// 						value: pointData.properties.relativeLocation.properties.bearing.value
// 					}
// 				};
//
// 				await weatherForecastRepository.save(weatherForecast);
//
// 				const actionType = existingForecast ? 'Updated' : 'Created';
// 				messageCollector.addSuccess(
// 					`${actionType} weather forecast for ${coord.latitude},${coord.longitude}`
// 				);
//
// 			} catch (error) {
// 				messageCollector.addError(
// 					`Failed to sync weather forecast for ${coord.latitude},${coord.longitude}: ${error instanceof Error ? error.message : 'Unknown error'}`
// 				);
// 				continue;
// 			}
// 		}
// 	} catch (error) {
// 		messageCollector.addError(
// 			`Weather sync job failed: ${error instanceof Error ? error.message : 'Unknown error'}`
// 		);
// 		throw error;
// 	}
// }