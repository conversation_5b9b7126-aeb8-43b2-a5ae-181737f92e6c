import nodemailer from 'nodemailer';

interface EmailOptions {
	to: string;
	subject: string;
	text?: string;
	html?: string;
}

const transporter = nodemailer.createTransport({
	host: 'smtp.office365.com',
	port: 587,
	auth: {
		user: '<EMAIL>',
		pass: process.env.EMAIL_PASSWORD, // Store the password in an environment variable
	},
});

export async function sendEmail({ to, subject, text, html }: EmailOptions): Promise<void> {
	try {
		await transporter.sendMail({
			from: '"Lumio Company" <<EMAIL>>',
			to,
			subject,
			text,
			html,
		});
	} catch (error) {
		console.error('Error sending email:', error);
		throw new Error('Failed to send email');
	}
}