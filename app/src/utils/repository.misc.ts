// import {Between, FindOptionsWhere, <PERSON><PERSON><PERSON><PERSON>r<PERSON>qua<PERSON>, More<PERSON>han<PERSON>r<PERSON>qual, Repository} from "typeorm";
// import {MessageCollector} from "@/utils/message-collector.utils.js";
//
//
// export class RepositoryMiscHelper {
//
// 	private repository: Repository<T>;
// 	private messageCollector: MessageCollector;
// 	constructor(repository: Repository<T>, messageCollector: MessageCollector) {
// 		this.repository = repository;
// 		this.messageCollector = messageCollector;
// 	}
//
// 	isDateTimeColumn(column: any): boolean {
// 		return column?.type === Date ||
// 			column?.type === 'datetime' ||
// 			column?.type === 'timestamp' ||
// 			column?.type === 'timestamptz';
// 	}
//
// 	processDateRangeParam(key: string): { isDateRange: boolean; type: 'start' | 'end'; columnName: string } | null {
// 		const startMatch = key.match(/^start(.+)At$/);
// 		const endMatch = key.match(/^end(.+)At$/);
//
// 		if (startMatch) {
// 			return {
// 				isDateRange: true,
// 				type: 'start',
// 				columnName: startMatch[1].charAt(0).toLowerCase() + startMatch[1].slice(1) + 'At'
// 			};
// 		}
//
// 		if (endMatch) {
// 			return {
// 				isDateRange: true,
// 				type: 'end',
// 				columnName: endMatch[1].charAt(0).toLowerCase() + endMatch[1].slice(1) + 'At'
// 			};
// 		}
//
// 		return null;
// 	}
//
// 	buildWhereConditions(params: any): FindOptionsWhere<T> {
// 		const entityColumns = this.repository.metadata.columns.map(col => col.propertyName);
// 		const whereConditions: Record<string, any> = {};
// 		const dateRanges: Record<string, { start?: Date; end?: Date }> = {};
// 		const filterDescriptions: string[] = [];
//
// 		const paginationParams = ['page', 'perPage', 'sortBy', 'orderBy', 'load'];
//
// 		Object.entries(params).forEach(([key, value]) => {
// 			if (!paginationParams.includes(key) && value !== undefined && value !== '') {
// 				// Check for date range parameters
// 				const dateRangeInfo = this.processDateRangeParam(key);
//
// 				if (dateRangeInfo) {
// 					const { type, columnName } = dateRangeInfo;
// 					const column = this.repository.metadata.findColumnWithPropertyName(columnName);
//
// 					if (column && this.isDateTimeColumn(column)) {
// 						try {
// 							const date = new Date(value as string);
// 							if (!isNaN(date.getTime())) {
// 								if (!dateRanges[columnName]) {
// 									dateRanges[columnName] = {};
// 								}
// 								dateRanges[columnName][type] = date;
// 							} else {
// 								this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
// 							}
// 						} catch (error) {
// 							this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
// 						}
// 					}
// 					return;
// 				}
//
// 				// Handle regular conditions
// 				if (entityColumns.includes(key)) {
// 					const column = this.repository.metadata.findColumnWithPropertyName(key);
// 					let processedValue = value;
//
// 					if (column?.type === Number || column?.type === 'int' || column?.type === 'integer') {
// 						processedValue = Number(value);
// 						filterDescriptions.push(`${key} is ${processedValue}`);
// 					} else if (this.isDateTimeColumn(column)) {
// 						try {
// 							processedValue = new Date(value as string);
// 							// @ts-ignore
// 							if (!processedValue && isNaN(processedValue.getTime())) {
// 								throw new Error('Invalid date');
// 							}
// 							filterDescriptions.push(`${key} is ${processedValue.toString()}`);
// 						} catch (error) {
// 							this.messageCollector.addWarning(`Invalid date value for ${key}: ${value}`);
// 							return;
// 						}
// 					} else {
// 						filterDescriptions.push(`${key} is "${value}"`);
// 					}
//
// 					whereConditions[key] = processedValue;
// 				} else {
// 					this.messageCollector.addWarning(`Ignoring invalid filter parameter: ${key}`);
// 				}
// 			}
// 		});
//
// // Process date ranges
// 		Object.entries(dateRanges).forEach(([columnName, range]) => {
// 			if (range.start && range.end) {
// 				whereConditions[columnName] = Between(range.start, range.end);
// 				filterDescriptions.push(`${columnName} is between ${range.start.toISOString()} and ${range.end.toISOString()}`);
// 			} else if (range.start) {
// 				whereConditions[columnName] = MoreThanOrEqual(range.start);
// 				filterDescriptions.push(`${columnName} is after ${range.start.toISOString()}`);
// 			} else if (range.end) {
// 				whereConditions[columnName] = LessThanOrEqual(range.end);
// 				filterDescriptions.push(`${columnName} is before ${range.end.toISOString()}`);
// 			}
// 		});
//
// // Add the filter message if there are any conditions
// 		if (filterDescriptions.length > 0) {
// 			const filterMessage = `Applying filters where ${filterDescriptions.join(' and ')}`;
// 			this.messageCollector.addInfo(filterMessage);
// 		}
//
// 		return whereConditions as FindOptionsWhere<T>;
// 	}
//
// 	parseRelations(loadString?: string): string[] {
// 		if (!loadString) return [];
//
// 		const relations:string[] = [];
// 		loadString.split(',').forEach(relation => {
// 			relations.push(relation.trim());
// 		});
//
// 		return relations as string[];
// 	}
// }
//
