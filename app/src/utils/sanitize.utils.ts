import { EntityTarget, ObjectLiteral } from 'typeorm';
import * as console from "node:console";

export type SanitizationRules<T> = {
	[K in keyof T]?: boolean | SanitizationRules<T[K]> | SanitizationRules<T[K] extends Array<infer U> ? U : never>;
};

export function createSanitizer<T extends ObjectLiteral>(entity: EntityTarget<T>) {
	function sanitize(data: any, rules: SanitizationRules<any>): any {
		try{
			if (typeof data !== 'object' || data === null) {
				return data;
			}

			if (Array.isArray(data)) {
				return data.map(item => sanitize(item, rules));
			}

			const result: any = {};
			for (const key in rules) {
				if (key in data) {
					if (rules[key] === true) {
						result[key] = data[key];
					} else if (typeof rules[key] === 'object') {
						result[key] = sanitize(data[key], rules[key] as SanitizationRules<any>);
					}
				}
			}
			return result;
		} catch (error){
			console.trace("SAN ERROR", error);
		}
		return null;

	}

	return (data: T | T[], rules: SanitizationRules<T>): any => {
		return sanitize(data, rules);
	};
}