export const sendSlackNotification = async (customMessage:string|null|undefined =  null): Promise<void> => {
	const webhookUrl = '*********************************************************************************';

	const message = {
		text: customMessage || `🚀 API is now live and listening!\n\n`
	};

	try {
		const response = await fetch(webhookUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(message),
		});
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}
		console.log('Slack notification sent successfully');
	} catch (error) {
		console.error('Failed to send Slack notification:', error);
	}
};
