import { FastifyError, FastifyReply, FastifyRequest } from "fastify";
import { MessageCollector } from "@/utils/message-collector.utils.js";

interface Message {
	message: string;
	type: string;
}


function getMessagesFromMessageCollector(messageCollector: MessageCollector): Message[] {
	return messageCollector.getMessages()	;
}

// 200
export const successResponse = (
	data: any,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(200).send({
		success: true,
		error: null,
		data: data,
		messages: messages
	});
};

// created 201
export const createdResponse = (
	data: any,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(201).send({
		success: true,
		error: null,
		data: data,
		messages: messages
	});
};

// no content 204
export const noContentResponse = (
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(204).send({
		success: true,
		error: null,
		data: null,
		messages: messages
	});
};

// bad request 400
export const badRequestResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(400).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

// unauthorized 401
export const unauthorizedResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(401).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

// forbidden 403
export const forbiddenResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(403).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

// not found 404
export const notFoundResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(404).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

// internal server error 500
export const internalServerErrorResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(500).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

export const catchControllerError = (error: unknown, request: FastifyRequest, reply: FastifyReply) => {
	if (error instanceof Error) {
		request.messageCollector?.addError(error.message);
	} else if (typeof error === 'string') {
		request.messageCollector?.addError(error);
	} else {
		request.messageCollector?.addError("An unexpected error occurred");
	}
	return returnApiResponse(null, request, reply, 500);
};

// not allowed 405
export const notAllowedResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(405).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

// 409 conflict
export const conflictResponse = (
	error: string,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	const messages = request.messageCollector ? getMessagesFromMessageCollector(request.messageCollector) : [];
	return reply.code(409).send({
		success: false,
		data: null,
		error: error,
		messages: messages
	});
};

export const returnApiResponse = (
	data: any,
	request: FastifyRequest,
	reply: FastifyReply,
	controllerStatusCodeSet: number|null = null
) => {
	const messageCollector = request.messageCollector as MessageCollector;
	const messages = messageCollector ? messageCollector.getMessages() : [];
	const hasErrors = messages.some(m => m.type === 'error');

	if(messageCollector.hasStatusCodeOverride() || controllerStatusCodeSet){
		const newStatusCode = controllerStatusCodeSet || (messageCollector.getStatusCodeOverride() || 200);
		const isError = newStatusCode !== 200;
		return reply.code(newStatusCode).send({
			success: !isError,
			error: isError ? messages[0]?.message : null,
			data: data,
			messages: messages,
			environment: process.env.NODE_ENV || 'development',
			codeTimestamp: process.env.CODE_TIMESTAMP || 'NA',
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
		});
	}

	if (hasErrors) {
		// If there are errors, return an error response
		const errorMessage = messages.find(m => m.type === 'error')?.message || 'An error occurred';
		return reply.code(500).send({
			success: false,
			error: errorMessage,
			data: null,
			messages: messages,
			environment: process.env.NODE_ENV || 'development',
			codeTimestamp: process.env.CODE_TIMESTAMP || 'NA',
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
		});
	} else if (data === null && controllerStatusCodeSet !== 204) {
		// If no data is returned (and it's not intentionally a 204 No Content), treat as Not Found
		return reply.code(404).send({
			success: false,
			error: 'Not Found',
			data: null,
			messages: messages,
			environment: process.env.NODE_ENV || 'development',
			codeTimestamp: process.env.CODE_TIMESTAMP || 'NA',
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
		});
	} else {
		return reply.code(200).send({
			success: true,
			error: null,
			data: data,
			messages: messages,
			environment: process.env.NODE_ENV || 'development',
			codeTimestamp: process.env.CODE_TIMESTAMP || 'NA',
			timestamp: new Date().toISOString(),
			version: process.env.npm_package_version || '1.0.0',
		});
	}
};


export const routeErrorHandler = (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
	if (error.validation) {
		// request.messageCollector?.addError("Validation error");
		error.validation.forEach((validationError) => {
			//remove anything but string
			const cleanInstancePath  = validationError.instancePath.replace(/[^a-zA-Z0-9]/g, '');
			const errorMessage =
				`Validation error: ${validationError.keyword} at ${cleanInstancePath}: ${validationError.message}`;
			request.messageCollector?.addError(errorMessage);
		});
		return returnApiResponse(null, request, reply, 422);
	} else if (error.message) {
		request.messageCollector?.addError(error.message || "An unexpected error occurred");
	}
	//request.messageCollector?.addError(error.message || "An unexpected error occurred");
	return returnApiResponse(null, request, reply, 500);
};

export const validationErrorResponse = (
	error: FastifyError,
	request: FastifyRequest,
	reply: FastifyReply
) => {
	if (error.validation) {
		error.validation.forEach((validationError) => {
			const errorMessage = validationError.message ||
				`Validation error: ${validationError.keyword} at ${validationError.instancePath}`;
			request.messageCollector?.addError(errorMessage);
		});
	} else {
		request.messageCollector?.addError(error.message || "An unexpected validation error occurred");
	}
	return returnApiResponse(null, request, reply, 422);
};