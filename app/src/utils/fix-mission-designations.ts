import AppDataSource from "@/config/database.js";
import MissionModel from '@/models/mission.model.js';
import OperationModel from '@/models/operation.model.js';
import { sendSlackNotification } from "@/utils/system-notices.utils.js";
import { Not, IsNull } from "typeorm";

/**
 * Utility function to fix missions with NULL designation values
 * This is needed because the designation column was made NOT NULL but existing records have NULL values
 */
export const fixMissionDesignations = async () => {
  try {
    // Initialize the connection if not already initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log("Data source has been initialized");
    }

    const missionRepository = AppDataSource.getRepository(MissionModel);
    const operationRepository = AppDataSource.getRepository(OperationModel);

    // Find all missions with NULL designation
    const missionsWithNullDesignation = await missionRepository.find({
      where: {
        designation: null
      },
      relations: ['operation']
    });

    console.log(`Found ${missionsWithNullDesignation.length} missions with NULL designation`);

    if (missionsWithNullDesignation.length === 0) {
      console.log("No missions with NULL designation found");
      return;
    }

    // Group missions by operation
    const missionsByOperation = new Map<number, MissionModel[]>();

    for (const mission of missionsWithNullDesignation) {
      if (!mission.operationId) {
        console.warn(`Mission ${mission.id} has no operation ID, skipping`);
        continue;
      }

      if (!missionsByOperation.has(mission.operationId)) {
        missionsByOperation.set(mission.operationId, []);
      }

      missionsByOperation.get(mission.operationId)?.push(mission);
    }

    // Process each operation's missions
    for (const [operationId, missions] of missionsByOperation.entries()) {
      const operation = await operationRepository.findOne({
        where: { id: operationId }
      });

      if (!operation || !operation.designation) {
        console.warn(`Operation ${operationId} not found or has no designation, skipping its missions`);
        continue;
      }

      console.log(`Processing ${missions.length} missions for operation ${operation.name} (${operation.designation})`);

      // Get the highest mission number for this operation
      const lastMission = await missionRepository.findOne({
        where: {
          operationId: operationId,
          designation: Not(IsNull())
        },
        order: {
          id: 'DESC'
        }
      });

      let nextNumber = 1;
      if (lastMission?.designation) {
        const parts = lastMission.designation.split('-');
        const lastNumber = parseInt(parts[parts.length - 1]);
        nextNumber = isNaN(lastNumber) ? 1 : lastNumber + 1;
      }

      // Update each mission with a new designation
      for (const mission of missions) {
        const formattedMissionsCount = nextNumber.toString().padStart(5, '0');
        mission.designation = `${operation.designation}-M${formattedMissionsCount}`;

        await missionRepository.save(mission);
        console.log(`Fixed designation for mission ID ${mission.id}: ${mission.designation}`);

        nextNumber++;
      }

      const slackMessage = `Fixed designations for ${missions.length} missions in operation ${operation.name}`;
      await sendSlackNotification(slackMessage);
    }

    console.log("Finished fixing mission designations");
  } catch (error) {
    console.error("Error fixing mission designations:", error);
  }
};
