export class MessageCollector {
	private messages: Array<{ type: 'error' | 'warning' | 'success' | 'info', message: string }> = [];
	private responseCodeOverride?: number|null|undefined;


	addThrowableError(error: unknown) {
		this.addError(this.getErrorMessage(error), error);
	}

	addError(message: string, error?: unknown) {
		this.responseCodeOverride = 500;
		const errorMessage = error ? `${message} ${this.getErrorMessage(error)}` : message;
		this.messages.push({ type: 'error', message: errorMessage });
	}

	addWarning(message: string) {
		this.messages.push({ type: 'warning', message });
	}

	addSuccess(message: string) {
		this.responseCodeOverride = 200;
		this.messages.push({ type: 'success', message });
	}

	addInfo(message: string) {
		this.messages.push({ type: 'info', message });
	}

	addConflict(message: string) {
		this.responseCodeOverride = 409;
		this.messages.push({ type: 'warning', message });
	}

	addDenied(message: string) {
		this.responseCodeOverride = 403;
		this.messages.push({ type: 'error', message });
	}

	addNotFound(message: string) {
		this.responseCodeOverride = 404;
		this.messages.push({ type: 'error', message });
	}

	addBadRequest(message: string) {
		this.responseCodeOverride = 400;
		this.messages.push({ type: 'error', message });
	}

	hasErrors() {
		return this.messages.some(m => m.type === 'error');
	}

	hasWarnings() {
		return this.messages.some(m => m.type === 'warning');
	}

	hasSuccess() {
		return this.messages.some(m => m.type === 'success');
	}

	hasInfo() {
		return this.messages.some(m => m.type === 'info');
	}

	getMessages() {
		return this.messages;
	}

// 	if(messageCollector.hasStatusCodeOverride() || controllerStatusCodeSet){
// 	controllerStatusCodeSet = messageCollector.getStatusCodeOverride();
// }
	getStatusCodeOverride(){
		return this.responseCodeOverride;
	}

	hasStatusCodeOverride(){
		return this.responseCodeOverride !== undefined;
	}

	reset() {
		this.messages = [];
	}

	private getErrorMessage(error: unknown): string {
		if (error instanceof Error) return error.message;
		return String(error);
	}
}

