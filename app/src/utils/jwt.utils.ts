// src/utils/jwt.utils.ts

import jwt from 'jsonwebtoken';
import UserModel from '../models/user.model.js';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'; // Use environment variable in production
const REFRESH_SECRET = process.env.REFRESH_SECRET || 'your-refresh-secret-key'; // Use environment variable in production
export function generateToken(user: UserModel, organizationId: string|number): string {
	return jwt.sign(
		{
			userId: user.id,
			email: user.email.toLowerCase(),
			accountType: user.accountType,
			organizationId: organizationId,
		},
		JWT_SECRET,
		//expire token in 5 years
		{ expiresIn: '5y' }
	);
}

export function verifyToken(token: string): jwt.JwtPayload {
	return jwt.verify(token, JWT_SECRET) as jwt.JwtPayload;
}

export function verifyRefreshToken(token: string): jwt.JwtPayload {
	return jwt.verify(token, REFRESH_SECRET) as jwt.JwtPayload;
}

export function generateRefreshToken(user: UserModel, organizationId: string|number): string {
	return jwt.sign(
		{
			userId: user.id,
			organizationId: organizationId,
		},
		REFRESH_SECRET,
		{ expiresIn: '7d' } // Refresh token valid for 7 days
	);
}