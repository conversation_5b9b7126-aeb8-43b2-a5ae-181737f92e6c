import fp from 'fastify-plugin';
import { FastifyInstance, FastifyPluginAsync, FastifyReply, FastifyRequest } from "fastify";
import { MessageCollector } from "@/utils/message-collector.utils.js";

declare module 'fastify' {
	interface FastifyRequest {
		messageCollector: MessageCollector;
	}
}

const messageCollectorMap = new WeakMap<FastifyRequest, MessageCollector>();

const messageCollectorPlugin: FastifyPluginAsync = fp(async (fastify: FastifyInstance) => {

	fastify.decorateRequest('messageCollector', {
		getter(this: FastifyRequest) {
			if (!messageCollectorMap.has(this)) {
				messageCollectorMap.set(this, new MessageCollector());
			}
			return messageCollectorMap.get(this)!;
		}
	});

	fastify.addHook('onRequest', (request: FastifyRequest, reply: FastifyReply, done) => {
		// Force creation of messageCollector
		done();
	});

}, {
	name: 'messageCollectorPlugin',
	fastify: '>=5.0.0'
});

export default messageCollectorPlugin;