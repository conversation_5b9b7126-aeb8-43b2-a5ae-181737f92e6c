import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { MessageCollector } from "@/utils/message-collector.utils.js";
import { SanitizationRules } from "@/utils/sanitize.utils.js";
import {IPaginationParams} from "@/interfaces/admin/admn.item-crud.interface.js";
import {IRequestingUser} from "@/interfaces/database.interface.js";


type ControllerMethod = 'create' | 'getAll' | 'getById' | 'update' | 'delete' | 'search';

type AdditionalQueryParams = {
	[key: string]: string | number | undefined;
};

type RequestType<T, CreateDTO, UpdateDTO, M extends ControllerMethod> =
	M extends 'create' ? FastifyRequest<{ Body: CreateDTO }> :
		M extends 'getAll' ? FastifyRequest<{ Querystring: IPaginationParams & { load?: string } }> :
			M extends 'getById' ? FastifyRequest<{ Params: { id: string }, Querystring: { load?: string } }> :
				M extends 'update' | 'delete' ? FastifyRequest<{ Params: { id: string } }> :
					M extends 'search' ? FastifyRequest<{ Querystring: { searchTerm: string, fields?: string } }> :
						FastifyRequest;

type CrudController<T, CreateDTO, UpdateDTO> = {
	create(request: FastifyRequest<{ Body: CreateDTO }>, reply: FastifyReply): Promise<unknown>;
	getAll(request: FastifyRequest<{ Querystring: IPaginationParams & { load?: string } }>, reply: FastifyReply): Promise<unknown>;
	getById(request: FastifyRequest<{ Params: { id: string }, Querystring: { load?: string } }>, reply: FastifyReply): Promise<unknown>;
	update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateDTO }>, reply: FastifyReply): Promise<unknown>;
	delete(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply): Promise<unknown>;
	search(request: FastifyRequest<{ Querystring: { searchTerm: string, fields?: string } }>, reply: FastifyReply): Promise<unknown>;
};

export function createCrudRouteHandlers<T, CreateDTO, UpdateDTO, C extends CrudController<T, CreateDTO, UpdateDTO>>(
	ControllerClass: new (messageCollector: MessageCollector, requestingUser:IRequestingUser|undefined, organizationId:number|undefined) => C
) {
	type WithControllerFunction = {
		<M extends ControllerMethod>(method: M):
			(request: RequestType<T, CreateDTO, UpdateDTO, M>, reply: FastifyReply) => Promise<unknown>;
	}

	const withController: WithControllerFunction = (method) => {
		return async (request, reply) => {
			const controller = new ControllerClass(request.messageCollector as MessageCollector, request.requestingUser, request.organizationId);
			return controller[method](request as any, reply);
		};
	};

	return {
		create: withController('create'),
		getAll: withController('getAll'),
		getById: withController('getById'),
		update: withController('update'),
		delete: withController('delete'),
		search: withController('search')
	};
}

export function registerCrudRoutes<T, CreateDTO, UpdateDTO, C extends CrudController<T, CreateDTO, UpdateDTO>>(
	fastify: FastifyInstance,
	handlers: ReturnType<typeof createCrudRouteHandlers<T, CreateDTO, UpdateDTO, C>>,
	path: string,
	options: {
		createSchema?: any;
		updateSchema?: any;
		preHandlerErrorHandlerOptions: any;
		sanitizationRules: SanitizationRules<T>;
	}
) {
	const { createSchema, updateSchema, preHandlerErrorHandlerOptions, sanitizationRules } = options;

	const attachSanitizationRules = (request: FastifyRequest, _reply: FastifyReply, done: () => void) => {
		(request as any).sanitizationRules = sanitizationRules;
		done();
	};

	const queryStringSchema = {
		type: 'object',
		properties: {
			page: { type: 'number' },
			perPage: { type: 'number' },
			sortBy: { type: 'string' },
			orderBy: { type: 'string' },
			load: { type: 'string' }
		},
		additionalProperties: true
	};

	fastify.post(path, {
		schema: createSchema,
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.create);

	fastify.get(`${path}/search`, {
		schema: {
			querystring: {
				type: 'object',
				properties: {
					searchTerm: { type: 'string', minLength: 2 },
					fields: { type: 'string', nullable: true }
				},
				required: ['searchTerm']
			}
		},
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.search);

	fastify.get(path, {
		schema: { querystring: queryStringSchema },
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.getAll);

	fastify.get(`${path}/:id`, {
		schema: {
			querystring: {
				type: 'object',
				properties: {
					load: { type: 'string' }
				}
			}
		},
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.getById);

	fastify.put(`${path}/:id`, {
		schema: updateSchema,
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.update);

	fastify.delete(`${path}/:id`, {
		...preHandlerErrorHandlerOptions,
		preHandler: [attachSanitizationRules, ...(preHandlerErrorHandlerOptions.preHandler || [])]
	}, handlers.delete);
}