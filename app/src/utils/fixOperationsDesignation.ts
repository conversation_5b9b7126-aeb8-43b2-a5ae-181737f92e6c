import OperationModel from '@/models/operation.model.js';
import AppDataSource from '@/config/database.js';
import OrganizationModel from '@/models/organization.model.js';
import MissionModel from '@/models/mission.model.js';


export async function fixOperationsDesignation() {
	try{
		const organizationRepository = AppDataSource.getRepository(OrganizationModel);
		const operationRepository = AppDataSource.getRepository(OperationModel);

		//fetch all oorganizations and load operations via relations
		const allOrgs = await organizationRepository.find({ relations: ['operations'] })
		//loop through all organizations
		for (const org of allOrgs) {
			console.log(`Fixing designation for organization: ${org.name}`);
			if(!org.operations || org.operations.length === 0) continue;
			//loop through all operations
			let opsCount = 1;
			for (const op of org.operations) {
				console.log(`Fixing designation for operation: ${op.name}`);
				//get the first letters of operation name
				const firstLetters = op.name?.split(' ').map((word) => word.charAt(0)).join('').toUpperCase();

				// ops count needs to be formatted to 4 digits
				const formattedOpsCount = opsCount.toString().padStart(5, '0');

				op.designation = `${firstLetters}-${formattedOpsCount}`;
				//save the operation
				await operationRepository.save(op);
				opsCount++;
				console.log(`Fixed designation for operation: ${op.name} to ${op.designation}`);
			}
		}
	} catch (error) {
		console.error('Error fixing operations designation:', error);
		return;
	}

}

export async function fixMissionDesignation() {

	try{
		const operationRepository = AppDataSource.getRepository(OperationModel);
		const missionRepository = AppDataSource.getRepository(MissionModel);

		//fetch all operations and load missions via relations
		const allOps = await operationRepository.find({ relations: ['missions'] })
		console.log(`Fixing designation for ${allOps.length} operations`);
		//loop through all operations
		for (const op of allOps) {
			console.log(`Fixing designation for operation: ${op.name}`);
			if(!op.missions || op.missions.length === 0) continue;
			//loop through all missions
			let missionsCount = 1;
			for (const mission of op.missions) {
				console.log(`Fixing designation for mission: ${mission.name}`);
				const oldName = mission.name;
				//mission desgination is op.designation + "-" + missionCount padded to 5 digits
				const formattedMissionsCount = missionsCount.toString().padStart(5, '0');
				mission.designation = `${op.designation}-M${formattedMissionsCount}`;
				//i need all before last dash in op designation
				const opDesignationBeginning = op.designation?.substring(0, op.designation.lastIndexOf('-'));
				mission.name = `${opDesignationBeginning} - M${formattedMissionsCount}`;
				mission.description = oldName;
				await missionRepository.save(mission);
				missionsCount++;
				console.log(`Fixed designation for mission: ${mission.name} to ${mission.designation}`);
			}
		}
	}catch (error) {
		console.error('Error fixing mission designation:', error);
		return;
	}

}