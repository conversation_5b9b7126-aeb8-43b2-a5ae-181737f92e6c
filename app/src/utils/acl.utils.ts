import { AccessType, IRequestingUser } from '@/interfaces/database.interface.js';

export const checkACLByRule = (entity:string, userToCheck:IRequestingUser|null|undefined, accessRequested: AccessType = AccessType.READ) => {
	try {
		if (!userToCheck) return false;
		const accessRules = userToCheck.defaultRules[entity]?.rules;

		//list, create, delete are also managed




		if(!accessRules || !accessRules[accessRequested]) return false;

		return accessRules[accessRequested].includes(userToCheck.accountType);
	} catch (error) {
		console.log("ACL ACCESS ERROR", error);
		return false;
	}

}