import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMissionOptimizationIndexes1689324815000 implements MigrationInterface {
	name = 'AddMissionOptimizationIndexes1689324815000'

	public async up(queryRunner: QueryRunner): Promise<void> {
		// First, get all tables and their columns
		const tables = await queryRunner.query(`
            SELECT table_name, column_name
            FROM information_schema.columns
            WHERE table_schema = 'public'
            ORDER BY table_name, column_name;
		`);

		console.log('Available tables and columns:', tables);

		// Create mission operation_id index
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_missions_operation_id" ON "missions" ("operation_id")`);

		// Create mission status index
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_missions_status" ON "missions" ("status")`);

		// Create mission dates index
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_missions_dates" ON "missions" ("start_at", "end_at")`);

		// Check if assets_missions exists and create index
		const assetsMissionsExists = await queryRunner.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public' AND table_name = 'assets_missions'
            );
		`);

		if (assetsMissionsExists[0].exists) {
			await queryRunner.query(`CREATE INDEX IF NOT EXISTS "idx_assets_mission_id" ON "assets_missions" ("mission_id")`);
		}

		// Find the correct name for missions_tais / areas_of_interest junction table
		const missionJunctionTables = await queryRunner.query(`
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name LIKE '%mission%' AND table_name LIKE '%tai%'
               OR table_name LIKE '%mission%' AND table_name LIKE '%area%';
		`);

		if (missionJunctionTables.length > 0) {
			const junctionTable = missionJunctionTables[0].table_name;
			await queryRunner.query(`
                CREATE INDEX IF NOT EXISTS "idx_tai_mission_id" ON "${junctionTable}" ("mission_id")
			`);
		}

		// Check isr_tracks table for the correct column name for asset relationship
		const isrTrackColumns = await queryRunner.query(`
            SELECT column_name FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = 'isr_tracks'
              AND (column_name LIKE '%asset%' OR column_name = 'asset_id');
		`);

		if (isrTrackColumns.length > 0) {
			const assetColumn = isrTrackColumns[0].column_name;
			await queryRunner.query(`
                CREATE INDEX IF NOT EXISTS "idx_isr_tracks_asset" ON "isr_tracks" ("${assetColumn}")
			`);
		}
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Drop all created indexes
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_missions_dates"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_missions_status"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_isr_tracks_asset"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_tai_mission_id"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_assets_mission_id"`);
		await queryRunner.query(`DROP INDEX IF EXISTS "idx_missions_operation_id"`);
	}
}