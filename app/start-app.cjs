#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
console.log('Starting app...');
const tsxPath = path.resolve(__dirname, 'node_modules', '.bin', 'tsx');
const entryFile = path.resolve(__dirname, 'src', 'index.ts');

const child = spawn(tsxPath, [entryFile], {
	stdio: 'inherit',
	env: { ...process.env }
});

child.on('error', (error) => {
	console.error('Failed to start tsx:', error);
	process.exit(1);
});

process.on('SIGTERM', () => {
	child.kill('SIGTERM');
});

process.on('SIGINT', () => {
	child.kill('SIGINT');
});