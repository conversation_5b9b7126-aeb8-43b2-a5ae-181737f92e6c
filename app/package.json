{"name": "intello-backend", "module": "src/index.ts", "type": "module", "packageManager": "pnpm@9.11.0", "version": "1.0.1beta", "scripts": {"startnode": "tsx src/cluster.ts", "build": "tsc -p tsconfig.json", "dev": "tsx watch src/index.ts | pino-pretty", "start": "pm2-runtime start ecosystem.config.js --env production", "pm2:start": "pm2-runtime start ecosystem.config.js --env production"}, "devDependencies": {"@types/jsonwebtoken": "^8.5.9", "@types/node": "^22.7.0", "@types/nodemailer": "^6.4.16", "@types/uuid": "^9.0.8", "pino-pretty": "^11.3.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "^5.6.2"}, "peerDependencies": {"typescript": "^5.0.0"}, "dependencies": {"@fastify/auth": "^5.0.1", "@fastify/cookie": "^10.0.1", "@fastify/cors": "^10.0.1", "@fastify/jwt": "^9.0.1", "@fastify/websocket": "^11.0.1", "@types/geojson": "^7946.0.14", "argon2": "^0.41.1", "dataloader": "^2.2.2", "dotenv": "^16.4.5", "fastify": "^5.0.0", "fastify-plugin": "^5.0.1", "geojson": "^0.5.0", "jsonwebtoken": "9.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "pg": "^8.13.0", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.20", "uuid": "^9.0.1"}}