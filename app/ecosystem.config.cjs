module.exports = {
	apps: [{
		name: 'lumio-api',
		script: './start-app.cjs',
		instances: 1,
		exec_mode: 'fork',
		max_memory_restart: '2G',

		// Environment variables
		env_production: {
			NODE_ENV: 'production',
			NODE_OPTIONS: '--max-old-space-size=4096'
		},

		// Process management
		autorestart: true,
		max_restarts: 10,
		min_uptime: '5s',
		kill_timeout: 5000,

		// Logging
		log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
		error_file: '/home/<USER>/.pm2/logs/lumio-api-error.log',
		out_file: '/home/<USER>/.pm2/logs/lumio-api-out.log',
		merge_logs: true,

		// Performance monitoring
		watch: false,

		// Graceful shutdown
		wait_ready: true,
		listen_timeout: 10000,
	}]
};