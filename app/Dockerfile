FROM node:20

# Set environment variables
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Create app directory
WORKDIR /app

# Create a non-root user
RUN groupadd -g 1001 nodejs && \
    useradd -u 1001 -g nodejs -s /bin/bash -m -d /home/<USER>

# Create necessary directories and set permissions
RUN mkdir -p "$PNPM_HOME" && \
    chown -R appuser:nodejs "$PNPM_HOME"

# Enable pnpm
RUN corepack disable && \
    npm install -g pnpm@9.11.0 && \
    pnpm --version

# Copy package files
COPY --chown=appuser:nodejs package.json pnpm-lock.yaml* ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy the rest of the application
COPY --chown=appuser:nodejs . .

# Switch to non-root user
USER appuser

# Expose the port
EXPOSE 3001

# Start the application
CMD ["pnpm", "run", "startnode"]