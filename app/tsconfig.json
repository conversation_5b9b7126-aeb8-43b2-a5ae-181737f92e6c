{"compilerOptions": {"lib": ["ES2022"], "module": "Node16", "target": "ES2022", "emitDecoratorMetadata": true, "experimentalDecorators": true, "moduleResolution": "Node16", "moduleDetection": "force", "noEmit": false, "composite": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "downlevelIteration": true, "skipLibCheck": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "baseUrl": "./src", "rootDir": "./src", "outDir": "./dist", "sourceMap": true, "esModuleInterop": true, "types": ["node"], "paths": {"@/*": ["./*"]}}, "include": ["src/**/*"], "exclude": ["node_modules"]}