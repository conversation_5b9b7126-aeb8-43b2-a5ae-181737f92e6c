version: '3.8'

services:
  # Production services
  postgres:
    build:
      context: ./postgres
      dockerfile: Dockerfile
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/migrations:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    env_file:
      - .env
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    networks:
      - lumio-be
    profiles: ["prod"]

  app:
    build:
      context: ./app
      dockerfile: Dockerfile
    volumes:
      - ./app/src:/app/src
      - /app/node_modules
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NODE_OPTIONS=--max-old-space-size=4096
      - PNPM_HOME=${PNPM_HOME:-/pnpm}
      - PATH=${PNPM_HOME:-/pnpm}:${PATH}
      - HOME=${HOME:-/home/<USER>
      - COREPACK_HOME=${COREPACK_HOME:-/home/<USER>/.corepack}
      - JWT_SECRET=${JWT_SECRET}
      - REFRESH_SECRET=${REFRESH_SECRET}
      - NODE_PORT=${NODE_PORT:-3001}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    env_file:
      - .env
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 6G
        reservations:
          cpus: '0.5'
          memory: 2G
    depends_on:
      - postgres
    networks:
      - lumio-be
    profiles: ["prod"]

  # Test services
  testpostgres:
    build:
      context: ./postgres
      dockerfile: Dockerfile
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ../lumio-intello-backend-test/postgres/migrations:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    env_file:
      - ./.env
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    networks:
      - lumio-be
    profiles: ["test"]

  testapp:
    build:
      context: ./app
      dockerfile: Dockerfile
    volumes:
      - ./app/src:/app/src
      - /app/node_modules
    ports:
      - "3002:3001"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - NODE_OPTIONS=--max-old-space-size=4096
      - PNPM_HOME=${PNPM_HOME:-/pnpm}
      - PATH=${PNPM_HOME:-/pnpm}:${PATH}
      - HOME=${HOME:-/home/<USER>
      - COREPACK_HOME=${COREPACK_HOME:-/home/<USER>/.corepack}
      - JWT_SECRET=${JWT_SECRET}
      - REFRESH_SECRET=${REFRESH_SECRET}
      - NODE_PORT=${NODE_PORT:-3001}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    env_file:
      - ./.env
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 6G
        reservations:
          cpus: '0.5'
          memory: 2G
    depends_on:
      - testpostgres
    networks:
      - lumio-be
    profiles: ["test"]

networks:
  lumio-be:
    name: lumio-be

volumes:
  postgres_data:
  postgres_map_data:
  t-rex_cache:
  postgres_test_data:
