lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      node-cron:
        specifier: ^3.0.3
        version: 3.0.3
    devDependencies:
      '@types/node-cron':
        specifier: ^3.0.11
        version: 3.0.11

packages:

  '@types/node-cron@3.0.11':
    resolution: {integrity: sha512-0ikrnug3/IyneSHqCBeslAhlK2aBfYek1fGo4bP4QnZPmiqSGRK+Oy7ZMisLWkesffJvQ1cqAcBnJC+8+nxIAg==}

  node-cron@3.0.3:
    resolution: {integrity: sha512-dOal67//nohNgYWb+nWmg5dkFdIwDm8EpeGYMekPMrngV3637lqnX0lbUcCtgibHTz6SEz7DAIjKvKDFYCnO1A==}
    engines: {node: '>=6.0.0'}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

snapshots:

  '@types/node-cron@3.0.11': {}

  node-cron@3.0.3:
    dependencies:
      uuid: 8.3.2

  uuid@8.3.2: {}
