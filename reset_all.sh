#!/bin/bash

set -e

echo "Stopping all running containers..."
sudo docker stop $(sudo docker ps -aq) 2>/dev/null || true

echo "Removing all containers..."
sudo docker rm $(sudo docker ps -aq) 2>/dev/null || true

echo "Removing all images..."
sudo docker rmi $(sudo docker images -q) 2>/dev/null || true

echo "Removing all volumes..."
sudo docker volume rm $(sudo docker volume ls -q) 2>/dev/null || true

echo "Removing all networks..."
sudo docker network rm $(sudo docker network ls -q) 2>/dev/null || true

echo "Performing a deep clean of the Docker system..."
sudo docker system prune -af --volumes

echo "Rebuilding and starting containers..."
docker-compose up --build -d

echo "Process completed. New containers are now running in detached mode."

echo "Current running containers:"
docker ps