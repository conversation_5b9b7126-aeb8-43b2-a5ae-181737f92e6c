version: '3.8'

services:
  postgres:
    build:
      context: ./postgres
      dockerfile: Dockerfile
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/migrations:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    env_file:
      - .env
    networks:
      - lumio-be

#  app:
#    build:
#      context: ./app
#      dockerfile: Dockerfile.DONOTUSE
#    volumes:
#      - ./app/src:/app/src
#      - /app/node_modules
#    ports:
#      - "${NODE_PORT:-3000}:${NODE_PORT:-3000}"
#    environment:
#      - NODE_ENV=${NODE_ENV:-production}
#      - PNPM_HOME=${PNPM_HOME:-/pnpm}
#      - PATH=${PNPM_HOME:-/pnpm}:${PATH}
#      - HOME=${HOME:-/home/<USER>
#      - COREPACK_HOME=${COREPACK_HOME:-/home/<USER>/.corepack}
#      - JWT_SECRET=${JWT_SECRET}
#      - REFRESH_SECRET=${REFRESH_SECRET}
#      - NODE_PORT=${NODE_PORT:-3000}
#      - POSTGRES_HOST=${POSTGRES_HOST}
#      - POSTGRES_DB=${POSTGRES_DB}
#      - POSTGRES_USER=${POSTGRES_USER}
#      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
#    env_file:
#      - .env
#    depends_on:
#      - postgres
#      - kafka
#    networks:
#      - lumio-be
#      - lumio-fe

#  kafka:
#    image: confluentinc/cp-kafka:latest
#    ports:
#      - "9092:9092"
#    volumes:
#      - ./kafka/config:/etc/kafka/
#    environment:
#      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
#      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
#      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
#      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
#    networks:
#      - lumio-be

#  nginx:
#    build:
#      context: ./nginx
#      dockerfile: Dockerfile.DONOTUSE
#    ports:
#      - "80:80"
#      - "443:443"
#    volumes:
#      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
#      - ./nginx/ssl:/etc/nginx/ssl
#    depends_on:
#      - app
#    networks:
#      - lumio-fe

networks:
  lumio-be:
    name: lumio-be
#  lumio-fe:
#    name: lumio-fe

volumes:
  postgres_data: