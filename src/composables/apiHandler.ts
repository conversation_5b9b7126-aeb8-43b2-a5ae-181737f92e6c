import { IApiError } from '@/types/Global.type';
import { AxiosError } from 'axios';


export function isApiError(response: any): response is IApiError {
	return response &&
		response.success === false &&
		'messages' in response &&
		Array.isArray(response.messages);
}

export function handleApiError(error: any) {
	if (error instanceof Error) {
		const axiosError = error as AxiosError<IApiError>;
		if (axiosError.response?.data && isApiError(axiosError.response.data)) {
			// Return the error response if it matches our API error format
			return axiosError.response.data;
		}
	}
	// Re-throw if it's not a handled API error
	throw error;
}