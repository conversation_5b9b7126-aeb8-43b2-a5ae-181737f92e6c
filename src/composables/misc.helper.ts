import { ICoordinates, RGBAColor } from '@/types/Global.type';

import type { SymbolItem } from '@/types/EsriMap';
import { Aoi } from '@/types/Aoi.type';
import { Isr } from '@/types/Isr.type';
import { useGeometryConverter } from '@/composables/useGeometryConverter';
import { IIsrTrack } from '@/types/IsrTrack.type';
import {Asset} from "@/types/Asset";
import { shiftCoordinatesByOneDegreeLatAndLong } from '@/composables/esri/MapGraphicLayers';
import {IEngagedMissionAsset} from "@/types/EngagedMissionAsset.type";

const geometryConverter = useGeometryConverter();
export function validateRGBAColor(color: number[] | unknown): RGBAColor {
	// Validate input as RGBAColor or return a default value
	if (Array.isArray(color) && color.length === 4 && color.every(n => typeof n === 'number')) {
		return color as RGBAColor; // Type assertion since we've validated the array
	}
	return [0, 0, 0, 1]; // Default RGBA color
}

//convert rgba color to [r,g,b,a] with transparency being 0-1 instead of 0-255
export function convertToRGBA(color: RGBAColor|null|undefined): string {
	if(!color) {
		return 'rgba(0, 0, 0, 1)';
	}
	// check if color 3 is 0 and if so set it to 0.001
	if(color[3] === 0) {
		color[3] = 0.001;
	}
	return `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${color[3] / 255})`;
}


export function parseMapItemsHelper(data: {
	aois?:Aoi[]|null,
	isrs?:Isr[]|null
	isr_tracks?: IIsrTrack[]|null,
    assets?: Asset[]|null,
    engaged_assets?: IEngagedMissionAsset[]|null,
}, marker:boolean = false):SymbolItem[]|[] {

    let shiftCount = 1;

    let assetTracksMapElements = [] as SymbolItem[];

	const aoisMapElements = (data.aois) ? data.aois.map((item:Aoi) => {
		const arcGISType = geometryConverter.geoJSONToArcGIS(item.mapElement?.elementType?.toLowerCase() || 'polygon');
		const symbolItem: SymbolItem = {
			id: item.id ? Number(item.id) : null, // Convert undefined to null as per SymbolItem interface
			title: item.name,
			designation: item.designation ?? undefined,
			description: item.description ?? '',
			itemType: 'aoi',
			type: arcGISType ?? 'polygon',
			coordinates: item.mapElement?.element.coordinates || [],
			selected: false,
			elementsColor: item.mapElement?.elementColor || [],
			elementsBorderColor: item.mapElement?.borderColor || [],
			elementsBorderThickness: item.mapElement?.borderThickness || 2,
			elementsBorderType: item.mapElement?.borderType || 'solid',
			isApproved: !!item.isApproved,
			isTargetable: !!item.isTargetable,
			isUserMade: true
		};
		return symbolItem;
	}) : [];

	const isrTracksMapElements = (data.isr_tracks) ? data.isr_tracks.map((item:IIsrTrack) => {
		const arcGISType = geometryConverter.geoJSONToArcGIS(item.mapElement?.elementType?.toLowerCase() || 'polygon');
		let assetString = "<ul>";
		const assignedAssets = item.assets || [];
		let additionToTitle = 'Track';
		let borderColor = item.mapElement?.borderColor || [0, 0, 0, 255];
        // let backgroundColor = item.mapElement?.elementColor || [0, 0, 0, 255];
		if(assignedAssets && assignedAssets.length > 0) {
			if(marker) {
				//join callSigns by comma if assignedAssets is not empty
				additionToTitle = `\n(Assets: ${assignedAssets.length ? assignedAssets.map((asset: Asset) => asset.callSign).join(', ') : ''}) `;
			}
			assignedAssets.forEach((asset:any) => {
				assetString += `<li>${asset.name} | CallSign: <strong>${asset.callSign}</strong></li>`;
			});
			assetString += "</ul>";

		}

		const symbolItem: SymbolItem = {
			id: item.id ? Number(item.id) : null, // Convert undefined to null as per SymbolItem interface
			title: "ISR-"+item.designation,
			designation: item.designation ?? '',
			description: "ISR Track",
			itemType: 'isr_track',
			type: arcGISType ?? 'polyline',
			coordinates: item.mapElement?.element.coordinates || [],
			selected: false,
			elementsColor: [100,100,100,255],
			elementsBorderColor: borderColor,
			elementsBorderThickness: 1,
			elementsBorderType: 'solid',
			isApproved: true,
			isTargetable: false,
			isUserMade: true,
			marker: additionToTitle
		};

		return symbolItem;
	}) : [];

    const engagedAssetsMapElements = (data.engaged_assets) ? data.engaged_assets.map((item:IEngagedMissionAsset) => {
        //random number between 1-5

        const isrTrack = item.isrTrack;
        const asset = item.asset;
        const mission = item.mission;
        console.log('shiftCount ', shiftCount);
        const oldCoordinates = isrTrack?.mapElement?.element.coordinates ? isrTrack.mapElement?.element.coordinates : [];
        const shiftedCoordinates = oldCoordinates?.length ? shiftCoordinatesByOneDegreeLatAndLong(oldCoordinates as ICoordinates[], shiftCount) : [];
        console.log('shiftedCoordinates => ', shiftedCoordinates);
        const oppositeBorderColor = (asset?.color) ? getContrastingTextColor(asset.color) : '#000000';
        const missionName = mission?.name ? mission.name : 'No Mission';
        const description = asset?.name ? asset.name : 'No Name ';
        shiftCount = shiftCount + 1;
        return {
            id: item.id ? Number(item.id) : new Date(), // convert to id or Timestamp
            title: asset?.name ?? 'No Name',
            designation: asset?.designation ? asset.designation : undefined,
            description: missionName + " | " + description,
            itemType: 'isr_track',
            type: 'polyline',
            coordinates: shiftedCoordinates,
            selected: false,
            elementsColor: asset?.color ? hexToRGBA(asset.color) : [0, 0, 0, 1],
            elementsBorderColor: asset?.color ? hexToRGBA(oppositeBorderColor) : [0, 0, 0, 1],
            elementsBorderThickness:  3,
            elementsBorderType: 'solid',
            isApproved: true,
            isTargetable: true,
            isUserMade: true,
            marker: asset?.callSign ?? 'N/A',
            markerCount: shiftCount,
        } as SymbolItem;

    }) : [];

	return [...aoisMapElements, ...isrTracksMapElements, ...assetTracksMapElements, ...engagedAssetsMapElements];

}

export const getPriorityColor = (priority: string) => {
	//Colour scheme for priority is highest (red), high (orange), medium (yellow),
	// low (green)
	//  How do we do word recognition to stop similar IRs??
	//  Need more space on the two line ones, noting we will try and avoid
	switch (priority) {
		case 'highest':
			return '#c30423';
		case 'high':
			return '#d58a02';
		case 'medium':
			return '#f6e600';
		case 'low':
			return '#1eba0e';
		default:
			return '#1e1e1e';
	}
};

export const getPriorityTextColor = (priority: string) => {
    //Colour scheme for priority is highest (red), high (orange), medium (yellow),
    // low (green)
    //  How do we do word recognition to stop similar IRs??
    //  Need more space on the two line ones, noting we will try and avoid
    switch (priority) {
        case 'highest':
            return '#eef73f';
        case 'high':
            return '#eef73f';
        case 'medium':
            return '#eef73f';
        case 'low':
            return '#eef73f';
        default:
            return '#eef73f';
    }
};

export const getStatusColor = (status: string) => {
    switch (status) {
        case 'active':
            return '#38B000'; // Vibrant green
        case 'inactive':
            return '#bcbcbc'; // Gray
        case 'engaged':
            return '#3B82F6'; // Blue
        case 'requested':
            return '#8B5CF6'; // Purple
        case 'pending_approval':
            return '#F59E0B'; // Amber
        case 'withdrawn':
            return '#6B7280'; // Gray-blue
        case 'cancelled':
            return '#EF4444'; // Red
        case 'rejected':
            return '#DC2626'; // Darker red
        case 'created':
            return '#8B5CF6'; // Purple
        case 'completed':
            return '#10B981'; // Teal
        case 'on_hold':
            return '#F59E0B'; // Amber
        case 'planned':
            return '#3B82F6'; // Blue
        case 'failed':
            return '#B91C1C'; // Deep red
        case 'delayed':
            return '#F97316'; // Orange
        case 'suspended':
            return '#9333EA'; // Violet
        default:
            return '#1E1E1E'; // Black/dark gray (same as your default)
    }
};

export const getStatusTextColor = (status: string) => {
    switch (status) {
        case 'active':
            return '#eef73f'; // White text on vibrant green
        case 'inactive':
            return '#121111'; // Black text on gray
        case 'engaged':
            return '#eef73f'; // White text on blue
        case 'requested':
            return '#eef73f'; // White text on purple
        case 'pending_approval':
            return '#000000'; // Black text on amber
        case 'withdrawn':
            return '#eef73f'; // White text on gray-blue
        case 'cancelled':
            return '#eef73f'; // White text on red
        case 'rejected':
            return '#eef73f'; // White text on darker red
        case 'created':
            return '#eef73f'; // White text on purple
        case 'completed':
            return '#eef73f'; // White text on teal
        case 'on_hold':
            return '#000000'; // Black text on amber
        case 'planned':
            return '#eef73f'; // White text on blue
        case 'failed':
            return '#eef73f'; // White text on deep red
        case 'delayed':
            return '#eef73f'; // White text on orange
        case 'suspended':
            return '#eef73f'; // White text on violet
        default:
            return '#eef73f'; // White text on black/dark gray
    }
};


//convert hex to rgba
const hexToRGBA = (hex:string, alpha = 255) => {
    // Remove the hash if it exists
    hex = hex.replace(/^#/, '');

    // Parse the hex values
    let r, g, b;

    // Handle both shorthand (3 digits) and full (6 digits) hex
    if (hex.length === 3) {
        r = parseInt(hex[0] + hex[0], 16);
        g = parseInt(hex[1] + hex[1], 16);
        b = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
    } else {
        throw new Error('Invalid hex color format');
    }

    // Return the RGBA array of integers
    return [r, g, b, alpha];
}

export const rgbaToHex = (rgba: RGBAColor) => {
    const [r, g, b, a] = rgba;

    // Convert each component to hexadecimal
    const hexR = ('0' + Math.round(r).toString(16)).slice(-2);
    const hexG = ('0' + Math.round(g).toString(16)).slice(-2);
    const hexB = ('0' + Math.round(b).toString(16)).slice(-2);
    const hexA = ('0' + Math.round(a * 255).toString(16)).slice(-2); // Convert alpha to 0-255 range

    // Return the hex color string
    return `#${hexR}${hexG}${hexB}${hexA}`;
}

export const fetchFriendlyColors = () => {
    // Web-safe colors array excluding red and red shades
// Web-safe colors array excluding red and red shades (with duplicates removed)
    // Array of visually distinct colors (no reds or red shades)
    return [
        // Blues (from light to dark)
        "#00FFFF", // Cyan
        "#1E90FF", // Dodger Blue
        "#0000FF", // Blue
        "#000080", // Navy
        "#4B0082", // Indigo

        // Greens (from light to dark)
        "#98FB98", // Pale Green
        "#32CD32", // Lime Green
        "#006400", // Dark Green

        // Yellows/Browns
        "#FFFF00", // Yellow
        "#FFA500", // Orange (careful: has some red component but still acceptable)
        "#8B4513", // Saddle Brown

        // Purples/Violets
        "#800080", // Purple
        "#8A2BE2", // Blue Violet
        "#4B0082", // Indigo

        // Teals/Aqua
        "#20B2AA", // Light Sea Green
        "#008080", // Teal

        // Neutrals
        "#D3D3D3", // Light Gray

        "#696969", // Dim Gray
        "#000000", // Black

        // Other distinct hues
        "#00BFFF", // Deep Sky Blue
        "#7CFC00", // Lawn Green
        "#7FFFD4", // Aquamarine
        "#DDA0DD", // Plum
        "#B0C4DE", // Light Steel Blue
        "#9ACD32", // Yellow Green
        "#FFDAB9", // Peach Puff (very light, minimal red)
        "#87CEEB", // Sky Blue
        "#6A5ACD",  // Slate Blue

        //brown
         "#3a1003", // Brown
        "#8B4513", // Saddle Brown
    ];
}

export const getAssetTypeColor = (assetType:string) => {
    switch (assetType) {
        case 'space':
            return '#9333EA'; // Purple
        case 'air':
            return '#2563EB'; // Blue
        case 'land':
            return '#92400E'; // Brown
        case 'sea':
            return '#06B6D4'; // Cyan
        case 'hybrid':
            return '#F97316'; // Orange
        case 'other':
            return '#6B7280'; // Grey
        default:
            return '#1E1E1E'; // Dark grey/black
    }
};

export const getAssetTypeTextColor = (assetType:string) => {
    const backgroundColor = getAssetTypeColor(assetType);
    return getContrastingTextColor(backgroundColor);
};

export const getContrastingTextColor = (hexColor:string|undefined|null) => {
    // Remove the # if it exists
    //check if hexColor is undefined or null or not string and just return black
    if(!hexColor || typeof hexColor !== 'string') {
        return '#000000'; // Default to black
    }
    const hex = hexColor.replace('#', '');

    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate perceived brightness using the YIQ formula
    // This formula gives more weight to colors that human eyes are more sensitive to
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Return black for bright backgrounds and white for dark ones
    // 128 is the middle of the 0-255 range
    return brightness > 128 ? '#000000' : '#FFFFFF';
};