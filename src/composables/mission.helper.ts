import {Mission} from "@/types/Mission";


export const doMissionsOverlap = (mission1: Mission, mission2: Mission) => {
    // Check if the missions overlap
    const start1 = new Date(mission1.startAt);
    const end1 = new Date(mission1.endAt);
    const start2 = new Date(mission2.startAt);
    const end2 = new Date(mission2.endAt);

    // Two time periods overlap if:
    // - first mission starts before second mission ends AND
    // - first mission ends after second mission starts
    return start1 <= end2 && end1 >= start2;
};