// src/composables/useNatoCountries.ts

export interface CountryCode {
	code: string;
	name: string;
}

export function useNatoCountries() {
	const natoCountries = [
		{ code: 'GB', name: 'United Kingdom' },
		{ code: 'US', name: 'United States' },
		{ code: 'CA', name: 'Canada' },
		{ code: 'AU', name: 'Australia' },
		{ code: 'AL', name: 'Albania' },
		{ code: 'BE', name: 'Belgium' },
		{ code: 'BG', name: 'Bulgaria' },
		{ code: 'HR', name: 'Croatia' },
		{ code: 'CZ', name: 'Czech Republic' },
		{ code: 'DK', name: 'Denmark' },
		{ code: 'EE', name: 'Estonia' },
		{ code: 'FR', name: 'France' },
		{ code: 'DE', name: 'Germany' },
		{ code: 'GR', name: 'Greece' },
		{ code: 'HU', name: 'Hungary' },
		{ code: 'IS', name: 'Iceland' },
		{ code: 'IT', name: 'Italy' },
		{ code: 'LV', name: 'Latvia' },
		{ code: 'LT', name: 'Lithuania' },
		{ code: 'LU', name: 'Luxembourg' },
		{ code: 'M<PERSON>', name: 'North Macedonia' },
		{ code: 'ME', name: 'Montenegro' },
		{ code: 'NL', name: 'Netherlands' },
		{ code: 'NO', name: 'Norway' },
		{ code: 'PL', name: 'Poland' },
		{ code: 'PT', name: 'Portugal' },
		{ code: 'RO', name: 'Romania' },
		{ code: 'SK', name: 'Slovakia' },
		{ code: 'SI', name: 'Slovenia' },
		{ code: 'ES', name: 'Spain' },
		{ code: 'TR', name: 'Turkey' },
		{ code: 'FI', name: 'Finland' },
		{ code: 'SE', name: 'Sweden' }
	];

	return {
		natoCountries
	};
}
