// composables/useRefreshManager.ts
import { ref, nextTick } from 'vue'
import { useSnackbar } from '@/composables/useSnackbar'

interface RefreshManagerConfig<T extends string> {
	componentName: string
	refreshKeys: readonly T[]
}

export function useRefreshManager<T extends string>({
	                                                    componentName,
	                                                    refreshKeys,
                                                    }: RefreshManagerConfig<T>) {
	const { showSnackbar } = useSnackbar()
	type RefreshState = Record<T, boolean>

	// This can be kept private since we don't expose it
	const refreshState = ref<RefreshState>(
		refreshKeys.reduce((acc, key) => {
			acc[key] = false
			return acc
		}, {} as RefreshState)
	)

	const triggerRefresh = async (
		id: string | number,
		key: T,
		callback: () => Promise<void>
	): Promise<boolean> => {
		if (!id) {
			console.warn(`[RefreshManager] Invalid ${componentName} ID provided`)
			return false
		}

		try {
			refreshState.value[key] = true
			await callback()
			await nextTick(() => {
				refreshState.value[key] = false
			})
			showSnackbar({
				text: `${componentName} was updated successfully`,
				color: 'success'
			})
			return true
		} catch (error) {
			refreshState.value[key] = false
			showSnackbar({
				text: `Failed to refresh ${componentName}`,
				color: 'error'
			})
			throw error
		}
	}

	const getRefreshKey = (id: string | number | undefined, key: T): string =>
		`${componentName}-${id}-${refreshState.value[key]}`

	return {
		triggerRefresh,
		getRefreshKey
	}
}