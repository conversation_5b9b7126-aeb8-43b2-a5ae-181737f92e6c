import {SymbolItem} from "@/types/EsriMap";
import Graphic from '@arcgis/core/Graphic';
import { Polygon, Polyline } from '@arcgis/core/geometry';
import { CIMSymbol, Symbol } from '@arcgis/core/symbols';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import { ICoordinates } from '@/types/Global.type';



export const addItem = (item: SymbolItem, graphicsLayer:GraphicsLayer) => {


    if (item.type && item.type === 'polygon') {
        const paths = item.coordinates[0] as number[][];

        const initialPolygonGraphic = new Graphic({
            geometry: new Polygon({
                rings: item.coordinates as number[][][],
                spatialReference: { wkid: 102100 }
            }),
            symbol: genSymbol(item),
            attributes: {
                geometryType: 'polygon',
                name: item.title ?? item.designation ?? '',
                title: item.name ?? item.title ?? '',
                description: item.description ?? '',
                designation: item.designation ?? '',
                isInitial: false,
                itemType: item.itemType ?? 'aoi',
                id: item.id ? item.id : null,
                coordinates: item.coordinates ?? [],
                isUserMade: true,
                type: 'polygon',
                selected: false,
                elementsColor: [...(item.elementsColor || [])],
                elementsBorderColor: [...(item.elementsBorderColor || [])],
                elementsBorderThickness: item.elementsBorderThickness || 2,
                elementsBorderType: item.elementsBorderType || 'solid',
                isApproved: item.isApproved,
                isTargetable: item.isTargetable

            },
            popupTemplate: {
                title: item.title ?? item.designation ?? '',
                content: item.description ?? '',
            },
        });
        graphicsLayer.add(initialPolygonGraphic);

        if (item.title || item.designation) {
            // Find the midpoint of the line
            //grab first coordinate and one before last
            const firstCoordinate = paths[0];
            const lastCoordinate = paths[paths.length - 2];
            //find midpoint between first and last coordinate
            const midPoint = [
                (firstCoordinate[0] + lastCoordinate[0]) / 2,
                (firstCoordinate[1] + lastCoordinate[1]) / 2,
            ];


            //
            // const midPoint = paths[midIndex];

            // Create a text graphic
            const textGraphic = new Graphic({
                geometry: {
                    //@ts-ignore
                    type: 'point',
                    x: midPoint[0],
                    y: midPoint[1],
                    spatialReference: { wkid: 102100 },
                },
                symbol: {
                    //@ts-ignore
                    type: 'text',
                    color: [0, 0, 0, 255],
                    haloColor: [255, 255, 255, 255],
                    // backgroundColor: [255, 255, 255, 255],
                    haloSize: 1.4,
                    text: item.title ?? item.designation ?? '',
                    font: {
                        size: 9,
                        family: 'Arial',
                        weight: 'bold',
                        color: [0, 0, 0, 255],
                    },
                },
                attributes: {
                    isTextLabel: true,
                    parentId: item.id,
                    isUserMade: false, // so it doesn't get selected
                },
            });
            graphicsLayer.add(textGraphic);
        }
    }

    if (item.type === 'polyline') {
        console.log('item', item);
        const paths = item.coordinates as number[][];

        const polylineGraphic = new Graphic({
            geometry: new Polyline({
                paths: [paths],
                spatialReference: { wkid: 102100 }
            }),
            symbol: genSymbol(item),
            attributes: {
                geometryType: 'polyline',
                name: item.title ?? item.designation ?? '',
                title: item.name ?? item.title ?? '',
                description: item.description ?? '',
                designation: item.designation ?? '',
                isInitial: false,
                itemType: item.itemType ?? 'isr_track',
                id: item.id ? item.id : null,
                coordinates: item.coordinates ?? [],
                isUserMade: true,
                type: 'polyline',
                selected: false,
                elementsColor: [...(item.elementsColor || [])],
                elementsBorderColor: [...(item.elementsBorderColor || [])],
                elementsBorderThickness: item.elementsBorderThickness || 2,
                elementsBorderType: item.elementsBorderType || 'solid',
                isApproved: item.isApproved,
                isTargetable: false,
            },
            popupTemplate: {
                title: item.title ?? item.designation ?? '',
                content: item.description ?? '',
            },
        });
        graphicsLayer.add(polylineGraphic);

        if (item.title || item.designation) {
            // Find the midpoint of the line

            const firstCoordinate = paths[0];
            const secondCoordinate = paths[1];
            // console.log('polyline path ', paths);
            //
            // const lastCoordinate = paths[paths.length - 1];
            // //find midpoint between first and last coordinate
            // const midPoint = [
            //     (firstCoordinate[0] + lastCoordinate[0]) / 2,
            //     (firstCoordinate[1] + lastCoordinate[1]) / 2,
            // ];
            // //
            // const midIndex = Math.floor(paths.length / 2);
            // const midPoint = paths[midIndex];

            // Create a text graphic
            const textGraphic = new Graphic({
                geometry: {
                    //@ts-ignore
                    type: 'point',
                    x: (item.isTargetable) ? secondCoordinate[0] : firstCoordinate[0],
                    y:  (item.isTargetable) ? secondCoordinate[1] : firstCoordinate[1],
                    spatialReference: { wkid: 102100 },
                },
                symbol: {
                    //@ts-ignore
                    type: 'text',
                    color: item.isTargetable ? item.elementsColor : [0, 0, 0, 255],
                    haloColor: item.isTargetable ? item.elementsBorderColor : [255, 255, 255, 255],
                    // backgroundColor: [255, 255, 255, 255],
                    haloSize: 1,
                    text: (item.title ?? item.designation ?? '') + " (" + item.marker+ ")",
                    outline: { // autocasts as new SimpleLineSymbol()
                        width: 0.5,
                        color: "darkblue"
                    },
                    font: {
                        size: 10,
                        family: 'Arial',
                        weight: 'bold',
                        color: item.isTargetable ? item.elementsColor : [0, 0, 0, 255],
                    },
                },
                attributes: {
                    isTextLabel: true,
                    parentId: item.id,
                    isUserMade: false, // so it doesn't get selected
                },
            });
            graphicsLayer.add(textGraphic);
        }
    }
};

export const genSymbol = (item: SymbolItem): Symbol => {
    if (item.type === 'polyline') {
        return new CIMSymbol({
            data: {
                type: 'CIMSymbolReference',
                symbol: {
                    type: 'CIMLineSymbol',
                    symbolLayers: [
                        {
                            type: 'CIMSolidStroke',
                            enable: true,
                            color: item.elementsColor ?? [0, 0, 0, 255],
                            width: item.elementsBorderThickness ?? 2,
                            capStyle: 'Round',
                            joinStyle: 'Round',
                            miterLimit: 10,
                            effects: item.elementsBorderType === 'dashed' ? [
                                {
                                    type: 'CIMGeometricEffectDashes',
                                    dashTemplate: [4, 4],
                                    lineDashEnding: 'NoConstraint',
                                    //@ts-ignore
                                    controlPointEnding: 'NoConstraint'
                                }
                            ] : []
                        }
                    ]
                }
            }
        });
    }

    const symbolLayers: any[] = [{
        type: 'CIMSolidFill',
        enable: true,
        color: item.selected ? [255, 255, 150, 100] : item.elementsColor ?? [0, 0, 0],
    },
        {
            type: 'CIMSolidStroke',
            enable: true,
            color: item.isApproved ? [0, 0, 0, 255] : [128, 128, 128, 255],
            width: item.elementsBorderThickness ?? 2,
            capStyle: 'Round',
            joinStyle: 'Round',
            miterLimit: 10,
            effects: item.elementsBorderType === 'dashed' ? [
                {
                    type: 'CIMGeometricEffectDashes',
                    dashTemplate: [4, 4],
                    lineDashEnding: 'NoConstraint',
                    controlPointEnding: 'NoConstraint'
                }
            ] : []
        }
    ];
    // Add target marker if needed
    if (item.isTargetable && item.itemType === 'aoi') {
        symbolLayers.push({
            type: 'CIMVectorMarker',
            enable: true,
            size: 30,
            frame: { xmin: -5, ymin: -5, xmax: 5, ymax: 5 },
            markerGraphics: [{
                type: 'CIMMarkerGraphic',
                geometry: {
                    rings: [[[-5, -5], [5, -5], [0, 5], [-5, -5]]]
                },
                symbol: {
                    type: 'CIMPolygonSymbol',
                    symbolLayers: [{
                        type: 'CIMSolidFill',
                        enable: true,
                        color: [0, 0, 0, 255]
                    }]
                }
            }]
        });
    }

    return new CIMSymbol({
        data: {
            type: 'CIMSymbolReference',
            symbol: {
                type: 'CIMPolygonSymbol',
                symbolLayers
            }
        }
    });
};


export const shiftCoordinatesByOneDegreeLatAndLong = (coordinates: ICoordinates[], shiftCount = 1) => {
    const displayceBy = shiftCount * 3000;
    //go for each coordinate and add 1 degree to the latitude and longitude
    const shiftedCoordinates = coordinates.map((coordinate: [number, number]) => {
        return [coordinate[0] + displayceBy, coordinate[1] + displayceBy];
    });
    console.log('shiftedCoordinates', shiftedCoordinates);
    return shiftedCoordinates;
}

export const getHeight = (height: string|null|number) => {
    //check if there is % in height
    if(!height) {
        return '85%';
    }
    if (height.toString().includes('%')) {
        //make sure height is not more than 80%
        //convert to int
        if (Number(height.toString().replace('%', '')) > 84) {
            return '85%';
        }
        return height;
    } else {
        return height + 'px';
    }
}