// useAccessControl.ts
import { ref, computed } from 'vue';

// Type definitions
type Role = 'admin' | 'org_admin' | 'manager' | 'user';
type Action = 'list' | 'read' | 'create' | 'delete' | 'update';
type ResourceKey = string;

interface ResourceRules {
    rules: {
        list: Role[];
        read: Role[];
        create: Role[];
        delete: Role[];
        update: Role[];
    };
    title: string;
}

interface ResourcesAccess {
    [key: ResourceKey]: ResourceRules;
}

// Create a singleton instance to store rules globally
const globalRules = ref<ResourcesAccess>({});

/**
 * Initialize access rules globally (call this once in your app initialization)
 * @param accessRules - The access rules object from API
 */
export function initializeAccessRules(accessRules: ResourcesAccess): void {
    globalRules.value = accessRules;
}

/**
 * Get the current access rules
 * @returns The current access rules
 */
export function getAccessRules(): ResourcesAccess {
    return globalRules.value;
}

/**
 * Update access rules if needed after initialization
 * @param accessRules - The new access rules object
 */
export function updateAccessRules(accessRules: ResourcesAccess): void {
    globalRules.value = accessRules;
}

/**
 * Composable for handling access control checks
 * @returns Object with methods to check permission
 */
export function useAccessControl() {
    /**
     * Check if a user with given role has permission to perform an action on a resource
     * @param role - The user's role
     * @param resourceKey - The resource key (e.g., 'aoi', 'asset')
     * @param action - The action to check permission for (default is 'read')
     * @returns True if access is granted, false otherwise
     */
    const hasAccess = (
        role: string | undefined,
        resourceKey: ResourceKey,
        action: Action = 'read',
    ): boolean => {
        // If role is undefined, deny access
        if (!role) {
            return false;
        }

        // Check if resource exists
        if (!globalRules.value[resourceKey]) {
            console.warn(`Resource "${resourceKey}" not found in access rules`);
            return false;
        }

        // Check if action exists in resource
        if (!globalRules.value[resourceKey].rules[action]) {
            console.warn(
                `Action "${action}" not found for resource "${resourceKey}"`,
            );
            return false;
        }

        // Check if role is allowed for action
        return globalRules.value[resourceKey].rules[action].includes(
            role as Role,
        );
    };

    /**
     * Get all resources a user with given role can access for a specific action
     * @param role - The user's role
     * @param action - The action to check permission for
     * @returns Array of resource keys
     */
    const getAccessibleResources = (
        role: Role,
        action: Action = 'read',
    ): ResourceKey[] => {
        return Object.keys(globalRules.value).filter((resourceKey) =>
            hasAccess(role, resourceKey, action),
        );
    };

    /**
     * Get a map of all resources and actions that a user with given role can access
     * @param role - The user's role
     * @returns Map of resource keys to allowed actions
     */
    const getUserPermissions = (role: Role) => {
        const permissions: Record<ResourceKey, Action[]> = {};

        Object.keys(globalRules.value).forEach((resourceKey) => {
            const allowedActions: Action[] = [];

            Object.keys(globalRules.value[resourceKey].rules).forEach(
                (actionKey) => {
                    const action = actionKey as Action;
                    if (hasAccess(role, resourceKey, action)) {
                        allowedActions.push(action);
                    }
                },
            );

            if (allowedActions.length > 0) {
                permissions[resourceKey] = allowedActions;
            }
        });

        return permissions;
    };

    return {
        hasAccess,
        getAccessibleResources,
        getUserPermissions,
        rules: computed(() => globalRules.value),
    };
}
