import { useSnackbarStore } from '@/stores/snackbar';

export function useSnackbar() {
    const snackbarStore = useSnackbarStore();

    const showSnackbar = (config: {
        text: string;
        color?: 'success' | 'info' | 'warning' | 'error' | string;
        timeout?: number;
        pos?: 'top-center' | 'top-left' | 'top-right' | 'bottom-center' | 'bottom-left' | 'bottom-right';
        button?: {
            text: string;
            to?: string;
            action?: () => void;
        };
    }) => {
        snackbarStore.showSnackbar(config);
    };

    return {
        showSnackbar,
    };
}
