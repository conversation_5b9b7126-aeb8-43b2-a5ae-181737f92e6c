// composables/useGeometryConverter.ts
import { ref } from 'vue'

// Types
export type GeoJSONType = 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon'
export type ArcGISType = 'point' | 'polyline' | 'polygon' | 'multipoint' | 'extent' | 'mesh'

export interface ISupportGeometry {
  type: GeoJSONType;
  coordinates: number[] | number[][] | number[][][];
}

export interface SymbolItem {
  id: number | null;
  type: 'point' | 'polyline' | 'polygon' | 'multipoint' | null;
  coordinates: number[] | number[][] | number[][][];
  title: string;
  elementsColor?: number[];
  elementsBorderColor?: number[];
  elementsBorderThickness?: number;
  elementsBorderType?: string;
  selected?: boolean;
}

export function useGeometryConverter() {
  const conversionError = ref<string | null>(null)

  /**
   * Converts GeoJSON geometry type to ArcGIS geometry type
   */
  const geoJSONToArcGIS = (geoType: GeoJSONType | string): ArcGISType | null => {
    conversionError.value = null

    switch (geoType.toLowerCase()) {
      case 'point':
        return 'point'
      case 'linestring':
        return 'polyline'
      case 'polygon':
        return 'polygon'
      case 'multipoint':
        return 'multipoint'
      case 'multilinestring':
        // Handle multiple linestrings as a single polyline in ArcGIS
        return 'polyline'
      case 'multipolygon':
        // Handle multiple polygons as a single polygon in ArcGIS
        return 'polygon'
      default:
        conversionError.value = `Unsupported GeoJSON type: ${geoType}`
        return null
    }
  }

  /**
   * Converts ArcGIS geometry type to GeoJSON geometry type
   */
  const arcGISToGeoJSON = (arcGISType: ArcGISType | string): GeoJSONType | null => {
    conversionError.value = null

    switch (arcGISType.toLowerCase()) {
      case 'point':
        return 'Point'
      case 'polyline':
        return 'LineString'
      case 'polygon':
        return 'Polygon'
      case 'multipoint':
        return 'MultiPoint'
      case 'extent':
        // Convert extent to polygon
        return 'Polygon'
      default:
        conversionError.value = `Unsupported ArcGIS type: ${arcGISType}`
        return null
    }
  }

  /**
   * Converts GeoJSON coordinates to ArcGIS format
   */
  const convertGeoJSONCoordinatesToArcGIS = (
    type: GeoJSONType,
    coordinates: number[] | number[][] | number[][][]
  ): number[] | number[][] | number[][][] => {
    try {
      switch (type) {
        case 'Point':
          return coordinates as number[]
        case 'LineString':
          return coordinates as number[][]
        case 'Polygon':
          return coordinates as number[][][]
        case 'MultiPoint':
          // Convert multipoint to array of points
          return (coordinates as number[][])
        case 'MultiLineString':
          // Flatten multilinestring to single array of points
          return (coordinates as number[][][]).flat()
        case 'MultiPolygon':
          // Take first polygon from multipolygon
          // return [(coordinates as number[][][][])[0][0]]
          //TODO: NOT IMPLEMENTED
        default:
          throw new Error(`Unsupported geometry type: ${type}`)
      }
    } catch (error) {
      conversionError.value = `Error converting coordinates`
      throw error
    }
  }

  /**
   * Complete conversion of GeoJSON to ArcGIS format
   */
  const convertToArcGIS = (geoJSON: ISupportGeometry) => {
    const arcGISType = geoJSONToArcGIS(geoJSON.type)

    if (!arcGISType) {
      throw new Error(`Unable to convert geometry type: ${geoJSON.type}`)
    }

    return {
      type: arcGISType,
      coordinates: convertGeoJSONCoordinatesToArcGIS(
        geoJSON.type,
        geoJSON.coordinates
      )
    }
  }


  /**
   * Convert SymbolItem to ArcGIS format
   */
  const convertSymbolItemToArcGIS = (item: SymbolItem): SymbolItem => {
    if (!item.type) return item

    try {
      const converted = convertToArcGIS({
        type: item.type.charAt(0).toUpperCase() + item.type.slice(1) as GeoJSONType,
        coordinates: item.coordinates
      })

      return {
        ...item,
        type: converted.type as SymbolItem['type'],
        coordinates: converted.coordinates
      }
    } catch (error) {
      conversionError.value = `Error converting symbol item`
      throw error
    }
  }

  /**
   * Validate coordinates structure based on geometry type
   */
  const validateCoordinates = (
    type: GeoJSONType | ArcGISType,
    coordinates: any
  ): boolean => {
    try {
      switch (type.toLowerCase()) {
        case 'point':
        case 'Point':
          return Array.isArray(coordinates) && coordinates.length === 2 &&
            coordinates.every(coord => typeof coord === 'number')
        case 'linestring':
        case 'polyline':
        case 'LineString':
          return Array.isArray(coordinates) && coordinates.length >= 2 &&
            coordinates.every(point => Array.isArray(point) && point.length === 2)
        case 'polygon':
        case 'Polygon':
          return Array.isArray(coordinates) && coordinates.length > 0 &&
            coordinates.every(ring => Array.isArray(ring) && ring.length >= 4)
        default:
          return false
      }
    } catch (error) {
      conversionError.value = `Validation error`
      return false
    }
  }

  return {
    // Conversion functions
    geoJSONToArcGIS,
    arcGISToGeoJSON,
    convertToArcGIS,
    convertSymbolItemToArcGIS,
    // Utility functions
    validateCoordinates,
    // Error handling
    conversionError
  }
}

// Export default and named exports for flexibility
export default useGeometryConverter
