/**
 * src/styles/settings.scss
 *
 * Configures SASS variables and Vuetify overwrites
 */

// https://vuetifyjs.com/features/sass-variables/`
 @use 'vuetify/settings' with (
   $color-pack: true
 );
//@import 'flag-icons/css/flag-icons.min.css';


.section-title {
  font-size: 20px;
  font-weight: bold;
 padding: 10px;
  color: #1E1E1E;
}

.lumio-card {
  .v-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3, h4, h5 {
      //text-uppercase
      text-transform: uppercase;
    }
    h4 {
      font-size: 1.5rem;
      font-weight: 600;
    }
    h5 {
      font-size: 1.25rem;
      font-weight: 600;
    }
  }
  .v-card-text {
    padding: 0;
    margin: 0;
  }
}

// NAI Form Styles
.create-NAIForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  width: 100%;
  background-color: #ffffff;
}

// Checkbox Container
.checkbox-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
  padding: 0.3rem 0;

  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.3rem;

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      cursor: pointer;
      accent-color: #1E1E1E;
    }

    label {
      font-size: 0.9rem;
      color: #333;
      cursor: pointer;
    }
  }
}

// Description Container
.description-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.3rem;

  label {
    font-weight: 500;
    color: #333;
  }

  textarea {
    width: 100%;
    min-height: 30px;
    padding: 0.65rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;

    &:focus {
      outline: none;
      border-color: #1E1E1E;
      box-shadow: 0 0 0 2px rgba(30, 30, 30, 0.1);
    }

    &::placeholder {
      color: #999;
    }
  }
}

// Coordinates Container
.coordinates-container {
  width: 100%;
  padding: 0.3rem;
  background-color: #f5f5f5;
  border-radius: 4px;

  p {
    margin: 0.2rem 0;
    font-family: monospace;
    font-size: 0.9rem;
    line-height: 1.2;
  }
}

// Save Button
.create-NAIButton {
  align-self: flex-start;
  padding: 0.75rem 1.5rem;
  background-color: #1E1E1E;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #333;
  }

  &:active {
    transform: translateY(1px);
  }
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 100;
  pointer-events: all;
}

:deep(.esri-popup) {
  z-index: 101;
}
