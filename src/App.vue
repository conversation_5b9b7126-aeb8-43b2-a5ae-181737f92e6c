<template>
    <v-app style="background-color: #eeeeee">
        <v-main>
            <router-view />
        </v-main>
        <v-snackbar v-model="show" :color="color" :timeout="timeout">
            {{ text }}

            <template v-slot:actions>
                <v-btn
                    v-if="snackbarStore.button"
                    :to="snackbarStore.button.to"
                    @click="handleButtonClick"
                    color="white"
                    variant="text"
                >
                    {{ snackbarStore.button.text }}
                </v-btn>
                <v-btn
                    v-else
                    color="white"
                    variant="text"
                    @click="snackbarStore.hideSnackbar"
                >
                    Dismiss
                </v-btn>
            </template>
        </v-snackbar>
    </v-app>
</template>

<script lang="ts" setup>
import { useSnackbarStore } from '@/stores/snackbar';
import { storeToRefs } from 'pinia';

const snackbarStore = useSnackbarStore();
const { show, text, color, timeout } = storeToRefs(snackbarStore);

const handleButtonClick = () => {
    if (snackbarStore.button?.action) {
        snackbarStore.button?.action();
    }
    snackbarStore.hideSnackbar();
};
</script>

<style>
.color-primary {
    color: #effa1a;
}
</style>
