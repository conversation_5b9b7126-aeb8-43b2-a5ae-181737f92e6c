import  Graphic from '@arcgis/core/Graphic';
import CIMSymbol from '@arcgis/core/symbols/CIMSymbol';
import { GraphicElement, Symbol } from '@/types/EsriMap';

export const webMercatorToGeographic = (x: number, y: number): [number, number] => {
  const RADIUS = 6378137.0;
  const longitude = (x / RADIUS) * (180 / Math.PI);
  const latitude = Math.PI / 2 - 2 * Math.atan(Math.exp(-y / RADIUS));
  const latitudeDegrees = latitude * (180 / Math.PI);
  return [longitude, latitudeDegrees];
};

export const convertGraphicToElement = (graphic: Graphic): GraphicElement => {
  const geometry = graphic.geometry.toJSON();
  const type = graphic.geometry.type;
  let coordinates: number[] = [];

  if (type === 'point') {
    coordinates = [geometry.x, geometry.y];
  } else if (type === 'polygon') {
    const points = geometry.rings[0];
    coordinates = points.map((point: number[]) =>
      webMercatorToGeographic(point[0], point[1])
    );
  } else if (type === 'polyline') {
    const points = geometry.paths[0];
    coordinates = points.map((point: number[]) =>
      webMercatorToGeographic(point[0], point[1])
    );
  }

  return {
    locationCoordinates: {
      type,
      coordinates,
      nativeCoordinates: [],
    }
  };
};

export const genSymbol = ({
                            id,
                            name,
                            designation,
                            description,
                            selected,
                            elementsColor = [0, 0, 0, 255]
                          }: Symbol): CIMSymbol => {
  return new CIMSymbol({
    data: {
      type: 'CIMSymbolReference',
      symbol: {
        type: 'CIMPolygonSymbol',
        symbolLayers: [
          {
            type: 'CIMSolidFill',
            enable: true,
            color: selected ? [51, 158, 255, 150] : [0, 0, 0, 40],
          },
          {
            type: 'CIMSolidStroke',
            enable: true,
            color: elementsColor,
            width: 2,
            effects: [
              {
                type: 'CIMGeometricEffectDashes',
                dashTemplate: [5, 5],
                lineDashEnding: 'FullGap',
                offsetAlongLine: 0,
              },
            ],
          },
          {
            type: 'CIMVectorMarker',
            enable: true,
            size: 10,
            // anchor: 'Center', // Position the text in the center
            offsetX: 0,
            offsetY: 0,
            frame: { xmin: 0, ymin: 0, xmax: 0, ymax: 0 },
            markerGraphics: [
              {
                type: 'CIMMarkerGraphic',
                geometry: { x: 0, y: 0 },
                symbol: {
                  type: 'CIMTextSymbol',
                  fontFamilyName: 'Arial',
                  fontStyleName: 'Bold',
                  height: 10,
                  horizontalAlignment: 'Center',
                  verticalAlignment: 'Center',
                  symbol: {
                    type: 'CIMPolygonSymbol',
                    symbolLayers: [
                      {
                        type: 'CIMSolidFill',
                        enable: true,
                        color: [255, 255, 255, 255] // White text
                      }
                    ]
                  },
                },
                textString: name || designation || description || id.toString()
              }
            ],
          },
        ],
      },
    },
  });
};


export const convertToAPIColor = (color: string | number[]): number[] => {
  // If color is already an array, return it
  if (Array.isArray(color)) {
    return color;
  }

  // Handle string input
  if (typeof color === 'string') {
    const matches = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)/);
    if (matches) {
      const r = parseInt(matches[1]);
      const g = parseInt(matches[2]);
      const b = parseInt(matches[3]);
      const a = matches[4] ? Math.round(parseFloat(matches[4]) * 255) : 255;
      return [r, g, b, a];
    }
  }

  return [0, 0, 0, 255]; // Default fallback
};

export const convertToRGBA = (color: number[]): string => {
  // Convert [232, 232, 232, 159] to "rgba(232, 232, 232, 0.625)"
  const [r, g, b, a] = color;
  const alpha = (a / 255).toFixed(3);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};
