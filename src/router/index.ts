/**
 * router/index.ts
 *
 * Automatic routes for `./src/pages/*.vue`
 */

// Composables
import { createRouter, createWebHistory } from 'vue-router/auto';
import { setupLayouts } from 'virtual:generated-layouts';
import { routes } from 'vue-router/auto-routes';
import { useAuthStore } from '@/stores/auth';

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        ...setupLayouts(routes),
        {
            path: '/:pathMatch(.*)*',
            component: () => import('../pages/404.vue'),
        },
    ],
});

// Workaround for https://github.com/vitejs/vite/issues/11804
router.onError((err, to) => {
    if (
        err?.message?.includes?.('Failed to fetch dynamically imported module')
    ) {
        if (!localStorage.getItem('vuetify:dynamic-reload')) {
            localStorage.setItem('vuetify:dynamic-reload', 'true');
            location.assign(to.fullPath);
        } else {
            console.error(
                'Dynamic import error, reloading page did not fix it',
                err,
            );
        }
    } else {
        console.error(err);
    }
});

router.beforeEach((to, from, next) => {
    const auth = useAuthStore();
    const accessToken = localStorage.getItem('accessToken');

    if (accessToken && to.meta.admin && !auth.isAdmin) {
        return next('/home');
    }

    if (to.meta.protected && !accessToken) {
        next('/signin');
    } else {
        next();
    }
});

router.isReady().then(() => {
    localStorage.removeItem('vuetify:dynamic-reload');
});

export default router;
