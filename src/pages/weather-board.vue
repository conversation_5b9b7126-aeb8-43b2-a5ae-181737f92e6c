<template>
    <div>
        <div class="weather-board px-4 py-4">
            <div class="weather-map">
                <div id="viewDiv" ref="mapContainer">
                    <div id="measurements" class="esri-widget h-100"></div>
                </div>
            </div>
            <div class="widget-container">
                <div id="openweathermap-widget-11" class="widget-small"></div>
                <div id="openweathermap-widget-15" class="widget-full"></div>
            </div>
        </div>

        <v-card class="mx-4 mt-4">
            <v-tabs v-model="tab">
                <v-tab value="one">24hr Effects </v-tab>
                <v-tab value="two">48hr Effects</v-tab>
                <v-tab value="three">72hr Effects</v-tab>
            </v-tabs>

            <v-card-text>
                <v-tabs-window v-model="tab">
                    <v-tabs-window-item value="one">
                        <v-table>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-sunny</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: high </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-rainy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: medium </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-snowy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: high </v-col>
                            </v-row>
                        </v-table>
                    </v-tabs-window-item>

                    <v-tabs-window-item value="two">
                        <v-table>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-rainy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> Operational </v-col>

                                <v-col> Priority: low </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-rainy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: medium </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-sunny</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> Operational </v-col>

                                <v-col> Priority: low </v-col>
                            </v-row>
                        </v-table>
                    </v-tabs-window-item>

                    <v-tabs-window-item value="three">
                        <v-table>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-rainy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: medium </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-rainy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: medium </v-col>
                            </v-row>
                            <v-row>
                                <v-col> Asset </v-col>
                                <v-col>
                                    <v-icon>mdi-weather-snowy</v-icon>
                                </v-col>
                                <v-col> Description of Weather Effect </v-col>
                                <v-col> DTG Operational </v-col>

                                <v-col> Priority: high </v-col>
                            </v-row>
                        </v-table>
                    </v-tabs-window-item>
                </v-tabs-window>
            </v-card-text>
        </v-card>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script setup lang="ts">
import { onMounted, nextTick } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import Point from '@arcgis/core/geometry/Point';
import TextSymbol from '@arcgis/core/symbols/TextSymbol';
import Graphic from '@arcgis/core/Graphic';

const tab = ref<string>('');


// Replace generateRandomPoints with Australian cities data
const generateAustralianCityPoints = () => {
    const cities = [
        { name: 'Sydney', lat: -33.8688, lon: 151.2093, temp: 28 },
        { name: 'Melbourne', lat: -37.8136, lon: 144.9631, temp: 25 },
        { name: 'Brisbane', lat: -27.4698, lon: 153.0251, temp: 30 },
        { name: 'Perth', lat: -31.9505, lon: 115.8605, temp: 31 },
        { name: 'Adelaide', lat: -34.9285, lon: 138.6007, temp: 27 },
        { name: 'Canberra', lat: -35.2809, lon: 149.13, temp: 24 },
        { name: 'Darwin', lat: -12.4634, lon: 130.8456, temp: 33 },
        { name: 'Hobart', lat: -42.8821, lon: 147.3272, temp: 22 },
    ];

    return cities.map((city, index) => ({
        geometry: new Point({
            longitude: city.lon,
            latitude: city.lat,
            spatialReference: { wkid: 4326 },
        }),
        attributes: {
            ObjectID: index,
            name: city.name,
            temperature: city.temp,
        },
    }));
};

const initializeMap = async () => {
    esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;

    // Wait for the next DOM update
    await nextTick();

    const map = new Map({
        basemap: 'satellite', // Changed to satellite
    });

    const view = new MapView({
        container: 'viewDiv',
        map: map,
        center: [133.7751, -25.2744], // Centered on Australia
        zoom: 4,
        popupEnabled: true,
    });

    // Add widgets
    const basemapToggle = new BasemapToggle({
        view: view,
    });
    view.ui.add(basemapToggle, 'top-right');

    const fullscreen = new Fullscreen({
        view: view,
    });
    view.ui.add(fullscreen, 'top-right');

    const scalebar = new ScaleBar({
        view: view,
        unit: 'metric',
    });
    view.ui.add(scalebar, 'bottom-right');

    // Replace heatmap layer with temperature points
    const cityPoints = generateAustralianCityPoints();

    cityPoints.forEach((point) => {
        const textSymbol = new TextSymbol({
            color: '#ffffff',
            haloColor: '#000000',
            haloSize: 1,
            text: `${point.attributes.name}\n${point.attributes.temperature}°C`,
            font: {
                size: 12,
                weight: 'bold',
            },
        });

        const graphic = new Graphic({
            geometry: point.geometry,
            symbol: textSymbol,
        });

        view.graphics.add(graphic);
    });
};

const loadWeatherWidgets = () => {
    // Configure widget parameters
    // @ts-ignore
    window.myWidgetParam = window.myWidgetParam || [];

    // First widget
    // @ts-ignore
    window.myWidgetParam.push({
        id: 11,
        cityid: '2643743',
        appid: 'e963941226451fabb5aee09aa422654b',
        units: 'metric',
        containerid: 'openweathermap-widget-11',
    });

    // Second widget
    // @ts-ignore
    window.myWidgetParam.push({
        id: 15,
        cityid: '2643743',
        appid: 'e963941226451fabb5aee09aa422654b',
        units: 'metric',
        containerid: 'openweathermap-widget-15',
    });

    // Load D3.js (only need to load once)
    const d3Script = document.createElement('script');
    d3Script.src =
        '//openweathermap.org/themes/openweathermap/assets/vendor/owm/js/d3.min.js';
    d3Script.async = true;

    // Load widget generator script (only need to load once)
    const widgetScript = document.createElement('script');
    widgetScript.src =
        '//openweathermap.org/themes/openweathermap/assets/vendor/owm/js/weather-widget-generator.js';
    widgetScript.async = true;
    widgetScript.charset = 'utf-8';

    // Append scripts to document
    document.head.appendChild(d3Script);
    document.head.appendChild(widgetScript);
};

onMounted(() => {
    initializeMap();
    loadWeatherWidgets();
});
</script>

<style lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

.weather-board {
    display: flex;
    width: 100%;
}

.weather-map {
    width: 50%; // Takes half of the container width
    height: 500px; // Fixed height
    margin-right: 16px; // Add some spacing for the right column
}

#viewDiv {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.widget-container {
    width: 50%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.widget-small {
    width: 100%;
    min-height: 200px; // Adjust this value as needed
}

.widget-full {
    width: 100%;
    min-height: 250px; // Adjust this value as needed
}

.widget-left,
.widget-right {
    width: 100% !important;
}
</style>
