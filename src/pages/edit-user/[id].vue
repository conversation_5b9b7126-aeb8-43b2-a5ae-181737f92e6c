<template>
    <h1 class="section-title">Edit user</h1>
    <v-container>
        <v-form ref="userForm" @submit.prevent="updateUser">
            <v-text-field
                label="First Name"
                v-model="firstName"
                :rules="rules.required"
            />
            <v-text-field
                label="Last Name"
                v-model="lastName"
                :rules="rules.required"
            />
            <v-text-field label="Email" v-model="email" :rules="rules.email" />
            <v-text-field
                label="Position Id"
                v-model="positionId"
                :rules="rules.required"
                placeholder="S23"
            />

            <time-zone class="mb-4" single @update="handleTimezoneUpdate" />

            <v-btn type="submit">Update User</v-btn>
        </v-form>
    </v-container>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script lang="ts" setup>
import { useUtilsStore } from '@/stores/utils';
import { useAuthStore } from '@/stores/auth';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useSnackbar } from '@/composables/useSnackbar';

const { showSnackbar } = useSnackbar();

const router = useRouter();
const utils = useUtilsStore();
const auth = useAuthStore();

const userForm = ref();
const route = useRoute();

const params = route.params as { id: string };
const id = params.id;

const firstName = ref('');
const lastName = ref('');
const email = ref('');
const positionId = ref('');

const timezone = ref('');

const loading = ref(false);

const rules = {
    required: [(v: string) => !!v || 'Required.'],
    email: [
        (v: string) => !!v || 'Email is required',
        (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ],
};

onMounted(async () => {
    try {
        loading.value = true;
        const user = await utils.getUserById(id);
        firstName.value = user.firstName;
        lastName.value = user.lastName;
        email.value = user.email;
        positionId.value = user.positionId;
    } catch (error) {
        showSnackbar({
            text: 'Failed to fetch user',
            color: 'error',
        });
        console.error(error);
    } finally {
        loading.value = false;
    }
});

const updateUser = async () => {
    try {
        loading.value = true;
        await auth.updateUser(id, {
            firstName: firstName.value,
            lastName: lastName.value,
            email: email.value,
            positionId: positionId.value,
            timezone: timezone.value,
        });
        showSnackbar({
            text: 'User updated successfully',
            color: 'success',
        });
        await router.push(`/admin/view-user/${id}`);
    } catch (error) {
        showSnackbar({
            text: 'Error updating user',
            color: 'error',
        });
        console.error(error);
    } finally {
        loading.value = false;
    }
};

const handleTimezoneUpdate = (val: string) => {
    timezone.value = val;
};
</script>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>
