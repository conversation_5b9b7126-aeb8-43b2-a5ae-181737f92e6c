<template>
    <div class="task-details">
        <v-row>
            <v-col cols="12">
                <v-card class="lumio-card">
                    <v-card-title
                        class="d-flex justify-space-between align-center pa-4"
                    >
                        <div class="d-flex align-center">
                            <v-btn
                                icon="mdi-arrow-left"
                                variant="text"
                                class="mr-4"
                                @click="router.push('/')"
                            ></v-btn>
                            <h2 class="text-h5">{{ task?.title }}</h2>
                        </div>
                        <div class="d-flex align-center">
                            <v-chip
                                :color="getStatusColor(task?.status)"
                                size="small"
                                class="mr-2"
                            >
                                {{ task?.status }}
                            </v-chip>
                            <v-chip
                                :color="getPriorityColor(task?.priority)"
                                size="small"
                            >
                                {{ task?.priority }}
                            </v-chip>
                        </div>
                    </v-card-title>

                    <v-divider></v-divider>

                    <v-card-text class="pa-4">
                        <v-row>
                            <v-col cols="12" md="8">
                                <div class="mb-6">
                                    <h3
                                        class="text-subtitle-1 font-weight-medium mb-2"
                                    >
                                        Description
                                    </h3>
                                    <p class="text-body-1">
                                        {{ task?.description }}
                                    </p>
                                </div>

                                <div class="mb-6">
                                    <h3
                                        class="text-subtitle-1 font-weight-medium mb-2"
                                    >
                                        Short Description
                                    </h3>
                                    <p class="text-body-1">
                                        {{
                                            task?.shortDescription ||
                                            'No short description provided'
                                        }}
                                    </p>
                                </div>

                                <div class="mb-6">
                                    <h3
                                        class="text-subtitle-1 font-weight-medium mb-2"
                                    >
                                        Labels
                                    </h3>
                                    <div
                                        v-if="task?.labels?.length"
                                        class="d-flex flex-wrap gap-2"
                                    >
                                        <v-chip
                                            v-for="label in task.labels"
                                            :key="label"
                                            size="small"
                                            color="primary"
                                            variant="outlined"
                                        >
                                            {{ label }}
                                        </v-chip>
                                    </div>
                                    <p v-else class="text-body-1">
                                        No labels assigned
                                    </p>
                                </div>

                                <div class="mb-6">
                                    <h3
                                        class="text-subtitle-1 font-weight-medium mb-2"
                                    >
                                        Assigned Members
                                    </h3>
                                    <div
                                        v-if="task?.members?.length"
                                        class="d-flex flex-wrap gap-4"
                                    >
                                        <v-card
                                            v-for="member in task.members"
                                            :key="member.id"
                                            variant="outlined"
                                            class="member-card mx-4"
                                        >
                                            <v-card-text class="pa-2">
                                                <div
                                                    class="d-flex align-center"
                                                >
                                                    <v-avatar
                                                        size="40"
                                                        color="primary"
                                                        class="mr-3"
                                                    >
                                                        {{
                                                            getInitials(member)
                                                        }}
                                                    </v-avatar>
                                                    <div>
                                                        <div
                                                            class="text-subtitle-1 font-weight-medium"
                                                        >
                                                            {{
                                                                member.firstName
                                                            }}
                                                            {{
                                                                member.lastName
                                                            }}
                                                        </div>
                                                        <div
                                                            class="text-caption text-medium-emphasis"
                                                        >
                                                            ID: #{{ member.id }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </v-card-text>
                                        </v-card>
                                    </div>
                                    <div class="d-flex align-center mt-2">
                                        <p
                                            v-if="!task?.members?.length"
                                            class="text-body-1 mr-2"
                                        >
                                            No members assigned
                                        </p>
                                        <v-btn
                                            size="small"
                                            color="primary"
                                            variant="text"
                                            @click="addMembers"
                                        >
                                            <v-icon left>mdi-plus</v-icon>
                                            Add Members
                                        </v-btn>
                                    </div>
                                </div>
                            </v-col>

                            <v-col cols="12" md="4">
                                <v-card
                                    variant="outlined"
                                    class="mb-4 details-card"
                                >
                                    <v-card-title
                                        class="text-subtitle-1 font-weight-medium pa-3"
                                    >
                                        Task Details
                                    </v-card-title>
                                    <v-divider></v-divider>
                                    <v-card-text class="pa-0">
                                        <v-list
                                            density="compact"
                                            class="details-list"
                                        >
                                            <v-list-item class="details-item">
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="primary"
                                                        class="mr-3"
                                                        >mdi-calendar-clock</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Due Date</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >{{
                                                        formatDate(task?.dueAt)
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>

                                            <v-divider></v-divider>

                                            <v-list-item class="details-item">
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="primary"
                                                        class="mr-3"
                                                        >mdi-account</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Created
                                                    By</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >User #{{
                                                        task?.createdByUserId
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>

                                            <v-divider></v-divider>

                                            <v-list-item class="details-item">
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="primary"
                                                        class="mr-3"
                                                        >mdi-calendar-plus</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Created
                                                    At</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >{{
                                                        formatDate(
                                                            task?.createdAt,
                                                        )
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>

                                            <v-divider></v-divider>

                                            <v-list-item class="details-item">
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="primary"
                                                        class="mr-3"
                                                        >mdi-calendar-edit</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Last
                                                    Updated</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >{{
                                                        formatDate(
                                                            task?.updatedAt,
                                                        )
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>

                                            <v-divider
                                                v-if="task?.completedAt"
                                            ></v-divider>

                                            <v-list-item
                                                v-if="task?.completedAt"
                                                class="details-item"
                                            >
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="success"
                                                        class="mr-3"
                                                        >mdi-check-circle</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Completed
                                                    At</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >{{
                                                        formatDate(
                                                            task?.completedAt,
                                                        )
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>

                                            <v-divider
                                                v-if="task?.archivedAt"
                                            ></v-divider>

                                            <v-list-item
                                                v-if="task?.archivedAt"
                                                class="details-item"
                                            >
                                                <template v-slot:prepend>
                                                    <v-icon
                                                        color="grey"
                                                        class="mr-3"
                                                        >mdi-archive</v-icon
                                                    >
                                                </template>
                                                <v-list-item-title
                                                    class="text-caption text-medium-emphasis"
                                                    >Archived
                                                    At</v-list-item-title
                                                >
                                                <v-list-item-subtitle
                                                    class="text-body-1"
                                                    >{{
                                                        formatDate(
                                                            task?.archivedAt,
                                                        )
                                                    }}</v-list-item-subtitle
                                                >
                                            </v-list-item>
                                        </v-list>
                                    </v-card-text>
                                </v-card>

                                <div class="d-flex justify-end gap-2">
                                    <v-btn
                                        color="primary"
                                        variant="outlined"
                                        @click="editTask"
                                    >
                                        Edit
                                    </v-btn>
                                    <v-btn
                                        color="error"
                                        variant="outlined"
                                        @click="deleteTask"
                                    >
                                        Delete
                                    </v-btn>
                                </div>
                            </v-col>
                        </v-row>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>

        <!-- Task Modal -->
        <CreateTaskModal
            v-model="showTaskModal"
            :taskId="selectedTaskId"
            @task-updated="handleTaskUpdated"
        />

        <!-- Assign Members Modal -->
        <AssignUserTaskModal
            v-if="selectedTaskForMembers"
            :model-value="showAssignMembersModal"
            :taskId="selectedTaskForMembers"
            @update:model-value="
                (value) => {
                    showAssignMembersModal = value;
                    if (!value) fetchTask();
                }
            "
        />
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useTaskStore, ITask } from '@/stores/admin/task.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAccessControl } from '@/composables/useAccessControl';
import CreateTaskModal from '@/components/tasks/CreateTaskModal.vue';
import AssignUserTaskModal from '@/components/tasks/AssignUserTaskModal.vue';

useAccessControl();

const route = useRoute();
const router = useRouter();
const taskStore = useTaskStore();
const { showSnackbar } = useSnackbar();

const task = ref<ITask | null>(null);
const isLoading = ref(false);
const showTaskModal = ref(false);
const selectedTaskId = ref<number | undefined>(undefined);
const showAssignMembersModal = ref(false);
const selectedTaskForMembers = ref<number | undefined>(undefined);

const getPriorityColor = (priority: string | undefined) => {
    switch (priority?.toLowerCase()) {
        case 'highest':
            return 'error';
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const getStatusColor = (status: string | undefined) => {
    switch (status) {
        case 'completed':
            return 'success';
        case 'in_progress':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'error';
        default:
            return 'grey';
    }
};

const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const fetchTask = async () => {
    isLoading.value = true;
    try {
        const taskId = Number((route.params as { id: string }).id);
        if (!taskId) {
            throw new Error('Task ID is required');
        }
        const response = await taskStore.fetchTaskById(taskId);
        if (response?.data?.task) {
            task.value = response.data.task;
        }
    } catch (error) {
        console.error('Error fetching task:', error);
        showSnackbar({
            text: 'Failed to fetch task details',
            color: 'error',
        });
    } finally {
        isLoading.value = false;
    }
};

const editTask = () => {
    if (task.value?.id) {
        selectedTaskId.value = task.value.id;
        showTaskModal.value = true;
    }
};

const deleteTask = async () => {
    if (!task.value?.id) return;

    const confirmDelete = window.confirm(
        `Are you sure you want to delete the task "${task.value.title}"?`,
    );

    if (!confirmDelete) return;

    try {
        await taskStore.deleteTask(task.value.id);
        showSnackbar({
            text: 'Task deleted successfully',
            color: 'success',
        });
        router.push('/');
    } catch (error) {
        console.error('Error deleting task:', error);
        showSnackbar({
            text: 'Failed to delete task',
            color: 'error',
        });
    }
};

const handleTaskUpdated = () => {
    showSnackbar({
        text: 'Task updated successfully',
        color: 'success',
    });
    fetchTask();
};

const getInitials = (member: { firstName: string; lastName: string }) => {
    return `${member.firstName.charAt(0)}${member.lastName.charAt(
        0,
    )}`.toUpperCase();
};

const addMembers = () => {
    if (task.value?.id) {
        selectedTaskForMembers.value = task.value.id;
        showAssignMembersModal.value = true;
    }
};

onMounted(() => {
    fetchTask();
});
</script>

<style scoped>
.task-details {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.gap-2 {
    gap: 8px;
}

.gap-3 {
    gap: 12px;
}

.mb-6 {
    margin-bottom: 24px;
}

:deep(.v-card-title) {
    padding: 16px;
}

:deep(.v-card-text) {
    padding: 16px;
}

.member-card {
    min-width: 300px;
    max-width: 100%;
}

.details-card {
    border-radius: 8px;
}

.details-list {
    background: transparent;
}

.details-item {
    min-height: 56px;
    padding: 8px 16px;
}

.details-item :deep(.v-list-item__prepend) {
    margin-right: 8px;
    width: 24px;
}

.details-item :deep(.v-list-item__content) {
    padding: 0;
}

.details-item :deep(.v-list-item__title) {
    margin-bottom: 2px;
}

.details-item :deep(.v-list-item__subtitle) {
    opacity: 1;
}
</style>
