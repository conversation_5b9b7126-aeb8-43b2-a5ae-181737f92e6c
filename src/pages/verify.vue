<template>

</template>

<script setup lang="ts">
import { AxiosError } from 'axios';

//TODO: extract email and token from url
//send request to backend to verify email via verifyEmail in register.store.ts
//show message if successful
import { useRegisterStore } from '@/stores/register.store';
import {onMounted} from "vue";
import {useSnackbarStore} from "@/stores/snackbar";
import {useRouter} from "vue-router";

const snackbarStore = useSnackbarStore();

const route = useRoute();
const router = useRouter();
const registerStore = useRegisterStore();


const verifyEmailAndToken = async () => {
  try{
    const email = (route.query?.email) ?  route.query.email as string : null;
    const token = (route.query?.token) ?  route.query.token as string : null;
    if(!email || !token) return;
    (await registerStore.verifyEmail(email, token)).data;

    snackbarStore.showSnackbar({
      text: "ALL DONE! Verified. Now you can sign in.",
      color: "success",
    });
    //redirect to login
  } catch (e){
    if (e instanceof AxiosError && e.response?.data) {
      // Axios-specific error
      const axiosResponse = e.response.data;
      snackbarStore.showSnackbar({
        text: "Error: " + axiosResponse.error,
        color: "error",
      });
    } else {
      // Generic error handling in case it's not an AxiosError
      snackbarStore.showSnackbar({
        text: "An unexpected error occurred",
        color: "error",
      });
    }

  } finally {
    //redirect to login
    await router.push('/signin');
  }

}
onMounted(async () => {
  await verifyEmailAndToken();
});


//run verification on mount


</script>
