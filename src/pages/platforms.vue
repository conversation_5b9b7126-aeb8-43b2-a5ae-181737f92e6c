<template>
	<ViewPlatformsContainer />

</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script lang="ts" setup>
import  ViewPlatformsContainer  from '@/components/platforms/ViewPlatformsContainer.vue';
import { useAccessControl } from '@/composables/useAccessControl';
import { onMounted } from 'vue';

useAccessControl();

// Now we can safely use the permissions since the parent layout
// ensures rules are loaded before rendering this component
onMounted(() => {

});
</script>
