<template>
	<div class="ma-4">
	<v-form ref="roleForm" @submit.prevent="addRole">
		<v-card class="lumio-card">
			<v-card-title>
				<h1 class="section-title">Add Role</h1>
			</v-card-title>
			<v-card-text class="pa-10">
				<v-row>
					<v-col cols="2">
						<v-select
							label="Role Type"
							v-model="formData.roleType"
							:items="['org_admin', 'manager', 'member', 'analyst', 'external', 'vendor']"
						></v-select>
					</v-col>
					<v-col cols="4">
						<div class="d-flex justify-space-start">
							<v-checkbox
								label="Is command role"
								v-model="formData.isCommandRole"
							/>
							<v-checkbox
								class="ml-4"
								label="Is staff role"
								v-model="formData.isStaffRole"
							/>
						</div>
					</v-col>
				</v-row>
			<v-row>

				<v-col cols="6">
					<v-text-field
						label="Role name"
						v-model="formData.roleName"
						:rules="rules.required"
						:loading="loading"
					/>
				</v-col>
				<v-col cols="3">
					<v-text-field
						label="Staff designation"
						v-model="formData.staffDesignation"
						:rules="rules.required"
						:loading="loading"
					/>
				</v-col>
				<v-col cols="3">
					<v-select
						label="Rank"
						v-model="formData.rank"
						:rules="rules.required"
						:items="[
							'No Rank',
                            'Sergeant',
                            'Lieutenant',
                            'Captain',
                            'Major',
                            'Colonel',
                            'General',
                        ]"
						:loading="loading"
					/>
				</v-col>
			</v-row>
				<v-row>
					<v-col cols="6">
						<v-text-field
							label="Functional area"
							v-model="formData.functionalArea"
							:rules="rules.required"
							:loading="loading"
						/>
					</v-col>
					<v-col cols="3">
						<v-select
							label="Unit level"
							v-model="formData.unitLevel"
							:rules="rules.required"
							:items="[
							'N/A',
							'Other',
                            'Squad',
                            'Platoon',
                            'Company',
                            'Battalion',
                            'Regiment',
                            'Brigade',
                            'Division',
                            'Corps',
                            'Army',
                            'Fleet',
                            'Task Force',
                            'Division',
                            'Regiment',
                            'Battalion',
                            'Company',
                            'Platoon',
                            'Squad',
                        ]"
							:loading="loading"
						/>
					</v-col>
					<v-col cols="3">
						<v-text-field
							label="Typical unit size"
							v-model="formData.typicalUnitSize"
							:rules="rules.required"
							:loading="loading"
						/>
					</v-col>

				</v-row>
				<v-row>
				<v-col cols="12">
					<v-textarea
						label="Role description"
						v-model="formData.roleDescription"
						:rules="rules.required"
						:loading="loading"
					/>
				</v-col>
			</v-row>
		</v-card-text>
		<v-card-actions class="d-flex justify-end align-content-end">
			<v-btn
				variant="flat"
				color="primary"
				type="submit"
				prepend-icon="mdi-account-plus"
			>Add Role</v-btn>
		</v-card-actions>
	</v-card>
	</v-form>
	</div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref } from 'vue';
import type { Role } from '@/types/Role';
import type { Organization } from '@/types/Organization';
const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const roleForm = ref();
const loading = ref(false);

const formData = reactive<Role>({
    roleName: '',
	roleType: 'user',
    roleDescription: '',
    unitLevel: '',
    staffDesignation: '',
    functionalArea: '',
    rank: '',
    isCommandRole: false,
    isStaffRole: false,
    typicalUnitSize: '',
    organizationId: null,
});

const availableOrganizations = ref([]);

const rules = {
    required: [(v: string) => !!v || 'Required.'],
};

const currentRoles = ref<Role[]>([]);

const getRoles = async () => {
    try {
        const response = await admin.fetchRoles();
        currentRoles.value = response.roles;
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error fetching roles',
            color: 'error',
        });
    }
};

const addRole = async () => {
    try {
        loading.value = true;
        const validation = await (roleForm.value as HTMLFormElement).validate();
        if (!validation.valid) {
            showSnackbar({
                text: 'Please fill in all required fields',
                color: 'error',
            });
            return;
        }

        await admin.addRole(formData);
        showSnackbar({
            text: 'Role added successfully',
            color: 'success',
        });
        roleForm.value.reset();
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error adding role',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    const { data } = await admin.fetchOrganizations(1, 1000);
    availableOrganizations.value = data.organizations.map(
        (org: Organization) => ({ name: org.name, id: org.id }),
    );

    await getRoles();
});
</script>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>
