<template>
    <h1 class="section-title">Add operation</h1>
    <v-container>
        <v-form ref="operationForm" @submit.prevent="addOperation">
            <v-row>
                <v-col>
                    <v-text-field
                        label="Operation name"
                        v-model="formData.name"
                        :rules="rules.required"
                        :loading="loading"
                    />

                    <v-text-field
                        label="Operation location"
                        v-model="formData.location"
                        :rules="rules.required"
                        :loading="loading"
                    />

                    <v-text-field
                        label="Operation designation"
                        v-model="formData.designation"
                        :rules="rules.required"
                        :loading="loading"
                    />

                    <v-textarea
                        label="Operation description"
                        v-model="formData.description"
                        :rules="rules.required"
                        :loading="loading"
                    />
                </v-col>
            </v-row>
            <EsriMap
                :is-editing="true"
                :draw-mode="'dot-single'"
                @area-selected="locationSelectedPoint = $event"
            />
            <div class="text-right">
                <v-btn :loading="loading" type="submit">Add operation</v-btn>
            </div>
        </v-form>
    </v-container>
</template>

<route lang="yaml">
name: admin-operations-create
meta:
layout: authenticated
protected: true
admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref } from 'vue';
import type { Operation } from '@/types/Operation';
import { IPoint } from '@/types/Global.type';
const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const operationForm = ref();
const loading = ref(false);
const locationSelectedPoint = ref<IPoint | null>(null);

const router = useRouter();

const formData = reactive<Operation>({
    name: '',
    location: '',
    designation: '',
    description: '',
});

const rules = {
    required: [(v: string) => !!v || 'Required.'],
};

const addOperation = async () => {
    try {
        loading.value = true;
        const validation = await (
            operationForm.value as HTMLFormElement
        ).validate();

        if (!validation.valid) {
            showSnackbar({
                text: 'Please fill in all required fields',
                color: 'error',
            });
            return;
        }

        const locationCoordinates: IPoint | null =
            locationSelectedPoint.value?.type === 'Point'
                ? {
                      type: 'Point',
                      coordinates: locationSelectedPoint.value?.coordinates,
                  }
                : null;

        await admin.addOperation({
            ...formData,
            // TODO: fix this
            // @ts-ignore
            locationCoordinates: locationCoordinates,
        });

        showSnackbar({
            text: 'Operation added successfully',
            color: 'success',
        });

        operationForm.value.reset();

        router.push('/admin/operations');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error adding operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>

<route lang="yaml">
meta:
	layout: authenticated
	protected: true
	admin: true
</route>
