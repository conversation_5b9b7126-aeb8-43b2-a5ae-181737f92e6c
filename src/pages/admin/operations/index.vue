<template>
    <div class="ma-4">
        <v-card class="lumio-card">
            <v-card-title>
                <h3>Operations</h3>

                <v-btn
                    v-if="getUserRole === 'org_admin'"
                    color="primary"
                    class="mr-4"
                    to="/admin/operations/builder"
                >
                    <v-icon>mdi-plus</v-icon>
                    Add operation
                </v-btn>
                <span v-else style="color: red; font-size: 12px">
                    You are not authorized to add operations.
                </span>
            </v-card-title>
            <v-card-text>
                <!--              <v-text-field-->
                <!--                v-model="search"-->
                <!--                label="Search operations"-->
                <!--                clearable-->
                <!--                variant="outlined"-->
                <!--                max-width="300px"-->
                <!--                @click:clear="getOperations"-->
                <!--                @input="searchOperations"-->
                <!--              />-->
                <v-progress-linear v-if="loading" indeterminate />
                <v-table
                    v-if="operationsList.length"
                    height="550px"
                    fixed-header
                >
                    <thead>
                        <tr>
                            <th class="text-left" style="width: 300px">Name</th>
                            <th>Location</th>
                            <th>Description</th>
                            <th style="width: 100px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="operation in operationsList"
                            :key="operation.id"
                        >
                            <td>
                                {{ operation.name }} <br />
                                <v-chip-group>
                                    <v-chip
                                        class="ma-2"
                                        color="primary"
                                        size="small"
                                        label
                                    >
                                        {{ operation.designation }}
                                    </v-chip>
                                    <v-chip
                                        class="ma-2"
                                        color="primary"
                                        size="small"
                                        prepend-icon="mdi-map-marker"
                                        label
                                    >
                                        {{ operation.location }}
                                    </v-chip>
                                </v-chip-group>
                            </td>
                            <td>{{ operation.description }}</td>
                            <td>
                                <v-tooltip
                                    v-if="getUserRole === 'org_admin'"
                                    location="top"
                                >
                                    <template v-slot:activator="{ props }">
                                        <v-btn
                                            icon
                                            v-bind="props"
                                            variant="plain"
                                            size="small"
                                            :to="`/admin/operations/${operation.id}/edit`"
                                        >
                                            <v-icon>mdi-pencil</v-icon>
                                        </v-btn>
                                    </template>
                                    <span>Edit</span>
                                </v-tooltip>

                                <!-- <v-tooltip location="top">
                  <template v-slot:activator="{ props }">
                      <v-btn
                          icon
                          v-bind="props"
                          variant="plain"
                          size="small"
                          @click="
                              deleteOperation(operation.id)
                          "
                      >
                          <v-icon>mdi-delete</v-icon>
                      </v-btn>
                  </template>
                  <span>Delete</span>
              </v-tooltip> -->

                                <!-- <v-tooltip location="top">
                  <template v-slot:activator="{ props }">
                      <v-btn
                          :to="`/admin/operations/${operation.id}`"
                          icon
                          v-bind="props"
                          variant="plain"
                          size="small"
                      >
                          <v-icon>mdi-eye</v-icon>
                      </v-btn>
                  </template>
                  <span>View</span>
              </v-tooltip> -->
                            </td>
                            <td>
                                <v-btn
                                    v-if="operation.isActive"
                                    color="error"
                                    size="small"
                                    variant="flat"
                                    @click="
                                        setOperationState(operation.id, false)
                                    "
                                >
                                    Make Inactive
                                </v-btn>
                                <v-btn
                                    v-else
                                    color="success"
                                    size="small"
                                    variant="flat"
                                    class="mr-2"
                                    @click="
                                        setOperationState(operation.id, true)
                                    "
                                >
                                    Make Active
                                </v-btn>
                            </td>
                        </tr>
                    </tbody>
                </v-table>
                <v-alert
                    v-if="operationsList.length === 0 && !loading"
                    type="info"
                    >No operations found. Please add a new operation.</v-alert
                >
                <v-pagination
                    v-model="page"
                    :length="pages"
                    @update:modelValue="getOperations"
                />
            </v-card-text>
        </v-card>
    </div>
</template>

<route lang="yaml">
name: admin-operations-index
meta:
    layout: authenticated # Make sure this is properly indented
    title: Operations
    description: Operations
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Operation } from '@/types/Operation';
import { useOperationStore } from '@/stores/operation.store';
import { useAdminStore } from '@/stores/admin';
import { useAuthStore } from '@/stores/auth';
import { useSnackbar } from '@/composables/useSnackbar';
import { storeToRefs } from 'pinia';

const adminStore = useAdminStore();
const operationStore = useOperationStore();
const { showSnackbar } = useSnackbar();
// import { useDebounceFn } from '@vueuse/core';
const { getUserRole } = storeToRefs(useAuthStore());

const loading = ref(false);
// const search = ref('');
const operationsList = ref<Operation[]>([]);
const page = ref(1);
const perPage = ref(10);
const pages = ref(0);

// const deleteOperation = async (id: string | undefined) => {
//     try {
//         loading.value = true;
//         await adminStore.deleteOperation(id);
//         operationsList.value = operationsList.value.filter(
//             (operation) => operation.id !== id,
//         );
//         await getOperations();
//         showSnackbar({
//             text: 'Operation deleted successfully',
//             color: 'success',
//         });
//     } catch (error) {
//         showSnackbar({
//             text: 'Error deleting operation',
//             color: 'error',
//         });
//     } finally {
//         loading.value = false;
//     }
// };

// const searchOperations = useDebounceFn(async () => {
//     try {
//         loading.value = true;
//         const { data } = search.value
//             ? await adminStore.searchOperations(search.value)
//             : await adminStore.fetchOperations(page.value, perPage.value);
//         operationsList.value = data.operations;
//     } catch (error) {
//         showSnackbar({
//             text: 'Error searching operations',
//             color: 'error',
//         });
//     } finally {
//         loading.value = false;
//     }
// }, 500);

const getOperations = async () => {
    try {
        loading.value = true;
        const { data } = await adminStore.fetchOperations(
            page.value,
            perPage.value,
        );
        const { operations, pagination } = data;
        operationsList.value = operations;
        pages.value = pagination.totalPages;
    } catch (error) {
        showSnackbar({
            text: 'Error fetching operations',
            color: 'error',
        });
        console.error(error);
    } finally {
        loading.value = false;
    }
};

const setOperationState = async (id: string | undefined, isActive: boolean) => {
    if (!id) return;
    try {
        await adminStore.updateOperationStatus(id, isActive);
        await operationStore.refreshCurrentOperation();
        await getOperations();
        showSnackbar({
            text: 'Operation state updated successfully',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
    }
};

onMounted(async () => {
    await getOperations();
});
</script>
