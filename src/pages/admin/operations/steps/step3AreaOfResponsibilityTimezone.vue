<template>
  <v-card class="lumio-card">
    <v-card-title>
      <h4 class="">Step 3: Tactical Area & Timezone</h4>
    </v-card-title>
    <v-card-text>
        <time-zone class="" />
		<v-divider class="my-4"></v-divider>
<!--        <EsriMap-->
<!--            v-if="currentStep === 2"-->
<!--            :key="`step3-map-${new Date().getTime()}`"-->
<!--            :is-editing="true"-->
<!--            :max-height="500"-->
<!--            :existing-coordinates="mappedAreaOfResponsibility"-->
<!--            :elements-color="[187, 49, 244, 255]"-->
<!--            @elements-selected="handleElementsSelected"-->
<!--        />-->
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';


// const timezones = ['UTC-12', 'UTC-11', 'UTC-10', 'UTC-9', 'UTC-8'];
// const timezone = ref('UTC-12');

const props = defineProps<{
    areaOfResponsibility: any;
    currentStep: number;
}>();

const emit = defineEmits(['update']);

// const mappedAreaOfResponsibility = computed(() => {
//     const elements = Array.isArray(props.areaOfResponsibility.elements)
//         ? props.areaOfResponsibility.elements
//         : [props.areaOfResponsibility.elements];
//
//     return elements.map((element: any) => ({
//         type: 'Polygon',
//         coordinates: element.locationCoordinates.coordinates,
//     }));
// });
//
// const handleElementsSelected = (elements: MapElementData) => {
//     emit('update', elements);
// };

const isVisible = ref(false);

watch(
    () => props.currentStep,
    (newStep) => {
        isVisible.value = newStep === 2;
    },
    { immediate: true },
);
</script>
