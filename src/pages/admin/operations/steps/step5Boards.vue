<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4 class="">Step 5: Boards</h4>
        </v-card-title>
        <v-card-text
            ><!-- V-for of checkboxs for main boards, current23, plans25, RCMBoards, and other -->
            <v-row class="px-4">
                <v-col col="4">
                    <h3>Main Boards</h3>
                    <v-checkbox
                        v-for="board in mainBoards"
                        :key="board"
                        v-model="selectedItems.mainBoards"
                        :label="board"
                        :value="board"
                        disabled
                    />
                    <h3>23 | Current</h3>
                    <v-checkbox
                        v-for="board in current23"
                        :key="board"
                        v-model="selectedItems.current23"
                        :label="board"
                        :value="board"
                        disabled
                    />
                    <h3>25 | Plans</h3>
                    <v-checkbox
                        v-for="board in plans25"
                        :key="board"
                        v-model="selectedItems.plans25"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>RCM Boards</h3>
                    <v-checkbox
                        v-for="board in RCMBoards"
                        :key="board"
                        v-model="selectedItems.RCMBoards"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>Other</h3>
                    <v-checkbox
                        v-for="board in other"
                        :key="board"
                        v-model="selectedItems.other"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, watch } from 'vue';

const mainBoards = ref<string[]>(['Home Board', 'Task Board', 'Weather']);
const current23 = ref<string[]>(['Int Log']);
const plans25 = ref<string[]>(['SIGACTS']);
const RCMBoards = ref<string[]>([
    'Collection Requirements',
    'Collection Overlay',
    'Collection Capabilities',
    'RFI',
    'Collection Waitlist',
    'Collection Plan',
    'ISR Synch',
    'ISR Log',
    'ISR Data',
]);

const other = ref<string[]>(['All Source']);

const emit = defineEmits<{
    (e: 'update', data: SelectedItems): void;
}>();

interface SelectedItems {
    mainBoards: string[];
    current23: string[];
    plans25: string[];
    RCMBoards: string[];
    other: string[];
}

const selectedItems = reactive<SelectedItems>({
    mainBoards: [...mainBoards.value],
    current23: [...current23.value],
    plans25: [...plans25.value],
    RCMBoards: [...RCMBoards.value],
    other: [...other.value],
});

// Emit initial values and whenever selectedItems changes
onMounted(() => {
    emit('update', selectedItems);
});

// Watch for changes in selectedItems and emit updates
watch(
    selectedItems,
    (newValue) => {
        emit('update', newValue);
    },
    { deep: true },
);
</script>
