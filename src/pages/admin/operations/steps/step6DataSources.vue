<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4 class="">Step 5: Data Sources</h4>
        </v-card-title>
        <v-card-text>
            <v-row class="px-4">
                <v-col col="4">
                    <h3>Geo Data</h3>
                    <v-checkbox
                        v-for="board in geoData"
                        :key="board"
                        v-model="selectedItems.geoData"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>Weather Data</h3>
                    <v-checkbox
                        v-for="board in weatherData"
                        :key="board"
                        v-model="selectedItems.weatherData"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>Threat Data</h3>
                    <v-checkbox
                        v-for="board in threatData"
                        :key="board"
                        v-model="selectedItems.threatData"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, watch } from 'vue';

const geoData = ref<string[]>(['Terrain', 'Locations', 'Loc']);
const weatherData = ref<string[]>([
    'Live Feed',
    'Forecast',
    '10 Day Outlook',
    'Oceanic',
]);
const threatData = ref<string[]>([
    'Conventional',
    'Hybrid',
    'Unconventional',
    'Disaster',
]);

const emit = defineEmits<{
    (e: 'update', data: SelectedItems): void;
}>();

interface SelectedItems {
    geoData: string[];
    weatherData: string[];
    threatData: string[];
}

const selectedItems = reactive<SelectedItems>({
    geoData: [...geoData.value],
    weatherData: [...weatherData.value],
    threatData: [...threatData.value],
});

// Emit initial values and whenever selectedItems changes
onMounted(() => {
    emit('update', selectedItems);
});

// Watch for changes in selectedItems and emit updates
watch(
    selectedItems,
    (newValue) => {
        emit('update', newValue);
    },
    { deep: true },
);
</script>
