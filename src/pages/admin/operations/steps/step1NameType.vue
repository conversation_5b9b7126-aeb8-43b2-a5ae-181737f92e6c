<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4 class="">Step 1: Name and type</h4>
        </v-card-title>
        <v-card-text>
            <v-form>
                <v-row>
                    <v-col cols="4">
                        <v-text-field
                            v-model="formData.name"
                            label="Operation Name"
                            @input="emitChanges"
                            @blur="setDesignation"
                        />
                    </v-col>
                    <v-col cols="3">
                        <v-text-field
                            v-model="formData.designation"
                            label="Designation"
                            placeholder="ODS-2024"
                            @input="emitChanges"
                        />
                    </v-col>
                    <v-col cols="2">
                        <v-select
                            v-model="formData.type"
                            :items="operationTypes"
                            label="Type"
                            @change="emitChanges"
                        >
                        </v-select>
                    </v-col>
                    <v-col cols="3">
                        <v-text-field
                            v-model="formData.location"
                            label="Location"
                            placeholder="Approximate location of the operation"
                            @input="emitChanges"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-textarea
                            v-model="formData.description"
                            label="Description"
                            placeholder="Description of the operation"
                            @input="emitChanges"
                            rows="3"
                        />
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import type { Step1FormData } from '@/types/operationBuilder/Step1FormData';

const emit = defineEmits<{
    (e: 'update', data: Step1FormData): void;
}>();

const operationTypes = ['conventional', 'unconventional', 'hybrid', 'hadr'];

const formData = reactive<Step1FormData>({
    name: '',
    type: 'conventional',
    designation: '',
    description: '',
    location: '',
});

const setDesignation = () => {
    // If the name is empty, don't set designation
    if (!formData.name) return;

    // Get the first letters of each word in the name
    const nameParts = formData.name.split(' ');
    const prefix = nameParts
        .map((part) => part.charAt(0).toUpperCase())
        .join('');

    // Generate four random numbers
    const randomNumbers = Math.floor(1000 + Math.random() * 9000);

    // Set designation
    formData.designation = prefix + '-' + randomNumbers;
};

const emitChanges = () => {
    emit('update', formData);
};
</script>
