<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4 class="">Step 7: Plugins</h4>
        </v-card-title>
        <v-card-text>
            <v-row class="px-4">
                <v-col col="4">
                    <h3>Social Media</h3>
                    <v-checkbox
                        v-for="board in socialMedia"
                        :key="board"
                        v-model="selectedItems.socialMedia"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>ISR</h3>
                    <v-checkbox
                        v-for="board in isr"
                        :key="board"
                        v-model="selectedItems.isr"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
                <v-col col="4">
                    <h3>Network</h3>
                    <v-checkbox
                        v-for="board in network"
                        :key="board"
                        v-model="selectedItems.network"
                        :label="board"
                        :value="board"
                        disabled
                    />
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, watch } from 'vue';

const socialMedia = ref<string[]>(['Facebook', 'X(formerly Twitter)']);
const isr = ref<string[]>(['P8', 'FA-18c', 'Trident', 'Reaper', 'Shadow']);
const network = ref<string[]>(['DDSN', 'DDRN', 'NIPR', 'SIPR', 'NATO']);

const emit = defineEmits<{
    (e: 'update', data: PluginsState): void;
}>();

interface PluginsState {
    socialMedia: string[];
    isr: string[];
    network: string[];
}

const selectedItems = reactive<PluginsState>({
    socialMedia: [...socialMedia.value],
    isr: [...isr.value],
    network: [...network.value],
});

// Emit initial values and whenever selectedItems changes
onMounted(() => {
    emit('update', selectedItems);
});

// Watch for changes in selectedItems and emit updates
watch(
    selectedItems,
    (newValue) => {
        emit('update', newValue);
    },
    { deep: true },
);
</script>
