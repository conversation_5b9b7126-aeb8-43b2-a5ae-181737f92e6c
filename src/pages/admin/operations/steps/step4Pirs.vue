<template>
    <v-card class="lumio-card">
        <v-card-title class="">
            <h4 class="">Step 4: PIRs</h4>
        </v-card-title>
        <v-card-text>
            <v-row class="mt-5">
                <v-col>
                    <v-table density="compact">
                        <thead>
                            <tr>
                                <th colspan="2">
                                    <v-row>
                                        <v-col cols="6" class="my-4">
                                            <v-text-field
                                                v-model="currentQuestionToAdd"
                                                label="Question"
                                                density="compact"
                                                color="primary"
                                                base-color="primary"
                                                hide-details
                                                @keyup.enter="handleAddPIR"
                                                class=""
                                            >
                                            </v-text-field>
                                        </v-col>
                                        <v-col cols="6" class="my-4">
                                            <v-btn
                                                @click="handleAddPIR"
                                                variant="text"
                                                color="primary"
                                                text="Add PIR"
                                                prepend-icon="mdi-plus"
                                                :disabled="
                                                    currentQuestionToAdd.length ===
                                                    0
                                                "
                                            ></v-btn>
                                        </v-col>
                                    </v-row>
                                </th>
                            </tr>
                        </thead>
                        <thead>
                            <tr>
                                <th style="width: 10%"></th>
                                <th>Current PIRs</th>
                            </tr>
                        </thead>
                        <tbody v-if="pirsCreated.length">
                            <tr
                                v-for="(pir, index) in pirsCreated"
                                :key="index"
                            >
                                <td>
                                    <v-btn
                                        variant="flat"
                                        color="error"
                                        size="small"
                                        class="ml-2"
                                        @click="deletePir(pir.question)"
                                    >
                                        <v-icon>mdi-delete</v-icon>
                                    </v-btn>
                                </td>
                                <td>{{ pir.question }}</td>
                            </tr>
                        </tbody>
                    </v-table>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
// import { useAdminPirStore } from '@/stores/admin/pir.store';
import {ref} from 'vue';
import type {IPir} from '@/types/Pir';
import {Priority} from "@/types/Global.type";

const props = defineProps<{
    pirs: IPir[];
}>();

// const adminPirStore = useAdminPirStore();
const currentQuestionToAdd = ref('');
const pirsCreated = ref<IPir[]>([]);
const emit = defineEmits(['update-pirs']);

const handleAddPIR = () => {
    const newPir = {
        question: currentQuestionToAdd.value,
        description: '',
        isActive: true,
		priority: Priority.MEDIUM,
		originator: '',

    } as IPir;
    pirsCreated.value.push(newPir);

    currentQuestionToAdd.value = '';
    emit('update-pirs', pirsCreated.value);
};

const deletePir = (question: string) => {
    pirsCreated.value = pirsCreated.value.filter(
        (pir) => pir.question !== question,
    );
    emit('update-pirs', pirsCreated.value);
};

onMounted(() => {
    pirsCreated.value = props.pirs;
});
</script>
