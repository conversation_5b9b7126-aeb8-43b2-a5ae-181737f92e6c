<template>
  <v-card class="lumio-card">
    <v-card-title>
      <h4 class="">Step 2: Area of Operation</h4>
    </v-card-title>
    <v-card-text>

		<template v-if="isLoading">
			<v-skeleton-loader
				class="mx-auto"
				type="card"
				width="100%"
				height="500"
			></v-skeleton-loader>
		</template>
		<template v-else>

		<EsriMapSingleArea
			:max-height="500"
			:zoom="zoom"
			:center-coordinates="getCenterCoordinates"
			:map-item="mappedAreaOfOperation"
			@area-deleted="handleAreaDeleted"
			@area-created="handleAreaCreated"
			@area-updated="handleAreaUpdated"
			@area-clicked="handleAreaClicked"
			@zoom-updated="handleZoomUpdated"
			@center-updated="handleCenterUpdated"
		></EsriMapSingleArea>
		</template>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import {IArea, IPoint} from '@/types/Global.type';
import { SymbolItem } from '@/types/EsriMap';
const isLoading = ref(true);
const props = defineProps<{
    areaOfOperation: IArea;
	locationOfOperation: IPoint;
	zoom: number;
    currentStep: number;
}>();

const opsParams  = ref<{
	areaOfOperation: IArea;
	locationOfOperation: IPoint;
	zoom: number;
}>({
	areaOfOperation: {
		type: 'Polygon',
		coordinates: [],
	},
	locationOfOperation: {
		type: 'Point',
		coordinates: [-98, 38],
	},
	zoom: 0
});

const emits = defineEmits(['update-area-of-ops']);

const getCenterCoordinates = (computed(() => {
	return props.locationOfOperation.coordinates ? props.locationOfOperation.coordinates : [-98, 38]
}));


const mappedAreaOfOperation = computed(() => {
	return {
		id: null,
		type: 'polygon',
		coordinates: props.areaOfOperation.coordinates,
	} as SymbolItem;
});

const handleAreaDeleted = () =>{
	opsParams.value.areaOfOperation.coordinates = [];
	sendUpdatedMap();
}
const handleAreaCreated = (item:any) =>{
	opsParams.value.areaOfOperation.coordinates = item.coordinates;
	sendUpdatedMap();
}
const handleAreaUpdated = (item:any) =>{
	opsParams.value.areaOfOperation.coordinates = item.coordinates;
	sendUpdatedMap();
}
const handleAreaClicked = (item:any) =>{
	//
}
const handleZoomUpdated = (zoom:number) =>{
	opsParams.value.zoom = zoom;
	sendUpdatedMap();
}
const handleCenterUpdated = (center:any) =>{
	opsParams.value.locationOfOperation.coordinates = [
		center.longitude,
		center.latitude
	]
	sendUpdatedMap();
}

const sendUpdatedMap = () => {
	emits('update-area-of-ops',opsParams.value);
}

onMounted(async () => {
	opsParams.value = {
		areaOfOperation: props.areaOfOperation,
		locationOfOperation: props.locationOfOperation,
		zoom: props.zoom
	}
	isLoading.value = false;
});
</script>
