<template>
    <Breadcrumbs :current-page-title="operation?.name ?? 'Operation'" />
    <v-btn
        color="primary"
        prepend-icon="mdi-arrow-left"
        class="mr-2"
        variant="text"
        to="/admin/operations"
    >
        Back to All Operations
    </v-btn>
    <v-card class="" border flat>
        <v-list-item class="px-5">
            <template v-slot:prepend>
                <v-avatar color="surface-light" size="32">
                    <v-icon size="32" color="blue">mdi-map-marker</v-icon>
                </v-avatar>
            </template>
            <template v-slot:title class="pa-2">
                <h2>
                    {{ operation?.name }} - ID:
                    <strong class="text-blue">{{
                        operation?.designation
                    }}</strong>
                </h2></template
            >
            <template v-slot:subtitle>
                Location:
                <strong class="text-blue">{{ operation?.location }}</strong>
            </template>

            <template v-slot:append>
                <v-btn
                    class="text-none btn btn-sm"
                    color="primary"
                    text="Edit"
                    variant="outlined"
                    slim
                    :to="`/admin/operations/${operation?.id}/edit`"
                ></v-btn>
            </template>
        </v-list-item>
        <v-divider></v-divider>
        <v-card-text class="text-medium-emphasis pa-0">
            <v-row no-gutters>
                <v-col class="pa-4" cols="12">
                    <h3>Description</h3>
                    <p>{{ operation?.description }}</p>
                </v-col>
                <v-col class="" cols="12">
                    <v-expansion-panels class="pa-0" multiple>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >{{
                                                operationUsers.length
                                            }}
                                            Users</v-col
                                        >
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text
                                class="pa-0 ma-0 bg-grey-lighten-4"
                            >
                                <template v-slot:default>
                                    <ViewManageOperationUsers
                                        :key="
                                            getRefreshKey(
                                                operation?.id,
                                                'users',
                                            )
                                        "
                                        :operation-id="operation?.id as string"
                                        @operation-updated="
                                            (id) =>
                                                handleOperationUpdated(
                                                    id,
                                                    'users',
                                                )
                                        "
                                        :operationWithUsers="operation as Operation"
                                    />
                                </template>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >Assets</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0"
                                                    >Assets</span
                                                >
                                                <span v-else key="1"
                                                    >-0 Assets</span
                                                >
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                <ViewManageOperationAssets
                                    :operationWithAssets="operation as Operation"
                                />
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >PIRs</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0"
                                                    >PIRs</span
                                                >
                                                <span v-else key="1"
                                                    >-0 PIRs</span
                                                >
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                //PIR component here
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >Map & Location</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0">{{
                                                    operation?.location
                                                }}</span>
                                                <span v-else key="1">{{
                                                    operation?.location
                                                }}</span>
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                <EsriMap
                                    v-if="areaSelected"
                                    class=""
                                    :is-editing="false"
                                    :existing-coordinates="getAreaSelected"
                                    :draw-mode="'dot-single'"
                                />
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                    </v-expansion-panels>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>

    <div></div>
</template>

<style lang="scss">
//@use '@/styles/settings.scss';
//$expansion-panel-text__wrapper-padding: 0;
</style>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ref, onMounted } from 'vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminStore } from '@/stores/admin';
import ViewManageOperationUsers from '@/components/operations/users/ViewManageOperationUsers.vue';
// import dayjs from 'dayjs';
import { Operation } from '@/types/Operation';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { useRefreshManager } from '@/composables/useRefreshManager';
import { IPoint } from '@/types/Global.type';
import { UserOperation } from '@/types/UserOperation';
const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const { triggerRefresh, getRefreshKey } = useRefreshManager({
    componentName: 'Operation',
    refreshKeys: ['users', 'assets', 'pirs'],
});

const loading = ref(false);

const operationUsers = ref<UserOperation[]>([]);
const areaSelected = ref<{ type: string; coordinates: number[] } | null>(null);

const route = useRoute();
const params = route.params as { operationId: string };
const operationId = params.operationId;
const operation = ref<Operation | null>(null);

const fetchOperationData = async () => {
    try {
        loading.value = true;
        const fetchedOperation = await admin.getOperationById(operationId);
        operation.value = fetchedOperation.operation;
        operationUsers.value = fetchedOperation.operation
            .users as UserOperation[];
        // @ts-ignore
        areaSelected.value = operation.value?.locationCoordinates;
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to load operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleOperationUpdated = async (
    operationId: string,
    key: 'users' | 'assets' | 'pirs',
) => {
    if (!operationId) return;

    try {
        loading.value = true;
        await triggerRefresh(operationId, key, fetchOperationData);

        showSnackbar({
            text: 'Operation updated successfully',
            color: 'success',
        });
    } catch (error) {
        showSnackbar({
            text: 'Failed to update operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    await fetchOperationData();
});

const getAreaSelected = computed(() => {
    return areaSelected.value ? ([areaSelected.value] as IPoint[]) : null;
});
</script>
