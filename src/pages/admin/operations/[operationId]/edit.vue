<template>
  <div class="ma-4">
    <v-form ref="operationForm" @submit.prevent="updateOperation">
    <v-card class="lumio-card">
      <v-card-title>
        <h3>Edit Operation: <span class="text-grey-darken-1">{{ formData.name }}</span></h3>
        <div class="d-flex justify-space-between mt-4">
          <v-btn :to="`/admin/operations`" class="mr-4" variant="text"
          >Cancel</v-btn
          >
          <v-btn
            color="primary"
            :loading="loading"
            type="submit"
            prepend-icon="mdi-check"
          >Update operation</v-btn>
        </div>
      </v-card-title>
      <v-card-text>
        <v-row class="ma-4">
          <v-col cols="3">
            <v-text-field
              label="Operation name"
              v-model="formData.name"
              :rules="rules.required"
              :loading="loading"
            />

            <v-text-field
              label="Operation location"
              v-model="formData.location"
              :rules="rules.required"
              :loading="loading"
            />

            <!--              <v-text-field-->
            <!--                label="Operation designation"-->
            <!--                v-model="formData.designation"-->
            <!--                :rules="rules.required"-->
            <!--                :loading="loading"-->
            <!--              />-->

            <v-textarea
              label="Operation description"
              v-model="formData.description"
              :rules="rules.required"
              :loading="loading"
            />
          </v-col>
          <v-col cols="9">
            <EsriMap
              v-if="pointFetched"
              :is-editing="true"
              class="my-4"
              :max-height="500"
              :existing-coordinates="transformedCoordinates"
              @elements-selected="handleAreaSelected"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    </v-form>
  </div>
</template>

<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref, computed } from 'vue';
import type { Operation } from '@/types/Operation';
import { useRoute, useRouter } from 'vue-router';
import { IPoint } from "@/types/Global.type";

const route = useRoute();
const router = useRouter();

const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const operationForm = ref();
const loading = ref(false);
const pointFetched = ref<IPoint | null>(null);

// Transform single point to array format expected by EsriMap
const transformedCoordinates = computed(() => {
  if (!pointFetched.value) return null;
  return [{
    type: pointFetched.value.type,
    coordinates: pointFetched.value.coordinates
  }];
});

const formData = reactive<Operation>({
  name: '',
  description: '',
  location: '',
  designation: '',
});

// Handle the elements-selected event from EsriMap
const handleAreaSelected = (mapData: any) => {

};

const rules = {
  required: [(v: string) => !!v || 'Required.'],
};

const updateOperation = async () => {
  try {
    const params = route.params as { operationId: string };
    const operationId = params.operationId;
    loading.value = true;
    await admin.updateOperation(operationId, {
      ...formData,
      locationCoordinates: formData.locationCoordinates || null
    });
    await router.push(`/admin/operations`);
    showSnackbar({
      text: 'Operation updated successfully',
      color: 'success',
    });
  } catch (error) {
    console.error(error);
    showSnackbar({
      text: 'Error updating operation',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  try {
    const params = route.params as { operationId: string };
    loading.value = true;
    const operationId = params.operationId;
    const { operation } = await admin.getOperationById(operationId as string);
    formData.name = operation.name;
    formData.description = operation.description;
    formData.location = operation.location;
    formData.designation = operation.designation;
    formData.locationCoordinates = operation.locationCoordinates || null;
    pointFetched.value = operation.locationCoordinates || null;
  } catch (error) {
    console.error(error);
    showSnackbar({
      text: 'Error fetching operation',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
});
</script>
