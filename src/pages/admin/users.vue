<template>
    <div class="ma-4">
        <v-card class="lumio-card">
            <v-card-title>
                <h3>Organization Users</h3>
                <v-btn
                    v-if="getUserRole === 'org_admin'"
                    color="primary"
                    class="mr-4"
                    to="/admin/add-user"
                    prepend-icon="mdi-plus"
                    >Add user</v-btn
                >
                <span style="color: red; font-size: 12px" v-else
                    >Adding users is only available for organization
                    admins</span
                >
            </v-card-title>
            <v-card-text>
                <v-row>
                    <v-col>
                        <v-progress-linear v-if="loading" indeterminate />
                        <v-table v-else>
                            <thead>
                                <tr>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Email</th>
                                    <th>Roles</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="user in userList" :key="user.id">
                                    <td>{{ user.firstName }}</td>
                                    <td>{{ user.lastName }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <template
                                            v-if="
                                                user.roles &&
                                                user.roles.length > 0
                                            "
                                        >
                                            <v-chip
                                                v-for="role in user.roles"
                                                :key="role.id"
                                                density="compact"
                                                border="1"
                                                color="white"
                                                class="bg-primary text-white mr-2"
                                                >{{ role.roleName }}</v-chip
                                            >
                                        </template>
                                        <span v-else> Roles not assigned </span>
                                    </td>
                                    <td>
                                        <v-tooltip location="top">
                                            <template
                                                v-slot:activator="{ props }"
                                            >
                                                <v-btn
                                                    icon
                                                    v-bind="props"
                                                    variant="plain"
                                                    size="small"
                                                    :to="`/edit-user/${user.id}`"
                                                >
                                                    <v-icon>mdi-pencil</v-icon>
                                                </v-btn>
                                            </template>
                                            <span>Edit</span>
                                        </v-tooltip>
                                        <v-tooltip location="top">
                                            <template
                                                v-slot:activator="{ props }"
                                            >
                                                <v-btn
                                                    icon
                                                    v-bind="props"
                                                    variant="plain"
                                                    size="small"
                                                >
                                                    <v-icon>mdi-delete</v-icon>
                                                </v-btn>
                                            </template>
                                            <span>Delete</span>
                                        </v-tooltip>
                                        <v-tooltip location="top">
                                            <template
                                                v-slot:activator="{ props }"
                                            >
                                                <v-btn
                                                    :to="`/admin/view-user/${user.id}`"
                                                    icon
                                                    v-bind="props"
                                                    variant="plain"
                                                    size="small"
                                                >
                                                    <v-icon>mdi-eye</v-icon>
                                                </v-btn>
                                            </template>
                                            <span>View</span>
                                        </v-tooltip>
                                    </td>
                                </tr>
                            </tbody>
                        </v-table>
                        <v-pagination
                            v-model="currentPage"
                            :length="totalPages"
                            @update:modelValue="fetchUsers(currentPage)"
                        />
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { useAdminStore } from '@/stores/admin';
import { ref, onMounted } from 'vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '@/stores/auth';
import type { Role } from '@/types/Role';

const { getUserRole } = storeToRefs(useAuthStore());

interface UserTable {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    positionId: string | null;
    accountType: string;
    roles: Role[];
}

const { showSnackbar } = useSnackbar();

const admin = useAdminStore();
const userList = ref([] as UserTable[]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);

const fetchUsers = async (page: number) => {
    try {
        loading.value = true;
        const { users, pagination } = await admin.fetchUsers(currentPage.value);
        userList.value = users;
        totalPages.value = pagination.totalPages;
    } catch (error: any) {
        showSnackbar({
            text: 'Failed to fetch users',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    await fetchUsers(currentPage.value);
});
</script>
