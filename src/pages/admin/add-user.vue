<template>
	<v-form ref="userForm" @submit.prevent="addUser">
	<v-card class="lumio-card">
		<v-card-title>
			<h1 class="section-title">Add User To Organization</h1>
		</v-card-title>
		<v-card-text class="pa-10">
			<v-row>
				<v-col cols="4" class="pa-3">
					<v-text-field
						label="First Name"
						v-model="firstName"
						:rules="rules.required"
					/>
				</v-col>
				<v-col cols="4" class="pa-3">
					<v-text-field
						label="Last Name"
						v-model="lastName"
						:rules="rules.required"
					/>
				</v-col>
				<v-col cols="4" class="pa-3">
					<v-text-field
						label="Email"
						v-model="email"
						:rules="rules.email"
					/>
				</v-col>
			</v-row>
			<v-row>
				<v-col cols="6" class="pa-3">
					<v-select
						label="Role"
						density="comfortable"
						v-model="role"
						:items="rolesList"
						item-title="title"
						item-value="value"
					/>
				</v-col>
				<v-col cols="6" class="pa-3">
					<time-zone
						single
						:timezone="timezone"
						@update="handleTimezoneUpdate"
					/>
				</v-col>
			</v-row>
		</v-card-text>
		<v-card-actions class="d-flex justify-end align-content-end">
			<v-btn
				variant="flat"
				color="primary"
				type="submit"
				prepend-icon="mdi-account-plus"
			>Add User</v-btn>
		</v-card-actions>
	</v-card>
    </v-form>

</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref, onMounted } from 'vue';
import type { Role } from '@/types/Role';
import { useRouter } from 'vue-router';

const router = useRouter();
const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const userForm = ref();

const firstName = ref('');
const lastName = ref('');
const email = ref('');
//const positionId = ref('1');
const timezone = ref('Zulu');
const role = ref(null);


//'org_admin', 'manager', 'member', 'analyst', 'external', 'vendor'
const rolesList = ref([
	{
		title: "Organization Admin",
		value: 'org_admin'
	},
	{
		title: "Manager",
		value: 'manager'
	},
	{
	title: "Member",
	value: 'member'
},
	{
		title: "External",
		value: 'external'
	},
	{
		title: "Vendor",
		value: 'vendor'
	}
]);
const rolesLoading = ref(false);

const fetchRoles = async () => {
    try {
        rolesLoading.value = true;
        const { roles } = await admin.fetchRoles(1, 100);

        rolesList.value = roles.map((role: Role) => ({
            title: role.roleName,
            value: role.id,
        }));
    } catch (error: any) {
        showSnackbar({
            text: 'Failed to fetch roles',
            color: 'error',
        });
    } finally {
        rolesLoading.value = false;
    }
};

const handleTimezoneUpdate = (val: string) => {
    timezone.value = val;
};

const rules = {
    required: [(v: string) => !!v || 'Required.'],
    email: [
        (v: string) => !!v || 'Email is required',
        (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ],
};

// const verificationLink = ref('');

const addUser = async () => {
    try {
        const payload = {
            firstName: firstName.value,
            lastName: lastName.value,
            email: email.value,
            roleId: role.value,
        };
        await admin.createUser(payload);
        showSnackbar({
            text: `${email.value} has been added to the organization`,
            color: 'success',
        });
        router.push('/admin/users');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to invite user',
            color: 'error',
        });
    }
};

onMounted(async () => {
    await fetchRoles();
});
</script>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>
