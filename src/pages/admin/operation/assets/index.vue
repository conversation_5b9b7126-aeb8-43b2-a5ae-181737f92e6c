<template>
  <div class="pa-4">
    <AssetDashboard />
  </div>
</template>

<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>

<script setup lang="ts" >
import AssetDashboard from '@/components/operations/assets/AssetDashboard.vue';
import { useOperationStore } from '@/stores/operation.store';
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { Operation } from '@/types/Operation';
const operationStore = useOperationStore();
const currentOperation = ref<Operation | null>(null);
const { currentOperationId } = storeToRefs(operationStore);

const fetchOperationById = async() =>{
  const { operation } = await operationStore.getOperationById(currentOperationId.value);
  currentOperation.value = operation;
}

onMounted(async () => {
  await fetchOperationById();
});

watch(currentOperationId, () => {
  fetchOperationById();
})

</script>
