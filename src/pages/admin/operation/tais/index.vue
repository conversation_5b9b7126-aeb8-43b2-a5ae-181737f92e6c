<template>
  <div class="">
		<Breadcrumbs  current-page-title="TAIs" />
		<h1 class="section-title">{{currentOperation?.name}} - <strong class="text-blue">TAIs</strong></h1>
	</div>
</template>

<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>


<script setup lang="ts">
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { storeToRefs } from 'pinia';
import { useOperationStore } from '@/stores/operation.store';
import { onMounted, ref } from 'vue';
import { Operation } from '@/types/Operation';
const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);
const currentOperation = ref<Operation | null>(null);
const fetchOperationById = async() =>{
	const { operation } = await operationStore.getOperationById(currentOperationId.value);
	currentOperation.value = operation;
}


onMounted(async () => {
	await fetchOperationById();
});

watch(currentOperationId, () => {
	fetchOperationById();
})

</script>
