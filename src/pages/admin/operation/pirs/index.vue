<template>
  <div class="">
		<Breadcrumbs  current-page-title="PIRs" />
		<h1 class="section-title">{{currentOperation?.name}} - <strong class="text-blue">PIRs</strong></h1>
    <ViewManageOperationPIRs
      :current-operation="currentOperation"
      :current-operation-id="currentOperationId.toString()"
    />
  </div>
</template>

<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>


<script setup lang="ts">
import ViewManageOperationPIRs from '@/components/operations/pirs/ViewManageOperationPIRs.vue';
import { useOperationStore } from '@/stores/operation.store';
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { Operation } from '@/types/Operation';
const operationStore = useOperationStore();
const currentOperation = ref<Operation | null>(null);
const { currentOperationId } = storeToRefs(operationStore);

const fetchOperationById = async() =>{
  const { operation } = await operationStore.getOperationById(currentOperationId.value);
  currentOperation.value = operation;
}

onMounted(async () => {
	await fetchOperationById();
});

watch(currentOperationId, () => {
	fetchOperationById();
})
</script>
