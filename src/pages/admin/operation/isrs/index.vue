<template>
  <div v-if="currentOperationId" class="">
    <Breadcrumbs  current-page-title="Operation ISRs" />
    <GlobalEditIsr :operation-id="currentOperationId.toString()" />
  </div>

</template>
<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>


<script setup lang="ts">

//load operation store
import { useOperationStore } from '@/stores/operation.store';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import { storeToRefs } from 'pinia';
import GlobalEditIsr from "@/components/operations/global_isr/GlobalEditIsr.vue";

const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);

// onMounted(async () => {
//
// });
//
// watch(currentOperationId, () => {
//
// })

</script>
