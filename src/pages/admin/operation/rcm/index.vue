<template>
    <v-card
        class="ma-4"
        color="white"
        v-if="currentOperation"
    >
        <v-card-text class="pa-0">
            <v-tabs
                v-model="tab as string"
                bg-color="transparent"
                grow
                density="compact"
                fixed-tabs
                stacked
                align-tabs="start"
                class="text-uppercase bg-grey-lighten-4"
                selected-class="bg-white"
            >
                <v-tab
                    v-for="tab in tabs"
                    :key="tab.keyValue"
                    style="font-size: 0.7rem"
                    border="single"
                    class="text-wrap font-weight-bold"
                    v-bind:value="tab.keyValue"
                    color="primary"
                    selected-class=""
                    v-html="tab.title"
                >
                </v-tab>
            </v-tabs>

            <v-tabs-window v-model="tab" class="">
                <v-tabs-window-item :key="tab + '-rfis'" value="rfis">
                    <Suspense>
                        <template v-if="currentOperationId">
                            <RFIList
                                :current-operation="currentOperation"
                                :current-operation-id="currentOperationId"
                                @refreshOperation="fetchOperationById"
                            />
                        </template>
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item
                    :key="tab + '-requirements'"
                    value="requirements"
                >
                    <Suspense>
                        <ViewManageOperationPIRs
                            :current-operation="currentOperation"
                            :current-operation-id="
                                currentOperationId.toString()
                            "
                            @refreshOperation="fetchOperationById"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item
                    :key="tab + '-overlay'"
                    value="overlay"
                    class="h-100"
                >
                    <Suspense>
                        <AOICard
                            class="h-100"
                            :operation="currentOperation"
                            @refreshOperation="fetchOperationById"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item
                    :key="tab + '-capabilities'"
                    value="capabilities"
                >
                    <Suspense>
                        <AssetDashboard
                            @refreshOperation="fetchOperationById"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-builder'" value="builder">
                    <Suspense>
                        <MissionBuilder />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-waitlist'" value="waitlist">
                    <Suspense>
                        <MissionCollectionWhitelist
                            @refreshOperation="fetchOperationById"
                            :current-operation-id="currentOperationId"
                            @mission-approved="fetchOperationById"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-plan'" value="plan">
                    <Suspense>
                        <MissionCollectionPlan
                            @refreshOperation="fetchOperationById"
                            :current-operation-id="currentOperationId"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-sync'" value="sync">
                    <Suspense>
                        <IsrSyncBoardContainer
                            :current-operation-id="currentOperationId"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item
                    :key="tab + '-sync'"
                    value="sync2"
                    class="h-100"
                >
                    <Suspense>
                        <IsrSyncBoardAltContainer
                            :current-operation-id="currentOperationId"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-washboard'" value="washboard">
                    <Suspense>
                        <MissionCollectionWashboard
                            @refreshOperation="fetchOperationById"
                            :current-operation-id="currentOperationId"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-logs'" value="logs">
                    <Suspense>
                        <MissionLog
                            :current-operation-id="currentOperationId"
                            @refreshOperation="fetchOperationById"
                        />
                    </Suspense>
                </v-tabs-window-item>
                <v-tabs-window-item :key="tab + '-analytics'" value="analytics">
                    <Suspense> Mission Analytics TBD </Suspense>
                </v-tabs-window-item>
            </v-tabs-window>
        </v-card-text>
    </v-card>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
</route>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import MissionBuilder from '@/components/operations/mission/mission-builder/MissionBuilder.vue';
import { storeToRefs } from 'pinia';
import { useOperationStore } from '@/stores/operation.store';
import { onMounted, ref } from 'vue';
import { Operation } from '@/types/Operation';
import ViewManageOperationPIRs from '@/components/operations/pirs/ViewManageOperationPIRs.vue';
import AssetDashboard from '@/components/operations/assets/AssetDashboard.vue';
import RFIList from '@/components/operations/rfis/RFIList.vue';
import MissionCollectionWashboard from '@/components/operations/mission/MissionCollectionWashboard.vue';
import IsrSyncBoardContainer from '@/components/operations/mission/IsrSyncBoardContainer.vue';
const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);

const tabs = [
    {
        keyValue: 'rfis',
        title: 'Requests for<br>Information (RFI)',
    },
    {
        keyValue: 'requirements',
        title: 'Collection<br>Requirements',
    },
    {
        keyValue: 'overlay',
        title: 'Collection<br>Overlay & ISR',
    },
    {
        keyValue: 'capabilities',
        title: 'Collection<br>Capabilities',
    },
    {
        keyValue: 'builder',
        title: 'Mission<br>Builder',
    },
    {
        keyValue: 'waitlist',
        title: 'Collection<br>Waitlist',
    },
    {
        keyValue: 'plan',
        title: 'Collection<br>Plan',
    },
    // {
    //     keyValue: 'sync',
    //     title: 'ISR<br>Sync',
    // },
    {
        keyValue: 'sync2',
        title: 'ISR<br>Sync',
    },
    {
        keyValue: 'washboard',
        title: 'Mission<br>Washboard',
    },
    {
        keyValue: 'logs',
        title: 'Mission<br>Logs',
    },
    {
        keyValue: 'analytics',
        title: 'Mission<br>Analytics',
    },
];

const currentOperation = ref<Operation | null>(null);
const panelOverlayIsLoading = ref(true);

const fetchOperationById = async () => {
    const { operation } = await operationStore.getOperationById(
        currentOperationId.value,
    );
    currentOperation.value = operation;
};

//get current section
//get it from route
const route = useRoute();
const router = useRouter();

const tab = ref<string | null>('rfis');

onMounted(async () => {
    await fetchOperationById();
    panelOverlayIsLoading.value = false;
});

watch(tab, (newTab) => {
    if (newTab) {
        // Preserve existing query parameters while updating the hash
        router.replace({
            hash: '#' + newTab,
            query: { ...route.query }, // Spread existing query params
        });
    }
});

watch(
    () => route.hash,
    (newHash) => {
        if (newHash) {
            tab.value = newHash.substring(1);
        }
    },
);

watch(
    () => route.hash,
    (newHash) => {
        if (newHash) {
            tab.value = newHash.substring(1); // Remove the # from hash
        }
    },
);

watch(currentOperationId, () => {
    fetchOperationById();
});

// const isPendingMission = computed(() => {
//     return Number(route.query.missionId);
// });

onMounted(async () => {
    if (route.hash) {
        tab.value = route.hash.substring(1);
    } else {
        tab.value = 'requirements'; // Default tab
        router.replace({
            hash: '#requirements',
            query: { ...route.query }, // Preserve query params when setting default hash
        });
    }
});
</script>
