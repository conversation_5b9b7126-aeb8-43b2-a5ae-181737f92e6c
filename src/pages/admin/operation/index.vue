<template>
	<div v-if="currentOperationId" class="pa-4">
		<Breadcrumbs  current-page-title="View Operation" />
		<operation-card
			:operationId="currentOperationId.toString()" />
	</div>

</template>
<route lang="yaml">
meta:
  layout: authenticated
  protected: true
  admin: true
</route>


<script setup lang="ts">

//load operation store
import { useOperationStore } from '@/stores/operation.store';
import { useRouter } from 'vue-router';

import { useSnackbar } from '@/composables/useSnackbar';
import Breadcrumbs from '@/components/Breadcrumbs.vue';
import OperationCard from '@/components/operations/OperationCard.vue';
import { storeToRefs } from 'pinia';

const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);

const router = useRouter();

const { showSnackbar } = useSnackbar();

// const fetchOperationData = async() =>{
// 	try {
// 		const operationId = currentOperationId.value;
// 		operation.value = (await operationStore.getOperationById(operationId, ['users', 'assets', 'pirs'])).operation as Operation;
// 	} catch (error) {
// 		console.error('Error fetching operation:', error)
// 		showSnackbar({
// 			text: 'Failed to fetch operation',
// 			color: 'error'
// 		})
// 	} finally {
//
// 	}
// }

onMounted(async () => {
	if(!currentOperationId.value) {
		router.push('/admin/home').then(() => {
			showSnackbar({
				text: 'Error fetching operation',
				color: 'error',
			});
		}).catch(() => {
		});
		return;
	}
});

watch(currentOperationId, () => {
	// fetchOperationData();
})

</script>
