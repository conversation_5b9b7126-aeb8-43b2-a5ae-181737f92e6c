<template>
    <div class="pa-4">

	<v-card class="lumio-card">
		<v-card-title>
			<h4>{{ operationWithUsers?.name }} -
				<strong class="text-blue">Users</strong>	</h4>
			<div v-if="operationWithUsers && operationWithUsers.id">
				<v-btn
					color="primary"
					prepend-icon="mdi-plus"
					class="ma-2"
					variant="flat"
					v-if="!isAddUsersToOperationSegmentOpen"
					@click="isAddUsersToOperationSegmentOpen = true"
				>
					Add Users to Operation
				</v-btn>
			</div>
		</v-card-title>
		<v-card-text v-if="operationWithUsers && operationWithUsers.id">
			<v-row class="" v-if="isAddUsersToOperationSegmentOpen">
				<v-col>
					<add-users-to-operation
						@close-section="
                            isAddUsersToOperationSegmentOpen = false
                        "
						@update-operation="
                            (id) => handleOperationUpdated(id, 'users')
                        "
						:existing-operation-users="operationUsers as UserOperation[]"
						:operation-id="operationWithUsers.id"
					/>
				</v-col>
			</v-row>
			<view-manage-operation-users
				:key="getRefreshKey(operationWithUsers.id, 'users')"
				:operationWithUsers="operationWithUsers as Operation"
				:operation-id="operationWithUsers.id"
				@operation-updated="(id) => handleOperationUpdated(id, 'users')"
			></view-manage-operation-users>
		</v-card-text>
	</v-card>
	</div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import AddUsersToOperation from '@/components/operations/users/AddUsersToOperation.vue';
import ViewManageOperationUsers from '@/components/operations/users/ViewManageOperationUsers.vue';
import { useOperationStore } from '@/stores/operation.store';
import { Operation } from '@/types/Operation';
import { ref, onMounted } from 'vue';

import { useRefreshManager } from '@/composables/useRefreshManager';
import { useSnackbar } from '@/composables/useSnackbar';
import { UserOperation } from '@/types/UserOperation';
import { storeToRefs } from 'pinia';

const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);

const operationWithUsers = ref<Operation | null>(null);

const { showSnackbar } = useSnackbar();

const isAddUsersToOperationSegmentOpen = ref<boolean>(false);
const operationUsers = ref<UserOperation[]>([]);

const { triggerRefresh, getRefreshKey } = useRefreshManager({
    componentName: 'Operation',
    refreshKeys: ['assets', 'users'] as const,
});

const fetchOperationWithUsers = async () => {
    currentOperationId.value = operationStore.currentOperationId;
    operationWithUsers.value = (
        await operationStore.getOperationById(currentOperationId.value, [
            'users',
            'users.user',
            'users.user.roles',
        ])
    ).operation as Operation;
    operationUsers.value =
        (operationWithUsers.value?.users as UserOperation[]) || [];
};

onMounted(async () => {
    await fetchOperationWithUsers();
});

const handleOperationUpdated = async (operationId: string, key: 'users') => {
    if (!operationId) return;

    try {
        await triggerRefresh(operationId, key, fetchOperationWithUsers);
    } catch (error) {
        showSnackbar({
            text: 'Failed to update operation',
            color: 'error',
        });
    } finally {
    }
};

watch(currentOperationId, () => {
    fetchOperationWithUsers();
});
</script>
