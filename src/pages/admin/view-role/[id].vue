<template>
    <div>
        <v-btn
            color="primary"
            prepend-icon="mdi-arrow-left"
            class="mr-2"
            variant="text"
            to="/admin/view-roles"
        >
            Back
        </v-btn>
        <v-container>
            <v-progress-linear v-if="loading" indeterminate />
            <v-card v-else>
                <v-card-title class="d-flex align-center">
                    <v-avatar size="80">
                        <img
                            v-if="user?.avatar"
                            :src="user.avatar as string"
                            :alt="user.firstName as string"
                        />
                        <v-icon color="#1E1E1E" size="80"
                            >mdi-account-circle</v-icon
                        >
                    </v-avatar>
                    <div class="ml-4">
                        <h2>
                            {{ role?.roleName }}({{ role?.staffDesignation }})
                        </h2>
                        <p class="font-weight-regular">{{ user?.email }}</p>
                    </div>
                </v-card-title>
                <v-card-text>
                    <v-table>
                        <template v-slot:default>
                            <tbody>
                                <tr>
                                    <td><b>Role description</b></td>
                                    <td>
                                        {{ role?.roleDescription ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Rank</b></td>
                                    <td>
                                        {{ role?.rank ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Unit level</b></td>
                                    <td>{{ role?.unitLevel ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><b>Subordinate roles</b></td>
                                    <td>
                                        <v-chip
                                            closable
                                            class="mr-2"
                                            v-for="subordinateRole in relatedRoles.subordinateRoles"
                                            :key="subordinateRole.id"
                                            @click:close="
                                                onDelete(
                                                    'subordinate',
                                                    subordinateRole.id,
                                                )
                                            "
                                        >
                                            {{ subordinateRole.roleName }}
                                        </v-chip>
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Superior roles</b></td>
                                    <td>
                                        <v-chip
                                            closable
                                            class="mr-2"
                                            v-for="superiorRole in relatedRoles.superiorRoles"
                                            :key="superiorRole.id"
                                            @click:close="
                                                onDelete(
                                                    'superior',
                                                    superiorRole.id,
                                                )
                                            "
                                        >
                                            {{ superiorRole.roleName }}
                                        </v-chip>
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Staff designation</b></td>
                                    <td>
                                        {{ role?.staffDesignation ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Typical unit size</b></td>
                                    <td>
                                        {{ role?.typicalUnitSize ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Country or organization</b></td>
                                    <td>
                                        {{ role?.organizationId ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Is command role</b></td>
                                    <td>
                                        {{ role?.isCommandRole ? 'Yes' : 'No' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Is staff role</b></td>
                                    <td>
                                        {{ role?.isStaffRole ? 'Yes' : 'No' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Functional area</b></td>
                                    <td>
                                        {{ role?.functionalArea ?? 'N/A' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Creation date</b></td>
                                    <td>
                                        {{
                                            role?.createdAt
                                                ? dayjs(role.createdAt).format(
                                                      'DD/MM/YYYY HH:mm',
                                                  )
                                                : 'N/A'
                                        }}
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Last change date</b></td>
                                    <td>
                                        {{
                                            role?.updatedAt
                                                ? dayjs(role.updatedAt).format(
                                                      'DD/MM/YYYY HH:mm',
                                                  )
                                                : 'N/A'
                                        }}
                                    </td>
                                </tr>
                            </tbody>
                        </template>
                    </v-table>
                </v-card-text>
                <v-card-actions class="d-flex justify-between">
                    <div class="d-flex">
                        <v-btn
                            v-if="relatedRoles.superiorRoles.length === 0"
                            color="primary"
                            variant="text"
                            prepend-icon="mdi-plus"
                            @click="handleDialog(true)"
                        >
                            Add superior roles
                        </v-btn>
                        <v-btn
                            v-else
                            color="primary"
                            variant="text"
                            prepend-icon="mdi-pencil"
                            @click="handleDialog(true, true)"
                        >
                            Change superior roles
                        </v-btn>
                        <v-btn
                            v-if="relatedRoles.subordinateRoles.length === 0"
                            color="primary"
                            variant="text"
                            prepend-icon="mdi-plus"
                            @click="handleDialog(false)"
                        >
                            Add subordinate roles
                        </v-btn>
                        <v-btn
                            v-else
                            color="primary"
                            variant="text"
                            prepend-icon="mdi-pencil"
                            @click="handleDialog(false, true)"
                        >
                            Change subordinate roles
                        </v-btn>
                    </div>
                    <v-spacer />
                    <div>
                        <v-btn
                            color="primary"
                            class="mr-2"
                            to="/admin/view-roles"
                        >
                            Cancel
                        </v-btn>
                        <v-btn
                            color="primary"
                            class="mr-2"
                            :to="`/admin/edit-role/${role?.id}`"
                        >
                            Edit
                        </v-btn>
                    </div>
                </v-card-actions>
            </v-card>

            <v-dialog v-model="dialog" width="500">
                <v-card>
                    <v-card-title>
                        {{ getTitle }}
                    </v-card-title>
                    <v-card-text>
                        <v-autocomplete
                            v-model="selectedRoles"
                            :items="roles"
                            item-title="name"
                            multiple
                            chips
                            item-value="id"
                            label="Select role"
                            :loading="loadingRoles"
                        />
                    </v-card-text>
                    <v-card-actions>
                        <v-btn color="primary" @click="hideDialog">
                            Cancel
                        </v-btn>
                        <v-btn color="primary" @click="handleAdd">
                            {{ isEdit ? 'Update' : 'Add' }}
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </v-container>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ref, onMounted, computed, watch } from 'vue';
import { User } from '@/types/User';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminStore } from '@/stores/admin';
import { Role } from '@/types/Role';
import dayjs from 'dayjs';

type RoleSelect = {
    id: string;
    name: string;
};

const admin = useAdminStore();
const { showSnackbar } = useSnackbar();

const loading = ref(false);

const route = useRoute();
const params = route.params as { id: string };
const roleId = params.id;
const role = ref<Role | null>(null);

const user = ref<User | null>(null);

const dialog = ref(false);

const superior = ref(false);
const isEdit = ref(false);

const roles = ref<RoleSelect[]>([]);
const loadingRoles = ref(false);

const getTitle = computed(() => {
    if (isEdit.value) {
        return superior.value
            ? 'Change superior roles'
            : 'Change subordinate roles';
    }
    return superior.value ? 'Add superior roles' : 'Add subordinate roles';
});

const selectedRoles = ref<string[] | undefined>(undefined);

const hideDialog = () => {
    dialog.value = false;
};

watch(dialog, (newVal) => {
    if (!newVal) {
        selectedRoles.value = [];
    }
});

const fetchRoles = async () => {
    try {
        loadingRoles.value = true;
        const fetchedRoles = await admin.fetchRoles(1, 100);
        roles.value = fetchedRoles.roles
            .filter((currentRole: any) => currentRole.id !== role.value?.id)
            .map((role: any) => ({
                id: role.id,
                name: role.roleName,
            }));
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to load roles',
            color: 'error',
        });
    } finally {
        loadingRoles.value = false;
    }
};

const relatedRoles = computed(() => {
    if (!role.value) return { subordinateRoles: [], superiorRoles: [] };

    return {
        subordinateRoles:
            role.value.subordinateRoles?.map((role) => role) || [],
        superiorRoles: role.value.superiorRoles?.map((role) => role) || [],
    };
});

const onDelete = async (
    type: 'subordinate' | 'superior',
    roleId: string | undefined,
) => {
    try {
        const method =
            type === 'subordinate'
                ? 'deleteSubordinateRole'
                : 'deleteSuperiorRole';
        await admin[method](role.value?.id as string, roleId);
        await fetchRoleData();
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to delete role',
            color: 'error',
        });
    }
};

const handleDialog = async (
    isSuperior: boolean = true,
    editing: boolean = false,
) => {
    isEdit.value = editing;
    dialog.value = !dialog.value;
    superior.value = isSuperior;

    if (editing) {
        selectedRoles.value = isSuperior
            ? relatedRoles.value.superiorRoles
                  ?.map((role) => role.id)
                  .filter((id): id is string => id !== undefined) ?? []
            : relatedRoles.value.subordinateRoles
                  ?.map((role) => role.id)
                  .filter((id): id is string => id !== undefined) ?? [];
    }

    if (roles.value.length === 0) {
        await fetchRoles();
    }
};

const handleAdd = async () => {
    try {
        const method = superior.value
            ? 'addSuperiorRoles'
            : 'addSubordinateRoles';
        const editMethod = superior.value
            ? 'changeSuperiorRoles'
            : 'changeSubordinateRoles';
        await admin[isEdit.value ? editMethod : method](
            role.value?.id as string,
            selectedRoles.value,
        );
        await fetchRoleData();
        hideDialog();
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to add roles',
            color: 'error',
        });
    }
};

const fetchRoleData = async () => {
    try {
        loading.value = true;
        const fetchedRole = await admin.getRoleById(roleId);
        role.value = fetchedRole.role;
    } catch (error) {
        showSnackbar({
            text: 'Failed to load user',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    await fetchRoleData();
});
</script>
