<template>
    <v-container>
        <v-card>
            <v-card-title>Add organization</v-card-title>
            <v-card-text>
                <v-form ref="orgForm" @submit.prevent="addOrganization">
                    <v-text-field
                        v-model="organization.name"
                        :rules="rules.name"
                        label="Name"
                        :loading="loading"
                    />
                    <v-text-field
                        v-model="organization.description"
                        :rules="rules.description"
                        label="Description"
                        :loading="loading"
                    />
                    <v-checkbox
                        v-model="organization.isMilitary"
                        label="Is Military"
                        :loading="loading"
                    />
                    <v-text-field
                        v-model="organization.designation"
                        :rules="rules.designation"
                        label="Designation"
                        :loading="loading"
                    />
                    <v-text-field
                        v-model="organization.logoUrl"
                        :rules="rules.logoUrl"
                        label="Logo URL"
                        :loading="loading"
                    />
                    <v-text-field
                        v-model="organization.sovereignty"
                        :rules="rules.sovereignty"
                        label="Sovereignty"
                        :loading="loading"
                    />
                    <!-- <v-text-field
                        v-model="organization.isoCountryCode"
                        :rules="rules.isoCountryCode"
                        label="ISO Country Code"
                        :loading="loading"
                    /> -->

                    <v-select
                        v-model="organization.isoCountryCode"
                        :items="['US', 'UK', 'AU']"
                        label="ISO Country Code"
                        :loading="loading"
                    />

                    <div class="text-right">
                        <v-btn type="submit">Add organization</v-btn>
                    </div>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref } from 'vue';
import type { Organization } from '@/types/Organization';
import { useRouter } from 'vue-router';

const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const router = useRouter();

const loading = ref(false);

const orgForm = ref<HTMLFormElement>();

const rules = {
    name: [
        (v: string) => !!v || 'Name is required',
        (v: string) =>
            v.length <= 255 || 'Name must be less than 255 characters',
    ],
    description: [
        (v: string) => !!v || 'Description is required',
        (v: string) =>
            v.length <= 255 || 'Description must be less than 255 characters',
    ],
    designation: [
        (v: string) => !!v || 'Designation is required',
        (v: string) =>
            v.length <= 255 || 'Designation must be less than 255 characters',
    ],
    logoUrl: [
        (v: string) => !!v || 'Logo URL is required',
        (v: string) =>
            v.length <= 255 || 'Logo URL must be less than 255 characters',
    ],
    sovereignty: [
        (v: string) => !!v || 'Sovereignty is required',
        (v: string) =>
            v.length <= 255 || 'Sovereignty must be less than 255 characters',
    ],
    isoCountryCode: [
        (v: string) => !!v || 'ISO Country Code is required',
        (v: string) =>
            v.length <= 255 ||
            'ISO Country Code must be less than 255 characters',
    ],
};

const organization = reactive<Organization>({
    name: '',
    description: '',
    isMilitary: false,
    designation: '',
    logoUrl: '',
    isoCountryCode: '',
    sovereignty: '',
});

const addOrganization = async () => {
    const validation = await orgForm.value?.validate();
    if (!validation.valid) {
        showSnackbar({
            text: 'Please fill in all required fields',
            color: 'error',
        });
        return;
    }

    loading.value = true;

    try {
        await admin.addOrganization(organization);
        await router.push('/admin/organizations');
    } catch (error) {
        showSnackbar({
            text: 'Failed to add organization',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>
