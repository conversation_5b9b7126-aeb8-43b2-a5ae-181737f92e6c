<template>
    <v-container>
        <v-card>
            <v-card-title class="d-flex align-center justify-space-between">
                <h2>Organizations</h2>
                <v-spacer />
                <v-btn class="mr-4" to="/admin/organizations/create"
                    >Add organization</v-btn
                >
                <v-text-field
                    v-model="search"
                    label="Search organizations"
                    clearable
                    variant="outlined"
                    max-width="300px"
                    @click:clear="fetchOrgs"
                    @input="searchOrganizations"
                />
            </v-card-title>
            <v-card-text>
                <v-progress-linear v-if="loading" indeterminate />
                <v-table v-if="orgList.length > 0" height="550px" fixed-header>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Logo</th>
                            <th>Military</th>
                            <th>Sovereignty</th>
                            <th>ISO Country Code</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="org in orgList" :key="org.id">
                            <td>{{ org.id }}</td>
                            <td>{{ org.name }}</td>
                            <td>{{ org.description }}</td>
                            <td>
                                <img
                                    :src="org.logoUrl"
                                    :alt="org.name"
                                    width="70"
                                />
                            </td>
                            <td>
                                <v-icon v-if="org.isMilitary" color="success"
                                    >mdi-check</v-icon
                                >
                                <v-icon v-else color="error">mdi-close</v-icon>
                            </td>
                            <td>{{ org.sovereignty }}</td>
                            <td>{{ org.isoCountryCode }}</td>
                            <td>
                                <v-tooltip location="top">
                                    <template v-slot:activator="{ props }">
                                        <v-btn
                                            icon
                                            v-bind="props"
                                            variant="plain"
                                            size="small"
                                            :to="`/admin/organizations/edit/${org.id}`"
                                        >
                                            <v-icon>mdi-pencil</v-icon>
                                        </v-btn>
                                    </template>
                                    <span>Edit</span>
                                </v-tooltip>

                                <v-tooltip location="top">
                                    <template v-slot:activator="{ props }">
                                        <v-btn
                                            icon
                                            v-bind="props"
                                            variant="plain"
                                            size="small"
                                            @click="deleteOrganization(org.id)"
                                        >
                                            <v-icon>mdi-delete</v-icon>
                                        </v-btn>
                                    </template>
                                    <span>Delete</span>
                                </v-tooltip>

                                <!-- <v-tooltip location="top">
                                    <template v-slot:activator="{ props }">
                                        <v-btn
                                            :to="`/admin/organizations/${org.id}`"
                                            icon
                                            v-bind="props"
                                            variant="plain"
                                            size="small"
                                        >
                                            <v-icon>mdi-eye</v-icon>
                                        </v-btn>
                                    </template>
                                    <span>View</span>
                                </v-tooltip> -->
                            </td>
                        </tr>
                    </tbody>
                </v-table>
                <v-alert v-if="orgList.length === 0 && !loading" type="info"
                    >No organizations found. Please add a new
                    organization.</v-alert
                >
                <v-pagination
                    v-model="page"
                    :length="pages"
                    @update:modelValue="fetchOrgs"
                />
            </v-card-text>
        </v-card>
    </v-container>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Organization } from '@/types/Organization';
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';

const adminStore = useAdminStore();
const { showSnackbar } = useSnackbar();
import { useDebounceFn } from '@vueuse/core';

const loading = ref(false);
const search = ref('');
const orgList = ref<Organization[]>([]);
const page = ref(1);
const perPage = ref(10);
const pages = ref(0);

const deleteOrganization = async (id: string | undefined) => {
    try {
        loading.value = true;
        await adminStore.deleteOrganization(id);
        orgList.value = orgList.value.filter((org) => org.id !== id);
        await fetchOrgs();
        showSnackbar({
            text: 'Organization deleted successfully',
            color: 'success',
        });
    } catch (error) {
        showSnackbar({
            text: 'Error deleting organization',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const searchOrganizations = useDebounceFn(async () => {
    try {
        loading.value = true;
        const { data } = search.value
            ? await adminStore.searchOrganizations(search.value)
            : await adminStore.fetchOrganizations(page.value, perPage.value);
        orgList.value = data.organizations;
    } catch (error) {
        showSnackbar({
            text: 'Error searching organizations',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
}, 500);
const fetchOrgs = async () => {
    try {
        loading.value = true;
        const { data } = await adminStore.fetchOrganizations(
            page.value,
            perPage.value,
        );
        const { organizations, pagination } = data;
        orgList.value = organizations;
        pages.value = pagination.totalPages;
    } catch (error) {
        showSnackbar({
            text: 'Error fetching organizations',
            color: 'error',
        });
        console.error(error);
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    await fetchOrgs();
});
</script>
