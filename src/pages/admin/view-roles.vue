<template>
  <div class="ma-4">
    <v-card class="lumio-card">
      <v-card-title>
        <h3>All roles</h3>
        <v-btn
          to="/admin/add-role"
          color="primary"
          prepend-icon="mdi-plus"
        >
          Add Role</v-btn>
      </v-card-title>
      <v-card-text>
        <v-progress-linear v-if="loading" indeterminate />
        <v-table
          v-if="rolesList.length > 0"
          height="550px"
          fixed-header
        >
          <thead>
          <tr>
            <th>Role</th>
	          <th>Role Type</th>
            <th>Unit Level</th>
            <th>Functional Area</th>
            <th>Rank</th>
            <th>Commanding</th>
            <th>Is Staff</th>
            <th>Unit Size</th>
            <th>Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="role in rolesList" :key="role.id">
            <td>
              {{ role.roleName }}
              <v-chip
                class="ma-2"
                color="primary"
                size="small"
                label
              >
                {{ role.staffDesignation }}
              </v-chip>
              <p class="text-caption text-grey-darken-1">
                {{ role.roleDescription }}
              </p>
            </td>
	          <td>{{role.roleType}}</td>
            <td>{{ role.unitLevel }}</td>
            <td>{{ role.functionalArea }}</td>
            <td>{{ role.rank }}</td>
            <td>
              <v-icon v-if="role.isCommandRole"
              >mdi-check</v-icon
              >
              <v-icon v-else>mdi-close</v-icon>
            </td>
            <td>
              <v-icon v-if="role.isStaffRole"
              >mdi-check</v-icon
              >
              <v-icon v-else>mdi-close</v-icon>
            </td>
            <td>{{ role.typicalUnitSize }}</td>
            <td>
              <v-chip
                v-if="role.isSystemRole"
                class="ma-2"
                color="info"
                text-color="black"
                size="small"
                label>
                System Role
              </v-chip>
              <template v-else>
                <v-tooltip location="top">
                  <template
                    v-slot:activator="{ props }"
                  >
                    <v-btn
                      icon
                      v-bind="props"
                      variant="plain"
                      size="small"
                      :to="`/admin/edit-role/${role.id}`"
                    >
                      <v-icon>mdi-pencil</v-icon>
                    </v-btn>
                  </template>
                  <span>Edit</span>
                </v-tooltip>

                <v-tooltip location="top">
                  <template
                    v-slot:activator="{ props }"
                  >
                    <v-btn
                      icon
                      v-bind="props"
                      variant="plain"
                      size="small"
                      @click="deleteRole(role.id)"
                    >
                      <v-icon>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  <span>Delete</span>
                </v-tooltip>

                <v-tooltip location="top">
                  <template
                    v-slot:activator="{ props }"
                  >
                    <v-btn
                      :to="`/admin/view-role/${role.id}`"
                      icon
                      v-bind="props"
                      variant="plain"
                      size="small"
                    >
                      <v-icon>mdi-eye</v-icon>
                    </v-btn>
                  </template>
                  <span>View</span>
                </v-tooltip>
              </template>

            </td>
          </tr>
          </tbody>
        </v-table>
        <v-alert
          v-if="rolesList.length === 0 && !loading"
          type="info"
          title="No roles found"
        ></v-alert>
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          @update:modelValue="fetchRoles"
        />

      </v-card-text>
    </v-card>
  </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { useAdminStore } from '@/stores/admin';
import { ref, onMounted } from 'vue';
import { useSnackbar } from '@/composables/useSnackbar';
import type { Role } from '@/types/Role';


const { showSnackbar } = useSnackbar();

const admin = useAdminStore();
const rolesList = ref([] as Role[]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
// const search = ref('');

const fetchRoles = async () => {
    try {
        loading.value = true;
        const { roles, pagination } = await admin.fetchRoles(currentPage.value);
        rolesList.value = roles.map((role: Role) => ({
            ...role
        }));
        totalPages.value = pagination.totalPages;
    } catch (error: any) {
        showSnackbar({
            text: 'Failed to fetch roles',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const deleteRole = async (id: string | undefined) => {
    try {
        loading.value = true;
        await admin.deleteRole(id);
        await fetchRoles();
        showSnackbar({
            text: 'Role deleted successfully',
            color: 'success',
        });
    } catch (error: any) {
        console.error(error);
        showSnackbar({
            text: 'Failed to delete role',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

// TODO: we don't need this right now
// const searchRoles = useDebounceFn(async () => {
//     try {
//         loading.value = true;
//         const { roles } = search.value
//             ? await admin.searchRoles(search.value)
//             : await admin.fetchRoles(currentPage.value);
//         rolesList.value = roles;
//     } catch (error: any) {
//         console.error(error);
//         showSnackbar({
//             text: 'Failed to search roles',
//             color: 'error',
//         });
//     } finally {
//         loading.value = false;
//     }
// }, 500);

onMounted(async () => {
    await fetchRoles();
});
</script>
