<template>
    <div class="ma-4">
        <v-form ref="roleForm" @submit.prevent="updateRole">
            <v-card class="lumio-card">
                <v-card-title>
                    <h1 class="section-title">Update Role</h1>
                </v-card-title>
                <v-card-text class="pa-10">
                    <v-row>
                        <v-col cols="2">
                            <v-select
                                label="Role Type"
                                v-model="formData.roleType"
                                :items="['org_admin', 'manager', 'member', 'analyst', 'external', 'vendor']"
                                :loading="loading"
                            ></v-select>
                        </v-col>
                        <v-col cols="4">
                            <div class="d-flex justify-space-start">
                                <v-checkbox
                                    label="Is command role"
                                    v-model="formData.isCommandRole"
                                />
                                <v-checkbox
                                    class="ml-4"
                                    label="Is staff role"
                                    v-model="formData.isStaffRole"
                                />
                            </div>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="6">
                            <v-text-field
                                label="Role name"
                                v-model="formData.roleName"
                                :rules="rules.required"
                                :loading="loading"
                            />
                        </v-col>
                        <v-col cols="3">
                            <v-text-field
                                label="Staff designation"
                                v-model="formData.staffDesignation"
                                :rules="rules.required"
                                :loading="loading"
                            />
                        </v-col>
                        <v-col cols="3">
                            <v-select
                                label="Rank"
                                v-model="formData.rank"
                                :rules="rules.required"
                                :items="[
                                    'No Rank',
                                    'Sergeant',
                                    'Lieutenant',
                                    'Captain',
                                    'Major',
                                    'Colonel',
                                    'General',
                                ]"
                                :loading="loading"
                            />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="6">
                            <v-text-field
                                label="Functional area"
                                v-model="formData.functionalArea"
                                :rules="rules.required"
                                :loading="loading"
                            />
                        </v-col>
                        <v-col cols="3">
                            <v-select
                                label="Unit level"
                                v-model="formData.unitLevel"
                                :rules="rules.required"
                                :items="[
                                    'N/A',
                                    'Other',
                                    'Squad',
                                    'Platoon',
                                    'Company',
                                    'Battalion',
                                    'Regiment',
                                    'Brigade',
                                    'Division',
                                    'Corps',
                                    'Army',
                                    'Fleet',
                                    'Task Force',
                                ]"
                                :loading="loading"
                            />
                        </v-col>
                        <v-col cols="3">
                            <v-text-field
                                label="Typical unit size"
                                v-model="formData.typicalUnitSize"
                                :rules="rules.required"
                                :loading="loading"
                            />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12">
                            <v-textarea
                                label="Role description"
                                v-model="formData.roleDescription"
                                :rules="rules.required"
                                :loading="loading"
                            />
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions class="d-flex justify-end align-content-end">
                    <v-btn :to="`/admin/view-roles`" class="mr-4" variant="text">Cancel</v-btn>
                    <v-btn
                        variant="flat"
                        color="primary"
                        :loading="loading"
                        type="submit"
                        prepend-icon="mdi-content-save"
                    >Update Role</v-btn>
                </v-card-actions>
            </v-card>
        </v-form>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script lang="ts" setup>
import { useAdminStore } from '@/stores/admin';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref, reactive, onMounted } from 'vue';
import type { Role } from '@/types/Role';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const roleForm = ref();
const loading = ref(false);

const formData = reactive<Role>({
    roleName: '',
    roleDescription: '',
    unitLevel: '',
    staffDesignation: '',
    functionalArea: '',
    rank: '',
    isCommandRole: false,
    isStaffRole: false,
    typicalUnitSize: '',
    roleType: '',
});

const rules = {
    required: [(v: string) => !!v || 'Required.'],
};

const updateRole = async () => {
    try {
        const params = route.params as { id: string };
        const id = params.id;
        loading.value = true;
        await admin.updateRole(id, formData as Role);
        await router.push(`/admin/view-roles`);
        showSnackbar({
            text: 'Role updated successfully',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error updating role',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    try {
        loading.value = true;
        const params = route.params as { id: string };
        const id = params.id;
        const response = await admin.getRoleById(id as string);
        
        // Check if response has a role property or if it is the role itself
        const role = response.role || response;
        
        console.log('Fetched role:', role); // Debug log
        
        // Assign values to formData
        formData.roleName = role.roleName || '';
        formData.roleDescription = role.roleDescription || '';
        formData.unitLevel = role.unitLevel || '';
        formData.staffDesignation = role.staffDesignation || '';
        formData.functionalArea = role.functionalArea || '';
        formData.rank = role.rank || '';
        formData.isCommandRole = role.isCommandRole || false;
        formData.isStaffRole = role.isStaffRole || false;
        formData.typicalUnitSize = role.typicalUnitSize || '';
        formData.roleType = role.roleType || '';
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Error fetching role',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
});
</script>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>
