<template>
    <div>
        <v-container>
            <v-progress-linear v-if="loading" indeterminate />
            <v-card v-else>
                <v-card-title class="d-flex align-center">
                    <v-avatar size="80">
                        <img
                            v-if="user?.avatar"
                            :src="user.avatar as string"
                            :alt="user.firstName as string"
                        />
                        <v-icon color="#1E1E1E" size="80"
                            >mdi-account-circle</v-icon
                        >
                    </v-avatar>
                    <div class="ml-4">
                        <h2>{{ user?.firstName }} {{ user?.lastName }}</h2>
                        <p class="font-weight-regular">{{ user?.email }}</p>
                    </div>
                </v-card-title>
                <v-card-text>
                    <v-table>
                        <template v-slot:default>
                            <tbody>
                                <tr>
                                    <td><b>Position id</b></td>
                                    <td>{{ user?.positionId ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><b>Account type</b></td>
                                    <td>{{ user?.accountType ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><b>Roles</b></td>
                                    <td>
                                        <v-chip
                                            v-for="role in user?.roles"
                                            :key="role.id"
                                            class="mr-1"
                                            closable
                                            @click:close="
                                                handleDelete(role.id as string)
                                            "
                                        >
                                            {{ role.roleName }}
                                        </v-chip>
                                    </td>
                                </tr>
                                <tr>
                                    <td><b>Operation</b></td>
                                    <td>
                                        <div class="d-flex align-center py-4">
                                            <v-autocomplete
                                                v-model="selectedOperation"
                                                class="mr-2"
                                                width="200"
                                                :items="availableOperations"
                                                label="Select operation"
                                                :loading="loadingOperations"
                                                item-title="name"
                                                item-value="id"
                                            />
                                            <v-select
                                                v-model="accessType"
                                                class="mr-2"
                                                :items="accessTypes"
                                                label="Access type"
                                                :loading="loadingOperations"
                                            />
                                            <v-btn
                                                @click="handleAssignOperation"
                                                :disabled="!selectedOperation"
                                                :loading="loading"
                                                >Assign</v-btn
                                            >
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </template>
                    </v-table>
                </v-card-text>
                <v-card-actions class="d-flex justify-end">
                    <v-btn @click="dialog = true">{{ getTitle }}</v-btn>
                    <v-spacer />
                    <v-btn color="primary" class="mr-2" to="/admin/users">
                        Back
                    </v-btn>
                    <v-btn
                        color="primary"
                        class="mr-2"
                        :to="`/edit-user/${user?.id}`"
                    >
                        Edit
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-container>

        <v-dialog v-model="dialog" width="500">
            <v-card>
                <v-card-title>
                    {{ getTitle }}
                </v-card-title>
                <v-card-text>
                    <v-autocomplete
                        v-model="selectedRoles"
                        :items="availableRoles"
                        item-title="name"
                        multiple
                        chips
                        item-value="id"
                        label="Select role"
                        :loading="loadingRoles"
                    />
                </v-card-text>
                <v-card-actions>
                    <v-btn color="primary" @click="hideDialog"> Cancel </v-btn>
                    <v-btn color="primary" @click="handleAdd">
                        {{ isEdit ? 'Update' : 'Add' }}
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<route lang="yaml">
meta:
    layout: authenticated
    protected: true
    admin: true
</route>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ref, onMounted } from 'vue';
import type { User } from '@/types/User';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminStore } from '@/stores/admin';
import type { Role } from '@/types/Role';
import { Operation } from '@/types/Operation';

const admin = useAdminStore();
const dialog = ref(false);
const isEdit = ref(false);
const selectedOperation = ref<null | undefined>(null);
const availableOperations = ref([]);
const loadingOperations = ref(false);
const selectedRoles = ref([]);
const accessType = ref('read');
const accessTypes = ref(['read', 'write', 'manage']);

const { showSnackbar } = useSnackbar();

const user = ref<User | null>(null);
const userRoles = ref<Role[]>([]);

const getTitle = computed(() => {
    return userRoles.value.length ? 'Edit roles' : 'Add roles';
});

const hideDialog = () => {
    dialog.value = false;
};

const fetchOperations = async () => {
    try {
        loadingOperations.value = true;
        const data = await admin.fetchOperations();
        availableOperations.value = data.data.operations;
    } catch (error) {
        console.error(error);
    } finally {
        loadingOperations.value = false;
    }
};

const handleAssignOperation = async () => {
    try {
        loading.value = true;
        await admin.assignUserToOperation(userId, {
            operationId: selectedOperation.value,
            accessType: accessType.value,
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to assign operation',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

watch(dialog, async (newVal) => {
    if (newVal && !availableRoles.value.length) {
        await fetchRoles();
    }

    if (userRoles.value.length) {
        isEdit.value = true;
    }
});

const availableRoles = ref([]);
const loadingRoles = ref(false);

const loading = ref(false);

const route = useRoute();
const params = route.params as { id: string };
const userId = params.id;


const handleAdd = async () => {
    try {
        loading.value = true;
        if (isEdit.value) {
            await admin.updateAssignedRoles(userId, selectedRoles.value);
        } else {
            await admin.assignRoleToUser(userId, selectedRoles.value);
        }
        await fetchUser();
        showSnackbar({
            text: 'Roles assigned successfully',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to assign roles',
            color: 'error',
        });
    } finally {
        loading.value = false;
        hideDialog();
    }
};

const handleDelete = async (roleId: string) => {
    try {
        await admin.deleteRoleFromUser(userId, roleId);
        await fetchUser();
        showSnackbar({
            text: 'Role deleted successfully',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to delete role',
            color: 'error',
        });
    }
};

const fetchRoles = async () => {
    try {
        loadingRoles.value = true;
        const data = await admin.fetchRoles(1, 100);
        availableRoles.value = data.roles.map((role: Role) => ({
            name: role.roleName,
            id: role.id,
        }));
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to fetch roles',
            color: 'error',
        });
    } finally {
        loadingRoles.value = false;
    }
};

const fetchUser = async () => {
    try {
        loading.value = true;
        user.value = await admin.getUserById(userId) as User;
	    userRoles.value = user.value?.roles as Role[];
		const userOperations = user.value?.operations as Operation[];
        if (userRoles.value.length) {
            // @ts-ignore
            selectedRoles.value = user.value.roles.map((role: Role) => role.id);
        }
        if (userOperations.length) {
            // @ts-ignore
            selectedOperation.value = user.value.operations[0].operation.id;
        }
    } catch (error) {
        showSnackbar({
            text: 'Failed to load user',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

onMounted(async () => {
    await fetchUser();
    await fetchOperations();
});
</script>
