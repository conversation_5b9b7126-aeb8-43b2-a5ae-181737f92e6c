<template>
    <v-container class="fill-height">
        <v-row>
            <v-col>
                <v-card class="text-center">
                    <v-card-title>404</v-card-title>
                    <v-card-text>
                        <p>Page not found</p>
                    </v-card-text>
                    <v-card-actions class="justify-center">
                        <v-btn :to="isAuthenticated ? '/home' : '/'">
                            Get Back Home
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts" setup>
const isAuthenticated = computed(() => {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');

    return accessToken && refreshToken;
});
</script>
