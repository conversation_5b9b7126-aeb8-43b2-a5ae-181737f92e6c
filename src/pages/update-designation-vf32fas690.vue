<template>
	<v-container class="h-screen d-flex justify-center align-center">

	<v-row>
			<v-col cols="6 mx-auto">
				<v-card class="lumio-card align-center justify-center rounded-lg">
					<v-card-title>
						<h3>Update Designation</h3>
					</v-card-title>
					<v-card-text class="pa-8">
						<v-btn
							@click="updateDesignation"
							variant="text"
							color="black"
							class="mb-4">
							UPDATE DESIGNATION
						</v-btn>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>
	</v-container>
</template>

<script setup lang="ts">

import { useUtilsStore } from '@/stores/utils';

const useUtils = useUtilsStore();


const updateDesignation = async () => {
	try {
		await useUtils.updateDesignation();
	} catch (error) {
		console.error('Error updating designation:', error);
	}
};

</script>
