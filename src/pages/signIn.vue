<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useOperationStore } from '@/stores/operation.store';
import { useRouter } from 'vue-router';
import { useSnackbar } from '@/composables/useSnackbar';

const formData = ref({
    email: '',
    password: '',
});

const { showSnackbar } = useSnackbar();

const operationStore = useOperationStore();
const authStore = useAuthStore();
const router = useRouter();
const loading = ref(false);

const rules = {
    email: [
        (v: string) => !!v || 'Email is required',
        (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ],
    password: [(v: string) => !!v || 'Password is required'],
};

const signIn = async () => {
    try {
        loading.value = true;
        await authStore.signIn(formData.value.email, formData.value.password);
        //reset persistant operation store
        await operationStore.reset();
        await router.push('/home');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to sign in',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <v-container class="h-screen d-flex justify-center align-center">
        <v-row>
            <v-col cols="6 mx-auto">
                <v-card class="lumio-card align-center justify-center rounded-lg">
                    <v-card-title>
                      <h3>Sign In</h3>
                      <v-btn variant="flat" color="primary" to="/register"
                      >Register Organization</v-btn
                      >
                    </v-card-title>
                    <v-card-text class="pa-8">
                        <v-form @submit.prevent="signIn">
                            <v-text-field
                                v-model="formData.email"
                                class="mb-4"
                                label="Email"
                                :rules="rules.email"
                                required
                            />
                            <v-text-field
                                v-model="formData.password"
                                class="mb-4"
                                label="Password"
                                type="password"
                                :rules="rules.password"
                                required
                            />
                            <v-row>

                                <v-col>
                                    <v-btn variant="text" to="/forgot-password"
                                        >Forgot Password</v-btn
                                    >
                                    <v-spacer />
                                </v-col>
                                <v-col class="text-right">
                                    <v-btn
                                        color="#effa1a"
                                        type="submit"
                                        :loading="loading"
                                        >Sign In</v-btn
                                    >
                                </v-col>
                            </v-row>
                          <v-divider class="mt-4" />
                          <v-row class="mt-4">
                            <v-col>
                              You will be signed into your default organization. If you want to sign into a
                              specific organization, please contact your organization's admin for
                              organization-specific login information.
                            </v-col>
                          </v-row>
                        </v-form>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
