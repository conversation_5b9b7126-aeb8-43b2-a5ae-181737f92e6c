<template>
  <v-container class="h-screen d-flex justify-center">
  <v-row>
    <v-col cols="12" class="mx-auto">
      <v-form v-model="isFormValid" ref="form" @submit.prevent="handleRegister">
      <v-card class="lumio-card">

          <v-card-title>
            <h2>
              Register For Lumio
            </h2>
          </v-card-title>
          <v-card-text class="px-4">
            <p>All fields are required</p>
            <v-row>
              <v-col cols="6" class="px-4">
                <h4 class="my-4">Organization</h4>
                <v-row>
                  <v-col cols="7">
                    <v-text-field
                      v-model="registrationData.organization.name"
                      label="Organization Name"
                      :rules="rules.required"
                      required
                    ></v-text-field>
                  </v-col>
                  <v-col cols="5">
                    <v-checkbox
                      v-model="registrationData.organization.isMilitary"
                      label="Is Military"
                    ></v-checkbox>
                  </v-col>
                </v-row>
                <v-text-field
                  v-model="registrationData.organization.description"
                  label="Organization Description"
                  :rules="rules.required"
                  required
                ></v-text-field>

                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="registrationData.organization.sovereignty"
                      label="Sovereignty"
                      :rules="rules.required"
                      required
                    >
                      <template v-slot:details>
                        <div class="text-caption">
                          <p>Sovereignty is the territory that the organization is operating in (i.e. NATO,
                            Australia, EU)</p>
                        </div>
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
					  <v-select
						  v-model="registrationData.organization.isoCountryCode"
						  label="Country"
						  :items="formattedCountries"
						  item-title="display"
						  item-value="code"
						  :rules="rules.required"
						  required
					  >
						  <template v-slot:selection="{ item }">
							  <div class="d-flex align-center">
								  <span :class="`fi fi-${(item.raw?.code) ? item.raw.code.toLowerCase() : ''} mr-2`"></span>
								  <span>{{ item.raw.display }}</span>
							  </div>
						  </template>
						  <template v-slot:item="{ item, props }">
							  <v-list-item v-bind="props">
								  <template v-slot:prepend>
									  <span :class="`fi fi-${item.raw.code.toLowerCase()} mr-1`"></span>
								  </template>
							  </v-list-item>
						  </template>
					  </v-select>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="6" class="px-4">
                <h4 class="my-4">Organization Owner Details</h4>
                <v-row>
                  <v-col cols="6">
                    <v-text-field
                      v-model="registrationData.user.firstName"
                      label="First Name"
                      :rules="rules.required"
                      required
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="registrationData.user.lastName"
                      label="Last Name"
                      :rules="rules.required"
                      required
                    ></v-text-field>
                  </v-col>
                </v-row>


                <v-text-field
                  v-model="registrationData.user.email"
                  label="Email"
                  :rules="rules.email"
                  required
                  autocomplete="email"
                ></v-text-field>
                <v-text-field
                  v-model="registrationData.user.password"
                  label="Password"
                  :rules="rules.password"
                  required
                  :type="showPassword ? 'text' : 'password'"
                  autocomplete="new-password"
                >
                  <template v-slot:append-inner>
                    <v-btn @click="showPassword = !showPassword"
                     class="ma-0"
                           variant="text"
                           icon
                    >
                      <v-icon v-if="showPassword">mdi-eye-off</v-icon>
                      <v-icon v-else>mdi-eye</v-icon>
                    </v-btn>
                  </template>

                </v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="text-right py-4">
                <v-btn
                  color="primary"
                  type="submit"
                  >Register</v-btn
                >
              </v-col>
            </v-row>


          </v-card-text>
      </v-card>
      </v-form>
    </v-col>
  </v-row>
  </v-container>


</template>

<route lang="yaml">
meta:
    layout: default
    title: Register
    description: Registration page
    protected: false
</route>

<script setup lang="ts">
import { useRegisterStore } from '@/stores/register.store';
import { IRegistration } from '@/types/Registration.type';
import { useSnackbarStore } from '@/stores/snackbar';
import { useRouter } from "vue-router";
import { AxiosError } from 'axios';
import { useNatoCountries } from '@/composables/useNatoCounries';
import { computed } from 'vue';

const registerStore = useRegisterStore();
const snackbarStore = useSnackbarStore();
const router = useRouter();
const isFormValid = ref(false);
const form = ref<any>(null);
const { natoCountries } = useNatoCountries();

// Add formatted countries computed property
const formattedCountries = computed(() => {
	return natoCountries.map(country => ({
		code: country.code,
		name: country.name,
		display: `${country.code} - ${country.name}`
	}));
});

const registrationData = ref<IRegistration>({
    organization: {
        name: '',
        description: '',
        isMilitary: false,
        isoCountryCode: '',
        sovereignty: '',
    },
    user: {
        firstName: '',
        lastName: '',
        email: '',
        password: '',
    },
});

const showPassword = ref(false);
const isSubmitted = ref(false);

const rules = {
  required: [(v: string) => !!v || 'This field is required'],
  email: [
    (v: string) => !!v || 'Email is required',
    (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
  ],
  password: [
    (v: string) => !!v || 'Password is required',
    (v: string) => v.length >= 8 || 'Password must be at least 8 characters long',
    (v: string) => /[A-Z]/.test(v) || 'Password must contain at least one uppercase letter',
    (v: string) => /[a-z]/.test(v) || 'Password must contain at least one lowercase letter',
    (v: string) => /[0-9]/.test(v) || 'Password must contain at least one number',
    (v: string) => /[!@#$%^&*(),.?":{}|<>]/.test(v) || 'Password must contain at least one special character'
  ],
};


const handleRegister = async () => {
  const { valid } = await form.value.validate()

  if (!valid) {
    snackbarStore.showSnackbar({
      text: 'Please fill out all required fields correctly',
      color: 'error',
    });
    return;
  }

  const { organization, user } = registrationData.value;
  const registration: IRegistration = {
    organization,
    user,
  };

  try {
    await registerStore.register(registration);
    snackbarStore.showSnackbar({
      text: 'Registration successful',
      color: 'success',
    });
    isSubmitted.value = true;
    await router.push('/');
  } catch (e) {
    if (e instanceof AxiosError && e.response?.data) {
      // Axios-specific error
      const axiosResponse = e.response.data;
      snackbarStore.showSnackbar({
        text: "Error: " + axiosResponse.error,
        color: "error",
      });
    } else {
      // Generic error handling in case it's not an AxiosError
      snackbarStore.showSnackbar({
        text: "An unexpected error occurred",
        color: "error",
      });
    }

  }
};



</script>
