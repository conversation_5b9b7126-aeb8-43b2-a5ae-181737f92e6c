<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter, useRoute } from 'vue-router';
import { useSnackbar } from '@/composables/useSnackbar';

const { showSnackbar } = useSnackbar();
const formData = ref({
    password: '',
});

const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const loading = ref(false);

const email = route.query.email as string;
const token = route.query.token as string;

const rules = {
    password: [(v: string) => !!v || 'Password is required'],
};

const changePassword = async () => {
    try {
        loading.value = true;
        // url encoded email
        await authStore.changePassword(email, token, formData.value.password);
        showSnackbar({
            text: 'Password changed successfully',
            color: 'success',
        });
        await router.push('/signin');
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to change password',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <v-container class="d-flex align-center justify-center" style="height: 100vh;">
        <v-row justify="center">
            <v-col cols="12" sm="8" md="6" lg="5">
                <v-card class="lumio-card">
                    <v-card-title>
                        <h3>
                            Set Password
                        </h3>
                    </v-card-title>
                    <v-card-subtitle>
                        <h3>
                            User: {{ email }}
                        </h3>
                    </v-card-subtitle>
                    <v-card-text class="pa-10">
                        <v-form @submit.prevent="changePassword">
                            <v-text-field
                                v-model="formData.password"
                                label="New Password"
                                type="password"
                                :rules="rules.password"
                                required
                            />
                            <v-btn
                                :disabled="loading || !formData.password"
                                type="submit"
                                variant="flat"
                                class="w-100"
                                color="primary"
                                density="default"
                                :loading="loading"
                                >Set Password</v-btn
                            >
                        </v-form>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
