<template>
    <!-- box with welcome text and link to the login route -->
    <v-container class="h-screen d-flex justify-center align-center bg-primary">
        <v-row class="mt-16">
            <v-col cols="4 mx-auto">
                <v-card
                    v-if="!isAuthenticated"
                    class="align-center justify-center rounded-lg"
                >
                    <v-card-title>Welcome to DeepHelm</v-card-title>
                    <v-card-text>
                        <p>Please login to continue</p>
                    </v-card-text>
                    <v-card-actions class="d-flex justify-center">
                        <v-btn class="bg-primary" to="/signin">Login</v-btn>
                        <!-- <v-btn to="/home">Go to home</v-btn> -->
                    </v-card-actions>
                </v-card>
                <v-card v-else>
                    <v-card-title>Welcome to DeepHelm</v-card-title>
                    <v-card-text>
                        <p class="mb-4">
                            You are already logged in. Click the button below to
                            go to the home page.
                        </p>

                        <v-btn to="/home" class="ml-4">Go to home</v-btn>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<route lang="yaml">
meta:
    layout: default
    title: Home
    description: Home page
    protected: false
</route>

<script lang="ts" setup>
const router = useRouter();

const isAuthenticated = computed(() => {
    return localStorage.getItem('accessToken');
});

if (isAuthenticated.value) {
    router.push('/home');
}
</script>
