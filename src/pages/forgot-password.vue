<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useSnackbar } from '@/composables/useSnackbar';
const formData = ref({
    email: '',
});

const { showSnackbar } = useSnackbar();

const authStore = useAuthStore();
const emailForm = ref();
const loading = ref(false);

const router = useRouter();

const resetUrl = ref('');

const rules = {
    email: [
        (v: string) => !!v || 'Email is required',
        (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
    ],
};

const resetPassword = async () => {
    try {
        loading.value = true;
        const response = await authStore.forgotPassword(formData.value.email);
        formData.value.email = '';
        // extract form url get params
        // @TODO: remove after email notification will be set
        const url = new URL(response.resetLink);
        const email = url.searchParams.get('email');
        const token = url.searchParams.get('token');
        resetUrl.value = `${
            import.meta.env.VITE_APP_URL
        }/reset-password?email=${email}&token=${token}`;
        showSnackbar({
            text: 'We have sent you an email to reset your password',
            color: 'success',
            button: {
                text: 'Copy to clipboard',
                action: copyToClipboard,
            },
        });
        router.push('/signin');
    } catch (error) {
        showSnackbar({
            text: 'Failed to reset password',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const copyToClipboard = () => {
    navigator.clipboard.writeText(resetUrl.value);
    showSnackbar({
        text: 'Link copied to clipboard',
        color: 'success',
    });
};
</script>

<template>
    <v-container>
        <v-row>
            <v-col>
                <v-card>
                    <v-card-title>Reset Password</v-card-title>
                    <v-card-text>
                        <v-form ref="emailForm" @submit.prevent="resetPassword">
                            <v-text-field
                                v-model="formData.email"
                                class="mb-4"
                                label="Email"
                                :rules="rules.email"
                                required
                            />
                            <v-btn
                                :disabled="loading || !formData.email"
                                type="submit"
                                :loading="loading"
                                >Reset Password</v-btn
                            >
                        </v-form>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
</template>
