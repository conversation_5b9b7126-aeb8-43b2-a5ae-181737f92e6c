<template>
    <v-dialog v-model="dialog" max-width="600px" persistent>
        <v-card>
            <v-card-title class="d-flex justify-space-between align-center">
                <span class="text-h5">{{
                    isEditMode ? 'Edit Task' : 'Create Task'
                }}</span>
                <v-btn
                    icon="mdi-close"
                    variant="text"
                    @click="closeModal"
                ></v-btn>
            </v-card-title>

            <v-card-text>
                <v-form ref="form" v-model="isValid">
                    <v-container>
                        <div
                            v-if="isFetching"
                            class="d-flex justify-center align-center pa-4"
                        >
                            <v-progress-circular
                                indeterminate
                                color="primary"
                            ></v-progress-circular>
                        </div>
                        <v-row v-else>
                            <v-col cols="12">
                                <v-text-field
                                    v-model="taskData.title"
                                    label="Title *"
                                    :rules="[(v) => !!v || 'Title is required']"
                                    required
                                    variant="outlined"
                                    density="compact"
                                    hide-details="auto"
                                ></v-text-field>
                            </v-col>

                            <v-col cols="12">
                                <v-textarea
                                    v-model="taskData.description"
                                    label="Description"
                                    variant="outlined"
                                    density="compact"
                                    hide-details="auto"
                                ></v-textarea>
                            </v-col>

                            <v-col cols="12" sm="6">
                                <v-select
                                    v-model="taskData.priority"
                                    :items="priorityOptions"
                                    label="Priority *"
                                    :rules="[
                                        (v) => !!v || 'Priority is required',
                                    ]"
                                    required
                                    variant="outlined"
                                    density="compact"
                                    hide-details="auto"
                                ></v-select>
                            </v-col>

                            <v-col cols="12" sm="6">
                                <LumioDateTimePicker
                                    v-model="taskData.dueAt"
                                    label="Due Date"
                                    :format="'YYYY-MM-DD HH:mm'"
                                    :placeholder="'Select due date and time'"
                                    :showTimezone="false"
                                />
                            </v-col>
                        </v-row>
                    </v-container>
                </v-form>
            </v-card-text>

            <v-card-actions class="pa-4">
                <v-spacer></v-spacer>
                <v-btn
                    color="error"
                    variant="text"
                    @click="closeModal"
                    :disabled="isSubmitting || isFetching"
                >
                    Cancel
                </v-btn>
                <v-btn
                    color="primary"
                    @click="saveTask"
                    :loading="isSubmitting"
                    :disabled="
                        !isValid ||
                        isSubmitting ||
                        isFetching ||
                        !currentOperationId
                    "
                >
                    {{ isEditMode ? 'Update' : 'Create' }}
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useTaskStore, ITask } from '@/stores/admin/task.store';
import { useOperationStore } from '@/stores/operation.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { storeToRefs } from 'pinia';
import type { Operation } from '@/types/Operation';
import dayjs from 'dayjs';
import LumioDateTimePicker from '@/components/elements/LumioDateTimePicker.vue';
import type { VForm } from 'vuetify/components';

const props = defineProps<{
    modelValue: boolean;
    taskId?: number;
}>();

const emit = defineEmits(['update:modelValue', 'task-created', 'task-updated']);

const taskStore = useTaskStore();
const operationStore = useOperationStore();
const { showSnackbar } = useSnackbar();
const { currentOperationId } = storeToRefs(operationStore);

const dialog = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});

const isEditMode = computed(() => !!props.taskId);
const isValid = ref(false);
const isSubmitting = ref(false);
const isFetching = ref(false);
const form = ref<VForm | null>(null);

const taskData = reactive<Partial<ITask>>({
    title: '',
    description: '',
    priority: 'medium',
    dueAt: null,
    operationId: 0,
});

const priorityOptions = [
    { title: 'Highest', value: 'highest' },
    { title: 'High', value: 'high' },
    { title: 'Medium', value: 'medium' },
    { title: 'Low', value: 'low' },
];

const operations = ref<Operation[]>([]);

const fetchTask = async (id: number) => {
    isFetching.value = true;
    try {
        const response = await taskStore.fetchTaskById(id);
        const task = response.data.task;

        // Update form data
        taskData.title = task.title;
        taskData.description = task.description || '';
        taskData.priority = task.priority;
        taskData.dueAt = task.dueAt || null;
        taskData.operationId = task.operationId;
    } catch (error) {
        console.error('Error fetching task:', error);
        showSnackbar({
            text: 'Failed to fetch task data',
            color: 'error',
        });
        closeModal();
    } finally {
        isFetching.value = false;
    }
};

const saveTask = async () => {
    if (!isValid.value || !currentOperationId.value) return;

    isSubmitting.value = true;
    try {
        const taskPayload = {
            ...taskData,
            operationId: Number(currentOperationId.value),
            dueAt: taskData.dueAt
                ? dayjs(taskData.dueAt).format('YYYY-MM-DD HH:mm')
                : undefined,
        };

        if (isEditMode.value && props.taskId) {
            await taskStore.updateTask(props.taskId, taskPayload);
            showSnackbar({
                text: 'Task updated successfully',
                color: 'success',
            });
            emit('task-updated');
        } else {
            await taskStore.createTask(taskPayload);
            showSnackbar({
                text: 'Task created successfully',
                color: 'success',
            });
            emit('task-created');
        }
        dialog.value = false;
        closeModal();
    } catch (error) {
        console.error('Error saving task:', error);
    } finally {
        isSubmitting.value = false;
    }
};

const closeModal = () => {
    if (isFetching.value || isSubmitting.value) return;

    dialog.value = false;

    // Reset form data
    Object.assign(taskData, {
        title: '',
        description: '',
        priority: 'medium',
        dueAt: null,
        operationId: 0,
    });

    // Reset form validation
    if (form.value) {
        form.value.reset();
        form.value.resetValidation();
    }
};

// Watch dialog state to reset form when closed
watch(
    () => dialog.value,
    (newValue) => {
        if (!newValue) {
            closeModal();
        }
    },
);

// Fetch task data if in edit mode
watch(
    () => props.taskId,
    (newId) => {
        if (newId) {
            fetchTask(newId);
        }
    },
    { immediate: true },
);

// Fetch operations on mount
onMounted(async () => {
    try {
        const { data } = await operationStore.fetchOperations();
        operations.value = data.operations;
    } catch (error) {
        console.error('Error fetching operations:', error);
        showSnackbar({
            text: 'Failed to fetch operations',
            color: 'error',
        });
    }
});
</script>
