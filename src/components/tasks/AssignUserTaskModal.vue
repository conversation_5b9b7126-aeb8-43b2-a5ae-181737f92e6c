<template>
    <v-dialog
        :model-value="modelValue"
        max-width="600px"
        @update:model-value="handleDialogClose"
    >
        <v-card>
            <v-card-title class="text-h5">
                Assign Members to Task
            </v-card-title>

            <v-card-text>
                <v-form ref="form" @submit.prevent="handleSubmit">
                    <v-select
                        v-model="selectedUsers"
                        :items="users"
                        item-title="name"
                        item-value="id"
                        label="Select Users *"
                        multiple
                        chips
                        :loading="loading"
                        :rules="rules.required"
                    />
                </v-form>
            </v-card-text>

            <v-card-actions>
                <v-spacer />
                <v-btn color="error" variant="text" @click="handleDialogClose">
                    Cancel
                </v-btn>
                <v-btn
                    color="primary"
                    @click="handleSubmit"
                    :loading="loading"
                    :disabled="selectedUsers.length === 0 || loading"
                >
                    Assign
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useTaskStore } from '@/stores/admin/task.store';
import { useAdminUserStore } from '@/stores/admin/user.store';
import { useSnackbar } from '@/composables/useSnackbar';

const props = defineProps<{
    modelValue: boolean;
    taskId: number;
}>();

const emit = defineEmits(['update:modelValue']);

const taskStore = useTaskStore();
const userStore = useAdminUserStore();
const { showSnackbar } = useSnackbar();

const loading = ref(false);
const selectedUsers = ref<number[]>([]);
const users = ref<
    Array<{ id: number; name: string; email: string; accountType: string }>
>([]);
const form = ref(null);

const rules = {
    required: [
        (v: any[]) => (v && v.length > 0) || 'Please select at least one user',
    ],
};

const handleDialogClose = () => {
    selectedUsers.value = [];
    emit('update:modelValue', false);
};

const fetchUsers = async () => {
    try {
        loading.value = true;
        const response = await userStore.getUsers();
        if (response?.data?.users) {
            users.value = response.data.users.map((user: any) => ({
                id: user.id,
                name: `${user.firstName} ${user.lastName}`,
                email: user.email,
                accountType: user.accountType,
            }));
        }
    } catch (error) {
        showSnackbar({
            text: 'Failed to fetch users',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const handleSubmit = async () => {
    try {
        loading.value = true;
        await taskStore.assignMembers(props.taskId, {
            userIds: selectedUsers.value,
        });
        showSnackbar({
            text: 'Members assigned successfully',
            color: 'success',
        });
        handleDialogClose();
    } catch (error) {
        showSnackbar({
            text: 'Failed to assign members',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

fetchUsers();
</script>
