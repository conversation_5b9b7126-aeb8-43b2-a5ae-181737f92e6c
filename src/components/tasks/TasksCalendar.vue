<script setup lang="ts">
//COMMENT: I commented out not used code but you can comment it back in. TypeScript was complaining about it.
// @kravchenkoWeb

import { ref, computed } from 'vue';
import {
    format,
    startOfWeek,
    endOfWeek,
    eachDayOfInterval,
    isSameDay,
    startOfMonth,
    endOfMonth,
    isSameMonth,
    isToday,
    addDays,
    // parseISO,
    // startOfDay,
    // endOfDay,
} from 'date-fns';
// import { toZonedTime, fromZonedTime } from 'date-fns-tz';
import { ITask } from '@/stores/admin/task.store';
import { useRouter } from 'vue-router';
// import { useTaskStore } from '@/stores/admin/task.store';

interface Props {
    tasks: ITask[];
}

const props = defineProps<Props>();

const currentDate = ref(new Date());
const viewMode = ref<'daily' | 'weekly'>('weekly');

const monthStart = computed(() => startOfMonth(currentDate.value));
const monthEnd = computed(() => endOfMonth(currentDate.value));

const calendarDays = computed(() => {
    const start = startOfWeek(monthStart.value, { weekStartsOn: 1 });
    const end = endOfWeek(monthEnd.value, { weekStartsOn: 1 });
    return eachDayOfInterval({ start, end });
});

// const weekDays = computed(() => {
//     const start = startOfWeek(currentDate.value, { weekStartsOn: 1 });
//     const end = endOfWeek(currentDate.value, { weekStartsOn: 1 });
//     return eachDayOfInterval({ start, end });
// });

const tasksForCurrentDay = computed(() => {
    return props.tasks
        .filter((task) => {
            if (!task.dueAt) return false;
            return isSameDay(new Date(task.dueAt), currentDate.value);
        })
        .sort((a, b) => {
            if (!a.dueAt || !b.dueAt) return 0;
            return new Date(a.dueAt).getTime() - new Date(b.dueAt).getTime();
        });
});

// const tasksForMonth = computed(() => {
//     return props.tasks.filter((task) => {
//         if (!task.dueAt) return false;
//         const taskDate = new Date(task.dueAt);
//         return taskDate >= monthStart.value && taskDate <= monthEnd.value;
//     });
// });

const getTasksForDay = (day: Date) => {
    return props.tasks
        .filter((task) => {
            if (!task.dueAt) return false;
            return isSameDay(new Date(task.dueAt), day);
        })
        .sort((a, b) => {
            if (!a.dueAt || !b.dueAt) return 0;
            return new Date(a.dueAt).getTime() - new Date(b.dueAt).getTime();
        });
};

const isCurrentWeek = (day: Date) => {
    const currentWeekStart = startOfWeek(currentDate.value, {
        weekStartsOn: 1,
    });
    const currentWeekEnd = endOfWeek(currentDate.value, { weekStartsOn: 1 });
    const isCurrentMonth = isSameMonth(day, new Date());
    return isCurrentMonth && day >= currentWeekStart && day <= currentWeekEnd;
};

const navigateDate = (direction: 'prev' | 'next') => {
    if (viewMode.value === 'daily') {
        currentDate.value = addDays(
            currentDate.value,
            direction === 'next' ? 1 : -1,
        );
    } else {
        const newDate = new Date(currentDate.value);
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        currentDate.value = newDate;
    }
};

const switchView = (mode: 'daily' | 'weekly') => {
    viewMode.value = mode;
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'completed':
            return 'success';
        case 'in_progress':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'cancelled':
            return 'error';
        default:
            return 'grey';
    }
};

const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
        case 'highest':
        case 'high':
            return 'error';
        case 'medium':
            return 'warning';
        case 'low':
            return 'success';
        default:
            return 'grey';
    }
};

const getInitials = (user: { firstName: string; lastName: string }) => {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(
        0,
    )}`.toUpperCase();
};

const formatTaskTime = (dateString: string) => {
    try {
        // Extract hours and minutes directly from the ISO string
        const [time] = dateString.split('T')[1].split('.');
        return time.substring(0, 5); // Get HH:mm part
    } catch {
        return '';
    }
};

const router = useRouter();

const navigateToTask = (taskId: number) => {
    router.push(`/task/${taskId}`);
};

// const taskStore = useTaskStore();
</script>

<template>
    <div class="calendar-container lumio-card">
        <div class="calendar-header">
            <div class="calendar-header-content">
                <h4 class="calendar-title">Tasks Calendar</h4>
                <div class="view-mode-controls">
                    <v-btn
                        :color="viewMode === 'daily' ? 'primary' : undefined"
                        variant="text"
                        size="small"
                        @click="switchView('daily')"
                    >
                        Daily
                    </v-btn>
                    <v-btn
                        :color="viewMode === 'weekly' ? 'primary' : undefined"
                        variant="text"
                        size="small"
                        @click="switchView('weekly')"
                    >
                        Monthly
                    </v-btn>
                </div>
                <div class="calendar-navigation">
                    <v-btn
                        icon="mdi-chevron-left"
                        variant="text"
                        size="small"
                        @click="navigateDate('prev')"
                    />
                    <h2 class="text-h6">
                        {{ format(currentDate, 'MMMM yyyy') }}
                    </h2>
                    <v-btn
                        icon="mdi-chevron-right"
                        variant="text"
                        size="small"
                        @click="navigateDate('next')"
                    />
                </div>
            </div>
        </div>

        <div class="calendar-body">
            <template v-if="viewMode === 'daily'">
                <div class="daily-view">
                    <div class="day-header">
                        {{ format(currentDate, 'EEEE, MMMM d') }}
                    </div>
                    <div class="tasks-list">
                        <v-card
                            v-for="task in tasksForCurrentDay"
                            :key="task.id"
                            class="task-item mb-2"
                            variant="outlined"
                        >
                            <v-card-item>
                                <div class="d-flex align-center">
                                    <div class="task-time mr-2">
                                        {{ formatTaskTime(task.dueAt || '') }}
                                    </div>
                                    <v-card-title
                                        class="text-subtitle-1 flex-grow-1"
                                    >
                                        {{ task.title }}
                                    </v-card-title>
                                </div>
                                <v-card-subtitle>
                                    <v-chip
                                        :color="getStatusColor(task.status)"
                                        size="small"
                                        class="mr-2"
                                    >
                                        {{ task.status }}
                                    </v-chip>
                                    <v-chip
                                        :color="getPriorityColor(task.priority)"
                                        size="small"
                                    >
                                        {{ task.priority }}
                                    </v-chip>
                                </v-card-subtitle>
                                <div class="d-flex align-center mt-2">
                                    <div
                                        v-if="task.members?.length"
                                        class="d-flex align-center"
                                    >
                                        <v-tooltip
                                            v-for="member in task.members"
                                            :key="member.id"
                                            :text="`${member.firstName} ${member.lastName}`"
                                            location="top"
                                        >
                                            <template
                                                v-slot:activator="{ props }"
                                            >
                                                <v-avatar
                                                    v-bind="props"
                                                    size="28"
                                                    color="primary"
                                                    class="mr-1"
                                                >
                                                    {{ getInitials(member) }}
                                                </v-avatar>
                                            </template>
                                        </v-tooltip>
                                    </div>
                                </div>
                            </v-card-item>
                        </v-card>
                    </div>
                </div>
            </template>

            <template v-else>
                <div class="monthly-view">
                    <div class="weekday-headers">
                        <div
                            v-for="day in [
                                'Mon',
                                'Tue',
                                'Wed',
                                'Thu',
                                'Fri',
                                'Sat',
                                'Sun',
                            ]"
                            :key="day"
                            class="weekday-header"
                        >
                            {{ day }}
                        </div>
                    </div>
                    <div class="calendar-grid">
                        <div
                            v-for="day in calendarDays"
                            :key="day.toString()"
                            class="day-cell"
                            :class="{
                                'other-month': !isSameMonth(day, currentDate),
                                'current-week': isCurrentWeek(day),
                                today: isToday(day),
                                'month-start': isSameDay(day, monthStart),
                                'month-end': isSameDay(day, monthEnd),
                            }"
                        >
                            <div class="day-header">
                                <div
                                    class="day-number"
                                    :class="{
                                        today: isToday(day),
                                        'other-month': !isSameMonth(
                                            day,
                                            currentDate,
                                        ),
                                    }"
                                >
                                    {{ format(day, 'd') }}
                                </div>
                                <div class="day-tasks">
                                    <v-menu
                                        v-if="getTasksForDay(day).length > 0"
                                        location="top"
                                        :close-on-content-click="false"
                                    >
                                        <template v-slot:activator="{ props }">
                                            <div
                                                v-bind="props"
                                                class="task-indicator"
                                                :class="{
                                                    'has-tasks':
                                                        getTasksForDay(day)
                                                            .length > 0,
                                                }"
                                            >
                                                {{ getTasksForDay(day).length }}
                                                tasks
                                            </div>
                                        </template>
                                        <div class="task-menu-content">
                                            <v-card
                                                v-for="task in getTasksForDay(
                                                    day,
                                                )"
                                                :key="task.id"
                                                class="task-item mb-2"
                                                variant="outlined"
                                                density="compact"
                                                @click="navigateToTask(task.id)"
                                            >
                                                <v-card-item class="pa-3">
                                                    <div
                                                        class="d-flex align-center"
                                                    >
                                                        <div
                                                            class="task-time mr-3 text-body-2"
                                                        >
                                                            {{
                                                                formatTaskTime(
                                                                    task.dueAt ||
                                                                        '',
                                                                )
                                                            }}
                                                        </div>
                                                        <v-card-title
                                                            class="text-body-1 flex-grow-1"
                                                        >
                                                            {{ task.title }}
                                                        </v-card-title>
                                                    </div>
                                                    <v-card-subtitle
                                                        class="pa-0 mt-1"
                                                    >
                                                        <v-chip
                                                            :color="
                                                                getStatusColor(
                                                                    task.status,
                                                                )
                                                            "
                                                            size="small"
                                                            class="mr-1"
                                                        >
                                                            {{ task.status }}
                                                        </v-chip>
                                                        <v-chip
                                                            :color="
                                                                getPriorityColor(
                                                                    task.priority,
                                                                )
                                                            "
                                                            size="small"
                                                        >
                                                            {{ task.priority }}
                                                        </v-chip>
                                                    </v-card-subtitle>
                                                </v-card-item>
                                            </v-card>
                                        </div>
                                    </v-menu>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<style scoped>
.calendar-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.calendar-header {
    padding: 16px 24px;
    border-bottom: 1px solid
        rgba(var(--v-border-color), var(--v-border-opacity));
}

.calendar-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
}

.calendar-title {
    margin: 0;
    min-width: 120px;
    font-size: 1.25rem;
    font-weight: 500;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-navigation h2 {
    margin: 0;
    min-width: 150px;
    text-align: center;
}

.view-mode-controls {
    display: flex;
    gap: 8px;
}

.calendar-body {
    padding: 16px;
    flex-grow: 1;
    overflow-y: auto;
}

.daily-view {
    min-height: 400px;
}

.day-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.weekday-headers {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 8px;
}

.weekday-header {
    text-align: center;
    font-weight: 500;
    color: rgba(var(--v-theme-on-surface), 0.6);
    padding: 8px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    min-height: 600px;
}

.day-cell {
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-radius: 4px;
    padding: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.other-month {
    background-color: rgba(var(--v-theme-surface-variant), 0.05);
    opacity: 0.7;
}

.other-month::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        rgba(var(--v-border-color), 0.1),
        rgba(var(--v-border-color), 0.1) 5px,
        transparent 5px,
        transparent 10px
    );
    pointer-events: none;
}

.month-start {
    border-left: 2px solid rgb(var(--v-theme-primary));
}

.month-end {
    border-right: 2px solid rgb(var(--v-theme-primary));
}

.current-week {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.day-number {
    font-size: 0.9em;
    padding: 2px 4px;
    border-radius: 4px;
}

.day-number.other-month {
    color: rgba(var(--v-theme-on-surface), 0.4);
}

.day-number.today {
    background-color: rgb(var(--v-theme-primary));
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.day-tasks {
    display: flex;
    align-items: center;
}

.task-item {
    cursor: pointer;
}

.task-item:hover {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-time {
    font-family: monospace;
    color: rgba(var(--v-theme-on-surface), 0.6);
    min-width: 45px;
}

.task-indicator {
    background-color: #1976d2;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    min-width: 60px;
}

.task-indicator:hover {
    background-color: #1565c0;
}

.task-tooltip-content {
    max-width: 300px;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
}

.task-tooltip-content .task-item {
    background-color: rgb(var(--v-theme-surface));
}

.task-menu-content {
    max-width: 350px;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    background-color: rgb(var(--v-theme-surface));
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    opacity: 1;
}

.task-menu-content .task-item {
    background-color: rgb(var(--v-theme-surface));
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    opacity: 1;
}

.task-menu-content .task-item:hover {
    background-color: rgba(var(--v-theme-primary), 0.05);
}

.task-menu-content .v-card-item {
    opacity: 1;
}

.task-menu-content .v-card-title,
.task-menu-content .v-card-subtitle,
.task-menu-content .task-time {
    opacity: 1;
}
</style>
