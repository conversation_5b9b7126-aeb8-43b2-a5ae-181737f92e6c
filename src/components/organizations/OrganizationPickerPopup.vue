<template>
	<v-card
		class="lumio-card">
		<v-card-title>
			<h2 class="text-h5">Select Organization</h2>
		</v-card-title>

		<v-card-text>
			<v-select
				v-model="pickedOrg"
				:items="organizations"
				item-title="name"
				return-object
				label="Select Organization"
				hide-details
				dense>
			</v-select>
		</v-card-text>
		<v-card-actions>
			<v-btn
				class="ml-2"
				color="grey darken-1"
				@click="cancelPick">
				Close
			</v-btn>
			<v-btn
				class="ml-2"
				color="primary"
				variant="flat"
				:disabled="!pickedOrg"
				@click="pickOrg">
				Switch Organization
			</v-btn>

		</v-card-actions>
	</v-card>
</template>

<script lang="ts" setup>
import { Organization } from '@/types/Organization';
import { useAdminUserStore } from '@/stores/admin/user.store'; // Remove await here

const userStore = useAdminUserStore();
const organizations = ref<Organization[]>([]);
const pickedOrg = ref<Organization | null>(null);
const props = defineProps({
	currentOrganizationId: {
		type: String,
		required: true,
	},
	userId: {
		type: String,
		required: true,
	},
});

const emits = defineEmits(['pick-org', 'cancel-pick']);

const getUserOrganizations = async () => {
	let response = (await userStore.getUserOrganizations(props.userId)).data;
	organizations.value = response.organizations;
};

const pickOrg = () => {
	if(pickedOrg.value?.id)  emits('pick-org', pickedOrg.value?.id.toString());
}

const cancelPick = () => {
	emits('cancel-pick');
};

onMounted(async () => {
	await getUserOrganizations();
});
</script>
