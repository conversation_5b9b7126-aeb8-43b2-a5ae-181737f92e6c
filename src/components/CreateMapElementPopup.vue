<template>
  <v-dialog
    persistent
    location="top"
    v-model="dialog" width="800">
    <v-card class="lumio-card">
      <v-card-title>
        Creating NAI
        <v-btn
          icon
          flat
          @click="handleClose"
          size="small"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pa-0 border ">
        <v-select
          v-model="selectedType"
          :items="['NAI', 'TAI', 'ISR Tracks', 'Collection Assets']"
          label="Select Type"
          variant="outlined"
          density="compact"
          class="ma-4"
          hide-details
        ></v-select>
      </v-card-text >

      <v-card-text  class="pa-0" v-if="selectedType === 'NAI'">
        <CreateAOIPartialPopup
          :isTargetable="graphic.isTargetable ?? false"
          :mapId="graphic.uid"
          :operationId="operationId"
          :coordinates="graphic.coordinates"
        />
      </v-card-text>
      <v-card-text v-if="selectedType === 'TAI'">
        This is TAI
      </v-card-text>
      <v-card-text v-if="selectedType === 'ISR Tracks'">
        This is ISR Tracks
      </v-card-text>
      <v-card-text v-if="selectedType === 'Collection Assets'">
        This is Collection Assets
      </v-card-text>
      <v-card-text>
        <v-btn color="primary" @click="handleClose">Close</v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  operationId: {
    type: Number,
    required: true
  },
  graphic: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: false
  }
});

const selectedType = ref(props.type);

const emit = defineEmits(['close']);
const dialog = ref(true);



const handleClose = () => {
  emit('close');
};
</script>
