<template>
    <v-progress-linear
        v-if="isLoading"
        indeterminate
        color="primary"
        class="my-4"
    ></v-progress-linear>

    <div class="d-flex flex-wrap flex-column gap-5 justify-start" v-else>
        <v-text-field
            v-model="rfiToEdit.title"
            label="Title"
            variant="outlined"
            density="compact"
            color="primary"
            base-color="primary"
            hide-details
            class="my-5 bg-white lex-grow-0"
        >
        </v-text-field>

        <v-select
            v-model="rfiToEdit.priority"
            :items="['highest', 'high', 'medium', 'low', 'none']"
            label="Priority"
            class="mb-4"
            variant="outlined"
            density="compact"
            color="primary"
            base-color="primary"
            hide-details
        >
        </v-select>

        <v-select
            v-model="rfiToEdit.status"
            :items="['created', 'sent', 'received', 'closed']"
            label="Status"
            variant="outlined"
            density="compact"
            class="mb-4"
            color="primary"
            base-color="primary"
            hide-details
        >
        </v-select>

        <v-checkbox
            v-model="rfiToEdit.checkSource as boolean"
            label="Check Source"
            class="mb-4"
            hide-details
        >
        </v-checkbox>

        <!-- <v-text-field
            v-model="rfiToEdit.ltiovDate"
            label="LTIoV Date"
            variant="outlined"
            density="compact"
            color="primary"
            class="mb-4"
            base-color="primary"
            hide-details
        >
        </v-text-field> -->
        <label for="" class="text-grey"> LTIOV Date (UTC/Zulu) </label>

        <LumioDateTimePicker
            v-model="rfiToEdit.ltiovDate as string"
            class="mb-4"
            :minDateTime="new Date()"
            placeholder="Select Date"
        ></LumioDateTimePicker>

        <v-select
            v-model="rfiToEdit.originatorId"
            :items="originators"
            item-title="title"
            item-value="id"
            label="Originator"
            variant="outlined"
            density="compact"
            color="primary"
            base-color="primary"
            hide-details
        >
        </v-select>
    </div>
    <div class="mt-6 d-flex justify-space-between">
        <v-btn
            @click="handleUpdateRFI"
            variant="flat"
            color="primary"
            text="Update RFI"
            prepend-icon="mdi-plus"
        >
        </v-btn>
        <v-btn
            @click="emit('close-update-popup')"
            variant="text"
            size="small"
            color="primary"
            text="Cancel"
            prepend-icon="mdi-close"
        >
        </v-btn>
    </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, nextTick } from 'vue';

import { IRFISingleResponse, useAdminRFIStore } from '@/stores/admin/rfi.store';
import { IOriginatorCollectionResponse } from '@/stores/admin/originator.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { IOriginator } from '@/types/Originator.type';
import { IRFI } from '@/types/RFI.type';
import { IPagination, Priority, RFIStatus } from '@/types/Global.type';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';

const { showSnackbar } = useSnackbar();
const adminRFIStore = useAdminRFIStore();
const adminOriginatorStore = useAdminOriginatorStore();
const props = defineProps<{
    rfiId: string | number;
    operationId: string;
}>();

const emit = defineEmits(['update-rfis', 'close-update-popup']);
const isLoading = ref(true);

const rfiToEdit = ref<Omit<IRFI, 'id'>>({
    title: '',
    ltiovDate: '',
    originatorId: '',
    originatorLabel: '',
    priority: Priority.NONE,
    operationId: props.rfiId as string,
    checkSource: false,
    status: RFIStatus.CREATED,
});

const originatorPaginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
});

const originators = ref<IOriginator[]>([]);

const fetchOriginators = async () => {
    const { data, success, messages } =
        (await adminOriginatorStore.fetchOriginators(
            originatorPaginationState.value,
            {
                operationId: props.operationId,
            },
        )) as IOriginatorCollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    originators.value = data?.originators as IOriginator[];
};

async function fetchRFIById() {
    const response = await adminRFIStore.fetchRFIById(props.rfiId);

    if (response.success && response.data?.rfi) {
        // Changed from data.data.originator to data.originator
        rfiToEdit.value = {
            title: response.data.rfi.title ?? '',
            ltiovDate: response.data.rfi.ltiovDate ?? '',
            originatorId: response.data.rfi.originatorId ?? '',
            originatorLabel: response.data.rfi.originatorLabel ?? '',
            priority: response.data.rfi.priority ?? Priority.NONE,
            operationId: response.data.rfi.operationId ?? '',
            checkSource: response.data.rfi.checkSource ?? false,
            status: response.data.rfi.status ?? RFIStatus.CREATED,
        };
    } else {
        showSnackbar({
            text: 'Failed to fetch Originator',
            color: 'error',
        });
    }
    isLoading.value = false;
}

async function handleUpdateRFI() {
    try {
        const response = (await adminRFIStore.updateRFI(
            props.rfiId,
            rfiToEdit.value as IRFI,
        )) as IRFISingleResponse;
        if (response.success) {
            showSnackbar({
                text: 'RFI updated successfully',
                color: 'success',
            });
            emit('update-rfis');
            await nextTick();
            emit('close-update-popup');
        } else {
            showSnackbar({
                text: 'Failed to update RFI',
                color: 'error',
            });
        }
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'An error occurred while updating RFI',
            color: 'error',
        });
    }
}

onMounted(async () => {
    await fetchRFIById();
    await fetchOriginators();
});
</script>
