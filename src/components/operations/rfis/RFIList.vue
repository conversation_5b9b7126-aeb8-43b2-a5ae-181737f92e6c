<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4>Requests for Information (RFI)</h4>
            <v-btn
                color="primary"
                prepend-icon="mdi-plus"
                class="ma-2"
                variant="flat"
                v-if="!isAddRFIToOperationSegmentOpen"
                @click="isAddRFIToOperationSegmentOpen = true"
            >
                Add RFI to Operation
            </v-btn>
        </v-card-title>
        <v-text-field
            v-model="search"
            label="Filter RFI"
            variant="outlined"
            class="ma-4"
            @update:model-value="filterRFIs"
        ></v-text-field>
        <v-card-text>
            <v-row class="" v-if="isAddRFIToOperationSegmentOpen">
                <v-col>
                    <add-r-f-i-to-operation
                        :operation-id="currentOperationId.toString()"
                        @close-section="isAddRFIToOperationSegmentOpen = false"
                        @update-rfis="getRFIs"
                    ></add-r-f-i-to-operation>
                </v-col>
            </v-row>
            <div>
                <v-data-table-server
                    v-model:items-per-page="paginationState.perPage"
                    :headers="headers"
                    :items="rfis"
                    :items-length="paginationState.total"
                    :loading="loading"
                    item-value="name"
                    style="max-height: 600px; overflow-y: auto"
                    @update:options="getRFIs"
                    @update:page="handlePage"
                >
                    <template v-slot:item.title="{ item }">
                        {{ item.title }}
                    </template>
                    <template v-slot:item.priority="{ item }">
                        <LumioPriorityChip
                            :priority="item.priority ? item.priority : 'N/A'"
                        />
                    </template>
                    <template v-slot:item.checkSource="{ item }">
                        <v-icon v-if="item.checkSource" color="green"
                            >mdi-check</v-icon
                        >
                        <v-icon v-else>mdi-close</v-icon>
                    </template>
                    <template v-slot:item.ltiovDate="{ item }">
                        <span v-if="item.ltiovDate">
                            {{ dayjs(item.ltiovDate).format('MMM-D, YYYY') }}
                        </span>
                        <span v-else> N/A </span>
                    </template>

                    <template v-slot:item.action="{ item }">
                        <v-btn
                            variant="flat"
                            color="primary"
                            size="small"
                            @click="convertToIR(item)"
                        >
                            TO IR
                        </v-btn>
                        <v-btn
                            variant="flat"
                            color="primary"
                            size="small"
                            class="ml-2"
                            @click="showEditPopup(item)"
                        >
                            <v-icon>mdi-pencil</v-icon>
                        </v-btn>
                        <v-btn
                            variant="flat"
                            color="error"
                            size="small"
                            class="ml-2"
                            @click="handleDelete(item.id as string)"
                        >
                            <v-icon>mdi-delete</v-icon>
                        </v-btn>
                    </template>
                </v-data-table-server>
            </div>
        </v-card-text>
    </v-card>
    <v-dialog
        v-model="isEditing"
        max-width="800"
        :close-on-back="false"
        :close-on-content-click="false"
        persistent
    >
        <v-card prepend-icon="mdi-tooltip-edit-outline" title="Edit Originator">
            <v-card-text>
                <!-- <edit-r-f-i-popup
                    v-if="isEditing"
                    :rfi-id="editRFI as string"
                    @close-update-popup="isEditing = false"
                    @update-rfis="getRFIs"
                    @delete-rfi="handleDelete"
                /> -->
                <edit-r-f-i-popup
                    @close-update-popup="isEditing = false"
                    @update-rfis="getRFIs"
                    @delete-rfi="handleDelete"
                    :operation-id="currentOperationId.toString()"
                    :rfi-id="editRFI ?? ''"
                ></edit-r-f-i-popup>
            </v-card-text>
        </v-card>
    </v-dialog>

    <v-dialog v-model="isConvertingToIR" max-width="800">
        <v-card
            prepend-icon="mdi-tooltip-edit-outline"
            title="Convert to IR | Select PIR to attach IR"
        >
            <v-card-text>
                <ConvertToIRModal
                    :selected-rfi="selectedRFI"
                    @close-convert-to-ir-modal="handleCloseConvert"
                    @delete-rfi="handleDelete"
                ></ConvertToIRModal>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import ConvertToIRModal from '@/components/operations/rfis/ConvertToIRModal.vue';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref, onMounted } from 'vue';

import { DataTableHeader, IPagination } from '@/types/Global.type';
import {
    IRFICollectionResponse,
    useAdminRFIStore,
} from '@/stores/admin/rfi.store';
import { IRFI } from '@/types/RFI.type';
import { Operation } from '@/types/Operation';

const adminRIFStore = useAdminRFIStore();
const { showSnackbar } = useSnackbar();

const search = ref('');

const filterRFIs = () => {
    if (!search.value) {
        getRFIs();
    } else {
        rfis.value = rfis.value.filter((rfi) =>
            rfi.title.toLowerCase().includes(search.value.toLowerCase()),
        );
    }
};

const loading = ref(false);
const rfis = ref<IRFI[]>([]);

const isEditing = ref(false);
const isConvertingToIR = ref(false);
const editRFI = ref<string | number | null>(null);
const selectedRFI = ref<IRFI | null>(null);
const paginationState = ref<IPagination>({
    page: 1,
    perPage: 10,
    total: 0,
    pages: 0,
    sortBy: 'asc',
    orderBy: 'priority',
});
const isAddRFIToOperationSegmentOpen = ref(false);

const handlePage = (page: number) => {
    paginationState.value.page = page;
    getRFIs();
};

const handleCloseConvert = () => {
    isConvertingToIR.value = false;
};

const headers: DataTableHeader[] = [
    {
        title: 'Title',
        align: 'start',
        sortable: false,
        key: 'title',
    },
    {
        title: 'Originator',
        align: 'start',
        sortable: false,
        key: 'originatorLabel',
    },
    {
        title: 'Priority',
        align: 'start',
        sortable: false,
        key: 'priority',
    },
    {
        title: 'Check Source',
        align: 'center',
        sortable: false,
        key: 'checkSource',
    },
    {
        title: 'LTIOV',
        align: 'center',
        sortable: false,
        key: 'ltiovDate',
    },
    {
        title: 'Status',
        align: 'center',
        sortable: false,
        key: 'status',
    },
    {
        title: 'Action',
        align: 'center',
        sortable: false,
        key: 'action',
    },
];

const emits = defineEmits(['refresh-operation']);

const props = defineProps<{
    currentOperationId: string | number;
    currentOperation: Operation | null;
}>();

const showEditPopup = (item: IRFI) => {
    editRFI.value = item.id as string;
    isEditing.value = true;
};

const getRFIs = async () => {
    const { data, success, messages } = (await adminRIFStore.fetchRFIs(
        paginationState.value,
        {
            operationId: props.currentOperationId,
        },
    )) as IRFICollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    paginationState.value = data?.pagination as IPagination;
    rfis.value = data?.rfis as IRFI[];
};
const { success, messages } = (await adminRIFStore.fetchRFIs(
    paginationState.value,
    {
        operationId: props.currentOperationId,
    },
)) as IRFICollectionResponse;
if (!success) {
    showSnackbar({
        text: messages[0].message as string,
        color: 'error',
    });
}
onMounted(async () => {
    await getRFIs();
});

const handleDelete = async (id: string) => {
    const confirmed = confirm('Are you sure you want to delete this RFI?');
    if (!confirmed) return;
    try {
        loading.value = true;
        await adminRIFStore.deleteRFI(id);
        showSnackbar({
            text: 'RFI deleted successfully',
            color: 'success',
        });
        await getRFIs();
        emits('refresh-operation');
    } catch (error) {
        showSnackbar({
            text: 'Error deleting RFI',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

const convertToIR = async (item: IRFI) => {
    isConvertingToIR.value = true;
    selectedRFI.value = item;
    // const { data } = (await adminPirStore.convertRFIToIR(
    //     item.id as string,
    // )) as IPirSingleResponse;
    // if (!data?.rfis?.length) {
    //   showSnackbar({
    //     text: 'IR created successfully',
    //     color: 'success',
    //   });
    //   await getRFIs();
    // } else {
    //   showSnackbar({
    //     text: 'Error creating IR',
    //     color: 'error',
    //   });
    // }
};
</script>
