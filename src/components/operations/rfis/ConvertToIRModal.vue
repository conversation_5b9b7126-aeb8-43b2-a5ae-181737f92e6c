<template>
    <v-card>
        <v-progress-linear
            v-if="loading"
            indeterminate
            color="primary"
            class="mb-0"
        ></v-progress-linear>
        <v-card-text v-else>
            <!-- <v-checkbox
                v-for="pir in mappedPirs"
                :key="pir.id"
                :label="pir.title"
                :value="pir"
                v-model="selectedPir"
                hide-details
            ></v-checkbox> -->
            <v-combobox
                v-model="selectedPir"
                :items="mappedPirs"
                item-value="id"
                item-title="title"
                variant="outlined"
                density="compact"
            ></v-combobox>
        </v-card-text>
        <v-card-actions>
            <v-btn
                variant="flat"
                color="error"
                @click="emit('close-convert-to-ir-modal')"
                >Cancel</v-btn
            >
            <v-btn
                variant="outlined"
                :disabled="!selectedPir"
                color="success"
                @click="createAndKeep"
                >Convert to IR</v-btn
            >
            <!-- <v-btn
                variant="flat"
                color="success"
                @click="emit('close-convert-to-ir-modal')"
                >Convert to IR and delete RFI</v-btn> -->
        </v-card-actions>
    </v-card>
</template>

<script lang="ts" setup>
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
import {
    IPirCollectionResponse,
    useAdminPirStore,
} from '@/stores/admin/pir.store';
import type { IPagination } from '@/types/Global.type';
import { IPir } from '@/types/Pir';

interface MappedPir {
    id: string | undefined;
    title: string;
}

const props = defineProps<{
    selectedRfi: any;
}>();

const adminPirStore = useAdminPirStore();
const operationStore = useOperationStore();
const { currentOperationId } = storeToRefs(operationStore);
const pirs = ref<IPir[]>([]);
const selectedPir = ref<MappedPir | null>(null);
const loading = ref(false);

const fetchPirsByOperationId = async () => {
    loading.value = true;
    const { data } = (await adminPirStore.fetchPirs(paginationState.value, {
        operationId: currentOperationId.value,
    })) as IPirCollectionResponse;
    paginationState.value = data?.pagination as IPagination;
    pirs.value = data?.pirs as IPir[];
    loading.value = false;
};

const mappedPirs = computed(() => {
    return pirs.value.map((pir) => ({
        id: pir.id,
        title: pir.question,
    }));
});

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
});

const createAndKeep = async () => {
    const payload = {
        informationRequirement: props.selectedRfi.title,
        pirId: selectedPir.value?.id,
        originator: 'CIMIC Team @ ODS-2024',
        priority: 'high',
        ltiovDate: props.selectedRfi.ltiovDate,
    };

    try {
        await adminPirStore.addIrToPir(payload);
    } catch (err) {}

    emit('close-convert-to-ir-modal');
    emit('delete-rfi', props.selectedRfi.id);
};

const emit = defineEmits(['close-convert-to-ir-modal', 'delete-rfi']);

onMounted(async () => {
    await fetchPirsByOperationId();
});
</script>
