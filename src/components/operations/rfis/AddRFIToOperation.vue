<template>
    <v-card
        class="ma-3 bg-grey-lighten-4 border border-solid border-grey-lighten-4 rounded-lg overflow-visible"
    >
        <v-card-title class="d-flex justify-space-between">
            <h3>Add RFI to Operation</h3>
            <v-btn
                class="ma-2"
                variant="flat"
                icon
                density="compact"
                @click="emit('close-section')"
            >
                <v-icon>mdi-close</v-icon>
            </v-btn>
        </v-card-title>
        <v-card-text>
            <div class="d-flex flex-wrap justify-start">
                <div class="w-66 px-2">
                    <v-text-field
                        v-model="rfiToAdd.title"
                        label="Title"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                        class="my-5 bg-white flex-grow-0"
                    >
                    </v-text-field>
                </div>
                <div class="w-33 px-2">
                    <v-select
                        v-model="rfiToAdd.priority"
                        :items="['highest', 'high', 'medium', 'low', 'none']"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                        class="my-5 bg-white flex-grow-0"
                        prefix="Priority"
                        :bg-color="getPriorityColor(rfiToAdd?.priority as string)"
                    >
                    </v-select>
                </div>
                <div class="w-33 px-2">
                    <v-select
                        v-model="rfiToAdd.originatorId"
                        :items="originators"
                        item-value="id"
                        label="Originator"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                        class="my-5 bg-white flex-grow-0"
                    >
                    </v-select>
                </div>
                <div class="w-33 px-2">
                    <label for="" class="d-block text-grey">
                        LTIOV Date (UTC/Zulu)
                    </label>
                    <LumioDateTimePicker
                        v-model="rfiToAdd.ltiovDate as string"
                        class="bg-white"
                        :minDateTime="new Date()"
                        placeholder="Select Date"
                    ></LumioDateTimePicker>
                </div>
                <div class="w-33 px-2">
                    <v-checkbox
                        v-model="rfiToAdd.checkSource as boolean"
                        label="Check Source"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                        class="my-5 w-33 flex-grow-0"
                    >
                    </v-checkbox>
                </div>
            </div>
            <div class="d-flex flex-wrap justify-start">
	            <div class="w-66 px-2 pb-3">
		            <v-btn
			            @click="handleAddRFI"
			            variant="flat"
			            color="primary"
			            text="Add RFI"
			            prepend-icon="mdi-plus"
		            >
		            </v-btn>
	            </div>

            </div>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { useAdminRFIStore } from '@/stores/admin/rfi.store';
import { useSnackbar } from '@/composables/useSnackbar';
import {
    IOriginatorCollectionResponse,
    useAdminOriginatorStore,
} from '@/stores/admin/originator.store';
import { defineEmits, onMounted, ref } from 'vue';
import type { IRFI } from '@/types/RFI.type';
import { IPagination, Priority, RFIStatus } from '@/types/Global.type';
import { IOriginator } from '@/types/Originator.type';
import '@vuepic/vue-datepicker/dist/main.css';
import {getPriorityColor} from "@/composables/misc.helper";

const { showSnackbar } = useSnackbar();
const adminOriginatorStore = useAdminOriginatorStore();
const adminRfiStore = useAdminRFIStore();
const originatorPaginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
});
const originators = ref<IOriginator[]>([]);
const emit = defineEmits(['close-section', 'update-rfis']);

const props = defineProps<{
    operationId: string;
}>();

const rfiToAdd = ref<Omit<IRFI, 'id'>>({
    title: '',
    ltiovDate: '',
    originatorId: '',
    originatorLabel: '',
    priority: Priority.NONE,
    operationId: props.operationId as string,
    checkSource: false,
    status: RFIStatus.CREATED,
});

const fetchOriginators = async () => {
    const { data, success, messages } =
        (await adminOriginatorStore.fetchOriginators(
            originatorPaginationState.value,
            {
                operationId: props.operationId,
            },
        )) as IOriginatorCollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    originators.value = data?.originators as IOriginator[];
};

const handleAddRFI = async () => {
    try {
        rfiToAdd.value.originatorLabel = originators.value.find(
            (o) => o.id === rfiToAdd.value.originatorId,
        )?.title;
        rfiToAdd.value.operationId = props.operationId;
        const { messages, success } = await adminRfiStore.createRFI(
            rfiToAdd.value as IRFI,
        );
        if (success) {
            showSnackbar({
                text: 'RFI added successfully',
                color: 'success',
            });
            rfiToAdd.value = {
                title: '',
                ltiovDate: '',
                originatorId: '',
                originatorLabel: '',
                priority: Priority.NONE,
                operationId: props.operationId as string,
                checkSource: false,
                status: RFIStatus.CREATED,
            };
            emit('update-rfis');
        } else {
            showSnackbar({
                pos: 'top-center',
                text: 'Failed to add RFI',
                color: 'error',
            });
            showSnackbar({
                pos: 'top-center',
                text: messages[0].message as string,
                color: 'error',
            });
        }
        //reset pirToAdd
    } catch (error) {
        console.error(error);
    } finally {
        //loading.value = false;
    }
};

onMounted(async () => {
    await fetchOriginators();
});
</script>
