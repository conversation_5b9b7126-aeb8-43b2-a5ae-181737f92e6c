<template>
    <template v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
    </template>
    <div v-else>
        <v-data-table-server
            v-model:items-per-page="paginationState.perPage"
            :headers="headers"
            :items="aois"
            :items-length="paginationState.total"
            :loading="loading"
            item-value="name"
            height="300px"
        >
            <template v-slot:item.name="{ item }">
                {{ item.name }}
            </template>
            <template v-slot:item.location="{ item }">
              <template v-if="item && item.mapElement && item.mapElement.element && item.mapElement.element.type ===
              'Point' &&
              item.mapElement.element.coordinates.length === 2">
                {{ item.mapElement.element.coordinates[0] }}, {{ item.mapElement.element.coordinates[1] }}
              </template>
            </template>

            <template v-slot:item.action="{ item }">
                <v-btn
                    variant="flat"
                    color="primary"
                    size="small"
                    @click="
                        router.push(`/admin/operation/aois/${item.id}/edit`)
                    "
                >
                    <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn
                    variant="flat"
                    color="error"
                    size="small"
                    class="ml-2"
                    @click="handleDelete(item.id as string)"
                >
                    <v-icon>mdi-delete</v-icon>
                </v-btn>
            </template>
        </v-data-table-server>
    </div>
</template>

<script setup lang="ts">
import { DataTableHeader, IPagination } from '@/types/Global.type';
import { Aoi } from '@/types/Aoi.type';
import { onMounted } from 'vue';
import {
    IAoiCollectionResponse,
    useAdminAoiStore,
} from '@/stores/admin/aoi.store';
import { useSnackbar } from '@/composables/useSnackbar';

const loading = ref<boolean>(false);
const { showSnackbar } = useSnackbar();
const adminAoiStore = useAdminAoiStore();
const router = useRouter();

const emit = defineEmits(['aois-fetched']);

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 30,
    pages: 1,
    total: 0,
    sortBy: 'createdAt',
    orderBy: 'id',
});

const aois = ref<Aoi[]>([]);

const props = defineProps<{
    currentOperationId: string | number;
    isTargetable: boolean;
    isApproved?: boolean;
}>();

const headers: DataTableHeader[] = [
    {
        title: 'Name',
        align: 'start',
        sortable: false,
        key: 'name',
    },
    {
        title: 'Location',
        align: 'center',
        sortable: false,
        key: 'location',
    },
    {
        title: 'Action',
        align: 'center',
        sortable: false,
        key: 'action',
    },
];

watch(
    () => props.isTargetable,
    async (newVal) => {
        await getAois();
    },
);

const getAois = async () => {
    const { data, success, messages } = (await adminAoiStore.fetchAois(
        paginationState.value,
        {
            operationId: props.currentOperationId,
            isTargetable: props.isTargetable ? 'true' : 'false',
        },
        ['operation', 'requestedByUser', 'approvedByUser'],
    )) as IAoiCollectionResponse;
    if (!success) {
        showSnackbar({
            text: messages[0].message as string,
            color: 'error',
        });
    }
    paginationState.value = data?.pagination as IPagination;
    aois.value = data?.aois as Aoi[];
    emit('aois-fetched', aois.value);
};

onMounted(async () => {
    await getAois();
});

// const handleApprove = async (id: string) => {
//     try {
//         loading.value = true;
//         await adminAoiStore.approveAoi(id);
//         showSnackbar({
//             text: 'NAI approved successfully',
//             color: 'success',
//             timeout: 2000,
//         });
//         await getAois();
//     } catch (error) {
//         showSnackbar({
//             text: 'Error approving NAI',
//             color: 'error',
//             timeout: 2000,
//         });
//     } finally {
//         loading.value = false;
//     }
// };

// const handleToTAI = async (id: string) => {
//     try {
//         loading.value = true;
//         await adminAoiStore.turnIntoTai(id);
//         showSnackbar({
//             text: 'NAI moved to TAI successfully',
//             color: 'success',
//             timeout: 2000,
//         });
//     } catch (error) {
//         showSnackbar({
//             text: 'Error moving NAI to TAI',
//             color: 'error',
//             timeout: 2000,
//         });
//     } finally {
//         loading.value = false;
//     }
// };

const handleDelete = async (id: string) => {
    const confirmed = confirm('Are you sure you want to delete this NAI?');
    if (!confirmed) return;
    try {
        loading.value = true;
        await adminAoiStore.deleteAoi(id);
        showSnackbar({
            text: 'NAI deleted successfully',
            color: 'success',
        });
        await getAois();
    } catch (error) {
        showSnackbar({
            text: 'Error deleting NAI',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};
</script>
