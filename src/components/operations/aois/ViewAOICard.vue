<template>
  <div class="h-100" v-if="aoi">
      <v-card class="q-pa-none w-100">
        <v-card-title class="d-flex flex-grow-0 pa-3">
          <div>
            <v-btn
              @click="goToListNAIs"
              variant="text"
              class="mb-4 font-weight-bold">
              <v-icon>mdi-arrow-left</v-icon>
              Back to List
            </v-btn>
          </div>
          <h3>{{ aoi.name }} -
            <strong class="text-blue"> {{ aoi.isTargetable ? 'TA-'+aoi.designation : 'NA-'+aoi.designation }}</strong></h3>
        </v-card-title>
        <v-card-text>
          <div class="d-bl flex-grow-1 h-100 ma-0 pa-0">
            <esri-map
              :center-coordinates="getCenterCoordinates"
              :editing-allowed="true"
              :draw-elements="['point', 'rectangle']"
              :existing-coordinates="getExistingArea"
              :is-editing="false"
            ></esri-map>
          </div>
        </v-card-text>
      </v-card>

    </div>
</template>

<script setup lang="ts">

import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import type { Aoi } from '@/types/Aoi.type';
import type { IArea } from '@/types/Global.type'; // Remove ICoordinates as it's unused
import { useAdminAoiStore } from '@/stores/admin/aoi.store';
import { useSnackbar } from '@/composables/useSnackbar';

const router = useRouter();
const aoi = ref<Aoi | null>(null);

const { showSnackbar } = useSnackbar();

const adminAoiStore = useAdminAoiStore();

const props = defineProps<{
  aoiId: string | number
}>();


const fetchAoi = async () => {
	try {
		const response = await adminAoiStore.fetchAoiById(props.aoiId.toString());
		if (response.error) {
			showSnackbar({
				text: response.messages[0]?.message?.toString() || 'Error fetching AOI',
				color: response.messages[0]?.type?.toString() || 'error',
				pos: 'top-center',
			});
		} else if (response.success && response.data) {
			aoi.value = response.data.aoi;
		}
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: 'Error fetching AOI details',
			color: 'error',
			pos: 'top-center',
		});
	}
};

const getCenterCoordinates = computed(() => {
	return [];
});

const goToListNAIs = () => {
	router.push('/admin/operation/rcm#overlay');
};

const getExistingArea = computed(() => {
	if (!aoi.value?.mapElement?.element) return [];
	return [aoi.value.mapElement.element] as IArea[];
});


onMounted(async () => {
  await fetchAoi();
});

</script>
