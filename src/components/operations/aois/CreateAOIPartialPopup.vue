<template>
<div>
<v-row>
  <v-col cols="4" class="pa-0 px-4">
    <v-text-field
      v-model="aoi.name"
      label="Label"
      variant="outlined"
      density="compact"
      class="ma-2"
    />
  </v-col>
  <v-col class="pa-0 px-4">
    <v-checkbox
      v-model="aoi.isApproved"
      color="primary"
      label="Approved"
      variant="outlined"
      density="compact"
      class="ma-2"
    ></v-checkbox>

  </v-col>
  <v-col cols="12" class="pa-0 px-4">
    <v-text-field
      v-model="aoi.description"
      label="Description"
      variant="outlined"
      density="compact"
      class="ma-2"
    />
  </v-col>
</v-row>

  <v-row v-if="aoi && aoi.mapElement">
    <v-col cols="12">

      <v-expansion-panels>
        <v-expansion-panel title="Style">
          <v-expansion-panel-text class="my-3">
            <v-row  class="">
              <v-col cols="8" class="pa-0 px-4">
                <v-row  class="">
                  <v-col cols="6" class="">
                    <div class="ma-2">
                      <ColorPickerPopup title="Background Color"
                                        :color="validateRGBAColor(aoi.mapElement?.elementColor)"
                                        @color-selected="handleElementColorSelected">

                      </ColorPickerPopup>
                    </div>

                  </v-col>
                  <v-col cols="6" class="">
                    <div class="ma-2">
                      <ColorPickerPopup title="Border Color"
                                        :color="validateRGBAColor(aoi.mapElement?.borderColor)"
                                        @color-selected="handleBorderColorSelected"></ColorPickerPopup>
                    </div>

                  </v-col>
                </v-row>
                <v-row  class="">
                  <v-col cols="6" class="">
                    <v-select
                      variant="outlined"
                      density="compact"
                      class="mx-2"
                      hide-details
                      v-model="aoi.mapElement.borderType"
                      :items="[BorderType.SOLID, BorderType.DOTTED, BorderType.DASHED, BorderType.NONE]"
                    ></v-select>
                  </v-col>
                  <v-col cols="6" class="">
                    <v-text-field
                      v-model="aoi.mapElement.borderThickness"
                      label="Border Thickness"
                      variant="outlined"
                      density="compact"
                      class="mx-2"
                      hide-details
                      type="number"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="4" class="">
                <v-sheet
                  :elevation="2"
                  width="100%"
                  height="100%"
                  rounded
                  :style="getRowStyle"
                  class="preview w-100 h-100 d-flex justify-center align-center"
                >
                  PREVIEW
                </v-sheet>
              </v-col>
            </v-row>
          </v-expansion-panel-text>
        </v-expansion-panel>

        <v-expansion-panel
          title="Coordinates"
        >
          <v-expansion-panel-text>
            <v-table density="compact" class="transparent-table">
              <thead>
              <tr>
                <th class="text-left">
                  Point
                </th>
                <th class="text-left">Latitude</th>
                <th class="text-left">Longitude</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(coordinate, index) in coordinates" :key="index">
                <template v-if="index < (coordinates.length - 1)">
                  <td class="text-left text-sm">{{ index + 1 }}</td>
                  <td class="text-left">
                    <v-text-field
                      v-model="coordinate[0]"
                      variant="outlined"
                      density="compact"
                      class="ma-0 pa-0"

                      hide-details
                    ></v-text-field>
                  </td>
                  <td class="text-left">
                    <v-text-field
                      v-model="coordinate[1]"
                      variant="outlined"
                      density="compact"
                      class="ma-0 pa-0"
                      hide-details
                    ></v-text-field>
                  </td>
                </template>
                <template v-else>
                   <td colspan="3" class="text-center">
                      Closed Loop
                   </td>
                </template>
              </tr>
              </tbody>
            </v-table>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>


    </v-col>
  </v-row>


    <v-card-actions class="mt-4">
      <v-row class="pa-0 ma-0">
        <v-col cols="5" class="">
          <v-btn color="primary" variant="outlined" @click="cancelAOI">Cancel</v-btn>

        </v-col>
        <v-col cols="7" class="text-right">
          <v-btn color="primary" variant="elevated"  @click="createAOI">Create {{ (isTargetable) ? 'TAI' : 'NAI' }}</v-btn>
        </v-col>
      </v-row>
    </v-card-actions>

</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {Aoi} from "@/types/Aoi.type";
import { useAdminAoiStore } from '@/stores/admin/aoi.store'
import ColorPickerPopup from "@/components/operations/aois/partial/ColorPickerPopup.vue";
import { BorderType, ICoordinates, RGBAColor } from '@/types/Global.type';

import { validateRGBAColor } from '@/composables/misc.helper';

const emit = defineEmits(['item-created', 'item-cancelled']);
const adminAoiStore = useAdminAoiStore();

const typeOfAoi = ref('NAI');

const props = defineProps({
  isTargetable: {
    type: Boolean,
    required: true
  },
  mapId: {
    type: Number,
    required: true
  },
  operationId: {
    type: Number,
    required: true
  },
  coordinates: {
    //[number, number][]
    type: Array as PropType<ICoordinates[]>,
    required: true
  }
});

const aoi = ref<Aoi>({
  id: 0,
  name: '',
  description: '',
  designation: '',
  isApproved: true,
  isTargetable: props.isTargetable,
  operationId: props.operationId,
  mapElement : {
    id: 0,
    element: {
      type: 'Polygon',
      coordinates: []
    },
    elementType: 'Polygon',
    elementColor: [204, 204, 204, 89], // 35% opacity (89/255)
    borderType: BorderType.SOLID,
    borderThickness: 2,
    borderColor: [0, 0, 0, 89] // 35% opacity (89/255)
  }
});

const getRowStyle = computed(() => {
  if (!aoi.value?.mapElement) return '';

  const { elementColor, borderColor, borderThickness, borderType } = aoi.value.mapElement;

  const elementColorStyle = elementColor
    ? `background: rgba(${elementColor[0] ?? 0}, ${elementColor[1] ?? 0}, ${elementColor[2] ?? 0}, 0.35);`
    : '';

  const borderColorStyle = borderColor
    ? `border: ${borderThickness ?? 0}px ${borderType ?? 'solid'} rgba(${borderColor[0] ?? 0}, ${borderColor[1] ?? 0}, ${borderColor[2] ?? 0}, 0.35);`
    : '';

  return `${elementColorStyle} ${borderColorStyle}`.trim();
});

const createAOI = async () => {
  let createdAOI = null as Aoi | null;
  try{
    const aoiToCreate = JSON.parse(JSON.stringify(aoi.value));
    aoiToCreate.mapElement.element = {
      type: 'Polygon',
      coordinates: [props.coordinates]
    };

    // Ensure color values are integers with fixed opacity
    if (Array.isArray(aoiToCreate.mapElement.elementColor)) {
      aoiToCreate.mapElement.elementColor = aoiToCreate.mapElement.elementColor.map((value: number, index: number) => {
        return Math.round(value);
      });
    }

    if (Array.isArray(aoiToCreate.mapElement.borderColor)) {
      aoiToCreate.mapElement.borderColor = aoiToCreate.mapElement.borderColor.map((value: number, index: number) => {
        return Math.round(value);
      });
    }

    let responseFromAPI = (await adminAoiStore.createAoi(aoiToCreate)).data;
    createdAOI = responseFromAPI ? responseFromAPI.aoi : null;
  } catch (error) {

  }
  try{
    //let's fetch proper aoi from API
    if(createdAOI && createdAOI.id) {
      const responseFromAPI2 = await adminAoiStore.fetchAoiById(createdAOI.id.toString(), ['mapElement']);
      const freshAOI = responseFromAPI2.data?.aoi;
      emit('item-created', freshAOI, props.mapId);
    }
  } catch (error) {

  }

};

const cancelAOI = () => {
  emit('item-cancelled');
};

const handleElementColorSelected = (color: number[]) => {
  const newAoi = { ...aoi.value };
  newAoi.mapElement = {
    id: newAoi.mapElement?.id ?? Date.now(),
    element: newAoi.mapElement?.element ?? { type: 'Point', coordinates: [0, 0] },
    elementType: newAoi.mapElement?.elementType ?? 'Point',
    elementColor: [color[0], color[1], color[2], 89] as RGBAColor,
    borderType: newAoi.mapElement?.borderType ?? BorderType.SOLID,
    borderThickness: newAoi.mapElement?.borderThickness ?? 1,
    borderColor: newAoi.mapElement?.borderColor ?? [0, 0, 0, 1]
  };
  aoi.value = newAoi;
};

const handleBorderColorSelected = (color: number[]) => {
  const newAoi = { ...aoi.value };
  newAoi.mapElement = {
    id: newAoi.mapElement?.id ?? Date.now(),
    element: newAoi.mapElement?.element ?? { type: 'Point', coordinates: [0, 0] },
    elementType: newAoi.mapElement?.elementType ?? 'Point',
    elementColor: newAoi.mapElement?.elementColor ?? [0, 0, 0, 1],
    borderType: newAoi.mapElement?.borderType ?? BorderType.SOLID,
    borderThickness: newAoi.mapElement?.borderThickness ?? 1,
    borderColor: [color[0], color[1], color[2], 89] as RGBAColor
  };
  aoi.value = newAoi;
};

watch(typeOfAoi, () => {
  if (typeOfAoi.value === 'TAI') {
    aoi.value.isApproved = true;
    aoi.value.isTargetable = true;
  } else {
    aoi.value.isTargetable = false;
  }
});
</script>
