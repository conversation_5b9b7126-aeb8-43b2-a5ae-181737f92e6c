<template>
  <tr>
    <td class="text-center">
      <v-btn
        v-if="!aoi.isTargetable"
        variant="flat"
        color="secondary"
        size="small"
        class="ml-2"
        @click="handleToTAI(aoi.id as string)"
      >
        Target
      </v-btn>
      <div class="text-center" v-else>
                  TAI

                </div>
    </td>
    <td class="text-center">
      <v-btn
        v-if="!aoi.isApproved"
        variant="flat"
        color="primary"
        size="small"
        @click="handleApprove(aoi.id as string)"
      >
        Approve
      </v-btn>
      <v-icon
        v-else
        color="success"
        size="small"
        class="ml-2"
      >
        mdi-check
      </v-icon>
    </td>
    <td>



          [{{(aoi.isTargetable) ? 'TAI-' : 'NAI-'}}{{aoi.designation}}]&nbsp;
          <span class="text-caption">{{ aoi.description}}</span>

    </td>

    <td>
      <div class="d-flex">
<!--        <v-btn-->
<!--          variant="plain"-->
<!--          color="primary"-->
<!--          class="ml-2"-->
<!--          size="small"-->
<!--          @click="router.push(`/admin/operation/aois/edit/${aoi.id}`)"-->
<!--        >-->
<!--          <v-icon>mdi-pencil</v-icon>-->
<!--        </v-btn>-->
        <v-btn
          variant="plain"
          color="primary"
          class="ml-2"
          size="small"
          @click="handleDelete(aoi.id as string)"
        >
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </div>

    </td>
  </tr>
</template>

<script setup lang="ts">
import { Aoi } from '@/types/Aoi.type';
import {
    useAdminAoiStore,
} from '@/stores/admin/aoi.store';
import { useSnackbar } from '@/composables/useSnackbar';

const loading = ref<boolean>(false);
const { showSnackbar } = useSnackbar();
const adminAoiStore = useAdminAoiStore();

const emit = defineEmits(['update-aois']);


defineProps<{
    aoi: Aoi
}>();



const handleApprove = async (id: string) => {
    try {
        loading.value = true;
        await adminAoiStore.approveAoi(id);
        showSnackbar({
            text: 'NAI approved successfully',
            color: 'success',
            timeout: 2000,
        });
        emit('update-aois')
    } catch (error) {
        showSnackbar({
            text: 'Error approving NAI',
            color: 'error',
            timeout: 2000,
        });
    } finally {
        loading.value = false;
    }
};

const handleToTAI = async (id: string) => {
    try {
        loading.value = true;
        await adminAoiStore.toTai(id);
        showSnackbar({
            text: 'NAI moved to TAI successfully',
            color: 'success',
            timeout: 2000,
        });
      emit('update-aois')
    } catch (error) {
        showSnackbar({
            text: 'Error moving NAI to TAI',
            color: 'error',
            timeout: 2000,
        });
    } finally {
        loading.value = false;
    }
};

const handleDelete = async (id: string) => {
    const confirmed = confirm('Are you sure you want to delete this NAI?');
    if (!confirmed) return;
    try {
        loading.value = true;
        await adminAoiStore.deleteAoi(id);
        showSnackbar({
            text: 'NAI deleted successfully',
            color: 'success',
        });
      emit('update-aois')
    } catch (error) {
        showSnackbar({
            text: 'Error deleting NAI',
            color: 'error',
        });
    } finally {
        loading.value = false;
    }
};

</script>
