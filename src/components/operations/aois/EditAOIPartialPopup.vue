<template>
  <div v-if="aoi.id">
    <v-row>
      <v-col cols="4" class="pa-0 px-4">
        <v-text-field
          v-model="aoi.name"
          label="Label"
          variant="outlined"
          density="compact"
          class="ma-2"
        />
      </v-col>
      <v-col class="pa-0 px-4">
        <v-text-field
          v-model="aoi.description"
          label="Description"
          variant="outlined"
          density="compact"
          class="ma-2"
        />
      </v-col>
      <v-col cols="3" class="pa-0 px-4">
        <v-checkbox
          :disabled="aoi.isTargetable"
          v-model="aoi.isApproved"
          color="primary"
          label="Approved"
          variant="outlined"
          density="compact"
          class="ma-2"
        ></v-checkbox>
      </v-col>
    </v-row>




    <v-row v-if="aoi?.mapElement">
      <v-col cols="12" class="pa-0">
        <v-expansion-panels>
          <v-expansion-panel title="Style">
            <v-expansion-panel-text class="my-3">
              <v-row>
                <v-col cols="8" class="pa-0 px-4">
                  <v-row  class="">
                    <v-col cols="6" class="">
                      <div class="ma-2">
                        <ColorPickerPopup title="Background Color" :color="validateRGBAColor(aoi.mapElement.elementColor)"
                                          @color-selected="handleElementColorSelected"></ColorPickerPopup>
                      </div>

                    </v-col>
                    <v-col cols="6" class="">
                      <div class="ma-2">
                        <ColorPickerPopup title="Border Color" :color="validateRGBAColor(aoi.mapElement.borderColor)"
                                          @color-selected="handleBorderColorSelected"></ColorPickerPopup>
                      </div>

                    </v-col>
                  </v-row>
                  <v-row  class="">
                    <v-col cols="6" class="">
                      <v-select
                        variant="outlined"
                        density="compact"
                        class="mx-2"
                        hide-details
                        v-model="aoi.mapElement.borderType"
                        :items="['solid', 'dotted', 'dashed','none']"
                      ></v-select>
                    </v-col>
                    <v-col cols="6" class="">
                      <v-text-field
                        v-model="aoi.mapElement.borderThickness"
                        label="Border Thickness"
                        variant="outlined"
                        density="compact"
                        class="mx-2"
                        hide-details
                        type="number"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="4" class="">
                  <v-sheet
                    :elevation="2"
                    width="100%"
                    height="100%"
                    rounded
                    :style="getRowStyle"
                    class="preview w-100 h-100 d-flex justify-center align-center"
                  >
                    PREVIEW
                  </v-sheet>
                </v-col>
              </v-row>
            </v-expansion-panel-text>
          </v-expansion-panel>
          <v-expansion-panel title="Coordinates">
            <v-expansion-panel-text>
              <v-table density="compact" class="transparent-table">
                <thead>
                <tr>
                  <th class="text-left w-10" style="max-width:15px; width:15px;">
                    <v-icon icon=""></v-icon>
                  </th>
                  <th class="text-left">Latitude</th>
                  <th class="text-left">Longitude</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(coordinate, index) in (aoi.mapElement.element.coordinates[0] as [number, number][])" :key="index">
                  <td class="text-left">{{ index + 1 }}</td>
                  <td class="text-left">
                    <v-text-field
                      v-model="(coordinate as [number, number])[0]"
                      variant="outlined"
                      density="compact"
                      class="ma-0 pa-0"
                      hide-details
                    ></v-text-field>
                  </td>
                  <td class="text-left">
                    <v-text-field
                      v-model="(coordinate as [number, number])[1]"
                      variant="outlined"
                      density="compact"
                      class="ma-0 pa-0"
                      hide-details
                    ></v-text-field>
                  </td>
                </tr>

                </tbody>
              </v-table>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-col>
    </v-row>

    <v-card-actions class="mt-4">
      <v-row class="pa-0 ma-0">
        <v-col cols="5" class="">
          <v-btn color="primary" variant="outlined" @click="cancelAOI">Cancel</v-btn>
        </v-col>
        <v-col cols="7" class="text-right">
          <v-btn color="primary" variant="elevated"  @click="updateAOI">UPDATE</v-btn>
        </v-col>
      </v-row>
    </v-card-actions>

  </div>
</template>

<script setup lang="ts">
import { computed, PropType, ref } from 'vue';
import { Aoi } from '@/types/Aoi.type';
// import {convertToAPIColor, convertToRGBA} from "@/utils/esriMap.utils";
import { useAdminAoiStore } from '@/stores/admin/aoi.store';
import ColorPickerPopup from '@/components/operations/aois/partial/ColorPickerPopup.vue';
import { BorderType, RGBAColor } from '@/types/Global.type';
import { validateRGBAColor } from '@/composables/misc.helper';

const emit = defineEmits(['item-updated', 'item-cancelled']);
const adminAoiStore = useAdminAoiStore();

const props = defineProps({
  aoiId: {
    type: [Number, String] as PropType<number | string>,
    required: true
  }
});

const aoi = ref<Aoi>({
  name: '',
  description: '',
  designation: '',
  isApproved: false,
  isTargetable: false,
  operationId: null,
  mapElement : {
    id: null,
    element: {
      type: 'Polygon',
      coordinates: []
    },
    elementType: 'Polygon',
    elementColor: [204, 204, 204, 89], // 35% opacity
    borderColor: [0, 0, 0, 255], // 35% opacity
    borderType: BorderType.SOLID,
    borderThickness: 2
  }
});

const handleElementColorSelected = (color: RGBAColor) => {
  aoi.value.mapElement!.elementColor = color;
};

const handleBorderColorSelected = (color: RGBAColor) => {
  aoi.value.mapElement!.borderColor = color;
};

const getRowStyle = computed(() => {
  if (!aoi.value?.mapElement) return '';

  const { elementColor, borderColor, borderThickness, borderType } = aoi.value.mapElement;

  const elementColorStyle = elementColor
    ? `background: rgba(${elementColor[0] ?? 0}, ${elementColor[1] ?? 0}, ${elementColor[2] ?? 0}, 0.35);`
    : '';

  const borderColorStyle = borderColor
    ? `border: ${borderThickness ?? 0}px ${borderType ?? 'solid'} rgba(${borderColor[0] ?? 0}, ${borderColor[1] ?? 0}, ${borderColor[2] ?? 0}, 0.35);`
    : '';

  return `${elementColorStyle} ${borderColorStyle}`.trim();
});

const updateAOI = async () => {
  // Create a deep copy of the AOI to modify
  const aoiToUpdate = JSON.parse(JSON.stringify(aoi.value));

  // Ensure element color values are integers
  if (Array.isArray(aoiToUpdate.mapElement.elementColor)) {
    aoiToUpdate.mapElement.elementColor = aoiToUpdate.mapElement.elementColor.map((value: number, index: number) => {
      if (index === 3 && value < 1) { // alpha channel
        return Math.round(value * 255);
      }
      return Math.round(value);
    });
  }

  // Ensure border color values are integers
  if (Array.isArray(aoiToUpdate.mapElement.borderColor)) {
    aoiToUpdate.mapElement.borderColor = aoiToUpdate.mapElement.borderColor.map((value: number, index: number) => {
      if (index === 3 && value < 1) { // alpha channel
        return Math.round(value * 255);
      }
      return Math.round(value);
    });
  }

  if(!aoi.value.id) return;
  let responseFromAPI = await adminAoiStore.updateAoi(aoi.value.id.toString(), aoiToUpdate);
  const updatedAOI = responseFromAPI.data?.aoi;
  emit('item-updated', updatedAOI);
};

const cancelAOI = () => {
  emit('item-cancelled');
}



// Keep fetchAOI simple
const fetchAOI = async () => {
  const responseFromAPI = await adminAoiStore.fetchAoiById(props.aoiId.toString(), ['mapElement']);
  if (responseFromAPI.data?.aoi) {
    aoi.value = responseFromAPI.data.aoi;
  }
};

onMounted(async () => {
  await fetchAOI();
});

</script>
