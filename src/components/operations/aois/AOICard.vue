<template>
	<v-card class="lumio-card h-100"  v-if="props.operation">
		<v-card-title>
			<h4>Area of Interest</h4>
			<div>
				<v-switch
					v-model="editMode"
					color="primary"
					:label="(editMode === 'create') ? 'Editing Mode On' : 'View Mode On'"
					false-icon="mdi-lock"
					true-icon="mdi-pencil"
					value="create"
					hide-details
				></v-switch>
			</div>
		</v-card-title>
		<v-card-text class="    h-100">
			<template v-if="isDataLoading">
				<v-skeleton-loader
					class="mx-auto"
					type="card"
					width="100%">
					<template #default>
						<v-row>
							<v-col cols="7">
								<v-skeleton-loader
									class="mx-auto"
									type="card"
									width="100%">
								</v-skeleton-loader>
							</v-col>
							<v-col cols="5">
								<v-skeleton-loader
									class="mx-auto"
									type="card"
									width="100%">
								</v-skeleton-loader>
							</v-col>
						</v-row>
					</template>
				</v-skeleton-loader>
			</template>
			<template v-else>
				<div v-if="operation"  class="h-100">
					<v-row class="h-100">
						<v-col cols="8" class="pa-0 ma-0 h-100" >
							<template v-if="editMode === 'create'">
								<EsriMapEditorV2
									:map-items="parseMapItems"
									:operation="operation"
									:centerCoordinates="getCenter"
									:zoom="operation.zoom ?? 4"
									:max-height="'100%'"
									@element-created="handleElementCreated"
									@element-selected="handleElementSelected"
									@element-deleted="handleElementDeleted"
									@element-updated="handleElementUpdated"
									@zoom-updated="handleZoomUpdated"
									@center-updated="handleCenterUpdated"
								></EsriMapEditorV2>
							</template>
							<template v-else>
								<EsriMapViewer
									:map-items="parseMapItems"
									:max-height="'100%'"
									:operation="operation"
									:centerCoordinates="getCenter"
									:zoom="operation.zoom ?? 4"
								></EsriMapViewer>
							</template>
						</v-col>
						<v-col cols="4" class="pa-0 ma-0">
							<div>
								<v-card class="lumio-card border-0">
									<v-card-title class="bg-grey-lighten-4">
										<h4>TAIs & NAIs</h4>
									</v-card-title>
									<v-card-text class="py-4">
										<v-table density="compact" class="">
											<thead>
											<tr>
												<th style="width:50px;min-width:50px;">
													Type
												</th>
												<th style="width:50px;min-width:50px;" class="text-center">
													Approved
												</th>
												<th>
													Description
												</th>
												<th style="width: 75px; min-width:75px;">

												</th>
											</tr>
											</thead>
											<tbody v-if="getFilteredAoi.length > 0">
											<AOIRow
												:aoi="aoi"
												@update-aois="fetchAois"
												v-for="aoi in getFilteredAoi" :key="aoi.id"></AOIRow>
											</tbody>
										</v-table>
									</v-card-text>
									<v-card-title class="bg-grey-lighten-4">
										<h4>ISR Tracks</h4>
									</v-card-title>
									<v-card-text class="py-4">
										<v-table density="compact" class="">
											<thead>
											<tr>
												<th>
													Track #
												</th>
												<th>
													Label
												</th>
												<th class="">
													Dates (LTIOV)
												</th>
												<th  class="text-center" style="width: 15px; min-width:15px;">

												</th>
											</tr>
											</thead>
											<tbody v-if="isrTracks.length > 0">
											<tr v-for="track in isrTracks" :key="track.id">
												<td>
													ISR-{{ track.designation }}
												</td>
												<td>
													{{track.label}}
												</td>
												<td>
													<em v-if="track.commenceAt && track.concludeAt">
														{{ dayjs(track.commenceAt).format('MMM-D, YYYY  HH:mm') }}
														-
														{{ dayjs(track.concludeAt).format('MMM-D, YYYY  HH:mm') }}
													</em>
													<strong v-else>
														-
													</strong>
													<br>
													<small v-if="track.ltiovDate">
														({{
															dayjs(track.ltiovDate).format('MMM-D, YYYY  HH:mm')
														}})
													</small>
													<div v-else>
														-
													</div>

												</td>
												<td>
													<v-btn
														variant="plain"
														color="primary"
														class="ml-2"
														size="small"
														@click="deleteIsrTrack(track.id as string)"
													>
														<v-icon>mdi-delete</v-icon>
													</v-btn>
												</td>

											</tr>
											</tbody>
										</v-table>
									</v-card-text>
								</v-card>
							</div>
						</v-col>

					</v-row>
				</div>
			</template>

		</v-card-text>
	</v-card>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { onMounted, ref, computed } from 'vue';
import { Aoi } from '@/types/Aoi.type';
import { useAdminAoiStore } from '@/stores/admin/aoi.store';
import { useIsrTrackStore } from '@/stores/isr-track.store';
import { IPagination } from '@/types/Global.type';
import { IIsrTrack} from '@/types/IsrTrack.type';
import AOIRow from './AOIRow.vue';
import EsriMapEditorV2 from '@/components/maps/esri/EsriMapEditorV2.vue';
import type { SymbolItem } from '@/types/EsriMap';

import { useSnackbar } from '@/composables/useSnackbar';

import { parseMapItemsHelper } from '@/composables/misc.helper'; // Add this import

// const operationStore = useOperationStore();
const adminAoiStore = useAdminAoiStore();
const isrTrackStore = useIsrTrackStore();

//con
const snackbar = useSnackbar();
const props = defineProps<{
	operation: any;
}>();
const editMode = ref('create');
const aois = ref<Aoi[]>([]);
const isrTracks = ref<IIsrTrack[]>([]);
const isTargetable = ref(null);
const isApproved = ref(null);
// const geometryConverter = useGeometryConverter();
const currentZoom = ref(15);
// const currentCenter = ref({
//   longitude: 0,
//   latitude: 0
// });
const currentCenterCoordinates = ref([0, 0]);
const isDataLoading = ref(true);

const emits = defineEmits(['refresh-operation']);
// First define getFilteredAoi since other computed properties depend on it
const getFilteredAoi = computed(() => {
	if (!aois.value) return [];

	return aois.value.filter(aoi => {
		if (!aoi) return false;

		if (isTargetable.value !== null && isApproved.value !== null) {
			return aoi.isTargetable === isTargetable.value && aoi.isApproved === isApproved.value;
		} else if (isTargetable.value !== null) {
			return aoi.isTargetable === isTargetable.value;
		} else if (isApproved.value !== null) {
			return aoi.isApproved === isApproved.value;
		}
		return true;
	});
});

const fetchAois = async () => {
	try {
		if (!props.operation?.id) return;

		const response = await adminAoiStore.fetchAois(
			{
				page: 1,
				perPage: 50,
				sortBy: 'desc',
				orderBy: 'id',
			} as IPagination,
			{
				operationId: props.operation.id,
			}
		);
		if (response?.data?.aois) {
			aois.value = response.data.aois;
		}
	} catch (error) {
		aois.value = [];
	} finally {
		//operation?.locationCoordinates?.coordinates
		currentCenterCoordinates.value = props.operation?.locationCoordinates?.coordinates;
	}
};

const fetchRSITracks = async() => {
	try{
		const response = await isrTrackStore.fetchIsrTracks(
			{
				page: 1,
				perPage: 500,
				sortBy: 'desc',
				orderBy: 'id',
			} as IPagination,
			{
				operationId: props.operation.id,
			}
		);
		if (response?.data?.isrTracks) {
			isrTracks.value = response.data.isrTracks;
		}
	} catch (error) {
		isrTracks.value = [];
	} finally {
		//operation?.locationCoordinates?.coordinates
		currentCenterCoordinates.value = props.operation?.locationCoordinates?.coordinates;
	}
}

const getCenter = computed(() => {
	return currentCenterCoordinates.value;
});

const handleElementCreated = async (element: any, type: string) => {
	if(type === 'aoi'){
		//push to arry
		aois.value.push(element);
		emits('refresh-operation')
	} else if(type === 'isr_track'){
		//push to arry
		isrTracks.value.push(element);
	}
}



const handleZoomUpdated = (data: any) => {
	currentZoom.value = data;
}

const handleCenterUpdated = (data:any) => {
	currentCenterCoordinates.value = [data.longitude, data.latitude];
}

const handleElementSelected = (element: any) => {

}

const handleElementDeleted = async (id: string, itemType: string) => {

	if(itemType === 'aoi'){
		await deleteAoi(id);
	} else if(itemType === 'isr_track'){
		await deleteIsrTrack(id);
	}
}

const handleElementUpdated = (element: any, itemType:string) => {
	if(itemType === 'aoi'){
		const index = aois.value.findIndex(aoi => aoi.id === element.id);
		if(index !== -1){
			aois.value[index] = element;
		}
	} else if(itemType === 'isr_track'){
		const index = isrTracks.value.findIndex(track => track.id === element.id);
		if(index !== -1){
			isrTracks.value[index] = element;
		}
	}
}

const parseMapItems = computed((): SymbolItem[] => {

	return parseMapItemsHelper({
		aois: aois.value,
		isr_tracks: isrTracks.value,
	});
});

const deleteAoi = async(id: string) => {
	await adminAoiStore.deleteAoi(id);
	snackbar.showSnackbar({
		text: `Area deleted successfully`,
		color: 'success',
		timeout: 2000,
	});
	aois.value = aois.value.filter(aoi => aoi.id !== id);
	emits('refresh-operation')
}

//fetch on mount
const deleteIsrTrack = async(id: string) => {
	await isrTrackStore.deleteIsrTrack(id);
	snackbar.showSnackbar({
		text: `ISR Track deleted successfully`,
		color: 'success',
		timeout: 2000,
	});
	isrTracks.value = isrTracks.value.filter(track => track.id !== id);
	emits('refresh-operation')
}

onMounted(async () => {
	await fetchAois();
	await fetchRSITracks();
	currentCenterCoordinates.value = props.operation.locationCoordinates?.coordinates;
	isDataLoading.value = false;
});

//computed center based on current center
// const getCenter = () => {
//   return currentCenter.value;
// }

</script>
