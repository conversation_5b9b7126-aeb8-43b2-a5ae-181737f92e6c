<template>
    <v-row class="flex-nowrap h-100" no-gutters v-if="aoi">
        <v-col class="flex-grow-0 flex-shrink-0 bg-grey-lighten-4" cols="3">
            <div class="d-flex flex-column flex-fill pa-2">
                <div>
                    <v-btn
                        @click="goToListNAIs"
                        variant="text"
                        class="mb-4 font-weight-bold"
                    >
                        <v-icon>mdi-arrow-left</v-icon>
                        Back to List
                    </v-btn>
                </div>
                <v-divider class="mb-4"></v-divider>
                <v-card class="q-pa-none">
                    <v-card-text>
                        <v-text-field
                            v-model="aoi.name"
                            label="NAI Name"
                            variant="outlined"
                            density="compact"
                            color="primary"
                            base-color="primary"
                            hide-details
                            class="my-2 bg-white w-100 flex-grow-0"
                        />
                        <v-textarea
                            v-model="aoi.description"
                            label="NAI Description"
                            variant="outlined"
                            color="primary"
                            density="comfortable"
                            base-color="primary"
                            hide-details
                            class="my-2 bg-white w-100 flex-grow-0"
                            style="border: 1px solid #e0e0e0"
                        />
                    </v-card-text>
                    <v-card-actions>
                        <v-btn
                            color="primary"
                            :disabled="!isFormValid"
                            prepend-icon="mdi-plus"
                            class="ma-2"
                            variant="flat"
                            @click="updateNAI"
                        >
                            UPDATE
                        </v-btn>

                        <v-btn
                            color="primary"
                            prepend-icon="mdi-plus"
                            class="ma-2"
                            variant="text"
                            @click="goToListNAIs"
                        >
                            Cancel
                        </v-btn>
                    </v-card-actions>
                </v-card>
            </div>
        </v-col>
        <v-col
            class="flex-grow-1 flex-shrink-0"
            cols="1"
            style="min-width: 100px; max-width: 100%"
        >
            <esri-map
                :key="aoi?.id"
                :center-coordinates="
                    operation?.locationCoordinates?.coordinates
                "
                :editing-allowed="true"
                @elements-selected="handleElementsSelected"
                :draw-elements="['point', 'rectangle']"
                :existing-coordinates="getExistingArea"
                :is-editing="true"
                :zoom="5"
            ></esri-map>
        </v-col>
    </v-row>
</template>

<script setup lang="ts">
import { type Aoi } from '@/types/Aoi.type';
import { useAdminAoiStore } from '@/stores/admin/aoi.store';
import EsriMap from '@/components/EsriMap.vue';
import { type MapElementData } from '@/types/Global.type';
import { useSnackbar } from '@/composables/useSnackbar';
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
const router = useRouter();
const aoi = ref<Aoi | null>(null);
const isEditingMap = ref(false);
// const currentElement = ref<any>(null);

const { showSnackbar } = useSnackbar();

const operationStore = useOperationStore();
const { operation } = storeToRefs(operationStore);

const adminAoiStore = useAdminAoiStore();

const props = defineProps<{
    aoiId: string | number;
}>();

const isFormValid = computed(() => {
    return Boolean(
        aoi.value &&
            aoi.value.name &&
            aoi.value.designation
    );
});

const handleElementsSelected = (data: MapElementData) => {
    try {
        const { elements, centerCoordinates } = data;

        elements[0]?.locationCoordinates
            ?.coordinates as any;
        if (aoi.value && aoi.value.mapElement && aoi.value.mapElement.element) {
            aoi.value.mapElement.element = {
                type: 'Point',
                coordinates: centerCoordinates,
            };
        }

        isEditingMap.value = false;
    } catch (error) {

    }
};

const fetchAoi = async () => {
    try {
        //get id from router
        const response = await adminAoiStore.fetchAoiById(
            props.aoiId as string,
        );
        //@ts-ignore
        aoi.value = response.data.aoi;
    } catch (error) {
        console.error(error);
    }
};

// const getCenterCoordinates = computed((): number[] | undefined => {
//     const coordinates = aoi.value?.location?.coordinates;
//     return coordinates ? coordinates : [0, 0];
// });

// const getZoom = computed(() => {
//     return aoi.value?.zoom || 10;
// });

const goToListNAIs = () => {
    router.push('/admin/operation/rcm#overlay');
};

const getExistingArea = computed(() => {
    const currentArea = aoi.value?.mapElement?.element;
    return currentArea ? [currentArea] : [];
});

const updateNAI = async () => {
    const currentAoi = aoi.value;
    if (!currentAoi) return;

    try {
        const response = await adminAoiStore.updateAoi(
            props.aoiId.toString(),
            currentAoi,
        );

        if (response.error) {
            showSnackbar({
                text:
                    response.messages[0]?.message?.toString() ||
                    'An error occurred',
                color: response.messages[0]?.type?.toString() || 'error',
                pos: 'top-center',
            });
        } else if (response.success) {
            showSnackbar({
                text: 'AOI updated successfully',
                color: 'success',
                timeout: 2000,
            });
            await router.push(`/admin/operation/aois/${props.aoiId}/edit`);
        }
    } catch (error) {
        console.error('Error updating AOI:', error);
    }
};

onMounted(async () => {
    await fetchAoi();
});
</script>

<style scoped></style>
