<script setup>
import { ref, watch, onMounted } from 'vue';

const isColorPickerOpen = ref(false);

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  color: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['color-selected']);

// Convert API color array to color picker object
const convertToColorPicker = (color) => ({
  r: color[0],
  g: color[1],
  b: color[2]
});

// Initialize with props
const colorPickerColor = ref(convertToColorPicker(props.color));

// Watch for color picker changes
watch(colorPickerColor, (newColor) => {
  if (newColor && !isEqual(newColor, convertToColorPicker(props.color))) {
    emit('color-selected', [
      Math.round(newColor.r),
      Math.round(newColor.g),
      Math.round(newColor.b),
      89 // Fixed 35% opacity
    ]);
  }
}, { deep: true });

// Watch for prop changes
watch(() => props.color, (newColor) => {
  if (!isEqual(colorPickerColor.value, convertToColorPicker(newColor))) {
    colorPickerColor.value = convertToColorPicker(newColor);
  }
}, { deep: true });

// Helper function to compare color objects
function isEqual(color1, color2) {
  return color1.r === color2.r &&
    color1.g === color2.g &&
    color1.b === color2.b;
}

onMounted(() => {
  colorPickerColor.value = convertToColorPicker(props.color);
});
</script>

<template>
  <div>
    <v-btn
      variant="outlined"
      density="compact"
      color="primary"
      hide-details
      :label="title"
      @click="isColorPickerOpen = !isColorPickerOpen"
      prepend-icon="mdi-format-color-fill"
    >
      RGB({{ Math.round(colorPickerColor.r) }},
      {{ Math.round(colorPickerColor.g) }},
      {{ Math.round(colorPickerColor.b) }})
    </v-btn>
    <v-dialog
      v-model="isColorPickerOpen"
      width="400"
      persistent
    >
      <v-card class="lumio-card">
        <v-card-title class="d-flex justify-space-between align-center">
          <h3>{{ title }}</h3>
          <v-btn
            icon
            @click="isColorPickerOpen = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-color-picker
            v-model="colorPickerColor"
            mode="rgb"
            :modes="['rgb']"
            class="ma-2 mx-auto"
          ></v-color-picker>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>
