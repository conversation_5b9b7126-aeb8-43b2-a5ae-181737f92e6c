<template>
  <div>
    <v-row>
      <v-col cols="4" class="pa-0 px-4">
        <v-text-field
          v-model="isrTrack.label"
          label="Label"
          variant="outlined"
          density="compact"
        />
      </v-col>
      <v-col class="pa-0 px-4">
        <v-select
          v-model="isrTrack.status"
          density="compact"
          label="Status"
          hide-details
          variant="outlined"
          :items="['created', 'active', 'inactive', 'completed', 'on_hold']"
        ></v-select>
      </v-col>
      <v-col cols="3" class="pa-0 px-4">
        <v-select
          v-model="isrTrack.priority"
          density="compact"
          label="Priority"
          hide-details
          variant="outlined"
          :items="['highest', 'high', 'medium', 'low', 'none']"
        ></v-select>
      </v-col>
      </v-row>
      <v-row >
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
          <v-card-title>
            <h4 class="text-sm-body-2">Commence Date/Time</h4>
          </v-card-title>
          <v-card-text>

			  <LumioDateTimePicker
				  v-model="isrTrack.commenceAt"
				  :minDateTime="minDate"
				  placeholder="Commence Date/Time"
			  ></LumioDateTimePicker>

          </v-card-text>

        </v-card>
      </v-col>
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
          <v-card-title>
            <h4 class="text-sm-body-2">Conclude Date/Time</h4>
          </v-card-title>
          <v-card-text>
			  <LumioDateTimePicker
				  v-model="isrTrack.concludeAt"
				  :minDateTime="minDate"
				  placeholder="Conclude Date/Time"
			  ></LumioDateTimePicker>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
        <v-card-title>
          <h4 class="text-sm-body-2">LTIOV Date</h4>
        </v-card-title>
        <v-card-text>
			<LumioDateTimePicker
				v-model="isrTrack.ltiovDate"
				:minDateTime="minDate"
				placeholder="LTIOV Date"
			></LumioDateTimePicker>
        </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row  v-if="isrTrack && isrTrack.mapElement">
      <v-col cols="12">
        <v-expansion-panels>
          <v-expansion-panel title="Style">
            <v-expansion-panel-text class="my-3">
              <v-row  class="">
                <v-col cols="8" class="pa-0 px-4">
                  <v-row  class="">
                    <v-col cols="6" class="">
                      <div class="ma-2">
                        <ColorPickerPopup title="Color"
                                          :color="validateRGBAColor(isrTrack.mapElement?.borderColor)"
                                          @color-selected="handleBorderColorSelected"></ColorPickerPopup>
                      </div>

                    </v-col>
                  </v-row>
                  <v-row  class="">
                    <v-col cols="6" class="">
                      <v-select
                        variant="outlined"
                        density="compact"
                        class="mx-2"
                        hide-details
                        v-model="isrTrack.mapElement.borderType"
                        :items="[BorderType.SOLID, BorderType.DOTTED, BorderType.DASHED, BorderType.NONE]"
                      ></v-select>
                    </v-col>
                    <v-col cols="6" class="">
                      <v-text-field
                        v-model="isrTrack.mapElement.borderThickness"
                        label="Border Thickness"
                        variant="outlined"
                        density="compact"
                        class="mx-2"
                        hide-details
                        type="number"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="4" class="">
                  <v-sheet
                    :elevation="2"
                    width="100%"
                    height="100%"
                    rounded

                    class="preview w-100 h-100 d-flex justify-center align-center"
                  >
                    <v-divider :style="getRowStyle" class="ma-2"></v-divider>

                  </v-sheet>
                </v-col>
              </v-row>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>

      </v-col>
    </v-row>

    <v-card-actions class="mt-4">
      <v-row class="pa-0 ma-0">
        <v-col cols="5" class="">
          <v-btn color="primary" variant="outlined" @click="cancelISRTrack">Cancel</v-btn>

        </v-col>
        <v-col cols="7" class="text-right">
          <v-btn color="primary" variant="elevated"  @click="createISRTrack">Create ISR Track</v-btn>
        </v-col>
      </v-row>
    </v-card-actions>
  </div>
</template>

<script setup lang="ts">

import { useIsrTrackStore } from '@/stores/isr-track.store';
import { BorderType, ICoordinates, RGBAColor } from '@/types/Global.type';
import { validateRGBAColor } from '@/composables/misc.helper';
import ColorPickerPopup from '@/components/operations/aois/partial/ColorPickerPopup.vue';
import { computed } from 'vue';
import { IIsrTrack } from '@/types/IsrTrack.type';

const isrTrackStore = useIsrTrackStore();
const emit = defineEmits(['item-created', 'item-cancelled']);

const minDate = computed(() => {
	return new Date();
});



const props = defineProps({
  mapId: {
    type: Number,
    required: true
  },
  operationId: {
    type: Number,
    required: true
  },
  coordinates: {
    //[number, number][]
    type: Array as PropType<ICoordinates[]>,
    required: true
  }
});

const isrTrack = ref<any>({
  id: null,
  label: 'ISR-',
  status: 'active',
  priority: 'medium',
  commenceAt: null,
  concludeAt: null,
  ltiovDate: null,
  mapElement : {
    id: 0,
    element: {
      type: 'LineString',
      coordinates: []
    },
    elementType: 'LineString',
    elementColor: [10, 10, 10, 255], //
    borderType: BorderType.SOLID,
    borderThickness: 2,
    borderColor: [0, 0, 0, 255] //
  }
});

const getRowStyle = computed(() => {
  if (!isrTrack.value?.mapElement) return '';

  const {  borderColor, borderThickness, borderType } = isrTrack.value.mapElement;

  const borderColorStyle = borderColor
    ?
    `opacity:1; border: ${borderThickness ?? 0}px ${borderType ?? 'solid'} rgba(${borderColor[0] ?? 0}, ${borderColor[1] ?? 0}, ${borderColor[2] ?? 0}, 1);`
    : '';

  return `${borderColorStyle}`.trim();
});

const handleBorderColorSelected = (color: number[]) => {
  const newIsrTrack = { ...isrTrack.value };
  newIsrTrack.mapElement = {
    id: newIsrTrack.mapElement?.id ?? Date.now(),
    element: newIsrTrack.mapElement?.element ?? { type: 'LineString', coordinates: [0, 0] },
    elementType: 'LineString',
    elementColor: newIsrTrack.mapElement?.elementColor ?? [0, 0, 0, 1],
    borderType: newIsrTrack.mapElement?.borderType ?? BorderType.SOLID,
    borderThickness: newIsrTrack.mapElement?.borderThickness ?? 1,
    borderColor: [color[0], color[1], color[2], 255] as RGBAColor
  };
  isrTrack.value = newIsrTrack;
};

const createISRTrack = async () => {
  let createdISRTrack = null as IIsrTrack | null;
  try {
    const isrTrackToCreate = JSON.parse(JSON.stringify(isrTrack.value));

    isrTrackToCreate.mapElement.element = {
      type: 'LineString',
      coordinates: props.coordinates
    };
    isrTrackToCreate.elementType = 'LineString';
    isrTrackToCreate.operationId = props.operationId;

    // Ensure color values are integers with fixed opacity
    if (Array.isArray(isrTrackToCreate.mapElement.elementColor)) {
      isrTrackToCreate.mapElement.elementColor = isrTrackToCreate.mapElement.elementColor.map((value: number, index: number) => {
        return Math.round(value);
      });
    }

    if (Array.isArray(isrTrackToCreate.mapElement.borderColor)) {
      isrTrackToCreate.mapElement.borderColor = isrTrackToCreate.mapElement.borderColor.map((value: number, index: number) => {
        return Math.round(value);
      });
    }

    let responseFromAPI = (await isrTrackStore.createIsrTrack(isrTrackToCreate)).data;
    if(responseFromAPI?.isrTrack){
      let fetchFreshResponse = (await isrTrackStore.fetchIsrTrackById(String(responseFromAPI.isrTrack.id))).data;
      if (fetchFreshResponse?.isrTrack) {
        createdISRTrack = fetchFreshResponse.isrTrack;
        emit('item-created', createdISRTrack, props.mapId);
      }
    }

  } catch (error) {
  }
}

const cancelISRTrack = () => {
  isrTrack.value = null;
  emit('item-cancelled');
};

</script>
