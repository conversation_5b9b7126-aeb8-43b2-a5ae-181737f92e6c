<template>
  <div>
    <v-row>
      <v-col cols="4" class="pa-0 px-4">
        <v-text-field
          v-model="isrTrack.label"
          label="Label"
          variant="outlined"
          density="compact"
        />
      </v-col>
      <v-col class="pa-0 px-4">
        <v-select
          v-model="isrTrack.status"
          density="compact"
          label="Status"
          hide-details
          variant="outlined"
          :items="['created', 'active', 'inactive', 'completed', 'on_hold']"
        ></v-select>
      </v-col>
      <v-col cols="3" class="pa-0 px-4">
        <v-select
          v-model="isrTrack.priority"
          density="compact"
          label="Priority"
          hide-details
          variant="outlined"
          :items="['highest', 'high', 'medium', 'low', 'none']"
        ></v-select>
      </v-col>
    </v-row>
    <v-row >
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
          <v-card-title>
            <h4 class="text-sm-body-2">Commence Date/Time</h4>
          </v-card-title>
          <v-card-text>

			  <LumioDateTimePicker
				  v-model="isrTrack.commenceAt"
				  :minDateTime="minDate"
				  placeholder="Commence Date/Time"
			  ></LumioDateTimePicker>

          </v-card-text>

        </v-card>
      </v-col>
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
          <v-card-title>
            <h4 class="text-sm-body-2">Conclude Date/Time</h4>
          </v-card-title>
          <v-card-text>
			  <LumioDateTimePicker
				  v-model="isrTrack.concludeAt"
				  :minDateTime="minDate"
				  placeholder="Conclude Date/Time"
			  ></LumioDateTimePicker>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="4" class="pa-0 px-4">
        <v-card class="lumio-card">
          <v-card-title>
            <h4 class="text-sm-body-2">LTIOV Date</h4>
          </v-card-title>
          <v-card-text>
			  <LumioDateTimePicker
				  v-model="isrTrack.ltiovDate"
				  :minDateTime="minDate"
				  placeholder="LTIOV Date"
			  ></LumioDateTimePicker>
          </v-card-text>
        </v-card>
      </v-col>

    </v-row>
      <v-row  v-if="isrTrack && isrTrack.mapElement">
        <v-col cols="12">
          <v-expansion-panels >
            <v-expansion-panel title="Style">
              <v-expansion-panel-text class="my-3">
                <v-row>
                  <v-col cols="8" class="pa-0 px-4">
                    <v-row  class="">
                      <v-col cols="6" class="">
                        <div class="ma-2">
                          <ColorPickerPopup title="Color"
                                            :color="validateRGBAColor(isrTrack.mapElement?.borderColor)"
                                            @color-selected="handleBorderColorSelected"></ColorPickerPopup>
                        </div>

                      </v-col>
                    </v-row>
                    <v-row  class="">
                      <v-col cols="6" class="">
                        <v-select
                          variant="outlined"
                          density="compact"
                          class="mx-2"
                          hide-details
                          v-model="isrTrack.mapElement.borderType"
                          :items="[BorderType.SOLID, BorderType.DOTTED, BorderType.DASHED, BorderType.NONE]"
                        ></v-select>
                      </v-col>
                      <v-col cols="6" class="">
                        <v-text-field
                          v-model="isrTrack.mapElement.borderThickness"
                          label="Border Thickness"
                          variant="outlined"
                          density="compact"
                          class="mx-2"
                          hide-details
                          type="number"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="4" class="">
                    <v-sheet
                      :elevation="2"
                      width="100%"
                      height="100%"
                      rounded
                      class="preview w-100 h-100 d-flex justify-center align-center"
                    >
                      <v-divider :style="getRowStyle" class="ma-2"></v-divider>
                    </v-sheet>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-col>
      </v-row>




    <v-card-actions class="mt-4">
      <v-row class="pa-0 ma-0">
        <v-col cols="5" class="">
          <v-btn color="primary" variant="outlined" @click="cancelISRTrack">Cancel</v-btn>
        </v-col>
        <v-col cols="7" class="text-right">
          <v-btn color="primary" variant="elevated" @click="updateISRTrack">Update ISR Track</v-btn>
        </v-col>
      </v-row>
    </v-card-actions>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useIsrTrackStore } from '@/stores/isr-track.store';
import { BorderType, RGBAColor } from '@/types/Global.type';
import { validateRGBAColor } from '@/composables/misc.helper';
import ColorPickerPopup from '@/components/operations/aois/partial/ColorPickerPopup.vue';


const isrTrackStore = useIsrTrackStore();
const emit = defineEmits(['item-updated', 'item-cancelled']);

const props = defineProps({

  isrTrackId: {
    type: String,
    required: true
  }
});

const isrTrack = ref<any>({
  id: null,
  label: null,
  status: null,
  priority: null,
  commenceAt: null,
  concludeAt: null,
  ltiovDate: null,
  mapElement: {
    id: 0,
    element: {
      type: 'LineString',
      coordinates: []
    },
    elementType: 'LineString',
    elementColor: [10, 10, 10, 255],
    borderType: BorderType.SOLID,
    borderThickness: 2,
    borderColor: [0, 0, 0, 255]
  }
});

onMounted(async () => {
  if (props.isrTrackId) {
    try {
      const response = await isrTrackStore.fetchIsrTrackById(props.isrTrackId);
      if (response && response.data && response.data.isrTrack) {

		  //convert dates to proper Date()
		  if( response.data.isrTrack.commenceAt)
		  response.data.isrTrack.commenceAt = new Date(response.data.isrTrack.commenceAt);
		  if( response.data.isrTrack.concludeAt)
		  response.data.isrTrack.concludeAt = new Date(response.data.isrTrack.concludeAt);
		  if( response.data.isrTrack.ltiovDate)
		  response.data.isrTrack.ltiovDate = new Date(response.data.isrTrack.ltiovDate);

        isrTrack.value = response.data.isrTrack;
      }


    } catch (error) {
      console.error('Error fetching ISR track:', error);
    }
  }
})

const getRowStyle = computed(() => {
  if (!isrTrack.value?.mapElement) return '';

  const { borderColor, borderThickness, borderType } = isrTrack.value.mapElement;

  const borderColorStyle = borderColor
    ?
    `opacity: 1; border: ${borderThickness ?? 0}px ${borderType ?? 'solid'} rgba(${borderColor[0] ?? 0}, ${borderColor[1] ?? 0}, ${borderColor[2] ?? 0}, 1);`
    : '';

  return `${borderColorStyle}`.trim();
});

const handleBorderColorSelected = (color: number[]) => {
  const newIsrTrack = { ...isrTrack.value };
  newIsrTrack.mapElement = {
    ...newIsrTrack.mapElement,
    borderColor: [color[0], color[1], color[2], 255] as RGBAColor
  };
  isrTrack.value = newIsrTrack;
};

// const disabledDates = computed(() => {
//   const today = new Date();
//   return (date: Date) => date < today;
// });

const updateISRTrack = async () => {
  try {
    const updatedTrack = {
      label: isrTrack.value.label,
      status: isrTrack.value.status,
      priority: isrTrack.value.priority,
      commenceAt: isrTrack.value.commenceAt,
      concludeAt: isrTrack.value.concludeAt,
      ltiovDate: isrTrack.value.ltiovDate,
      mapElement: {
        element: {
          type: 'LineString',
          coordinates: Array.isArray(isrTrack.value.mapElement.element.coordinates[0][0])
            ? isrTrack.value.mapElement.element.coordinates[0]
            : isrTrack.value.mapElement.element.coordinates
        },
        elementType: 'LineString',
        elementColor: isrTrack.value.mapElement.elementColor,
        borderType: isrTrack.value.mapElement.borderType,
        borderThickness: isrTrack.value.mapElement.borderThickness,
        borderColor: isrTrack.value.mapElement.borderColor
      }
    };

    const response = await isrTrackStore.updateIsrTrack(props.isrTrackId, updatedTrack);

    if (response && response.data && response.data.isrTrack) {
      emit('item-updated', response.data.isrTrack, isrTrack.value.uid);
    }
  } catch (error) {
    console.error('Error updating ISR track:', error);
  }
}

const cancelISRTrack = () => {
  emit('item-cancelled');
};

const minDate = computed(() => {
  return new Date();
});
</script>
