<script setup>
import { ref, watch, onMounted } from 'vue';
import {getContrastingTextColor} from "@/composables/misc.helper";

const isColorPickerOpen = ref(false);

const props = defineProps({
	title: {
		type: String,
		required: true
	},
	color: {
		type: String,
		required: true,
	},
});

const emit = defineEmits(['color-selected']);
const originalColor = props.color;

const pickColor = (pickedColor) => {
	console.log("PICK COLOR SET", pickedColor);
	emit('color-selected', pickedColor);
	colorPickerColor.value = pickedColor;
	isColorPickerOpen.value = false;
}

// Initialize with props
const colorPickerColor = ref();

const cancelColorPick = () => {
	colorPickerColor.value = originalColor;
	isColorPickerOpen.value = false;
}

//computed to call get contrasting color
const getTextColor = computed(() => {
	if(!colorPickerColor.value) {
		return '#000';
	}
	return getContrastingTextColor(colorPickerColor.value);
});

onMounted(() => {
	colorPickerColor.value = props.color;
});
</script>

<template>
	<div>
		<v-btn
			variant="flat"
			density="compact"
			:style="`background-color:${colorPickerColor}; color:${getTextColor};`"
			:label="title"
			@click="isColorPickerOpen = !isColorPickerOpen"

		>
			<template v-slot:prepend>
				<v-icon>mdi-palette</v-icon>
				Change
			</template>
		</v-btn>
		<v-dialog
			v-model="isColorPickerOpen"
			width="400"
			persistent
		>
			<LumioFriendlyColorPicker
				:color="color"
				@color-selected="pickColor"
				@cancel-color-pick="cancelColorPick"
			></LumioFriendlyColorPicker>
		</v-dialog>
	</div>
</template>
