<template>
	<v-card
		class="ma-3 bg-grey-lighten-4 border border-solid border-grey-lighten-4 rounded-lg"
	>
		<v-card-title class="d-flex justify-space-between">
			<h3>Add Platform to Operation</h3>
			<v-btn
				class="ma-2"
				variant="flat"
				icon
				density="compact"
				@click="emit('close-section')"
			>
				<v-icon>mdi-close</v-icon>
			</v-btn>
		</v-card-title>
		<v-card-text class="px-4">
			<div class="d-flex ga-3">
				<v-text-field
					v-model="searchTerm"
					variant="outlined"
					density="compact"
					color="primary"
					base-color="primary"
					hide-details
					class="my-5 bg-white w-100"
					clearable
					@keydown.enter="searchPlatforms"
				/>
				<v-select
					v-model="countryCodeSearchTerm"
					:items="formattedCountries"
					item-title="display"
					item-value="code"
					label="Country"
					variant="outlined"
					density="compact"
					color="primary"
					base-color="primary"
					hide-details
					clearable
					class="my-5 bg-white w-66"
				>
					<template v-slot:selection="{ item }">
						<div class="d-flex align-center">
							<span :class="`fi fi-${(item.raw?.code) ? item.raw.code.toLowerCase() : ''} mr-2`"></span>
							<span>{{ item.raw.display }}</span>
						</div>
					</template>
					<template v-slot:item="{ item, props }">
						<v-list-item v-bind="props">
							<template v-slot:prepend>
								<span :class="`fi fi-${item.raw.code.toLowerCase()} mr-1`"></span>
							</template>
						</v-list-item>
					</template>
				</v-select>
				<v-btn
					@click="searchPlatforms"
					class="my-5 px-4"
					variant="flat"
					color="primary"
					size="lg"
					density="default"
					text="Search"
					prepend-icon="mdi-magnify"
				>
				</v-btn>
				<v-btn
					@click="clearSearch"
					class="my-5 px-4"
					variant="text"
					color="primary"
					size="lg"
					density="default"
					text="Clear"
					prepend-icon="mdi-delete"
				>
				</v-btn>
			</div>

			<!-- View toggle switch -->
			<div class="d-flex justify-end mb-4" v-if="platforms.length">
				<v-btn-toggle
					v-model="viewMode"
					color="primary"
					density="compact"
					mandatory
				>
					<v-btn value="table">
						<v-icon>mdi-table</v-icon>
					</v-btn>
					<v-btn value="card">
						<v-icon>mdi-view-grid</v-icon>
					</v-btn>
				</v-btn-toggle>
			</div>

			<!-- Card View -->
			<v-row v-if="platforms.length && viewMode === 'card'">
				<add-platform-to-operation-search-platform-row
					v-for="platform in platforms"
					:key="platform.id"
					:platform="platform"
					:current-assets="props.existingAssets as Asset[]"
					@assign-platform-to-operation="assignPlatformToOperation"
					@remove-platform-from-operation="removePlatformFromOperation"
				/>
			</v-row>

			<!-- Table View -->
			<v-table v-if="platforms.length && viewMode === 'table'" class="bg-grey-lighten-5 mb-5 rounded">
				<thead>
				<tr>
					<th>Type</th>
					<th>Name</th>
					<th>Country</th>
					<th>Designation</th>
					<th>Combat Radius</th>
					<th>Actions</th>
				</tr>
				</thead>
				<tbody>
				<tr v-for="platform in platforms" :key="platform.id">
					<td>
						<v-chip :color="getPlatformTypeColor(platform.type)" size="small" label text-color="white">
							{{ platform.type.toUpperCase() }}
						</v-chip>
					</td>
					<td>{{ platform.name }}</td>
					<td>
						<span :class="`fi fi-${platform.countryIsoCode.toLowerCase()} mr-2`"></span>
						{{ getCountryName(platform.countryIsoCode) }}
					</td>
					<td>{{ platform.designation || 'N/A' }}</td>
					<td>{{ platform.combatRadius || 'N/A' }}</td>
					<td>
						<div v-if="assetIsAlreadyAssigned(platform.id.toString())">
							<div class="text-green">
								Already Assigned: {{ getAssetCallSign(platform.id.toString()) }}
							</div>
							<v-btn
								color="red"
								size="small"
								variant="text"
								density="compact"
								@click="removePlatformFromOperation(platform.id.toString())"
							>
								Remove
							</v-btn>
						</div>
						<div v-else class="d-flex gap-2 align-center">
							<v-text-field
								v-model="platformCallSigns[platform.id.toString()]"
								label="Callsign"
								variant="outlined"
								density="compact"
								hide-details
								class="flex-grow-1"
							/>
							<v-btn
								color="primary"

								variant="flat"
								@click="assignPlatformToOperation(
                                        platform.id.toString(),
                                        platformCallSigns[platform.id.toString()] || '',
                                        'pending_approval'
                                    )"
							>
								Assign
							</v-btn>
						</div>
					</td>
				</tr>
				</tbody>
			</v-table>

			<div
				class="text-center py-4"
				v-if="!platforms.length && searchPerformed"
			>
				<v-icon size="100">mdi-magnify</v-icon>
				<h3>No platforms found for the search criteria <span class="text-secondary bg-primary pa-2">{{lastSearchString
					}}</span> </h3>
			</div>
		</v-card-text>
	</v-card>
</template>

<script setup lang="ts">
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminAssetStore } from '@/stores/admin/asset.store';
import { useAdminPlatformStore } from '@/stores/admin/platform.store';
import { ref, defineEmits, computed } from 'vue';
import { Platform } from '@/types/Platform';
import AddPlatformToOperationSearchPlatformRow from '@/components/operations/assets/AddPlatformToOperationSearchPlatformRow.vue';
import { Asset } from '@/types/Asset';
import { useNatoCountries } from '@/composables/useNatoCounries';

const platforms = ref<Platform[]>([]);
const adminAssetStore = useAdminAssetStore();
const adminPlatformStore = useAdminPlatformStore();
const emit = defineEmits(['close-section', 'update-operation', 'remove-platform-from-operation']);
const { showSnackbar } = useSnackbar();
const { natoCountries } = useNatoCountries();
const searchPerformed = ref<boolean>(false);
const viewMode = ref<string>('table'); // Default to table view
const platformCallSigns = ref<Record<string, string>>({});
const lastSearchString = ref<string>('');
const props = defineProps<{
	existingAssets: Asset[];
	operationId: string;
}>();

const searchTerm = ref<string>('');
const countryCodeSearchTerm = ref<string>('');

// Format countries for the dropdown with flags
const formattedCountries = computed(() => {
	return natoCountries.map(country => ({
		code: country.code,
		name: country.name,
		display: `${country.code} - ${country.name}`
	}));
});

const assignPlatformToOperation = async (
	platformId: string,
	callSign: string,
	status: string = 'active',
	assetDetails: any = {},
) => {
	if (!callSign) {
		showSnackbar({
			text: 'Please enter a platform callsign',
			color: 'error',
		});
		return;
	}

	try {
		let results = await adminAssetStore.createAsset(
			platformId,
			props.operationId,
			callSign,
			status,
			assetDetails,
		);
		//if error show snackbar errors
		if (!results.success) {
			showSnackbar({
				text: 'Error happened',
				color: 'error',
			});
			return;
		}
		await sendUpdateEmit();
		showSnackbar({
			text: 'Platform Assigned to Operation as Asset',
			color: 'success',
		});
		// Clear the call sign after successful assignment
		platformCallSigns.value[platformId] = '';
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: 'Failed to assign platform',
			color: 'error',
		});
	}
};

const removePlatformFromOperation = async(platformId: string) => {
	emit('remove-platform-from-operation', platformId);
}

async function searchPlatforms() {
	try {
		lastSearchString.value = searchTerm.value;
		searchPerformed.value = true;
		const response = await adminPlatformStore.searchPlatforms(
			searchTerm.value,
			countryCodeSearchTerm.value,
		);

		// Filter out platforms that are already assigned to this operation
		const existingPlatformIds = props.existingAssets.map(asset =>
			asset.platformId.toString()
		);

		platforms.value = response.data.platforms.filter((platform: Platform) =>
			!existingPlatformIds.includes(platform.id.toString())
		);

		// Initialize call signs for each platform
		platforms.value.forEach(platform => {
			if (!platformCallSigns.value[platform.id.toString()]) {
				platformCallSigns.value[platform.id.toString()] = '';
			}
		});
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: 'Failed to search platforms',
			color: 'error',
		});
	}
}

async function clearSearch() {
	searchTerm.value = '';
	countryCodeSearchTerm.value = '';
	platforms.value = [];
	searchPerformed.value = false;
	platformCallSigns.value = {};
}

async function sendUpdateEmit() {
	emit('update-operation', props.operationId);
	return;
}

// Helper functions for table view
function getPlatformTypeColor(type: string) {
	const colors: {[key: string]: string} = {
		space: 'purple',
		air: 'blue',
		land: 'brown',
		sea: 'cyan',
		hybrid: 'orange',
		other: 'grey',
	};
	return colors[type] || 'grey';
}

function getCountryName(code: string): string {
	const country = natoCountries.find(c => c.code.toLowerCase() === code.toLowerCase());
	return country?.name || code;
}

function assetIsAlreadyAssigned(platformId: string): boolean {
	return props.existingAssets.some(a => a.platformId.toString() === platformId);
}

function getAssetCallSign(platformId: string): string {
	const asset = props.existingAssets.find(a => a.platformId.toString() === platformId);
	return asset?.callSign || '';
}
</script>

<style scoped>
.v-table {
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
</style>
