<template>
	<v-col cols="4">
		<v-card
			:class="`lumio-card`"
		>
				<v-card-title :class="`bg-${getPlatformTypeColor(platform.type)}-lighten-5 ` ">

            <h5>
              [<span :class="`text-${getPlatformTypeColor(platform.type)}`">{{ platform.type.toUpperCase()
                }}</span>]
              {{ platform.name }}
            </h5>

          <div>
            <span :class="`fi fi-${platform.countryIsoCode.toLowerCase()}`"></span>
          </div>

				</v-card-title>
				<v-card-text class="pa-3">
          <p>
              Designation: <strong>{{ platform.designation }}</strong>
          </p>
          <p>
            Combat Radius: <strong>{{ platform.combatRadius || 'N/A' }}</strong>
          </p>
          <p  v-if="platform.aliases?.length">
             Aliases: <strong>{{platform.aliases.join(', ') }}</strong>
          </p>
        </v-card-text>
      <v-divider class="mx-4 mb-1"></v-divider>

			<template v-if="assetIsAlreadyAssigned()">
				<v-card-text class="pa-4 text-center">
					<h3 class="text-green w-100"> Asset Already Assigned</h3>
          <p>Callsign: <strong>{{currentPAsset?.callSign}}</strong></p>
				</v-card-text>
        <v-card-actions>
          <v-btn
            class="text-none btn btn-sm"
            color="red"
            text="REMOVE ASSET"
            variant="flat"
            block
            slim
            @click="removePlatformFromOperation()"
          >
            <v-icon>mdi-plus</v-icon> Remove Asset
          </v-btn>
        </v-card-actions>
			</template>
			<template v-else>
        <v-card-text class="pa-4">
          <h3> Assign Platform</h3>
          <v-chip
            class="ma-2"
            color="primary"
            label
          >
            <v-icon icon="mdi-close" start></v-icon>
            {{ platform.quantity || 1 }} Qty
          </v-chip>
          <v-text-field
            v-model="platformCallSign"
            label="Callsign"
            variant="outlined"
            density="compact"
            :base-color="(callSignErorr) ? 'red' : ''"
          >
            <template v-slot:details v-if="callSignErorr" class="text-center">
              <span class="text-red">Callsign is required</span>
            </template>
          </v-text-field>
          <v-select
            v-model="assetStatus"
            density="compact"
            :items="['active', 'inactive', 'engaged', 'requested', 'pending_approval', 'withdrawn', 'cancelled', 'rejected']"
            label="Asset Status"
            variant="outlined"
            :rules="[(v) => !!v || 'Status is required']"
          />
	        <AssetColorPickerPopup @color-selected="colorSelected" color="#FF0033" title="Asset Color"></AssetColorPickerPopup>
        </v-card-text>
				<v-card-actions>
				<v-btn
					class="text-none btn btn-sm"
					color="primary"
					text="Edit"
					variant="flat"
					block
					slim
					@click="assignPlatformToOperation()"
				>
					<v-icon>mdi-plus</v-icon> Assign As Asset
				</v-btn>
			</v-card-actions>
			</template>
		</v-card>
	</v-col>

</template>

<script setup lang="ts">
import { ref} from 'vue';
import { Platform } from '@/types/Platform';
import { Asset } from '@/types/Asset';
import { useSnackbar} from '@/composables/useSnackbar';

const platformCallSign = ref<string>('');
const assetStatus = ref<string>('pending_approval');
const callSignErorr = ref<boolean>(false);
const currentPAsset = ref();
const snackbar = useSnackbar();
const props = defineProps<{
	platform: Platform;
	currentAssets: Asset[];
}>();

//(platformId: string, title:string, status:string = 'pending_approval', assetDetails: any = {}) => {
const emit = defineEmits(['assign-platform-to-operation', 'remove-platform-from-operation']);
// const emit = defineEmits<{
// 	'assign-platform-to-operation': [platformId: string|number, title: string, status: string, assetDetails: any];
// }>();

const assignPlatformToOperation = () => {
  if(!platformCallSign.value) {
    snackbar.showSnackbar({
      color: 'error',
      text: 'Please enter a platform callsign'
    });
    callSignErorr.value = true;
    return;
  }
	emit('assign-platform-to-operation', props.platform.id.toString(), platformCallSign.value, assetStatus.value);
};

const removePlatformFromOperation = () => {
  emit('remove-platform-from-operation', props.platform.id.toString());
}

onMounted(() => {
	platformCallSign.value = ''
  const currentPlatformId = props.platform?.id.toString() || '';
  currentPAsset.value = props.currentAssets.findLast(a => a.platformId.toString() === currentPlatformId);
});


const assetIsAlreadyAssigned = () => {
	const currentPlatformId = props.platform?.id.toString() || '';
	return props.currentAssets.some(a => a.platformId.toString() === currentPlatformId);
}

const colorSelected = (color: string) => {
	//@ts-ignore
	props.currentAssets.color = color;
}

const getPlatformTypeColor = (type: string) => {
	const colors:{[key: string]: string} = {
		space: 'purple',
		air: 'blue',
		land: 'brown',
		sea: 'cyan',
		hybrid: 'orange',
		other: 'grey',
	};
	return colors[type] as string;
};

</script>
