<template>
	<tr v-if="!removeSelf && thisPlatform">
    <td>
		{{thisPlatform.designation}}
    </td>
    <td>
	    <LumioAssetTypeChip :asset-type="thisPlatform.type" :full-width="false" />
		{{thisPlatform.name}}
    </td>
		<td>
			<div v-if="isChangingAssetStatus" class="d-flex align-center">
				<v-select
					v-model="asset.status"
					:items="['pending_approval', 'active', 'inactive', 'engaged', 'requested', 'withdrawn', 'cancelled', 'rejected']"
					label="Set Status"
					variant="outlined"
					density="compact"
					color="primary"
					base-color="primary"
					hide-details
					class="my-5 bg-white w-100 h-100"
					direction="horizontal"
					:menu-props="{ maxHeight: 'none' }"
				></v-select>

				<v-btn
					@click="changeAssetStatus()"
					variant="text"
					color="primary"
					width="25"
					max-width="25"
					class="ml-2 px-2 w-25"
				>
					<v-icon>mdi-check</v-icon>
				</v-btn>
				<v-btn
					variant="text"
					class="ml-3"
					color="primary"
					density="default"
					icon="mdi-close"
					@click="isChangingAssetStatus = false"
				>
				</v-btn>
			</div>
			<template v-else>
				<LumioStatusChip @click="isChangingAssetStatus = true" :status="asset.status" />
			</template>
		</td>
    <td class="text-center">
      {{thisPlatform.hasCollectionCapability ? 'Yes' : 'No'}}
    </td>
    <td class="text-center">
      <span :class="`fi fi-${thisPlatform.countryIsoCode.toLowerCase()}`"></span> {{thisPlatform.countryIsoCode}}
    </td>
    <td class="text-center">
      -pending-
    </td>
    <td class="text-center">
      1 DIV
    </td>
    <td class="text-center">
      {{ asset.callSign }}
    </td>
		<td class="text-center">
			<AssetColorPickerPopup title="Color" :color="asset.color || '#595858'" @color-selected="updateAssetColor" />
		</td>
		<td>
			<v-btn
				class="text-red-500"
				@click="removeAsset()"
				variant="outlined"
				size="small"
				color="error"
			>
				<v-icon>mdi-delete</v-icon>
			</v-btn>
		</td>
	</tr>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Platform } from '@/types/Platform';
import type { Asset } from '@/types/Asset';
import AssetColorPickerPopup from "@/components/operations/assets/AssetColorPickerPopup.vue";

const isChangingAssetStatus = ref<boolean>(false);
const removeSelf =ref<boolean>(false);
const emit = defineEmits(['change-asset-status', 'remove-asset', 'change-asset-color']);

const props = defineProps<{
	asset: Asset;
}>();

const thisPlatform = computed((): Platform | undefined => {
	return props.asset.platform;
});



const changeAssetStatus = () => {
	emit('change-asset-status', props.asset.id.toString(), props.asset.status);
	isChangingAssetStatus.value = false;
}

const updateAssetColor = (color: string) => {
	console.log("COLOR SETN", color)
	emit('change-asset-color', props.asset.id.toString(), color);
}

const removeAsset = () => {
	const confirmed = confirm("Are you sure you want to remove this user?");
	if(confirmed){
		const assetId = props.asset.id.toString();
		emit('remove-asset', assetId);
		setTimeout(() => {
			removeSelf.value = true;
		}, 1000);
	}
	return;

}

</script>
