<template>
    <div v-if="panelIsLoading" class="d-flex justify-center pa-5">
        <v-progress-circular
            :width="3"
            :size="50"
            color="primary"
            indeterminate
        ></v-progress-circular>
    </div>
    <div v-else>
        <v-card class="lumio-card mb-4">
            <v-card-title class="">
                <h4 class="">ISR Objectives</h4>
            </v-card-title>
            <v-card-text>
                <v-row>
                    <v-col cols="6">
                        <v-textarea
                            v-model="mainEfforts"
                            label="Main Efforts"
                            variant="outlined"
                            density="compact"
                            class="ma-2"
                        >
                            <template v-slot:append>
                                <v-btn
                                    size="small"
                                    color="primary"
                                    icon
                                    variant="outlined"
                                    @click="updateOperationConfig"
                                >
                                    <v-icon>mdi-content-save</v-icon>
                                </v-btn>
                            </template>
                        </v-textarea>
                    </v-col>
                    <v-col cols="6">
                        <v-textarea
                            v-model="supportingEfforts"
                            label="Supporting Efforts"
                            variant="outlined"
                            density="compact"
                            class="ma-2"
                        >
                            <template v-slot:append>
                                <v-btn
                                    size="small"
                                    color="primary"
                                    icon
                                    variant="outlined"
                                    @click="updateOperationConfig"
                                >
                                    <v-icon>mdi-content-save</v-icon>
                                </v-btn>
                            </template>
                        </v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>

        <v-card class="lumio-card">
            <v-card-title class="">
                <h4 class="">Collection Requirements (PIR)</h4>
                <v-btn
                    color="primary"
                    prepend-icon="mdi-plus"
                    class="ma-2"
                    variant="flat"
                    v-if="!isAddPIRsToOperationSegmentOpen"
                    @click="isAddPIRsToOperationSegmentOpen = true"
                >
                    Add PIR to Operation
                </v-btn>
            </v-card-title>
            <v-card-text class="pa-0 ma-0">
                <v-row class="" v-if="isAddPIRsToOperationSegmentOpen">
                    <v-col class="pa-5">
                        <add-p-i-r-to-operation
                            @close-section="
                                isAddPIRsToOperationSegmentOpen = false
                            "
                            :operation-id="currentOperationId.toString()"
                            @update-pirs="fetchPirsByOperationId"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-table density="compact" v-if="pirs.length > 0">
                            <thead>
                                <tr class="text-center">
                                    <th
                                    ></th>
                                    <th style="">PIR #</th>
                                    <th
                                        class="text-center"
                                    >
                                        Priority
                                    </th>
                                    <th
                                        class="text-center"
                                        style="
                                            width: 30px;
                                            max-width: 30px;
                                            min-width: 30px;
                                        "
                                    >
                                        Status
                                    </th>
                                    <th class="text-center">LTIOV</th>
                                    <th>Question</th>
                                    <th class="text-center" style="width: 20%">
                                        Originator
                                    </th>
                                    <th></th>
                                </tr>
                            </thead>
                            <PirRow
                                :pir="pir"
                                :key="pir.id"
                                @fetch-pirs="fetchPirsByOperationId"
                                v-for="pir in pirs"
                            ></PirRow>
                        </v-table>
                        <div
                            v-if="pirs.length === 0"
                            class="d-flex justify-center pa-5"
                        >
                            <v-alert
                                type="info"
                                color="secondary"
                                density="compact"
                                class="ma-0 pa-5"
                                elevation="0"
                            >
                                No PIRs found for this operation
                            </v-alert>
                        </div>
                    </v-col>
                </v-row>
            </v-card-text>
        </v-card>
    </div>
</template>

<script setup lang="ts">
import {
    IPirCollectionResponse,
    useAdminPirStore,
} from '@/stores/admin/pir.store';
// import { useOperationStore } from '@/stores/operation.store';
import { ref, onMounted } from 'vue';
// import { storeToRefs } from 'pinia';
import { IPir } from '@/types/Pir';
import AddPIRToOperation from '@/components/operations/pirs/AddPIRToOperation.vue';
import type { IPagination } from '@/types/Global.type';
import PirRow from '@/components/operations/pirs/PirRow.vue';
import { Operation } from '@/types/Operation';
import { useOperationStore } from '@/stores/operation.store';
import { useSnackbar } from '@/composables/useSnackbar';

const operationStore = useOperationStore();
const { showSnackbar } = useSnackbar();

const props = defineProps<{
    currentOperationId: string | number;
    currentOperation: Operation | null;
}>();

const adminPirStore = useAdminPirStore();

const isAddPIRsToOperationSegmentOpen = ref(false);

const panelIsLoading = ref(true);
const pirs = ref<IPir[]>([]);
const mainEfforts = ref<string>(
    props.currentOperation?.config?.mainEfforts || '',
);

const emits = defineEmits(['refresh-operation']);

const supportingEfforts = ref<string>(
    props.currentOperation?.config?.supportingEfforts || '',
);

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    total: 0,
    pages: 0,
    sortBy: 'asc',
    orderBy: 'pirNumber',
});

// const updateEfforts = async (type: 'main' | 'supporting', value: string) => {
//     await operationStore.updateOperation(props.currentOperationId, {
// 		config : {
// 			...props.currentOperation?.config,
// 			[type === 'main' ? 'mainEfforts' : 'supportingEfforts']: value,
// 		},
// 	})
// 	showSnackbar({
// 		message: `${type === 'main' ? 'Main' : 'Supporting'} Efforts updated`,
// 		type: 'success',
// 	});
//
// 	//show snackbar
//
// };

const fetchPirsByOperationId = async () => {
    console.log('fetchPirsByOperationId', paginationState.value);
    const pirResponse = (await adminPirStore.fetchPirs(
        paginationState.value,
        {
            operationId: props.currentOperationId,
        },
        ['informationRequirements'],
    )) as IPirCollectionResponse;
    const responsePagination = pirResponse?.data?.pagination as IPagination;
    if (responsePagination) {
        paginationState.value = {
            ...paginationState.value,
            ...responsePagination,
        };
    } else {
        paginationState.value = {
            page: 1,
            perPage: 100,
            total: 0,
            pages: 0,
            sortBy: 'asc',
            orderBy: 'pirNumber',
        };
    }

    pirs.value = pirResponse?.data?.pirs as IPir[];
};

onMounted(async () => {
    await fetchPirsByOperationId();
    panelIsLoading.value = false;

    const mainEffortsStored = localStorage.getItem(
        `operation-${props.currentOperationId}-main-efforts`,
    );
    const supportingEffortsStored = localStorage.getItem(
        `operation-${props.currentOperationId}-supporting-efforts`,
    );

    if (mainEffortsStored) {
        mainEfforts.value = mainEffortsStored;
    }
    if (supportingEffortsStored) {
        supportingEfforts.value = supportingEffortsStored;
    }
});

//TODO: add update operation config
// const updateMainEfforts = (value: string) => {
//   //update currentOperation.config.mainEfforts with the new value
// };
//
// const updateSupportingEfforts = (value: string) => {
//   //update currentOperation.config.supportingEfforts with the new value
// };

const updateOperationConfig = async () => {
    await operationStore.updateOperation(props.currentOperationId.toString(), {
        config: {
            ...props.currentOperation?.config,
            mainEfforts: mainEfforts.value,
            supportingEfforts: supportingEfforts.value,
        },
    });
    emits('refresh-operation');
    showSnackbar({
        text: 'Operation data updated',
        color: 'success',
    });
};
</script>
