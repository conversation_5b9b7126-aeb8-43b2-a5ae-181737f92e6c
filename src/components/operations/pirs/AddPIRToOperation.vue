<template>
    <v-card class="lumio-card bg-grey-lighten-4">
        <v-card-title class="">
            <h6 class="">Add PIR to Operation</h6>
            <v-btn icon variant="text" @click="closePirDialogBox">
                <v-icon>mdi-close</v-icon>
            </v-btn>
        </v-card-title>
        <v-card-text class="pa-5">
            <v-row>
                <!-- <v-col cols="2">
					<v-select
						v-model="pirToAdd.priority"
						label="Priority"
						variant="outlined"
						density="compact"
						color="primary"
						base-color="primary"
						hide-details
						:items="Object.values(Priority)"
					>
					</v-select>
				</v-col> -->
                <v-col cols="4">
                    <v-text-field
                        v-model="pirToAdd.question"
                        label="Question"
                        required
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                    >
                    </v-text-field>
                </v-col>
                <v-col cols="8">
                    <v-text-field
                        v-model="pirToAdd.description"
                        label="Description"
                        variant="outlined"
                        density="compact"
                        color="primary"
                        base-color="primary"
                        hide-details
                    >
                    </v-text-field>
                </v-col>
                <v-col cols="2">
                    <v-btn
                        variant="flat"
                        color="primary"
                        text="Add PIR"
                        prepend-icon="mdi-plus"
                        @click="handleAddPIR"
                    >
                    </v-btn>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { defineEmits, ref } from 'vue';
import { IPir } from '@/types/Pir';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { Priority } from '@/types/Global.type';

const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const props = defineProps<{
    operationId: string;
    emitPir?: boolean;
}>();

const pirToAdd = ref<IPir>({
    question: '',
    description: '',
    isActive: true,
    operationId: props.operationId,
    priority: Priority.MEDIUM,
    originator: '',
});

const emit = defineEmits(['update-pirs', 'close-section']);

const closePirDialogBox = () => {
    emit('close-section');
};

const handleAddPIR = async () => {
    try {
        pirToAdd.value.operationId = props.operationId;
        const { messages, success } = await adminPirStore.createPir(
            pirToAdd.value,
        );
        if (success) {
            showSnackbar({
                text: 'PIR added successfully',
                color: 'success',
            });
            pirToAdd.value = {
                pirNumber: '',
                question: '',
                description: '',
                isActive: false,
                operationId: props.operationId,
                priority: Priority.MEDIUM,
                originator: '',
            };
            emit('update-pirs');
        } else {
            showSnackbar({
                pos: 'top-center',
                text: 'Failed to add PIR',
                color: 'error',
            });
            if (messages && messages[0]) {
                showSnackbar({
                    pos: 'top-center',
                    text: messages[0].message as string,
                    color: 'error',
                });
            }
        }
        //emit update
        //reset pirToAdd
    } catch (error) {
        console.error(error);
    } finally {
        //loading.value = false;
    }
};
</script>
