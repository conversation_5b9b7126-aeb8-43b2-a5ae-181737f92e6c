<template>
    <v-form class="">
        <v-row>
            <v-col cols="12">
                <v-textarea
                    density="compact"
                    v-model="IRFields.informationRequirement"
                    label="Information Requirement"
                    rows="2"
                    hide-details
                />
            </v-col>
        </v-row>
        <v-row>
            <v-col col="4">
                <v-select
                    density="compact"
                    v-model="IRFields.originator"
                    :items="originators"
                    label="Originator"
                    hide-details
                />
            </v-col>
            <v-col col="4">
                <v-select
                    density="compact"
                    v-model="IRFields.priority"
                    :items="priorities"
                    label="Priority"
                    hide-details
                />
            </v-col>
            <v-col col="4">
                <LumioDateTimePicker
                    v-model="IRFields.ltiovDate as string"
                    class="mb-4"
                    :minDateTime="new Date()"
                    placeholder="LTIoV Date"
                ></LumioDateTimePicker>
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="6">
                <v-btn variant="text" @click="closeIRModal" class="mr-2"
                    >Cancel</v-btn
                >
            </v-col>
            <v-col cols="6" class="text-right">
                <v-btn color="primary" @click="createIR">Create IR</v-btn>
            </v-col>
        </v-row>
        <div class="d-flex justify-end"></div>
    </v-form>
</template>

<script setup lang="ts">

import LumioDateTimePicker from '@/components/elements/LumioDateTimePicker.vue';
import { IOriginator } from '@/types/Originator.type';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { IPagination } from '@/types/Global.type';
const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    pirId: string | number | null;
    operationId: string | number | null;
}>();

const emit = defineEmits(['close-ir-modal', 'create-ir']);

const originators = ref<IOriginator[]>([]);

const priorities = ref<string[]>(['highest', 'high', 'medium', 'low', 'none']);

const IRFields = ref({
    pirId: props.pirId as string,
    originator: '',
    informationRequirement: '',
    ltiovDate: '',
    priority: 'medium',
});

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 100,
    sortBy: 'desc',
    orderBy: 'createdAt',
    pages: 1,
    total: 0,
});

const closeIRModal = () => {
    emit('close-ir-modal');
};

const createIR = () => {
    emit('create-ir', IRFields.value);
};

onMounted(async () => {
    IRFields.value.pirId = props.pirId as string;
    const response = await adminOriginatorStore.fetchOriginators(
        paginationState.value,
        {
            operationId: props.operationId as string,
        },
    );
    originators.value = response.data?.originators ?? [];
});
</script>
