<template>
    <thead>
        <tr class="font-weight-bold bg-grey-lighten-4">
            <th>
                <template
                    v-if="
                        pir &&
                        pir.informationRequirements &&
                        pir.informationRequirements?.length > 0
                    "
                >
                    <v-btn
                        @click="sectionExpanded = !sectionExpanded"
                        variant="plain"
                        color="black"
                        class="mr-2"
                        size="medium"
                    >
                        <v-icon
                            v-if="sectionExpanded"
                            icon="mdi-chevron-down"
                            color="black"
                        />
                        <v-icon
                            v-else
                            icon="mdi-chevron-right"
                            color="black"
                        /> </v-btn
                    >`
                </template>
            </th>
            <th>[PIR:{{ pir.pirNumber }}]</th>
            <th class="text-center">
                <!-- <LumioPriorityChip :priority="pir.priority" /> -->
            </th>
            <th class="text-center">
                <LumioStatusChip
                    :status="pir.isActive ? 'active' : 'inactive'"
                />
            </th>
            <th class="text-left"></th>
            <th class="d-flex justify-space-between">
                <v-btn
                    @click="showEditPopup"
                    variant="plain"
                    color="black"
                    class="font-weight-black"
                    prepend-icon="mdi-pencil"
                >
                    {{ pir.question }}
                </v-btn>
            </th>

            <th class="text-center">
	            {{pir.originator}}
            </th>
            <th v-if="!props.noIrs" class="text-center">
                <v-btn
                    v-if="pir.isActive"
                    @click="addInformationRequirement"
                    variant="elevated"
                    size="small"
                    class="ml-2"
                    color="primary"
                >
                    ADD IR <v-icon>mdi-plus</v-icon>
                </v-btn>
                <div v-else>INACTIVE</div>
            </th>
        </tr>
    </thead>
    <tbody>
        <IrRow
            v-for="ir in pir.informationRequirements"
            v-show="sectionExpanded"
            :key="ir.id"
            :ir="ir"
            :pir="pir"
            @refreshPirs="refreshPirs"
            @showIrEditPopup="showIrEditPopup"
        ></IrRow>
    </tbody>
    <v-dialog v-model="isAddingIr" max-width="800">
        <v-card
            prepend-icon="mdi-tooltip-edit-outline"
            title="Add Information Requirement"
        >
            <v-card-text v-if="pir && pir.operationId">
                <add-ir-modal
                    v-if="isAddingIr"
                    :pir-id="pir?.id?.toString() ?? ''"
                    :operation-id="pir?.operationId?.toString()"
                    @close-ir-modal="isAddingIr = false"
                    @create-ir="createIR"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="changingIr" max-width="800">
        <v-card
            prepend-icon="mdi-tooltip-edit-outline"
            title="Change Information Requirement"
        >
            <v-card-text v-if="pir && pir.operationId">
                <edit-ir-modal
                    v-if="changingIr"
                    :pir-id="pir?.id?.toString() ?? ''"
                    :operation-id="pir?.operationId?.toString()"
                    @close-ir-modal="changingIr = false"
                    @update-ir="updateIR"
                    :parent-pir="pir"
                    :ir-id="irId"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="isEditing" max-width="800">
        <v-card prepend-icon="mdi-tooltip-edit-outline" title="Edit PIR">
            <v-card-text v-if="pir && pir.id && pir.operationId">
                <edit-p-i-r-popup
                    v-if="isEditing"
                    :pir-id="pir.id.toString()"
                    :operation-id="pir.operationId.toString()"
                    @close-update-popup="isEditing = false"
                    @update-pirs="refreshPirs"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
</template>
<script setup lang="ts">
import { IPir } from '@/types/Pir';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useAdminIRStore } from '@/stores/admin/ir.store';
import IrRow from '@/components/operations/pirs/IrRow.vue';
const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const adminIrStore = useAdminIRStore();
const isAddingIr = ref(false);
const isEditing = ref(false);
const sectionExpanded = ref(true);
const changingIr = ref(false);
const irId = ref<string | number | null>(null);

const props = defineProps<{
    pir: IPir;
    noIrs?: boolean;
}>();

const emit = defineEmits(['fetch-pirs']);

const addInformationRequirement = () => {
    isAddingIr.value = true;
};

const showEditPopup = () => {
    isEditing.value = true;
};

const showIrEditPopup = (irIdEditable: string | number) => {
    changingIr.value = true;
    irId.value = irIdEditable;
};

const createIR = async (ir: any) => {
    //make sure ir required fields are set
    if (!ir.ltiovDate || !ir.priority || !ir.informationRequirement) {
        showSnackbar({
            text: 'Please fill all required fields',
            color: 'error',
        });
        return;
    }
    ir.pirId = props.pir.id;
    ir.operationId = props.pir.operationId;
    isAddingIr.value = true;

    await adminPirStore.addIrToPir(ir);
    isAddingIr.value = false;
    refreshPirs();
};

const updateIR = async (ir: any) => {
    await adminIrStore.updateIr(ir.irId as string, ir);
    refreshPirs();
    changingIr.value = false;
};

const refreshPirs = () => {
    emit('fetch-pirs');
};
</script>
