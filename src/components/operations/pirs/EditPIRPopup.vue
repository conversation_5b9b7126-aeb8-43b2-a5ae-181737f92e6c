<template>
    <v-progress-linear
        v-if="isLoading"
        indeterminate
        color="primary"
        class="my-4"
    ></v-progress-linear>

    <div v-else>
		<div class="d-flex flex-wrap justify-start" >
			<v-text-field
				v-model="pirToEdit.question"
				label="Question"
				variant="outlined"
				density="compact"
				color="primary"
				base-color="primary"
				hide-details
				class="my-5 bg-white w-66 flex-grow-0 px-2"
			>
			</v-text-field>
			<v-select
				v-model="pirToEdit.priority"
				label="Priority"
				variant="outlined"
				density="compact"
				color="primary"
				base-color="primary"
				hide-details
				class="my-5 bg-white flex-grow-0  px-2"
				:items="Object.values(Priority)"
			>
			</v-select>
			<v-checkbox
				v-model="pirToEdit.isActive"
				label="Active"
				density="compact"
				color="primary"
				base-color="primary"
				hide-details
				variant="outlined"
				type="checkbox"
				class="my-5 bg-white flex-grow-0  px-2"
			>
			</v-checkbox>
			<v-select
				density="compact"
				v-model="pirToEdit.originator"
				:items="originators"
				label="Originator"
				hide-details
			/>
		</div>

        <v-textarea
            v-model="pirToEdit.description"
            label="Description"
            variant="outlined"
            density="compact"
            color="primary"
            base-color="primary"
            hide-details
            class="my-5 bg-white w-100 h-33"
        >
        </v-textarea>
    </div>
    <div class="d-flex justify-space-between">
		<v-btn
			@click="emit('close-update-popup')"
			variant="text"
			size="small"
			color="primary"
			text="Cancel"
			prepend-icon="mdi-close"
		>
		</v-btn>
        <v-btn
            @click="handleUpdatePIR"
            variant="flat"
            color="primary"
            text="Update PIR"
            prepend-icon="mdi-plus"
        >
        </v-btn>

    </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, nextTick } from 'vue';
import { IPir } from '@/types/Pir';
import { IPirSingleResponse, useAdminPirStore } from '@/stores/admin/pir.store';
import { useSnackbar } from '@/composables/useSnackbar';
import {Priority} from "@/types/Global.type";
import {IOriginator} from "@/types/Originator.type";
import {useAdminOriginatorStore} from "@/stores/admin/originator.store";

const { showSnackbar } = useSnackbar();
const adminPirStore = useAdminPirStore();
const adminOriginatorStore = useAdminOriginatorStore();

const props = defineProps<{
    pirId: string | number | null;
	operationId: string | number | null;
}>();

const emit = defineEmits(['update-pirs', 'close-update-popup']);
const isLoading = ref(true);
const originators = ref<IOriginator[]>([]);

const pirToEdit = ref<IPir>({
    question: '',
    description: '',
    isActive: false,
    operationId: '',
	priority: Priority.MEDIUM,
	originator: '',
});

const paginationState = ref({
	page: 1,
	perPage: 100,
	sortBy: 'desc',
	orderBy: 'createdAt',
	pages: 1,
	total: 0,
});

async function fetchPirById() {
    const response = await adminPirStore.fetchPirById(props.pirId as string);

    if (response.success && response.data?.pir) {
        // Changed from data.data.pir to data.pir
        pirToEdit.value = {
            question: response.data.pir.question ?? '',
            description: response.data.pir.description ?? '',
            isActive: response.data.pir.isActive ?? false,
            operationId: response.data.pir.operationId ?? '',
			priority: response.data.pir.priority ?? Priority.MEDIUM,
			originator: response.data.pir.originator ?? '',
        };
    } else {
        showSnackbar({
            text: 'Failed to fetch PIR',
            color: 'error',
        });
    }
    isLoading.value = false;
}

async function handleUpdatePIR() {
    try {
        const response = (await adminPirStore.updatePir(
            props.pirId as string,
            pirToEdit.value,
        )) as IPirSingleResponse;
        if (response.success) {
            showSnackbar({
                text: 'PIR updated successfully',
                color: 'success',
            });
            emit('update-pirs');
            await nextTick();
            emit('close-update-popup');
        } else {
            showSnackbar({
                text: 'Failed to update PIR',
                color: 'error',
            });
        }
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'An error occurred while updating PIR',
            color: 'error',
        });
    }
}

async function fetchOriginators(){

	const response = await adminOriginatorStore.fetchOriginators(
		paginationState.value,
		{
			operationId: props.operationId as string,
		}
	);
	originators.value = response.data?.originators ?? [];
}

onMounted(async () => {
    await fetchPirById();
	await fetchOriginators();
});
</script>
