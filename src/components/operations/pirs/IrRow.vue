<template>
    <tr>
        <td class=""></td>
        <td class="text-left">[IR: {{ pir.pirNumber }}.{{ ir.irNumber }}]</td>
        <td class="text-center">
            <LumioPriorityChip :priority="ir.priority" />
        </td>
        <td class="text-center">
            <LumioStatusChip v-if="ir.isAnswered" :status="`answered`" />
        </td>
        <td class="text-center">
            {{ dayjs(ir.ltiovDate).format('MMM-D, YYYY  HH:mm') }}
        </td>
        <td>
            <div class="ml-4">
                <v-btn
                    @click="showIrEditPopup"
                    variant="plain"
                    color="black"
                    class="font-weight-black"
                    prepend-icon="mdi-pencil"
                >
                    {{ ir.informationRequirement }}
                </v-btn>
            </div>
        </td>

        <td class="text-center">
            {{ ir.originator }}
        </td>
        <td class="text-center">
            <v-btn variant="plain" color="primary" @click="deleteIR(ir.id)">
                <v-icon>mdi-trash-can</v-icon>
            </v-btn>
        </td>
    </tr>
</template>
<script setup lang="ts">
import dayjs from 'dayjs';
import { useSnackbar } from '@/composables/useSnackbar';
import { useAdminIRStore } from '@/stores/admin/ir.store';
const { showSnackbar } = useSnackbar();
const adminIrStore = useAdminIRStore();
const emits = defineEmits(['refreshPirs', 'showIrEditPopup']);

const props = defineProps({
    pir: {
        type: Object,
        required: true,
    },
    ir: {
        type: Object,
        required: true,
    },
});

const deleteIR = async (id: number | string) => {
    const confirmed = confirm('Are you sure you want to delete this IR?');
    if (!confirmed) return;

    await adminIrStore.deleteIr(id.toString());

    showSnackbar({
        text: 'Information Requirement Deleted',
        color: 'success',
    });
    emits('refreshPirs');
};

const showIrEditPopup = () => {
    emits('showIrEditPopup', props.ir.id);
};
</script>
