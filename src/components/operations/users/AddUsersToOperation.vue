<template>
	<v-card class="ma-3 bg-grey-lighten-4 border border-solid border-grey-lighten-4 rounded-lg">
		<v-card-title class="d-flex justify-space-between">
			<h3>Add Users to Operation</h3>
			<v-btn
				class="ma-2"
				variant="flat"
				icon
				density="compact"
				@click="emit('close-section')"
			>
				<v-icon>mdi-close</v-icon>
			</v-btn>
		</v-card-title>
		<v-card-text>
			<div class="d-flex" >
				<v-text-field v-model="searchUserField"
				              label="Search User by email"
				              variant="outlined"
				              density="compact"
				              color="primary"
				              base-color="primary"
				              hide-details
				              class="my-5 bg-white w-100"
				              @keydown.enter="searchUser"
				>
				</v-text-field>

				<v-btn @click="searchUser"
				       class=" my-5 px-4"
				       variant="flat"
				       color="primary"
				       size="lg"
				       density="default"
				       text="Search"
				       prepend-icon="mdi-magnify"
				>
				</v-btn>
				<v-btn @click="clearSearch"
				       class=" my-5 px-4"
				       variant="text"
				       color="primary"
				       size="lg"
				       density="default"
				       text="Clear"
				       prepend-icon="mdi-delete"
				>
				</v-btn>
			</div>

			<template v-if="isSearching">
				<v-progress-linear indeterminate></v-progress-linear>
			</template>
			<v-table v-else density="compact">
				<thead>
				<tr>
					<th>User Name</th>
					<th>User Email</th>
					<th>User Roles</th>
					<th class="w-50">Assign</th>
				</tr>
				</thead>
				<tbody>
				<template v-if="users.length > 0">
					<add-user-to-operation-row
						v-for="user in users"
						:key="user.id"
						:user="user as User"
						@assign-user="handleAssignSingleUserToOperation"
						:existing-operation-users="existingOperationUsers"
					/>
				</template>
				<template v-else>
					<tr>
						<td colspan="4" class="text-center" v-if="nothingFoundForQuery">
							<v-alert
								prominent
								density="compact"
							>
								<template v-slot:title>
									No users found for email that starts with: <span class="text-info">{{ nothingFoundForQuery }}</span>
								</template>
							</v-alert>
						</td>
						<td v-else colspan="4" class="text-center">
							Search users by email
						</td>
					</tr>
				</template>


				</tbody>
			</v-table>
		</v-card-text>
	</v-card>




</template>


<script setup lang="ts">
import { useAdminStore } from '@/stores/admin'
import type { UserOperation } from '@/types/UserOperation';
import { User } from '@/types/User';
import { ref, defineEmits, defineProps } from 'vue';
import AddUserToOperationRow from '@/components/operations/users/AddUserToOperationSearchRow.vue';
import { useAdminUserStore } from '@/stores/admin/user.store';
import { useSnackbar } from '@/composables/useSnackbar';

const isSearching = ref<boolean>(false);
const adminUserStore = useAdminUserStore();
const searchUserField = ref<string>('');
const nothingFoundForQuery = ref<string|null>(null)
const admin = useAdminStore();
const { showSnackbar } = useSnackbar();
const props = defineProps<{
  existingOperationUsers: UserOperation[];
  operationId: string;
}>();
const users = ref<User[]>([]);

const searchUser = async() => {
	try{
		isSearching.value = true;
		nothingFoundForQuery.value = null;
		//if(!searchUserField.value.length < 2) return;
		const apiResponse = await adminUserStore.searchUsers(searchUserField.value, ['email']);
		users.value = apiResponse.data.users;
		if(users.value.length === 0) {
			nothingFoundForQuery.value = searchUserField.value;
		}
	} catch (error) {
		console.error("ERROR");
	} finally {
		isSearching.value = false;
	}

}

const emit = defineEmits(['close-section', 'assign-user', 'update-user', 'remove-user', 'update-operation']);


const handleAssignSingleUserToOperation = async(userId: string, accessType:string) =>{
	try {
		// loading.value = true;
		await admin.assignUserToOperation(userId, {
			operationId: props.operationId.toString(),
			accessType: accessType
		});
		await sendUpdateEmit();
		showSnackbar({
			text: 'User Assigned to Operation',
			color: 'success',
		});
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: 'Failed to assign user',
			color: 'error',
		});
	} finally {
		//loading.value = false;
	}
}

const clearSearch = () => {
	searchUserField.value = '';
	users.value = [];
}

async function sendUpdateEmit() {
	emit('update-operation', props.operationId);
	return
}

</script>
