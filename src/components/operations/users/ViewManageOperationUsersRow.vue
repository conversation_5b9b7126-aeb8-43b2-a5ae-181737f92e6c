<template>
	<transition name="fade" :duration="500">
		<tr v-if="!hideThisRow" :class="userRowClass">
		<td>

		</td>
		<td>
			{{ operationUserItem?.user?.firstName }} {{ operationUserItem?.user?.lastName }}
			<br><span class="text-blue">{{ operationUserItem?.user?.email }}</span>
		</td>
		<td>
			<template v-if="getRoles && getRoles.length > 0">
				<v-chip
					v-for="role in getRoles"
					:key="role.id"
					density="compact"
					border="1"
					color="white"
					class="bg-primary text-white mr-2"
				>{{ role.roleName }}</v-chip>
			</template>
			<span v-else>
						Roles not assigned
					</span>
		</td>
		<td>
			<div v-if="isEditingInProgress" class="d-flex align-center">
				<v-select
					v-model="setAccessTypeForUser"
					:items="['read', 'write', 'manage'] as AccessType[]"
					label="Access type"
					variant="outlined"
					density="compact"
					color="primary"
					base-color="primary"
					hide-details
					class="my-5 bg-white w-100"
				></v-select>
				<v-btn
					@click="handleEditUser()"
					variant="outlined"
					color="primary"
					width="25"
					max-width="25"
					class="ml-2 px-2 w-25"
				>
					<v-icon>mdi-check</v-icon>
				</v-btn>
				<v-btn
					@click="isEditingInProgress = false"
					variant="text"
					class="ml-3"
					color="primary"
					density="default"
					icon="mdi-close"
				>
				</v-btn>
			</div>
			<div v-else>
				<v-chip
					:color="getAccessTypeColor(operationUserItem.accessType as string)"
					prepend-icon="mdi-pencil"
					@click="isEditingInProgress = true"
				>
					{{ operationUserItem?.accessType }}
				</v-chip>

			</div>
		</td>
		<td class="text-right">
			<v-btn
				@click="handleRemoveUser()"
				variant="outlined"
				size="small"
				text="Remove"
				color="error"
			>
				<v-icon>mdi-delete</v-icon> Remove
			</v-btn>
			<v-progress-linear v-if="setToRemove" indeterminate></v-progress-linear>
		</td>
	</tr>
	</transition>
</template>

<style scoped>
  .strikethrough {
    text-decoration: line-through;
  }
</style>
<script setup lang="ts">
import { UserOperation } from '@/types/UserOperation';
import { Role } from '@/types/Role';
import { User } from '@/types/User';
// import { IAccessType } from '@/types/Operation';

type AccessType = 'read' | 'write' | 'manage';

const props = defineProps<{
	operationUserItem: UserOperation;
}>();

const hideThisRow = ref<boolean>(false);

const emit = defineEmits(['remove-user', 'update-user']);
const userRowClass = ref<string>('');

const setAccessTypeForUser = ref<AccessType>('read');
const isEditingInProgress = ref<boolean>(false);
const setToRemove = ref<boolean>(false);


const getAccessTypeColor = (type: string) => {
	const colors: Record<string, string> = {
		read: 'green',
		write: 'blue',
		manage: 'orange',
		list: 'grey',
	};
	return colors[type];
};

const handleEditUser = () => {
	userRowClass.value = 'bg-blue-lighten-4';
	// Type assertion to ensure accessType is correctly typed
	props.operationUserItem.accessType = setAccessTypeForUser.value as AccessType;
	const oU = props.operationUserItem;
	const u = oU.user as User;
	emit('update-user', u.id, setAccessTypeForUser.value);
	isEditingInProgress.value = false;

	setTimeout(() => {
		userRowClass.value = '';
	}, 1000);
};

const handleRemoveUser = () => {
	userRowClass.value = 'bg-red-lighten-4 strikethrough';
	const confirmed = confirm("Are you sure you want to remove this user?");
	if (confirmed) {
		setToRemove.value = true;
		const oU = props.operationUserItem;
		const u = oU.user as User;
		emit('remove-user', u.id);
		setTimeout(() => {
			hideThisRow.value = true;
		}, 1000);
	} else {
		userRowClass.value = '';
	}
};

const getRoles = computed(() => {
	const user = props.operationUserItem.user;
	if (!user || !user.roles) return [] as Role[];

	return user.roles.map(role => ({
		...role,
		roleName: role.roleName || ''
	})) as Role[];
});

onMounted(() => {
	if (props.operationUserItem.accessType) {
		setAccessTypeForUser.value = props.operationUserItem.accessType;
	}
});

</script>
