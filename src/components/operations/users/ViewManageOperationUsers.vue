<template>
    <v-row>
        <v-col class="" cols="12">
            <!-- <v-card
                class="ma-3 bg-grey-lighten-4 border border-solid border-grey-lighten-4 rounded-lg"
            >
                <v-card-title class="d-flex justify-space-between">
                    <h3>Current Users</h3>
                </v-card-title>
                <v-card-text>
                    <v-table
                        class="pa-0 ma-0 border"
                        density="compact"
                        fixed-header
                    >
                        <template v-slot:default>
                            <thead>
                                <tr>
                                    <th colspan="3">
                                        <template
                                            v-if="operationUsers.length"
                                            class=""
                                        >
                                            <v-text-field
                                                v-model="userSearch"
                                                label="Search user by email"
                                                hide-details
                                                single-line
                                                density="compact"
                                                class="ma-3 pa-0"
                                            />
                                        </template>
                                    </th>
                                    <th colspan="2"></th>
                                </tr>
                                <tr>
                                    <th class=""></th>
                                    <th>User</th>
                                    <th>User Roles</th>
                                    <th>Access Type</th>
                                    <th class="text-right"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <view-manage-operation-users-row
                                    v-for="item in filteredUsers"
                                    :key="item.id"
                                    :operation-user-item="item"
                                    @update-user="
                                        handleUpdateSingleUserOperation
                                    "
                                    @remove-user="handleRemoveUser"
                                />
                            </tbody>
                        </template>
                    </v-table>
                </v-card-text>
            </v-card> -->

            <v-card class="mt-3">
                <v-card-title>Users in organization</v-card-title>
                <v-card-text>
                    <template v-if="usersFetching">
                        <v-progress-linear indeterminate></v-progress-linear>
                    </template>
                    <v-table v-else density="compact">
                        <thead>
                            <tr>
                                <th>User Name</th>
                                <th>User Email</th>
                                <th>User Roles</th>
                                <th class="w-50">Assign</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-if="operationUsers.length">
                                <add-user-to-operation-row
                                    v-for="user in operationUsers"
                                    :key="user.id"
                                    :user="user as unknown as User"
                                    @assign-user="
                                        handleAssignSingleUserToOperation
                                    "
                                    :existing-operation-users="operationUsers"
                                />
                            </template>
                        </tbody>
                    </v-table>
                </v-card-text>
            </v-card>
        </v-col>
    </v-row>
</template>

<script setup lang="ts">
import { UserOperation } from '@/types/UserOperation';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref } from 'vue';
import { useAdminStore } from '@/stores/admin';
import { Operation } from '@/types/Operation';
import AddUserToOperationRow from '@/components/operations/users/AddUserToOperationSearchRow.vue';
const { showSnackbar } = useSnackbar();
import { User } from '@/types/User';

//add props for operationId
const props = defineProps<{
    operationWithUsers: Operation;
    operationId: number | string;
}>();

//define emits
const emit = defineEmits(['operation-updated']);
// const userSearch = ref('');

const admin = useAdminStore();
// const loading = ref(false);

const operationUsers = ref<UserOperation[]>([]);

const currentPage = ref<number>(1);
const totalPages = ref<number>(1);

const usersFetching = ref<boolean>(false);

const fetchUsers = async (page: number) => {
    try {
        usersFetching.value = true;
        const { users, pagination } = await admin.fetchUsers(currentPage.value);
        operationUsers.value = users;
        totalPages.value = pagination.totalPages;
    } catch (error: any) {
        showSnackbar({
            text: 'Failed to fetch users',
            color: 'error',
        });
    } finally {
        usersFetching.value = false;
    }
};

const handleAssignSingleUserToOperation = async (
    userId: string,
    accessType: string,
) => {
    try {
        // loading.value = true;
        await admin.assignUserToOperation(userId, {
            operationId: props.operationId.toString(),
            accessType: accessType,
        });
        showSnackbar({
            text: 'User Assigned to Operation',
            color: 'success',
        });
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to assign user',
            color: 'error',
        });
    } finally {
        //loading.value = false;
    }
};

// const filteredUsers = computed(() => {
//     return operationUsers.value.filterhandleUpdateSingleUserOperation((user: UserOperation) => {
//         if (!user.user) return false;
//         return user.user.email
//             .toLowerCase()
//             .includes(userSearch.value.toLowerCase());
//     });
// });

// const handleUpdateSingleUserOperation = async (
//     userId: string,
//     accessType: string,
// ) => {
//     try {
//         //	loading.value = true;
//         if (!props.operationWithUsers.id) return;
//         const payload = [
//             {
//                 id: userId,
//                 accessType: accessType,
//             },
//         ];
//         await admin.updateSingleUserOperation(
//             props.operationWithUsers.id.toString(),
//             payload as UserOperation[],
//         );
//         // sendUpdateEmit();
//         showSnackbar({
//             text: 'User updated',
//             color: 'success',
//         });
//     } catch (error) {
//         console.error(error);
//         showSnackbar({
//             text: 'Failed to update user',
//             color: 'error',
//         });
//     } finally {
//         //	loading.value = false;
//     }
// };

// const handleRemoveUser = async (userId: string | number) => {
//     //pop a simple javascript confirm dialog

//     //loading.value = true;
//     try {
//         if (!props.operationWithUsers.id) return;
//         await admin.removeUserFromOperation(
//             props.operationWithUsers.id,
//             userId,
//         );
//         // sendUpdateEmit();
//     } catch (error) {
//         console.error(error);
//         showSnackbar({
//             text: 'Failed to remove user from operation',
//             color: 'error',
//         });
//     } finally {
//         //loading.value = false;
//     }
// };

// const sendUpdateEmit = () => {
// 	emit('operation-updated', props.operationWithUsers.id);
// 	return
// };

//on mounted set operationId and OperationUsers
onMounted(async () => {
    await fetchUsers(currentPage.value);
    // operationUsers.value = props.operationWithUsers.users as UserOperation[];
});
</script>
