<template>
  <div>
    <v-list v-if="isrs">
      <v-list-subheader>ISR Tracks</v-list-subheader>
      <v-list-item
        class="border"
        v-for="isr in isrs"
        :key="isr.id"
        :value="isr.id"
        color="primary"
        variant="plain"
      >
        <template v-slot:prepend>
          <v-icon :icon="getIconByIsrType(isr.type)"></v-icon>
        </template>
        <v-list-item-title v-text="isr.label"></v-list-item-title>
        <v-list-item-subtitle v-text="isr.ltiovDate"></v-list-item-subtitle>
      </v-list-item>
    </v-list>
  </div>
</template>


<script setup lang="ts">
import {useAdminGlobalIsrStore} from '@/stores/admin/globalIsr.store';
import {IPagination} from "@/types/Global.type";
import {IGlobalIsr} from "@/types/GlobalIsr.type";
const adminIsrStore = useAdminGlobalIsrStore();

const isrs = ref<IGlobalIsr[]>([]);

const props = defineProps<{
  operationId?: string | number | null
}>();

const paginationState = ref<IPagination>({
  page: 1,
  perPage: 100,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const getIconByIsrType = (type: string) => {
  switch (type) {
    case 'point':
      return 'mdi-map-marker-radius';
    case 'polygon':
      return 'mdi-texture-box';
    case 'line':
      return 'mdi-map-marker-distance';
    default:
      return 'mdi-map-marker';
  }
};


const fetchIsrs = async () => {
  try {
    const response = await adminIsrStore.fetchIsrs(paginationState.value, {
      operationId: props.operationId
    });

    // Safely handle the response data
    if (response?.data?.globalIsrs) {
      isrs.value = response.data.globalIsrs;
    }

    // Pagination is inside data property
    if (response?.data?.pagination) {
      paginationState.value = response.data.pagination;
    }
  } catch (e) {
    console.error(e);
  }
}

onMounted(fetchIsrs);

</script>
