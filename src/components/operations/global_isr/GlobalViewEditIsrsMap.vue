<template>
    <div v-if="isUpdating">
        <v-progress-circular indeterminate color="primary" />
    </div>
    <div v-else>
        <template v-if="hasChanges">
            <v-btn color="primary" class="ma-2" @click="saveIsrTrack">
                Save
            </v-btn>
            <v-btn class="ma-2" flat @click="cancelIsrTrack"> Cancel </v-btn>
        </template>
    </div>
    <EsriMap
        :key="isr.id"
        :center-coordinates="getCenterCoordinates"
        :existing-coordinates="getExistingShapes"
        :zoom="getZoom"
        :is-editing="true"
        :editing-allowed="true"
        :max-height="600"
        @elements-selected="onDrawComplete"
    />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAdminGlobalIsrStore } from '@/stores/admin/globalIsr.store';

const adminIsrStore = useAdminGlobalIsrStore();
import { IGlobalIsr } from '@/types/GlobalIsr.type';
import { DrawCompleteData } from '@/types/Global.type';
const isUpdating = ref(false);
const emits = defineEmits(['re-fetch-isrs']);

const props = defineProps<{
    isr: IGlobalIsr;
}>();

const updatedIsr = ref<IGlobalIsr>({
    id: props.isr.id,
    designation: props.isr.designation,
    label: props.isr.label,
    type: props.isr.type,
    coordinates: props.isr.coordinates,
    zoom: props.isr.zoom,
    centerCoordinates: props.isr.centerCoordinates,
    status: props.isr.status,
    priority: props.isr.priority,
    commenceAt: props.isr.commenceAt,
    concludeAt: props.isr.concludeAt,
    ltiovDate: props.isr.ltiovDate,
    createdAt: props.isr.createdAt,
    updatedAt: props.isr.updatedAt,
    assetId: props.isr.assetId,
    operationId: props.isr.operationId,
    createdByUserId: props.isr.createdByUserId,
    asset: props.isr.asset,
    operation: props.isr.operation,
    createdByUser: props.isr.createdByUser,
});

const getCenterCoordinates = computed(() => {
    // Extract coordinates from the Point type and return as number[]
    return updatedIsr.value.centerCoordinates?.coordinates || [0, 0]; // provide a default value
});

const getZoom = computed(() => {
    return updatedIsr.value.zoom || 10;
});

const getExistingShapes = computed(() => {
    if (!updatedIsr.value.coordinates?.geometries) return [];

    return updatedIsr.value.coordinates.geometries.map((geometry) => {
        const type =
            geometry.type === 'LineString'
                ? ('LineString' as const)
                : geometry.type === 'Polygon'
                ? ('Polygon' as const)
                : ('Point' as const);

        return {
            type,
            coordinates: geometry.coordinates,
            title: updatedIsr.value.label || undefined,
        };
    });
});

const onDrawComplete = async (data: DrawCompleteData) => {
    isUpdating.value = true;
    if (!updatedIsr.value.coordinates) {
        updatedIsr.value.coordinates = {
            type: 'GeometryCollection',
            geometries: [],
        };
    }

    updatedIsr.value.coordinates.geometries = [];

    for (const element of data.elements) {
        const type = element.locationCoordinates.type.toLowerCase();

        let coordinates = [];
        // Handle different coordinate formats based on type
        if (type === 'point') {
            // For points, wrap the single coordinate pair in an array
            coordinates = element.locationCoordinates.coordinates as [
                number,
                number,
            ];
        } else {
            // For lines and polygons, use the coordinates array as is
            coordinates = element.locationCoordinates.coordinates as [
                number,
                number,
            ][];
        }

        const elType =
            type === 'polyline'
                ? ('LineString' as const)
                : type === 'polygon'
                ? ('Polygon' as const)
                : ('Point' as const);

        updatedIsr.value.coordinates.geometries.push({
            type: elType,
            coordinates: elType === 'Polygon' ? [coordinates] : coordinates,
        });
    }


    updatedIsr.value.centerCoordinates = {
        type: 'Point' as const,
        coordinates: data.centerCoordinates,
    };

    updatedIsr.value.zoom = data.zoom;

    isUpdating.value = false;
    await saveIsrTrack();
};

const saveIsrTrack = async () => {
    isUpdating.value = true;
    await adminIsrStore.updateIsr(props.isr.id.toString(), updatedIsr.value);
    emits('re-fetch-isrs');
    isUpdating.value = false;
};

const cancelIsrTrack = async () => {
    isUpdating.value = true;
    //reset the isr to props
    updatedIsr.value = props.isr;
    emits('re-fetch-isrs');
    isUpdating.value = false;
};

//watch isr for changes
const hasChanges = computed(() => {
    return JSON.stringify(updatedIsr.value) !== JSON.stringify(props.isr);
});
</script>
