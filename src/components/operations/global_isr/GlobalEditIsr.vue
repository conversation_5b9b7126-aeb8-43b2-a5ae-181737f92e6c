<template>
  <div v-if="isLoadingSomething">

  </div>
  <div v-else class="pa-4">
    <v-row v-if="operation">
      <v-col cols="3">
        <div v-if="assets" class="d-flex justify-start align-center mb-2 ">
          <v-select
            density="compact"
            hide-details
            class=" flex-grow-1"
            label="ISR Assets"
            :items="assets"
            v-model="selectedAssetId"
            item-title="name"
            item-value="id">
          </v-select>
          <v-btn
            @click="createISR"
            flat
            class="ma-0 pa-0 flex-grow-1"
            v-if="selectedAssetId">
            <v-icon>mdi-plus</v-icon>
            ISR Track
          </v-btn>
        </div>
        <v-divider></v-divider>

        <ul class="">
          <li v-for="isr in isrs" :key="isr.id" :class="getClass(isr.id)">
            <div class="d-flex flex-row">
              <div class="flex-grow-1 ma-2">
               {{isr.label}}
              </div>
              <div class="flex-grow-0" v-if="!selectedIsr">
                <v-btn flat @click="selectedIsr = isr">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn variant="text"  @click="deleteIsr(isr.id)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </div>
              <div class="flex-grow-0" v-if="selectedIsr && selectedIsr.id === isr.id">
                <v-btn variant="tonal" color="secondary" @click="selectedIsr = null">
                  cancel
                </v-btn>
              </div>
            </div>
          </li>
        </ul>
      </v-col>
      <v-col cols="9">
        <template v-if="selectedIsr">
          <GlobalViewEditIsrsMap
            :isr="selectedIsr"
            :key="selectedIsr.id+'map'"
            @re-fetch-isrs="fetchIsrs" />
        </template>
      </v-col>
    </v-row>
  </div>

</template>
<script setup lang="ts">

import { ref } from 'vue';
import { useAdminAssetStore } from '@/stores/admin/asset.store';
import {IGlobalIsrCollectionResponse, useAdminGlobalIsrStore} from '@/stores/admin/globalIsr.store';
import { useOperationStore } from '@/stores/operation.store';
import {IPagination} from "@/types/Global.type";
import {Asset} from "@/types/Asset";
import { useSnackbar } from "@/composables/useSnackbar";
import GlobalViewEditIsrsMap from "@/components/operations/global_isr/GlobalViewEditIsrsMap.vue";
const snackbar = useSnackbar();
const adminAssetStore = useAdminAssetStore();
const adminIsrStore = useAdminGlobalIsrStore();
const adminOperationStore = useOperationStore();
import {IGlobalIsr} from "@/types/GlobalIsr.type";
import {Operation} from "@/types/Operation";

const isLoadingSomething = ref(true);
const props = defineProps<{
  operationId: string | number | null | undefined
}>();

const operation = ref<Operation|null>(null);
const isrs = ref<IGlobalIsr[]>([]);
const assets = ref<Asset[]>([]);
const selectedIsr = ref<IGlobalIsr|null>(null);


const isrsPaginationState = ref<IPagination>({
  page: 1,
  perPage: 1000,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const selectedAssetId = ref<number|string|null>(null);

const fetchOperationalAssets = async () => {
  try {
    const response = await adminAssetStore.fetchAssets(
      isrsPaginationState.value,
      {
        operationId: operation.value?.id
      },
      ['platform'],
    );
    // The response is of type IAssetCollectionData which directly contains assets
    // @ts-ignore
    assets.value = response.data?.assets || [];
  } catch (error) {
    console.error(error);
  }
};

const fetchIsrs = async () => {
  try{
    selectedIsr.value = null;
    isLoadingSomething.value = true;
    const response = await adminIsrStore.fetchIsrs(isrsPaginationState.value, {
      operationId: props.operationId
    }) as IGlobalIsrCollectionResponse;
    isrs.value = (response?.data?.globalIsrs) ? response?.data?.globalIsrs : [] as IGlobalIsr[];
    isrsPaginationState.value = response?.data?.pagination as IPagination;
  } catch (e){
    console.error(e);
  } finally {
    isLoadingSomething.value = false;
  }
}

const createISR = async () => {
  if (!selectedAssetId.value) {
    snackbar.showSnackbar({
      text: 'Please select an asset first',
      color: 'error',
    });
    return;
  }

  const thisAsset = assets.value.find(asset =>
    asset.id.toString() === selectedAssetId.value?.toString()
  );

  if (!thisAsset) {
    snackbar.showSnackbar({
      text: 'Asset not found',
      color: 'error',
    });
    return;
  }

  const response = await adminIsrStore.createIsr({
    assetId: thisAsset.id,
    operationId: Number(props.operationId),
    label: thisAsset.name,
    type: 'point'
  });
  if(response.success) {
    snackbar.showSnackbar({
      text: 'ISR created successfully',
      color: 'success',
    });
  } else {
    snackbar.showSnackbar({
      text: 'ISR creation failed',
      color: 'error',
    });
  }
  await fetchIsrs();
};

const fetchOperation = async () => {
  try {
    if (!props.operationId) {
      snackbar.showSnackbar({
        text: 'Operation ID is required',
        color: 'error',
      });
      return;
    }
    const response = await adminOperationStore.getOperationById(props.operationId?.toString());

    operation.value = response.operation;
  } catch (error) {
    console.error(error);
  }
};

const deleteIsr = async (isrId: string | number) => {
  try {
    const confirmed = confirm('Are you sure you want to delete this ISR?');
    if (!confirmed) return;

    await adminIsrStore.deleteIsr(isrId.toString());
    await fetchIsrs();
    snackbar.showSnackbar({
      text: 'ISR deleted successfully',
      color: 'success',
    });
  } catch (error) {
    console.error(error);
  } finally {

  }
};


const getClass = (isrId: string | number) => {
  if (selectedIsr.value && selectedIsr.value?.id.toString() === isrId.toString()) {
    return 'bg-primary text-white';
  }
  return '';
};

onMounted(async () => {
  await fetchOperation();
  await fetchOperationalAssets();
  await fetchIsrs();
  isLoadingSomething.value = false;
});

</script>
