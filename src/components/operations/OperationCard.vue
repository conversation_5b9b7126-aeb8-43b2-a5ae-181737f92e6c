<template>
    <v-card class="" border flat v-if="operation">
        <v-list-item class="px-5">
            <template v-slot:prepend>
                <v-avatar color="surface-light" size="32">
                    <v-icon size="32" color="blue">mdi-map-marker</v-icon>
                </v-avatar>
            </template>
            <template v-slot:title class="pa-2">
                <h2>
                    {{ operation?.name }} - ID:
                    <strong class="text-blue">{{
                        operation?.designation
                    }}</strong>
                </h2></template
            >
            <template v-slot:subtitle>
                Location:
                <strong class="text-blue">{{ operation?.location }}</strong>
            </template>

            <template v-slot:append>
                <v-btn
                    class="text-none btn btn-sm"
                    color="primary"
                    text="Edit"
                    variant="outlined"
                    slim
                    :to="`/admin/operations/${operation?.id}/edit`"
                ></v-btn>
            </template>
        </v-list-item>
        <v-divider></v-divider>
        <v-card-text class="text-medium-emphasis pa-0">
            <v-row no-gutters>
                <v-col class="pa-4" cols="12">
                    <h3>Description</h3>
                    <p>{{ operation?.description }}</p>
                </v-col>
                <v-col class="" cols="12">
                    <v-expansion-panels class="pa-0" multiple>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >{{
                                                operationUsers.length
                                            }}
                                            Users</v-col
                                        >
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text
                                class="pa-0 ma-0 bg-grey-lighten-4"
                            >
                                <template v-slot:default>
                                    <ViewManageOperationUsers
                                        :key="
                                            getRefreshKey(
                                                operation?.id,
                                                'users',
                                            )
                                        "
                                        :operation-id="operation?.id as string"
                                        @operation-updated="
                                            (id) =>
                                                handleOperationUpdated(
                                                    id,
                                                    'users',
                                                )
                                        "
                                        :operationWithUsers="operation as Operation"
                                    />
                                </template>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >Assets</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0"
                                                    >Assets</span
                                                >
                                                <span v-else key="1"
                                                    >-0 Assets</span
                                                >
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                <template v-if="operation">
                                    <ViewManageOperationAssets
                                        :operationWithAssets="operation as Operation"
                                    />
                                </template>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >PIRs</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0"
                                                    >PIRs</span
                                                >
                                                <span v-else key="1"
                                                    >-0 PIRs</span
                                                >
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                //PIR component here
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        <v-expansion-panel class="pa-0 ma-0">
                            <v-expansion-panel-title>
                                <template v-slot:default="{ expanded }">
                                    <v-row no-gutters>
                                        <v-col
                                            class="d-flex justify-start"
                                            cols="4"
                                            >Map & Location</v-col
                                        >
                                        <v-col class="text-grey" cols="8"
                                            ><v-fade-transition leave-absolute>
                                                <span v-if="expanded" key="0">{{
                                                    operation?.location
                                                }}</span>
                                                <span v-else key="1">{{
                                                    operation?.location
                                                }}</span>
                                            </v-fade-transition>
                                        </v-col>
                                    </v-row>
                                </template>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text class="pa-0">
                                <EsriMap
                                    v-if="areaSelected"
                                    class=""
                                    :is-editing="false"
                                    :existing-coordinates="getAreaSelected"
                                    :draw-mode="'dot-single'"
                                />
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                    </v-expansion-panels>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>
<script setup lang="ts">
import { useSnackbar } from '@/composables/useSnackbar';
import type { Operation } from '@/types/Operation';
import { useOperationStore } from '@/stores/operation.store';
import { ref } from 'vue';
import ViewManageOperationUsers from '@/components/operations/users/ViewManageOperationUsers.vue';
import { useRefreshManager } from '@/composables/useRefreshManager';
import type { UserOperation } from '@/types/UserOperation';
import { IPoint } from '@/types/Global.type';
const { showSnackbar } = useSnackbar();
const areaSelected = ref<{ type: string; coordinates: number[] } | null>(null);

const operation = ref<Operation | null>(null) as Ref<Operation | null>;

const operationStore = useOperationStore();
const { triggerRefresh, getRefreshKey } = useRefreshManager({
    componentName: 'Operation',
    refreshKeys: ['assets', 'pirs', 'users'] as const,
});

const operationUsers = computed((): UserOperation[] => {
    return operation.value?.users || [];
});

const props = defineProps<{
    operationId: number | string;
}>();

const handleOperationUpdated = async (
    operationId: string,
    key: 'users' | 'assets' | 'pirs',
) => {
    if (!operationId) return;

    try {
        await triggerRefresh(operationId, key, fetchFullOperation);
        showSnackbar({
            text: 'Operation updated successfully',
            color: 'success',
        });
    } catch (error) {
        showSnackbar({
            text: 'Failed to update operation',
            color: 'error',
        });
    } finally {
    }
};

const fetchFullOperation = async () => {
    try {
        const operationId = props.operationId.toString();
        operation.value = (
            await operationStore.getOperationById(operationId, [
                'users',
                'assets',
                'pirs',
            ])
        ).operation as Operation;
        // @ts-ignore
        areaSelected.value = operation.value?.locationCoordinates;
    } catch (error) {
        console.error(error);
        showSnackbar({
            text: 'Failed to load operation',
            color: 'error',
        });
    } finally {
    }
};

onMounted(async () => {
    await fetchFullOperation();
});
const operationIdRef = toRef(props, 'operationId');

watch(operationIdRef, () => {
    fetchFullOperation();
});

const getAreaSelected = computed(() => {
    return areaSelected.value ? ([areaSelected.value] as IPoint[]) : null;
});
</script>
