<template>
    <div
        v-if="!isLoading"
        :key="currentOperationId"
        :class="`ml-6 rounded-lg d-flex justify-center flex-grow-0 px-3`"
    >
        <div class="pa-4 border-left-2 b-accent" v-if="isChangingOperation">
            <div
                v-if="!currentOperationId && !operations.length"
                key="no-operation"
                class="d-flex justify-center align-center ml-6 bg-grey-lighten-4 rounded-lg"
            >
                <span class="text-red ml-2"
                    >No operations found. Please
                    <router-link class="text-red" to="admin/operations/builder"
                        >create</router-link
                    >
                    one.</span
                >
            </div>
            <div v-else class="d-flex justify-start">
                <div class="pl-4 d-flex align-center">
                    <v-select
                        :items="items"
                        v-model="operationId"
                        label="Select Operation"
                        single-line
                        width="400"
                        class="mt-1 bg-primary"
                        density="compact"
                        hide-details
                    >
                    </v-select>
                </div>
                <div class="d-flex align-center ml-4">
                    <div class="d-inline-flex">
                        <v-btn
                            color="white"
                            text="SET"
                            variant="outlined"
                            slim
                            @click="setOperation"
                        ></v-btn>
                        <v-btn
                            color="white"
                            variant="text"
                            slim
                            text="cancel"
                            @click="cancelOperation"
                        ></v-btn>
                    </div>
                </div>
            </div>
        </div>
        <div
            v-else
            class="py-4 px-auto rounded-lg flex-0-1 justify-start border-left-2 b-accent flex-grow-0"
        >
            <span
                class="color-primary rounded-lg text-left font-weight-bold"
                v-if="operation"
            >
                <span
                    v-if="operation.location"
                    :class="`fi fi-${operation.location.toLowerCase()}`"
                ></span>
                {{ operation.name }} (<span class="text-secondary text-bold">{{
                    operation.designation
                }}</span
                >)
            </span>
            <v-icon
                v-if="operation && operation.isActive"
                class="color-primary"
            >
                mdi-airplane
            </v-icon>
	        <LumioStatusChip v-else :status="`inactive`" :full-width="false" />
            <v-btn
                color="white"
                variant="text"
                slim
                class="ma-0 ml-2"
                size="small"
                prepend-icon="mdi-swap-horizontal"
                @click="handleChangeOperation"
            >
                <template v-slot:prepend>
                    <v-icon class="color-primary">mdi-swap-horizontal</v-icon>
                    <span class="color-primary">Change</span>
                </template>
            </v-btn>
        </div>
    </div>
    <v-progress-circular
        v-if="isLoading"
        class="ml-6"
        color="#effa1a"
        indeterminate
    ></v-progress-circular>
</template>

<script setup lang="ts">



import { ref, computed } from 'vue';
// import { useRoute } from 'vue-router';
import { useOperationStore } from '@/stores/operation.store';
import type { Operation } from '@/types/Operation';
import { storeToRefs } from 'pinia';
import { useSnackbar } from '@/composables/useSnackbar';
// import router from '@/router';
// const route = useRoute();
const operationStore = useOperationStore();
const operations = ref<Operation[]>([]);
const isChangingOperation = ref(false);
const operationId = ref('');

const isLoading = ref(false);

const { operation, currentOperationId } = storeToRefs(operationStore);

const router = useRouter();
const { showSnackbar } = useSnackbar();


const items = computed(() => {
    return operations.value.map((operation) => ({
        title: `${operation.name} (${operation.designation}) - ${
            operation.isActive ? 'ACTIVE' : 'INACTIVE'
        }`,
        value: operation.id,
    }));
});

const setOperation = async () => {
    await operationStore.setOperationCurrent(operationId.value);
    isChangingOperation.value = false;
	router.replace('/home');
    //  router.replace(`/admin/operation`);
};

async function fetchOperations() {
    const { data } = await operationStore.fetchOperations();
    operations.value = data.operations;
}

onMounted(async () => {
    // if no operation is selected, fetch operations and set the first one as current
    if (!currentOperationId.value) {
        isLoading.value = true;
        await fetchOperations();
        // if no operations are found, push to the operations builder page
        if (!items.value.length && !currentOperationId.value) {
            router.push('/admin/operations/builder');
            showSnackbar({
                text: 'To proceed, please create an operation.',
                color: 'warning',
                timeout: 5000,
            });
        }

        if (items.value.length) {
            operationId.value = operations.value[0]?.id as string;
            await setOperation();
            showSnackbar({
                text: 'Operation set to ' + operations.value[0]?.name,
                color: 'success',
                timeout: 5000,
            });
        }
        isLoading.value = false;
    }
});

const handleChangeOperation = async () => {
    await fetchOperations();
    isChangingOperation.value = true;
};

const cancelOperation = () => {
    isChangingOperation.value = false;
};

//add watch for currentOperationId
watch(currentOperationId, (oldId, newId) => {
    if (currentOperationId.value) {
        setOperation();
    }
});
</script>
