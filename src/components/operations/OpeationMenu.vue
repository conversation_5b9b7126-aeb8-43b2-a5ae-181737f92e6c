<template>
    <v-list color="#1E1E1E" density="compact" v-if="currentOperation">
        <v-list-item
            v-for="item in opMenu"
            :key="item.title"
            :to="item.to"
            :value="item.title"
            :class="`${item.class} ml-2`"
            density="compact"
            active-class="text-secondary"
        >
            <template #prepend>
                <v-list-item-action start>
                    <v-icon color="secondary" size="18" class="ml-2">
                        {{ item.icon }}
                    </v-icon>
                </v-list-item-action>
            </template>

            <!-- Changed to use v-list-item-title properly -->
            <template #default>
                <v-list-item-title
                    :class="[
                        item.titleClass,
                        getRoutePath === item.to
                            ? 'text-secondary'
                            : 'text-white',
                    ]"
                >
                    {{ item.title }}
                </v-list-item-title>
            </template>
        </v-list-item>
    </v-list>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
const route = useRoute();
// const operation = ref<Operation>({} as Operation);
const operationStore = useOperationStore();

const { operation } = storeToRefs(operationStore);



const getRoutePath = computed(() => route.path);

const opMenu = computed(() => [
    {
        title: 'Home Board',
        subtitle: null,
        to: `/home`,
        icon: 'mdi-home',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: 'Task Board',
        subtitle: null,
        to: `/under-development/task-board`,
        icon: 'mdi-clipboard-list',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: 'Weather',
        subtitle: null,
        to: `/weather-board`,
        icon: 'mdi-weather-partly-snowy',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: '23 | Current',
        subtitle: null,
        to: `/under-development/current`,
        icon: 'mdi-flag',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: '25 | Plans',
        subtitle: null,
        to: `/under-development/plans`,
        icon: 'mdi-flag-outline',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: 'RCM',
        subtitle: null,
        to: `/admin/operation/rcm`,
        icon: 'mdi-airplane',
        class: 'ml-2 text-red-500',
        titleClass: 'text-body-2',
    },
    {
        title: 'All Source',
        subtitle: null,
        to: `/under-development/all-source`,
        icon: 'mdi-hexagon',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
    {
        title: 'Daily Update',
        subtitle: null,
        to: `/under-development/daily-update`,
        icon: 'mdi-calendar-check',
        class: 'ml-2',
        titleClass: 'text-body-2',
    },
]);

const currentOperation = computed(() => {
    if (!operation.value) {
        return null;
    }
    return operation.value;
});
</script>

<style scoped>
:deep(.v-list-item__spacer) {
    width: 0 !important;
    flex-grow: 0 !important;
}

.v-list-item {
    min-height: 35px !important;
    padding: 0 8px !important;
}

.v-list-group__items .v-list-item {
    padding-inline-start: 8px !important;
}

.v-list-item--active {
    background: rgba(238, 247, 63, 0.1);
}

.v-list-item:hover {
    background: rgba(238, 247, 63, 0.05);
}
</style>
