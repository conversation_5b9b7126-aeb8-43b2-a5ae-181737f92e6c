<template>
  <v-card class="lumio-card">
    <v-card-title>
      <h4>Mission Collection Washboard</h4>
    </v-card-title>
    <v-card-text>

		<template v-if="isLoading">
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
			<v-skeleton-loader
				type="table"
				:loading-text="`Loading Missions...`"
				:height="50"
				:loading="isLoading"
			></v-skeleton-loader>
		</template>
		<template v-else>
			<div v-if="missionsList && missionsList.length > 0">

				<v-data-table
					:headers="headers"
					:items="missionsList"
					:items-per-page="paginationState.perPage"
					:server-items-length="paginationState.total"
				>
					<template v-slot:item.actions="{ item }">
						<!--        <router-link :to="`/admin/operation/rcm?section=builder&missionId=${item.id}`" v-if="item.status !== 'active'">-->
						<!--          <v-btn-->
						<!--            variant="flat"-->
						<!--            color="primary"-->
						<!--            size="small"-->
						<!--            class="mr-2"-->
						<!--          >-->
						<!--            <v-icon>mdi-pencil</v-icon>-->
						<!--          </v-btn>-->
						<!--		</router-link>-->
						<v-btn
							v-if="item.id"
							variant="flat"
							size="small"
							class="mr-2"
							color="primary"
							label="Submit Report"
							@click="handleSubmitReportClick(item.id.toString())"
						>
							<v-icon>mdi-check</v-icon> Submit Report
						</v-btn>

					</template>
				</v-data-table>
			</div>
			<div v-else class="pa-4">
				<v-alert type="info" color="bg-gray-lightened-2" icon="mdi-information-outline" class="my-4">
					<div class="">
						<div>
							<h4>No Missions</h4>
							<p>You have no missions planned.</p>
						</div>
					</div>
				</v-alert>
			</div>

		</template>

    </v-card-text>
  </v-card>


			<v-dialog
				max-width="500" v-model="submitReportDialogBoxOpen"

				width="auto"
			>
		<SubmitReportDialogBox
			v-if="currentMissionReport"
			:missionId="currentMissionReport"
			:open="submitReportDialogBoxOpen"
			@close="submitReportDialogBoxOpen = false"
			@submit="submitReport"
		/>
		</v-dialog>


</template>


<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import {Mission} from "@/types/Mission";
import {DataTableHeader, IPagination} from "@/types/Global.type";
const missionStore = useMissionStore();
const props = defineProps<{
  currentOperationId: string | number | null
}>();

const emits = defineEmits(['refresh-operation']);

const paginationState = ref<IPagination>({
  page: 1,
  perPage: 30,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});
const missionsList = ref<Mission[]>([]);
const getMissions = async () => {
  try {
    const response = await missionStore.getMissions(
      paginationState.value,
      {
        operationId: props.currentOperationId?.toString(),
		  status: 'active'
      }
    );
    missionsList.value = response?.data?.missions
    paginationState.value = response?.data?.pagination

  } catch (error) {
    console.error(error);
  }
};
const currentMissionReport = ref<number|null>(null);
const isLoading = ref<boolean>(true);
const headers:DataTableHeader[] = [
  {
    title: 'Name',
    align: 'start',
    sortable: false,
    key: 'name',
  },
  {
    title: 'Description',
    align: 'start',
    sortable: false,
    key: 'description',
  },
  {
    title: 'Type',
    align: 'start',
    sortable: false,
    key: 'type',
  },
  {
    title: 'Classification',
    align: 'start',
    sortable: false,
    key: 'classification',
  },
  {
    title: 'Priority',
    align: 'start',
    sortable: false,
    key: 'priority',
  },
  {
    title: 'Status',
    align: 'start',
    sortable: false,
    key: 'status',
  },
  {
    title: 'Actions',
    align: 'start',
    sortable: false,
    key: 'actions',
  }
]

const submitReportDialogBoxOpen = ref<boolean>(false);

const handleSubmitReportClick = async (missionId:string) => {
  try {
	  	currentMissionReport.value = parseInt(missionId);
		submitReportDialogBoxOpen.value = true;
  } catch (error) {
	console.error(error);
  }
}

const submitReport = async () => {
  try {
	  //close dialog
	  submitReportDialogBoxOpen.value = false;
	  currentMissionReport.value = null;
	await getMissions();
  	emits('refresh-operation')
  } catch (error) {
	console.error(error);
  }
}

onMounted( async() => {
	isLoading.value = true;
  await getMissions();
  	isLoading.value = false;
});


</script>
