<template>
    <v-card class="lumio-card">
        <v-card-title>
            <h4>Collection Waitlist</h4>
            <v-btn
                v-if="getUserRole !== 'user'"
                variant="flat"
                color="primary"
                @click="approve(null)"
                append-icon="mdi-arrow-right"
            >
                Approve All
            </v-btn>
            <span style="color: red; font-size: 12px" v-else>
                Pending admin approval
            </span>
        </v-card-title>
        <v-card-text>
            <div v-if="missionsList && missionsList.length > 0" class="ma-4">
                <v-data-table
                    v-model:items-per-page="paginationState.perPage"
                    :headers="headers"
                    :items="missionsList"
                    :items-length="paginationState.total"
                    fixed-footer
                    fixed-header
                    style="max-height: 600px; overflow-y: auto"
                    @update:options="getMissions"
                    @update:page="handlePage"
                >
                    <template v-slot:item.name="{ item }">
                        <!-- <router-link :to="`/admin/operation/rcm?missionId=${item.id}#builder`" v-if="item.status
            !== 'active'"> -->
                        <v-btn
                            variant="text"
                            color="primary"
                            size="small"
                            class="mr-2"
                            prepend-icon="mdi-eye"
                            @click="openSummaryDialog(item)"
                        >
                            {{ item.name }}
                        </v-btn>
                        <!-- </router-link> -->
                    </template>
                    <template v-slot:item.actions="{ item }">
                        <v-btn
                            variant="flat"
                            color="secondary"
                            size="small"
                            class="mr-2"
                            prepend-icon="mdi-chevron-left"
                            @click="goToEditMission(item)"
                        >
                            EDIT
                        </v-btn>
                        <v-btn
                            variant="flat"
                            color="primary"
                            size="small"
                            class="mr-2"
                            @click="approve(item.id)"
                            append-icon="mdi-chevron-right"
                        >
                            Approve
                        </v-btn>
                    </template>
                </v-data-table>
            </div>
            <div v-else class="text-center">
                <v-alert type="info" class="ma-4">
                    <div class="d-flex align-center">
                        <div>
                            <h1>No Missions</h1>
                            <p>You have no missions to approve.</p>
                        </div>
                    </div>
                </v-alert>
            </div>
        </v-card-text>
    </v-card>

    <v-dialog v-model="summaryDialog" max-width="1200">
        <v-card prepend-icon="mdi-tooltip-edit-outline" title="Summary">
            <v-card-text>
                <ViewSummaryModal
                    :mission-details="selectedMission"
                    :operation-id="currentOperationId"
                    @close-summary-modal="summaryDialog = false"
                    @edit-mission="goToEditMission"
                />
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import { Mission } from '@/types/Mission';
import { DataTableHeader, IPagination } from '@/types/Global.type';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '@/stores/auth';
// import { useAccessControl } from '@/composables/useAccessControl';

const missionStore = useMissionStore();
const router = useRouter();
const props = defineProps<{
    currentOperationId: string | number | null;
}>();
const { getUserRole } = storeToRefs(useAuthStore());
// const { hasAccess } = useAccessControl();
//const emits = defineEmits(['refresh-operation']);
const emits = defineEmits(['mission-approved', 'refresh-operation']);

const summaryDialog = ref(false);
const selectedMission = ref<Mission | null>(null);

const openSummaryDialog = (mission: Mission) => {
    selectedMission.value = mission;
    summaryDialog.value = true;
};

const goToEditMission = (mission: Mission) => {
    selectedMission.value = mission;
    router.push(`/admin/operation/rcm?missionId=${mission.id}#builder`);
};

const handlePage = (page: number) => {
    paginationState.value.page = page;
    getMissions();
};

const paginationState = ref<IPagination>({
    page: 1,
    perPage: 10,
    pages: 1,
    total: 0,
    sortBy: 'createdAt',
    orderBy: 'id',
});

const missionsList = ref<Mission[]>([]);

const getMissions = async () => {
    try {
        const response = await missionStore.getMissions(paginationState.value, {
            operationId: props.currentOperationId?.toString(),
            status: 'pending_approval',
        });
        missionsList.value = response?.data?.missions;
        paginationState.value = response?.data?.pagination;
    } catch (error) {
        console.error(error);
    }
};
const headers: DataTableHeader[] = [
    {
        title: 'Name',
        align: 'start',
        sortable: false,
        key: 'name',
    },
    {
        title: 'Description',
        align: 'start',
        sortable: false,
        key: 'description',
    },
    {
        title: 'Type',
        align: 'start',
        sortable: false,
        key: 'type',
    },
    {
        title: 'Classification',
        align: 'start',
        sortable: false,
        key: 'classification',
    },
    {
        title: 'Priority',
        align: 'start',
        sortable: false,
        key: 'priority',
    },
    {
        title: 'Status',
        align: 'start',
        sortable: false,
        key: 'status',
    },
    {
        title: 'Actions',
        align: 'start',
        sortable: false,
        key: 'actions',
    },
];

onMounted(getMissions);

const approve = async (id: null | number | string) => {
    try {
        const approveIds = !id
            ? missionsList.value.map((m) => m.id)
            : [id as number];
        for (const i of approveIds) {
            await missionStore.updateMission(Number(i), { status: 'planned' });
            // remove from list
            missionsList.value = missionsList.value.filter((m) => m.id !== i);
            emits('mission-approved', i);
            emits('refresh-operation');
        }
    } catch (error) {}
};
</script>
