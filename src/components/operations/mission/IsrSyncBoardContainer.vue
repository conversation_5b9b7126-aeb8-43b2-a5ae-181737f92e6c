<template>
	<v-card class="lumio-card">
		<v-card-title>
			<h4>ISR Sync Board</h4>
			<div>
				<v-tabs v-model="isrSyncTab" class="mb-4 w-full" grow>
					<v-tab key="24" value="24" variant="elevated" selected-class="bg-primary">
						24 HRS
					</v-tab>
					<v-tab key="48" value="48" variant="elevated" selected-class="bg-primary">
						48 HRS
					</v-tab>
					<v-tab key="72" value="72" variant="elevated" selected-class="bg-primary">
						72 HRS
					</v-tab>
					<v-tab key="custom" value="custom" variant="elevated" selected-class="bg-primary">
						CUSTOM
					</v-tab>
				</v-tabs>
			</div>
		</v-card-title>
		<v-card-text class="">
			<v-row>
				<v-col cols="12" md="6" class="py-0  ">
					<v-card  class="mx-3 mb-2 "
							variant="outlined"
					>
						<v-card-subtitle class="px-3 py-2 font-weight-bold border-b bg-grey-lighten-5">
							Main Effort
						</v-card-subtitle>
						<v-card-text class="px-3 py-2 bg-white">
							<em>{{currentOperation?.config?.mainEfforts}}</em>
						</v-card-text>
					</v-card>
				</v-col>
				<v-col cols="12" md="6" class="py-0  ">
					<v-card  class="mx-3 mb-2 "
							 variant="outlined"
					>
						<v-card-subtitle class="px-3 py-2  font-weight-bold border-b bg-grey-lighten-5">
							Supporting Effort
						</v-card-subtitle>
						<v-card-text class="px-3 py-2">
							<em>{{currentOperation?.config?.supportingEfforts}}</em>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>
			<v-row>
				<v-col cols="12" class="py-0">
					<v-tabs-window v-model="isrSyncTab">
						<template v-if="currentOperationId">
							<v-tabs-window-item key="24" value="24">
								<div data-panel-id="24">
									<IsrSyncBoardPanel
										:key="`panel-24-${refreshCounter}`"
										:current-operation-id="currentOperationId"
										type="24"
									/>
								</div>
							</v-tabs-window-item>
							<v-tabs-window-item key="48" value="48">
								<div data-panel-id="48">
									<IsrSyncBoardPanel
										:key="`panel-48-${refreshCounter}`"
										:current-operation-id="currentOperationId"
										type="48"
									/>
								</div>
							</v-tabs-window-item>
							<v-tabs-window-item key="72" value="72">
								<div data-panel-id="72">
									<IsrSyncBoardPanel
										:key="`panel-72-${refreshCounter}`"
										:current-operation-id="currentOperationId"
										type="72"
									/>
								</div>
							</v-tabs-window-item>
							<v-tabs-window-item key="custom" value="custom">
								<div data-panel-id="custom">
									<IsrSyncBoardPanel
										:key="`panel-custom-${refreshCounter}`"
										:current-operation-id="currentOperationId"
										type="custom"
									/>
								</div>
							</v-tabs-window-item>
						</template>
					</v-tabs-window>
				</v-col>
			</v-row>


		</v-card-text>
	</v-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import IsrSyncBoardPanel from "@/components/operations/mission/IsrSyncBoardPanel.vue";
import {Operation} from "@/types/Operation";
import {useOperationStore} from "@/stores/operation.store";

const isrSyncTab = ref('24');
const refreshCounter = ref(0);
const currentOperation = ref<Operation | null>(null);
const operationStore = useOperationStore();


const props = defineProps<{
	currentOperationId: string | number | null
}>();

const getCurrentOperation = async () => {
	try {
		const response = await operationStore.getOperationById(
			String(props.currentOperationId),
			['isrTracks'],
		);
		currentOperation.value = response.operation;
	} catch (error) {}
};

onMounted(() => {
	if (props.currentOperationId) {
		getCurrentOperation();
	}
});

// Increment the counter when tab changes to force re-render
watch(isrSyncTab, () => {
	refreshCounter.value++;
});
</script>
