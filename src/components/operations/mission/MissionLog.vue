<template>
	<v-card class="lumio-card">
		<v-card-title>
			<h4>Mission Log</h4>
		</v-card-title>
		<v-card-text>
			<div class="text-center pa-4">
				<v-table>
					<thead>
					<tr>
						<th
							v-for="header in headers"
							:key="header.key"
							class="text-center"
						>
							{{ header.title }}
						</th>
					</tr>
					</thead>
					<tbody>
					<tr
						v-for="item in getMissionListWithoutPlanned"
						:key="item.id?.toString() || ''"
					>
						<td>
							<LumioStatusChip :status="item.status" />
						</td>
						<td>
							<LumioPriorityChip :priority="item.priority" />
						</td>
						<td>
							{{ item.name }} ({{ item.designation }})
						</td>
						<td>
							{{ dayjs(item.startAt).format('MMM-D, YYYY  HH:mm') }}
							<v-icon>mdi-minus</v-icon>
							{{ dayjs(item.endAt).format('MMM-D, YYYY  HH:mm') }}
						</td>
						<td>-</td>
						<td>-</td>
						<td>{{ item.type }}</td>
						<td>
							<v-btn
								v-if="item.id"
								variant="flat"
								color="primary"
								size="small"
								@click="handleViewReportClick(parseInt(item.id.toString()))"
								:disabled="!item.id"
							>
								<v-icon>mdi-file-document</v-icon> View Report
							</v-btn>
						</td>
						<td>
							<v-icon v-if="item.status === 'completed'">mdi-check</v-icon>
							<v-icon v-else>mdi-close</v-icon>
						</td>
						<td>
							{{ item.informationRequirements.filter(ir => ir.isAnswered).length }} / {{
								item.informationRequirements.length
							}}
						</td>
						<td>
							<v-btn
								size="small"
								color="red"
								variant="text"
								@click="handleKickBackToWashboard(item.id)"
								:disabled="!item.id"
							>
								<v-icon>mdi-arrow-left</v-icon>
								Send Back
								<v-icon>mdi-lock-outline</v-icon>
							</v-btn>
						</td>
					</tr>
					</tbody>
				</v-table>
			</div>
		</v-card-text>
	</v-card>

	<v-dialog max-width="500" v-model="viewReportDialog" width="auto">
		<v-card class="lumio-card">
			<v-card-title>
				<h4>Mission Report</h4>
			</v-card-title>
			<v-card-text>
				<h3>Submitted Report</h3>
				<p>{{ currentReportText }}</p>
			</v-card-text>
			<v-card-text>
				<h3>Answered Information Requirements</h3>
				<ul>
					<li v-for="ir in answeredIrs" :key="ir">
						{{ ir }}
					</li>
				</ul>
			</v-card-text>
			<v-card-actions class="d-flex justify-end">
				<v-btn variant="text" @click="handleClose">Close</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import { Mission } from "@/types/Mission";
import { IPagination } from "@/types/Global.type";
import dayjs from "dayjs";

const headers = computed(() => [
	{ title: 'Status', key: 'status' },
	{ title: 'Priority', key: 'priority' },
	{ title: 'Mission', key: '' },
	{ title: 'Dates', key: 'date' },
	{ title: 'Location', key: 'location' },
	{ title: 'Asset', key: 'asset' },
	{ title: 'Type', key: 'type' },
	{ title: 'View Report', key: 'report' },

	{ title: 'Op Outcome', key: 'opOutcome' },
	{ title: 'IR Answered', key: 'intOutcome' },
	{ title: 'Send To Washboard', key: 'kickback' },
]);

const missionStore = useMissionStore();
const props = defineProps<{ currentOperationId: string | number | null }>();

const paginationState = ref<IPagination>({
	page: 1,
	perPage: 30,
	pages: 1,
	total: 0,
	sortBy: 'createdAt',
	orderBy: 'id',
});
const missionsList = ref<Mission[]>([]);
const currentReportText = ref<string | null>(null);
const answeredIrs = ref<string[] | number[]>([]);

const getMissions = async () => {
	try {
		const response = await missionStore.getMissionsLog(
			paginationState.value,
			{ operationId: props.currentOperationId?.toString() },
			['informationRequirements']
		);
		console.log('response', response);
		missionsList.value = response?.data?.missions;
		paginationState.value = response?.data?.pagination;
	} catch (error) {
		console.error(error);
	}
};

const isLoading = ref<boolean>(true);

const getMissionListWithoutPlanned = computed(() => {
	return missionsList.value.filter(
		(m) => m.status !== 'planned' && m.status !== 'pending_approval' && m.status !== 'active'
	);
});

const emits = defineEmits(['refresh-operation']);

const viewReportDialog = ref<boolean>(false);

const handleViewReportClick = async (missionId: number | null) => {
	if (missionId === null) return;
	viewReportDialog.value = true;
	const currentMission = missionsList.value.find((m) => m.id === missionId);
	currentReportText.value = currentMission?.reportTypeConfiguration?.submittedReport || '';
	answeredIrs.value = currentMission?.informationRequirements
		? currentMission.informationRequirements.filter((ir) => ir.isAnswered).map((ir) => ir.id.toString())
		: [];
};

const handleClose = () => {
	viewReportDialog.value = false;
	currentReportText.value = null;
	answeredIrs.value = [];
};

const handleKickBackToWashboard = async (missionId: number | string | null | undefined) => {
	try {
		if (!missionId || typeof missionId !== 'number') return;

		const confirmClick = confirm("Are you sure you want to send this mission back to the washboard?");
		if (!confirmClick) return;

		await missionStore.updateMission(missionId, { status: 'active' });
		await getMissions();
		emits('refresh-operation');
	} catch (error) {
		console.error(error);
	}
};

onMounted(async () => {
	isLoading.value = true;
	await getMissions();
	isLoading.value = false;
});
</script>
