<template>
	<v-card class="lumio-card" v-if="mission">
		<v-card-title>
			<h4>Submit Report: {{mission.name}}</h4>
		</v-card-title>
		<v-card-text>
			<v-textarea
				v-model="missionReportText"
				label="Mission Report"
				rows="5"
				auto-grow
			></v-textarea>
			<div v-for="ir in mission.informationRequirements" :key="ir.id">
				<v-checkbox
					:disabled="ir.isAnswered"
					v-model="answeredIrs"
					:value="ir.id"
					:label="`IR - ${ ir.irNumber }}: ${ir.informationRequirement}`"
				></v-checkbox>
			</div>
			<v-select
				v-model="missionStatus"
				:items="Object.values(MissionStatus)"
				label="Mission Status"
			></v-select>

		</v-card-text>
		<v-card-actions class="d-flex justify-end">
			<v-btn
				variant="text"
				@click="$emit('close')">Close</v-btn>
			<v-btn
				variant="flat"
				color="primary"
				@click="submitReport">Submit Report</v-btn>
		</v-card-actions>
	</v-card>
	<v-skeleton-loader v-else></v-skeleton-loader>

</template>


<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import { useAdminIRStore} from "@/stores/admin/ir.store";
import {Mission, MissionStatus} from "@/types/Mission";
import { ref, onMounted } from 'vue';

const missionStore = useMissionStore();
const adminIRStore = useAdminIRStore();
const answeredIrs = ref<number[]>([]);
const missionReportText = ref<string>("");
const missionStatus = ref<MissionStatus>(MissionStatus.ACTIVE);

const mission = ref<Mission|null>(null);

const emits = defineEmits(['close', 'submit']);
const props = defineProps<{
	missionId: number;
}>();

const fetchMission = async (id: number) => {
	try {
		const response = await missionStore.getMissionById(id,  ['informationRequirements'] );
		mission.value = response?.data?.mission;
	} catch (error) {
		console.error(error);
	}
};

const submitReport = async () => {
	try {
		if(!mission.value || !mission.value.id) return;
		const response = await missionStore.updateMission(parseInt(mission.value.id.toString()), {
			reportTypeConfiguration: {
				reportTypes: mission.value?.reportTypeConfiguration.reportTypes,
				submittedReport: missionReportText.value,
			},
			status: missionStatus.value
		});

		//now for each answered ir, we need to update the status of the IR to answered
		for (const irId of answeredIrs.value) {
			await adminIRStore.updateIr(irId.toString(), {
				isAnswered: true,
			});
		}

		emits('submit', response?.data?.mission);
	} catch (error) {
		console.error(error);
	}
};

onMounted(async () => {
	await fetchMission(props.missionId);
	// Populate missionReportText with the current report
	missionReportText.value = mission.value?.reportTypeConfiguration?.submittedReport
		? mission.value?.reportTypeConfiguration?.submittedReport
		: '';
	const currentMissionStatus = mission.value?.status as MissionStatus;
	// Check if currentMissionStatus is legit in MissionStatus
	if (currentMissionStatus && Object.values(MissionStatus).includes(currentMissionStatus)) {
		missionStatus.value = currentMissionStatus;
	} else {
		missionStatus.value = MissionStatus.ACTIVE;
	}
});
</script>
