<template>
  <div v-if="isLoadingSomething">

  </div>
  <div v-else>
    <v-row v-if="mission">
      <v-col cols="3">
        <div class="d-flex">
          <v-select
            v-model="addGlobalIsrsIds"
            label="Global ISRs"
            multiple
            chips
            item-value="id"
            :items="globalIsrs"
            item-title="label"
          >
          </v-select>
          <v-btn
            @click="addGlobalIsr"
            flat
            class="ma-0 pa-0 flex-grow-1"
            >
            <v-icon>mdi-plus</v-icon>
            Assign
          </v-btn>
        </div>
        <v-divider></v-divider>
        <div v-if="assets" class="d-flex justify-start align-center mb-2 ">
          <v-select
            density="compact"
            hide-details
            class=" flex-grow-1"
            label="ISR Assets"
            :items="assets"
            v-model="selectedAssetId"
            item-title="name"
            item-value="id">
          </v-select>
          <v-btn
            @click="createISR"
            flat
            class="ma-0 pa-0 flex-grow-1"
            v-if="selectedAssetId">
            <v-icon>mdi-plus</v-icon>
            ISR Track
          </v-btn>
        </div>
        <v-divider></v-divider>

        <ul>
          <li v-for="isr in isrs" :key="isr.id" :class="getClass(isr.id)">
            <div class="d-flex flex-row">
              <div class="flex-grow-1 ma-2 cursor-pointer" @click="viewIsr(isr.id)">
                {{isr.label}} <span v-if="isr.isGlobal">(global)</span> <br>
                <span v-if="isr.ltiovDate"  class="ml-2">
                  LTIOV {{ getDate(isr.ltiovDate.toString()) }}
                </span>
              </div>
              <div class="flex-grow-0" v-if="!selectedIsr">
                <v-btn variant="text" @click="editIsr(isr.id)" color="primary">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn variant="text"  @click="deleteIsr(isr.id)" color="red">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </div>
              <div class="flex-grow-0" v-if="selectedIsr && selectedIsr.id === isr.id">
                <v-btn variant="tonal" color="secondary" @click="selectedIsr = null">
                  cancel
                </v-btn>
              </div>
            </div>
          </li>
        </ul>
      </v-col>
      <v-col cols="9">
        <template v-if="selectedIsr && !previewIsr">
          <ViewEditIsrsMap
            :isr="selectedIsr"
            :readonly="false"
            :is-preview="false"
            :key="selectedIsr.id+'map'"
            @re-fetch-isrs="fetchIsrs" />
        </template>
        <template v-if="previewIsr">
          <ViewEditIsrsMap
            :is-preview="true"
            :readonly="true"
            :isr="previewIsr"
            :key="previewIsr.id+'map'"
             />
        </template>
        <template v-else>
          <div class="text-center mt-10" v-if="!selectedIsr && !previewIsr">
            <v-alert type="info" variant="outlined" class="text-body-1">
              No ISR selected
            </v-alert>
          </div>
        </template>
      </v-col>
    </v-row>
  </div>

</template>
<script setup lang="ts">

import { ref } from 'vue';
import { useAdminAssetStore } from '@/stores/admin/asset.store';
import {IIsrCollectionResponse, useAdminIsrStore} from '@/stores/admin/isr.store';
import {useAdminGlobalIsrStore} from "@/stores/admin/globalIsr.store";
import { useMissionStore } from '@/stores/admin/mission.store';
import {IPagination} from "@/types/Global.type";
import {Asset} from "@/types/Asset";
import { useSnackbar } from "@/composables/useSnackbar";
const snackbar = useSnackbar();
const adminAssetStore = useAdminAssetStore();
const adminIsrStore = useAdminIsrStore();
const adminGlobalIsrStore = useAdminGlobalIsrStore();
const adminMissionStore = useMissionStore();
import {Isr} from "@/types/Isr.type";
import { IGlobalIsr } from "@/types/GlobalIsr.type";
import {Mission} from "@/types/Mission";

const isLoadingSomething = ref(true);

const previewIsr = ref<Isr|null>(null);
const props = defineProps<{
  missionId: string | number | null | undefined
}>();

const addGlobalIsrsIds = ref<string[]>([]);

const mission = ref<Mission|null>(null);
const isrs = ref<Isr[]>([]);
const globalIsrs = ref<IGlobalIsr[]>([]);
const assets = ref<Asset[]>([]);
const selectedIsr = ref<Isr|null>(null);


const isrsPaginationState = ref<IPagination>({
  page: 1,
  perPage: 1000,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const globalIsrsPagiantionState = ref<IPagination>({
  page: 1,
  perPage: 1000,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const selectedAssetId = ref<number|string|null>(null);

// const assetsList = computed(() => {
//   return assets.value.map((asset) => ({
//     label: asset.name,
//     value: asset.id,
//   }));
// });

const fetchOperationalAssets = async () => {
  try {
    const response = await adminAssetStore.fetchAssets(
      isrsPaginationState.value,
      {
        operationId: mission.value?.operationId
      },
      ['platform'],
    );
    // The response is of type IAssetCollectionData which directly contains assets
    // @ts-ignore
    assets.value = response.data?.assets || [];
  } catch (error) {
    console.error(error);
  }
};

//
// const filteredAssets = computed(() => {
//   const extractIsrsAssetIds = isrs.value.map(isr => isr.assetId);
//   return assets.value.filter(asset => !extractIsrsAssetIds.includes(asset.id));
// });

const fetchGlobalIsrs = async() => {
  try{
    if(!mission.value) return null;
    const response = await adminGlobalIsrStore.fetchIsrs(globalIsrsPagiantionState.value, {
      operationId: mission.value.operationId
    })

    globalIsrs.value = response.data?.globalIsrs as IGlobalIsr[];
    if(response.data?.pagination){
      globalIsrsPagiantionState.value = response.data.pagination as IPagination
    }
  } catch (e){
    console.error(e);
  }

}

const fetchIsrs = async () => {
  try{
    selectedIsr.value = null;
    isLoadingSomething.value = true;
    const response = await adminIsrStore.fetchIsrs(isrsPaginationState.value, {
      missionId: props.missionId
    }) as IIsrCollectionResponse;
    isrs.value = (response?.data?.isrs) ? response?.data?.isrs : [] as Isr[];
    isrsPaginationState.value = response?.data?.pagination as IPagination;
  } catch (e){
    console.error(e);
  } finally {
    isLoadingSomething.value = false;
  }
}

const createISR = async () => {
  if (!selectedAssetId.value) {
    snackbar.showSnackbar({
      text: 'Please select an asset first',
      color: 'error',
    });
    return;
  }

  const thisAsset = assets.value.find(asset =>
    asset.id.toString() === selectedAssetId.value?.toString()
  );

  if (!thisAsset) {
    snackbar.showSnackbar({
      text: 'Asset not found',
      color: 'error',
    });
    return;
  }

  const response = await adminIsrStore.createIsr({
    assetId: thisAsset.id,
    missionId: Number(props.missionId),
    label: thisAsset.name,
    type: 'point'
  });
  if(response.success) {
    snackbar.showSnackbar({
      text: 'ISR created successfully',
      color: 'success',
    });
  } else {
    snackbar.showSnackbar({
      text: 'ISR creation failed',
      color: 'error',
    });
  }
  await fetchIsrs();
};


const fetchMission = async () => {
  try {
    if (!props.missionId) {
      snackbar.showSnackbar({
        text: 'Mission ID is required',
        color: 'error',
      });
      return;
    }
    const response = await adminMissionStore.getMissionById(props.missionId?.toString());
    mission.value = response.data.mission;
  } catch (error) {
    console.error(error);
  }
};

const deleteIsr = async (isrId: string | number) => {
  try {
    const confirmed = confirm('Are you sure you want to delete this ISR?');
    if (!confirmed) return;

    await adminIsrStore.deleteIsr(isrId.toString());
    await fetchIsrs();
    snackbar.showSnackbar({
      text: 'ISR deleted successfully',
      color: 'success',
    });
  } catch (error) {
    console.error(error);
  } finally {

  }
};


const addGlobalIsr = async () => {
  try {

    await adminMissionStore.addGlobalIsrToMission(String(props.missionId), addGlobalIsrsIds.value);
    //show success snackbar
    snackbar.showSnackbar({
      text: 'Global ISRs added successfully',
      color: 'success',
    });
    addGlobalIsrsIds.value = [];
    await fetchIsrs();
  } catch (error) {
    console.error(error);
  }
}

const viewIsr = async(id: string | number) => {
  selectedIsr.value = null;
  previewIsr.value = isrs.value.find(isr => isr.id.toString() === id.toString()) as Isr;
}

const editIsr = async(id: string | number) => {
  previewIsr.value = null;
  selectedIsr.value = isrs.value.find(isr => isr.id.toString() === id.toString()) as Isr;
}

const getClass = (isrId: string | number) => {
  //check if previewing
  if (previewIsr.value && previewIsr.value?.id.toString() === isrId.toString()) {
    return 'bg-info text-white text-bold';
  }
  if (selectedIsr.value && selectedIsr.value?.id.toString() === isrId.toString()) {
    return 'bg-primary text-white';
  }
  return '';
};

const getDate = (date: string) => {
  return new Date(date).toLocaleString('en-US', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

onMounted(async () => {
  await fetchMission();
  await fetchOperationalAssets();
  await fetchIsrs();
  await fetchGlobalIsrs();
  isLoadingSomething.value = false;
});

</script>
