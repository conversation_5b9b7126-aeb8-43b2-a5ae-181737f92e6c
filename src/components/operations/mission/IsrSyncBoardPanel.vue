<template>
    <div v-if="isPanelLoading" class="d-flex justify-center pa-5" :key="type">
        <v-progress-circular
            :width="3"
            :size="50"
            color="primary"
            indeterminate
        ></v-progress-circular>
    </div>
    <div v-else class="isr-panel " :data-panel-id="type">
        <v-row
            v-if="currentOperation && currentOperation.id"
            class="ma-0 pa-0"
        >
            <v-col cols="10" class="map-container ma-0 pa-0">
                <EsriMapViewer
                    :key="esriMApKey"
                    v-if="currentOperation && currentOperation.id && isVisible"
                    :map-items="parseMapItems"
                    :operation="currentOperation"
                    :centerCoordinates="getCenterCoordinates"
                    :zoom="getOperationZoom"
					:max-height="400"
                ></EsriMapViewer>
            </v-col>
            <v-col cols="2" class="px-0 py-0 my-0">
				<v-card class="mx-2" v-if="props.type === 'custom'">
					<v-card-title class="px-3 py-1">
						Custom Dates
					</v-card-title>
					<v-card-text class="pl-3">
						<div class="py-0 pr-2  ">
							<v-label class="text-xs">
								<span class="px-2">Start Date</span>
							</v-label>
							<LumioDateTimePicker
								:label="`Start Date & Time`"
								v-model="isrSyncStartDate"
							>
							</LumioDateTimePicker>
						</div>
						<div class="py-0 pr-2  ">
							<v-label class="text-xs">
								<span class="px-2">End Date</span>
							</v-label>
							<LumioDateTimePicker
								:label="`End Date & Time`"
								v-model="isrSyncEndDate"
								:min-date-time="isrSyncStartDate"
							>
							</LumioDateTimePicker>
						</div>
					</v-card-text>
					<v-card-actions>
						<v-btn @click="updateTimeline" color="secondary"  variant="flat" class="w-100">
							View Timeline
						</v-btn>
					</v-card-actions>
				</v-card>

				<v-card class="ma-2">
					<v-card-title class="px-3 py-1">
						Filters
					</v-card-title>
					<v-card-text class="pl-3">
						<div class="d-flex flex-wrap">

							<v-switch
								v-model="selectedItems"
								density="compact"
								color="primary"
								label="NAIs"
								value="nais"
								class="mx-2"
								hide-details
							></v-switch>
							<v-switch
								v-model="selectedItems"
								density="compact"
								color="primary"
								label="TAIs"
								value="tais"
								class="mx-2"
								hide-details></v-switch>
							<v-switch
								v-model="selectedItems"
								density="compact"
								color="primary"
								label="ISR Tracks"
								value="isrs"
								class="mx-2"
								hide-details></v-switch>
							<v-switch
								v-model="selectedItems"
								density="compact"
								color="primary"
								label="Missions"
								value="mission"
								class="mx-2"
								hide-details></v-switch>
						</div>



					</v-card-text>
				</v-card>
            </v-col>
        </v-row>
		<v-row class="ma-0 mt-2">

		</v-row>
        <v-row class="ma-0">
            <v-col
                cols="12"
                class=""
                v-if="ongoingMissions && ongoingMissions.length > 0"
            >
                <template v-if="isTimeLineloading">
                    <v-skeleton-loader
                        class="mx-auto"
                        :loading="isTimeLineloading"
                        :height="50"
                        width="100%"
                        transition="fade-transition"
                    ></v-skeleton-loader>
                </template>
                <template v-else>
					<LumioSyncTimeline
						:hours-to-show="getCustomTimeFrame"
						:items="getTimelineItems"
						:show-current-time="true"
						:timeline-start="isrSyncStartDate"
					></LumioSyncTimeline>

				</template>
            </v-col>
			<v-col
				v-else
				class="ml-2 pa-4 mb-10"
				cols="9">
				<v-alert
					class="mx-auto pa-3 bg-secondary"
					border="start"
					:height="50"
					>
						<h3 class="ml-3">No ISR Syncs Found</h3>
				</v-alert>
			</v-col>
        </v-row>
    </div>
</template>

<script setup lang="ts">
import 'vue-timeline-chart/style.css';
import {
    onMounted,
    ref,
    computed,
    watch,
    nextTick,
    onBeforeUnmount,
} from 'vue';
import { useOperationStore } from '@/stores/operation.store';
import { Mission } from '@/types/Mission';
import { Operation } from '@/types/Operation';
import { parseMapItemsHelper } from '@/composables/misc.helper';
import { Aoi } from '@/types/Aoi.type';
import LumioSyncTimeline from '@/components/elements/LumioSyncTimeline.vue';
import LumioDateTimePicker from '@/components/elements/LumioDateTimePicker.vue';
import { IIsrTrack } from '@/types/IsrTrack.type';
import { TimelineItem } from '@/types/Global.type';
import { Asset } from '@/types/Asset';

const operationStore = useOperationStore();

const isrSyncStartDate = ref<Date>(new Date());
const isrSyncEndDate = ref<Date>(new Date(Date.now() + 24 * 60 * 60 * 1000));

const props = defineProps<{
    currentOperationId: string | number | null;
    type: string;
}>();
const isTimeLineloading = ref(true);
const isPanelLoading = ref(true);
const ongoingMissions = ref<any[]>([]);
const currentOperation = ref<Operation | null>(null);
const esriMApKey = ref(new Date().getTime());
const isVisible = ref(false);
let observer: IntersectionObserver | null = null;

const step = ref<number>(1);

const collectedAois = ref<Aoi[]>([]);
const collectedIsrTracks = ref<IIsrTrack[]>([]);

const selectedItems = ref<string[]>(['tais', 'isrs', 'nais', 'mission']);

const todayTimeStamp = new Date().getTime();

const getCurrentOperation = async () => {
    try {
        const response = await operationStore.getOperationById(
            String(props.currentOperationId),
            ['isrTracks'],
        );
        currentOperation.value = response.operation;
    } catch (error) {}
};

const getIntervals = computed(() => {
    if (props.type === '72') {
        return ['4', '6', '12', '24'];
    }
    if (props.type === '48') {
        return ['2', '4', '6', '12'];
    }
    if (props.type === 'custom') {
        return ['4', '5', '6', '12', '24'];
    }
    return ['1', '4', '6'];
});

const getOperationZoom = computed(() => {
    if (!currentOperation.value) return 10;
    return currentOperation.value.zoom;
});

const getSync = async () => {
    try {
        let response;
        if (props.type === 'custom') {

            // convert startDate to currentTimeStamp
            // const timeStamp = dayjs(isrSyncStartDate.value).unix();
			const timeStamp = new Date(isrSyncStartDate.value).getTime();
			console.log('custom', isrSyncStartDate.value, isrSyncEndDate.value, todayTimeStamp, timeStamp);
            response = await operationStore.getISRSync(
                String(props.currentOperationId),
                String(props.type),
                String(timeStamp),
                String(isrSyncEndDate.value),
            );
        } else {
            response = await operationStore.getISRSync(
                String(props.currentOperationId),
                String(props.type),
                String(todayTimeStamp),
                null,
            );
        }

        ongoingMissions.value = response.syncs;
    } catch (error) {}
};

const getherMissionsData = () => {
    // Reset collections to prevent duplicates when switching tabs
    collectedAois.value = [];
    collectedIsrTracks.value = [];
    ongoingMissions.value.forEach((mission) => {
        if (mission.tais && selectedItems.value.includes('tais')) {
            mission.tais.forEach((aoi: Aoi) => {
                collectedAois.value.push(aoi);
            });
        } else {
            collectedAois.value = [];
        }

        if (mission.assets && selectedItems.value.includes('isrs')) {
            //lets do call signs
            let callSigns = [];
            mission.assets.forEach((asset: Asset) => {
                callSigns.push(asset.callSign);
                if (asset.isrTracks) {
                    asset.isrTracks.forEach((isrTrack: IIsrTrack) => {
                        // Check if the isrTrack already exists in the collection update if exists or push if not
                        const existingIndex =
                            collectedIsrTracks.value.findIndex(
                                (item) => item.id === isrTrack.id,
                            );
                        if (existingIndex !== -1) {
                            collectedIsrTracks.value[existingIndex] = isrTrack;
                        } else {
                            collectedIsrTracks.value.push(isrTrack);
                        }
                    });
                }
            });
        }
    });
};

const getCenterCoordinates = computed(() => {
    if (!currentOperation.value?.locationCoordinates) return [0, 0];
    return currentOperation.value.locationCoordinates.coordinates;
});

const parseMapItems = computed(() => {
    if (!currentOperation.value) return [];
    if (!ongoingMissions.value) return [];

    return parseMapItemsHelper({
        aois: collectedAois.value,
        isr_tracks: collectedIsrTracks.value,
    });
});

const timeFrame = ref(24);

const getTimelineItems = computed<TimelineItem[]>(() => {
    let isrTracks = [] as TimelineItem[];

    ongoingMissions.value.forEach((mission: Mission) => {
        if (!mission.startAt || !mission.endAt) return;

	    if (mission.assets) {
		    let assetCallSigns = [];
		    mission.assets.forEach((asset) => {
			    if (asset.isrTracks) {
				    assetCallSigns.push(asset.callSign);
				    asset.isrTracks.forEach((isrTrack: IIsrTrack) => {
					    if (!isrTrack.commenceAt || !isrTrack.concludeAt)
						    return;

					    // const isrItem = {
						//     id: isrTrack.id,
						//     label:
						// 	    asset.name +
						// 	    " <span class='bg-secondary text-primary px-3'>(" +
						// 	    assetCallSigns.join(',') +
						// 	    ')</span>' || 'NO NAME ISR TRACK',
						//     startDateTime: mission.startAt.toString(),
						//     endDateTime: mission.endAt.toString(),
						//     itemBackgroundColor: asset.color,
						//     itemLabel: `${asset.name} (${assetCallSigns.join(',')})`,
						//     priority: 'low',
						//     isrTrackOtherAssets: assetCallSigns,
						//     segmentClasses: 'isr-track-segment',
						//     rowClasses: 'bg-secondary-lighten-4',
					    // };

					    const isrItem = {
						    id: isrTrack.id,
						    callSign: asset.callSign ? asset.callSign : 'N/A',
						    assetLabel: asset.title,
						    isrDesignation: isrTrack.designation,
						    isrTrackName: isrTrack.label ? isrTrack.label : 'N/A',
						    label:
							    mission.name +
							    " <span class='bg-secondary text-primary px-3'>(" +
							    asset.callSign +
							    ')</span>' || 'NO NAME ISR TRACK',
						    isrTrackOtherAssets: assetCallSigns ? assetCallSigns : [],
						    startDateTime: mission.startAt.toString(),
						    endDateTime: mission.endAt.toString(),
						    itemBackgroundColor: asset.color,
						    itemLabel: `${asset.name} (${asset.callSign})`,
						    priority: 'low',
						    segmentClasses: 'isr-track-segment',
						    rowClasses: 'bg-secondary-lighten-4',
					    } as TimelineItem;

					    //find by id if exist, update, if not push
					    const existingIndex = isrTracks.findIndex(
						    (item) => item.id === isrItem.id,
					    );
					    if (existingIndex !== -1) {
						    isrTracks[existingIndex] = isrItem;
					    } else {
						    isrTracks.push(isrItem);
					    }
				    });
			    }
		    });
	    }
    });

    return [...isrTracks];
});

const getCustomTimeFrame = computed(() => {
    //if current props.type === 'custom'
    if (
        props.type === 'custom' &&
        isrSyncStartDate.value &&
        isrSyncEndDate.value
    ) {
        return Math.ceil(
            (isrSyncEndDate.value.getTime() -
                isrSyncStartDate.value.getTime()) /
                (1000 * 60 * 60),
        );
    } else {
        return parseInt(props.type.toString());
    }
});
// Function to update map key and force reinitialization
const updateMapKey = () => {
    nextTick(() => {
        esriMApKey.value = new Date().getTime();
    });
};

const updateTimeline = async () => {
    isPanelLoading.value = true;
    isTimeLineloading.value = true;
    await getSync();
    await getherMissionsData();
    updateMapKey();

    isPanelLoading.value = false;
    isTimeLineloading.value = false;
};

// Monitor tab changes
watch(
    () => props.type,
    async (newType) => {
        isPanelLoading.value = true;
        isVisible.value = false;

        // Reset collections
        collectedAois.value = [];
        collectedIsrTracks.value = [];
        timeFrame.value = Number(newType);

        try {
            // Reload data for the new timeframe
            await getSync();
            getherMissionsData();

            // Update step based on new intervals
            let intervals = getIntervals.value;
            step.value = parseInt(intervals[0]);

            // Force map reload with slight delay
            setTimeout(() => {
                isVisible.value = true;
                updateMapKey();
                isPanelLoading.value = false;
            }, 300);
        } catch (error) {
            console.error('Error updating panel:', error);
            isPanelLoading.value = false;
        }
    },
);

watch(
    () => selectedItems.value,
    (newVal) => {
        if (newVal) {
            isPanelLoading.value = true;
            getherMissionsData();
            updateMapKey();
            isPanelLoading.value = false;
            isTimeLineloading.value = false;
        }
    },
);

onMounted(async () => {
    isPanelLoading.value = true;
    try {
        await getCurrentOperation();
        await getSync();
        getherMissionsData();
        timeFrame.value = Number(props.type);
        let intervals = getIntervals.value;
        step.value = parseInt(intervals[0]);

        // Set up visibility observer to detect when tab is visible
        observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        isVisible.value = true;
                        updateMapKey();
                    } else {
                        isVisible.value = false;
                    }
                });
            },
            { threshold: 0.1 },
        );

        // Create a slight delay to ensure DOM is ready
        setTimeout(() => {
            // Start observing the current panel
            const panelElement = document.querySelector(
                `[data-panel-id="${props.type}"]`,
            );
            if (observer && panelElement) {
                observer.observe(panelElement);
            }

            isVisible.value = true;
            updateMapKey();
            isPanelLoading.value = false;
        }, 300);
    } catch (error) {
        console.error('Error initializing panel:', error);
        isPanelLoading.value = false;
        isTimeLineloading.value = false;
    }
    isTimeLineloading.value = false;
});

onBeforeUnmount(() => {
    // Clean up observer
    if (observer) {
        observer.disconnect();
        observer = null;
    }
});
</script>

<style scoped>
.isr-panel {
    position: relative;
    width: 100%;
}

.map-container {
    //min-height: 500px;
}
</style>
