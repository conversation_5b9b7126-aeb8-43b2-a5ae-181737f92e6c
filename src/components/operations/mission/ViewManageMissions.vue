<template>
  <div v-if="missionsList && missionsList.length > 0" class="ma-4">
    <h1>Missions:</h1>
    <v-data-table
      :headers="headers"
      :items="missionsList"
      :items-per-page="paginationState.perPage"
      :server-items-length="paginationState.total"
      >
      <template v-slot:item.actions="{ item }">
        <router-link :to="`/admin/operation/mission/builder?missionId=${item.id}`" v-if="item.status !== 'active'">
          <v-btn
            variant="flat"
            color="primary"
            size="small"
            class="mr-2"
          >
            <v-icon>mdi-pencil</v-icon>
          </v-btn>
        </router-link>
      </template>
    </v-data-table>
  </div>
  <div v-else>
    <h1>No Missions</h1>
  </div>
</template>


<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import {Mission} from "@/types/Mission";
import {DataTableHeader, IPagination} from "@/types/Global.type";
const missionStore = useMissionStore();
const props = defineProps<{
  currentOperationId: string | number | null
}>();

const paginationState = ref<IPagination>({
  page: 1,
  perPage: 30,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const missionsList = ref<Mission[]>([]);

const getMissions = async () => {
  try {
    const response = await missionStore.getMissions(
      paginationState.value,
      {
        operationId: props.currentOperationId?.toString(),
      }
    );
    missionsList.value = response?.data?.missions
    paginationState.value = response?.data?.pagination

  } catch (error) {
    console.error(error);
  }
};
const headers:DataTableHeader[] = [
  {
    title: 'Name',
    align: 'start',
    sortable: false,
    key: 'name',
  },
  {
    title: 'Description',
    align: 'start',
    sortable: false,
    key: 'description',
  },
  {
    title: 'Type',
    align: 'start',
    sortable: false,
    key: 'type',
  },
  {
    title: 'Classification',
    align: 'start',
    sortable: false,
    key: 'classification',
  },
  {
    title: 'Priority',
    align: 'start',
    sortable: false,
    key: 'priority',
  },
  {
    title: 'Status',
    align: 'start',
    sortable: false,
    key: 'status',
  },
  {
    title: 'Actions',
    align: 'start',
    sortable: false,
    key: 'actions',
  }]


onMounted(getMissions);

</script>
