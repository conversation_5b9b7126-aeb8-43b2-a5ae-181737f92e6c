<template>
    <v-card class="lumio-card" v-if="!isLoading">
        <v-card-title class="pt-4">
            <h4>
                Mission Builder
                <small class="text-info">{{
                    mission.id ? `${mission.name}` : ''
                }}</small>
            </h4>
            <div>
                <v-btn
                    v-if="mission.id"
                    :href="`/admin/operation/rcm?step=1#builder`"
                    class="ma-2"
                    variant="flat"
                    color="primary"
                    prepend-icon="mdi-plus"
                >
                    <span>Create New Mission</span>
                </v-btn>
            </div>
        </v-card-title>
        <v-card-text>
            <div v-if="mission.id">
                <v-stepper
                    v-model="step as number"
                    :key="step"
                    :items="items"
                    hide-actions
                    flat
                    elevation="0"
                    editable
                    clickable
                    class="ma-0 pa-0 noPadStepper "
                    @click="goToStep(step)"
                >
                    <template v-slot:item.1>
                        <MissionInformation
                            :mission="mission"
                            @fetch-mission="fetchMission"
                        />
                    </template>
                    <template v-slot:item.2>
                        <MissionInformationRequirements
                            :mission="mission"
                            @fetch-mission="fetchMission"
                        />
                    </template>

                    <template v-slot:item.3 class="h-100">
                        <MissionAreasOfInterest
                            class="h-100"
                            :mission="mission"
                            @fetch-mission="fetchMission"
                            @next-step="goToStep(4)"
                            :operation="currentOperation"
                        />
                    </template>

                    <template v-slot:item.4>
                        <div class="h-100">
                            <MissionIsrTracks
                                v-if="mission.id && currentOperation"
                                @next-step="goToStep(5)"
                                :mission="mission"
                                :operation="currentOperation"
                                @fetch-mission="fetchMission"
                            ></MissionIsrTracks>
                        </div>
                    </template>

                    <template v-slot:item.5>
                        <MissionSummary
                            v-if="mission.id && currentOperation"
                            :mission="mission"
                            :operation="currentOperation"
                        />
                    </template>
                </v-stepper>
            </div>
            <div v-else>
                <div class="d-flex justify-center flex-column">
                    <div class="text-center">
                        <h3>Create a new mission</h3>
                        <p>
                            Click the button below to create a new mission for
                            your operation and start building your collection
                            plan.
                        </p>
                    </div>

                    <div class="d-flex justify-center">
                        <v-btn
                            color="primary"
                            prepend-icon="mdi-plus"
                            class="ma-2"
                            variant="flat"
                            @click="createNewMission"
                            v-if="!mission.id"
                        >
                            Create New Mission
                        </v-btn>
                    </div>
                </div>
            </div>
        </v-card-text>
    </v-card>
    <div v-else>
        <div>
            <v-skeleton-loader
                class="mx-auto"
                type="card"
                :loading="isLoading"
            ></v-skeleton-loader>
        </div>
    </div>
</template>

<style lang="scss">
.v-window-item {
    height: 90%;
}
.noPadStepper {
    .v-stepper-window {
        margin: 0 !important;
        height: 100%;
    }
}
</style>
<script setup lang="ts">
import { ref } from 'vue';
import '@vuepic/vue-datepicker/dist/main.css';
import { useMissionStore } from '@/stores/admin/mission.store';
import { Mission } from '@/types/Mission';
const missionStore = useMissionStore();
import MissionInformation from '@/components/operations/mission/mission-builder/MissionInformation.vue';
import MissionAreasOfInterest from '@/components/operations/mission/mission-builder/MissionAreasOfInterest.vue';
import MissionIsrTracks from '@/components/operations/mission/mission-builder/MissionISRTracks.vue';
// import Step4Details from '@/components/operations/mission/Step4Details.vue';
import MissionSummary from '@/components/operations/mission/mission-builder/MissionSummary.vue';
import { useRoute } from 'vue-router';
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
import { useSnackbar } from '@/composables/useSnackbar';
import { Operation } from '@/types/Operation';
const route = useRoute();
const step = ref(1);
const operationStore = useOperationStore();
const { operation } = storeToRefs(operationStore);
const { showSnackbar } = useSnackbar();
const isLoading = ref(false);
const router = useRouter();

const currentOperation = ref<Operation | null>(null);
const initialMissionState: Mission = {
    id: null,
    name: 'New Mission: ' + Date.now(),
    description: '',
    designation: '',
    type: '',
    classification: '',
    priority: '',
    operationId: null,
    reportTypeConfiguration: {
        reportTypes: [],
        submittedReport: null,
    },
    startAt: '',
    endAt: '',
    status: '',
    createdAt: '',
    updatedAt: '',
    indicatorsDescription: '',
    warningsDescription: '',
    requestedByUserId: null,
    approvedByUserId: null,
    requestedByUser: null,
    approvedByUser: null,
    informationRequirements: [],
    isrs: [],
    tais: [],
    operation: null,
};

const mission = ref<Mission>({ ...initialMissionState });

const items = ref([
    {
        title: 'Type',
        value: 1,
        disabled: false,
        editable: true,
    },
    {
        title: 'Information Requirements',
        value: 2,
        disabled: false,
        editable: true,
    },
    {
        title: 'TAIs',
        value: 3,
        disabled: false,
        editable: true,
    },
    {
        title: 'ISR Tracks',
        value: 4,
        disabled: false,
        editable: true,
    },
    {
        title: 'Summary',
        value: 5,
        disabled: false,
        editable: true,
    },
]);

const fetchMission = async (id: string | null | number, nextStep: number) => {
    try {
        if (!id) return;
        isLoading.value = true;
        const response = await missionStore.getMissionById(id.toString(), [
            'assets',
            'informationRequirements',
            'tais',
            'engagedAssets',
        ]);
        mission.value = response.data.mission;
        mission.value.reportTypeConfiguration.reportTypes =
            response.data.mission.reportTypeConfiguration.reportTypes;
        step.value = nextStep;
    } catch (error) {
        console.error(error);
    } finally {
        isLoading.value = false;
    }
};

const fetchOperation = async () => {
    if (!operation.value) return;
    const response = await operationStore.getOperationById(operation.value?.id);
    currentOperation.value = response.operation;
};

onMounted(async () => {
    try {
        await fetchOperation();
        const missionId = route.query.missionId;
        const nextStep = route.query.step ?? '1';
        if (missionId) {
            isLoading.value = true;
            await fetchMission(missionId as string, Number(nextStep));
            isLoading.value = false;
        }
    } catch (error) {}
});

const goToStep = (stepNumber: number) => {
    step.value = stepNumber;
    //update route query and keep the hash too
    router.replace({
        query: { ...route.query, step: stepNumber },
        hash: '#builder',
    });
};

const createNewMission = async () => {
    try {
        isLoading.value = true;
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const missionPayload = {
            name: 'Mission ' + operation?.value?.name,
            description: 'TBD',
            type: 'surveillance',
            classification: 'secret',
            priority: 'medium',
            reportTypeConfiguration: {
                reportTypes: [],
                submittedReport: null,
            },
            startAt: now.toISOString(), // This will format as "2024-01-24T15:30:00.000Z"
            endAt: tomorrow.toISOString(), // This will format as "2024-01-25T15:30:00.000Z"
            operationId: operation?.value?.id,
        };

        const response = await missionStore.createMission(missionPayload);
        const missionId = response.data.mission.id;
        showSnackbar({
            text: 'Mission created successfully',
            color: 'success',
        });
        router.push(`${route.path}?missionId=${missionId}&step=1`);
        await fetchMission(missionId as string, 1);
    } catch (error) {
        console.error(error);
        showSnackbar({ text: 'Failed to create mission', color: 'error' });
    } finally {
        isLoading.value = false;
    }
};

watch(
    () => route.query.missionId,
    async (newMissionId, oldMissionId) => {
        if (newMissionId === oldMissionId) return;

        isLoading.value = true;
        try {
            if (newMissionId) {
                // Fetch mission with new ID
                await fetchMission(
                    newMissionId as string,
                    Number(route.query.step ?? '1'),
                );
            } else {
                // Reset mission to initial state
                mission.value = {
                    ...initialMissionState,
                    name: 'New Mission: ' + Date.now(), // Update timestamp for new mission
                };
                step.value = 1;
            }
        } catch (error) {
            console.error('Error updating mission:', error);
            showSnackbar({
                text: 'Failed to update mission',
                color: 'error',
            });
        } finally {
            isLoading.value = false;
        }
    },
    { immediate: true },
);
</script>
