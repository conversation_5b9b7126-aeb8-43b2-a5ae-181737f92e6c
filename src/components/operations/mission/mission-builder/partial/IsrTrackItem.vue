<template>
	<v-card class="lumio-card mb-2 bg-white" variant="outlined">
		<v-card-title>
			<h4 class="text-h6 ">
				[ISR-{{isrTrack.designation}}] {{isrTrack.label}}
			</h4>
			<div class="d-flex justify-space-between">
				<LumioStatusChip class="mr-2"
				                 :status="isrTrack.status ? isrTrack.status : '' "></LumioStatusChip>
<!--				<LumioPriorityChip-->
<!--					:priority="isrTrack.priority"></LumioPriorityChip>-->
			</div>
		</v-card-title>
		<v-card-subtitle>
			<template v-if="isrTrack.commenceAt">
				{{ dayjs(isrTrack.commenceAt).format('MMM-D, YYYY  HH:mm') }} -
			</template>
			<template v-if="isrTrack.concludeAt">
				{{ dayjs(isrTrack.concludeAt).format('MMM-D, YYYY  HH:mm') }}
			</template>
		</v-card-subtitle>
		<v-card-item>
            <v-select
                v-if="mission?.assets?.length"
                density="compact"
                v-model="currentAssetSelectedId"
                :items="mission.assets"
                :label="`Select Asset`"
                item-value="id"
                item-title="title"
                @update:model-value="setCurrentAssetSelected"
            >
                <template v-slot:selection="{ item }">
                    <div class="d-flex align-center">
                        <div class="color-box mr-3" :style="`background: ${(item as any).raw.color}; height: 20px; width: 20px; border-radius: 4px; margin-right: 8px;`"></div>
                        <span>{{ (item as any).raw.title }} ({{ (item as any).raw.callSign }})</span>
                    </div>
                </template>
                <template v-slot:item="{ item, props }">
                    <v-list-item v-bind="props" :title="undefined">
                        <template v-slot:prepend>
                            <div class="color-box mr-3" :style="`background: ${(item as any).raw.color}; height: 20px; width: 20px; border-radius: 4px;`"></div>
                        </template>
                        <template v-slot:default>
                            {{ (item as any).raw.title }} ({{ (item as any).raw.callSign }})
                        </template>
                    </v-list-item>
                </template>
                <template v-slot:append>
                    <v-btn
                        v-if="!alreadyAssignedToIsr && currentAssetSelectedId"
                        variant="text"
                        @click="addAssetToTrack(currentAssetSelectedId)"
                    >
                        <v-icon>mdi-plus</v-icon>
                        Assign
                    </v-btn>
                </template>
            </v-select>
            <v-alert v-else>
                No Assets Assigned To Mission. <br>Please assign assets to the mission first.
            </v-alert>
		</v-card-item>
        <template v-if="currentIsrTrackEngagedAssets.length">

	        <v-expansion-panels
		        variant="accordion"
		        v-model="isrTrackAssetsPanelToggler"
	        >
		        <v-expansion-panel>
			        <v-expansion-panel-title static>
				        <template v-slot:default>
					        Assets On ISR-{{isrTrack.designation}} & Missions
					        <v-chip
						        color="primary"
					            class="ml-2"
					        >
						        Assets: {{ currentIsrTrackEngagedAssets.length }}
					        </v-chip>
				        </template>
			        </v-expansion-panel-title>
			        <v-expansion-panel-text>
				        <template v-for="item in currentIsrTrackEngagedAssets" :key="item.id">
					        <IsrTrackAssetsMissions
						        v-if="item.asset && item.mission"
						        :asset="item.asset"
						        :mission="item.mission"
						        :id="(item?.id) ? parseInt(item.id.toString()) : 0"
						        @remove-asset-from-track="removeAssetFromTrack"
						         />
				        </template>
			        </v-expansion-panel-text>
		        </v-expansion-panel>

	        </v-expansion-panels>
        </template>
	</v-card>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import {Asset} from "@/types/Asset";
import { Mission } from "@/types/Mission";
import { IIsrTrack } from "@/types/IsrTrack.type";
import {IEngagedMissionAsset} from "@/types/EngagedMissionAsset.type";

const props = defineProps<{
	engagedMissionAssets: IEngagedMissionAsset[];
	isrTrack: IIsrTrack;
	mission: Mission;
}>();
const currentAssetSelectedId = ref<number|null>(null);
const currentAssetSelected = ref<Asset|null>(null);
const isrTrackAssetsPanelToggler = ref(false);

const emits = defineEmits(['add-asset-to-isr-track', 'remove-asset-from-isr-track']);

const setCurrentAssetSelected = (value: number | null) => {
    currentAssetSelectedId.value = value;
    currentAssetSelected.value = value ? props.mission?.assets?.find(asset => asset.id === value) || null : null;
};

//check if isrTrack.assets match ids and assets.missions.id is also current mission
const alreadyAssignedToIsr = computed(() => {
	console.log('engagedMissionAssets', props.engagedMissionAssets);
	if(!props.engagedMissionAssets || props.engagedMissionAssets.length === 0) return false;
	return !!(props.engagedMissionAssets.find(engagedAsset => engagedAsset.asset?.id === currentAssetSelectedId.value)
		&& props.engagedMissionAssets.find(engagedAsset => engagedAsset.mission?.id === props.mission.id)
	&& props.engagedMissionAssets.find(engagedAsset => engagedAsset.isrTrack?.id === props.isrTrack.id));
});


const currentIsrTrackEngagedAssets = computed(() => {
	if(!props.engagedMissionAssets || props.engagedMissionAssets.length === 0) return [];
	return props.engagedMissionAssets.filter(engagedAsset => engagedAsset.isrTrack?.id === props.isrTrack.id);
});

const addAssetToTrack = (assetId: number) => {
    // await isrTrackStore.assignAssetsToIsrTrack(props.isrTrack.id, [assetId]);
    emits('add-asset-to-isr-track', {
        isrTrackId: props.isrTrack.id,
        assetId: assetId,
    });
};

const removeAssetFromTrack = (id: number) => {
	console.log('removeAssetFromTrack 222', id);
    emits('remove-asset-from-isr-track', id);
};

</script>