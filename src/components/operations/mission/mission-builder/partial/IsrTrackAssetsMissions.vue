<template>
	<v-card class="lumio-card mb-4" variant="outlined" :style="`border-left:10px solid ${asset.color} !important;`"  >
		<v-card-item>
			<v-card-title class="d-flex align-center border-l" :border-color="asset.color">
				<h5 class="text-h6 limitChars">
					<v-chip density="compact"
					        variant="flat"
					        color="secondary"
					        prepend-icon="mdi-tag"
					>
						<span>{{ asset.callSign }}</span>
					</v-chip>
					<v-tooltip top activator="parent">
							<span>{{ asset.title }}</span>
					</v-tooltip>

					{{ asset.title }}</h5>
				<v-btn
					variant="outlined"
					color="red"
					size="small"
					@click="removeAssetFromTrack(id)"
					prepend-icon="mdi-trash-can"
				>
					Remove
				</v-btn>
			</v-card-title>
			<v-card-subtitle>
				<div class="text-subtitle-1 font-weight-bold">{{ mission.name }}</div>
				<div class="mission-dates">
					<small v-if="mission.startAt">
						{{ dayjs(mission.startAt).format('MMM-D, YYYY HH:mm') }} -
					</small>
					<small v-if="mission.endAt">
						{{ dayjs(mission.endAt).format('MMM-D, YYYY HH:mm') }}
					</small>
				</div>
			</v-card-subtitle>
		</v-card-item>
	</v-card>
</template>
<script setup lang="ts">
import dayjs from 'dayjs';
import { Asset } from '@/types/Asset';
import { Mission } from '@/types/Mission';


defineProps<{
	id: number;
    asset: Asset;
    mission: Mission;
}>();

const emits = defineEmits(['remove-asset-from-track']);
const removeAssetFromTrack = (id: number) => {
	console.log('removeAssetFromTrack', id);
    emits('remove-asset-from-track', id);
};



</script>

<style scoped lang="scss">

.limitChars {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 500px;
}
</style>