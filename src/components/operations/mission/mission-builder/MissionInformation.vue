<template>
    <v-card class="lumio-card px-4 pt-4">
        <v-alert v-if="unsavedChanges" type="warning">
            There are unsaved changes
        </v-alert>
        <v-card-title class="d-flex align-center">
            <h4 class="text-subtitle-1">{{ mission.name }}</h4>
            <div class="d-flex justify-end">
                <v-btn
                    color="red"
                    variant="outlined"
                    prepend-icon="mdi-trash-can"
                    append-icon="mdi-arrow-right"
                    class="text-sm-body-1"
                    @click="discardChanges"
                    v-if="mission.id"
                    size="sm"
                    style="padding: 5px 3px"
                >
                    Discard Changes & Next Step
                </v-btn>

                <v-btn
                    class="ml-2 text-sm-body-1"
                    color="primary"
                    prepend-icon="mdi-content-save-check"
                    append-icon="mdi-arrow-right"
                    @click="updateMission"
                    v-if="mission.id"
                    size="sm"
                    style="padding: 5px 3px"
                >
                    Update Mission & Next Step
                </v-btn>
            </div>
        </v-card-title>
        <v-card-text class="pt-4" v-if="!isLoading">
            <v-row>
                <v-col cols="6">
                    <v-text-field
                        class="mb-2"
                        label="Name"
                        v-model="mission.name"
                    ></v-text-field>
                    <div class="d-flex mb-4">
                        <div class="flex-grow-1 pr-2">
                            Mission Duration:
                            <LumioDateTimeRangePicker
                                class="w-100"
                                :periods="predefinedPeriods"
                                :startDateTime="missionStartDateTime"
                                :endDateTime="missionEndDateTime"
                                @range-changed="handleRangeChanged"
                                :startToday="true"
                            />
                        </div>
                    </div>
                    <div class="d-flex mb-1">
                        <div class="flex-grow-1 pr-2">
                            <v-select
                                density="compact"
                                class="mb-2"
                                label="Type"
                                :items="['surveillance']"
                                hide-details
                                v-model="mission.type"
                            ></v-select>
                        </div>
                        <div class="flex-grow-1 pr-2">
                            <v-select
                                density="compact"
                                label="Classification"
                                class="mb-2"
                                hide-details
                                :items="[
                                    'Baseline',
                                    'Protected',
                                    'Secret',
                                    'Top Secret',
                                    'Unclassified',
                                ]"
                                v-model="mission.classification"
                            ></v-select>
                        </div>
                        <div class="flex-grow-1">
                            <v-select
                                density="compact"
                                label="Priority"
                                class="mb-2"
                                hide-details
                                :items="['high', 'medium', 'low', 'none']"
                                v-model="mission.priority"
                            ></v-select>
                        </div>
                    </div>

                    <v-select
                        density="comfortable"
                        variant="outlined"
                        class="mb-2"
                        label="Mission Assets"
                        :item-title="
                            (item) => item.name + ' (' + item.callSign + ')'
                        "
                        :item-value="(item) => item.id"
                        :items="assets"
                        v-model="mappedAssets"
                        multiple
                        hide-details
                        chips
                    ></v-select>

                    <v-select
                        density="comfortable"
                        variant="outlined"
                        class="mb-2"
                        label="Report format"
                        chips
                        :items="[
                            'Written report',
                            'Verbal Brief',
                            'SITREP',
                            'SMS',
                            'official document',
                            'other',
                        ]"
                        multiple
                        v-model="reportTypes"
                    ></v-select>
                </v-col>
                <v-col cols="6">
                    <div class="d-flex mb-1">
                        <v-textarea
                            rows="3"
                            density="compact"
                            label="Description"
                            v-model="mission.description"
                        ></v-textarea>
                    </div>
                    <div class="d-flex mb-1">
                        <v-textarea
                            rows="3"
                            density="compact"
                            label="Indicators Description"
                            v-model="mission.indicatorsDescription"
                        ></v-textarea>
                    </div>
                    <div class="d-flex mb-1">
                        <v-textarea
                            rows="3"
                            density="compact"
                            label="Warnings Description"
                            v-model="mission.warningsDescription"
                        ></v-textarea>
                    </div>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { useOperationStore } from '@/stores/operation.store';
import { useMissionStore } from '@/stores/admin/mission.store';
import { Mission } from '@/types/Mission';
import { IPagination } from '@/types/Global.type';
import { useAdminAssetStore } from '@/stores/admin/asset.store';
import { Asset, IAssetCollectionData } from '@/types/Asset';
import LumioDateTimeRangePicker from '@/components/elements/LumioDateTimeRangePicker.vue';

// Now update the ref to use this extended interface

const missionHasChanges = ref(false);
const assetsHaveChanges = ref(false);
const operationStore = useOperationStore();
const missionStore = useMissionStore();
const adminAssetStore = useAdminAssetStore();

const isLoading = ref(true);

const missionStartDateTime = ref();
const missionEndDateTime = ref();
// Get current date and time
const now = new Date();
const formatISODateTime = (date: Date) => date.toISOString();

const predefinedPeriods = [
    {
        label: 'Next 24 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 24 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 48 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 48 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 72 Hours',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 72 * 60 * 60 * 1000),
        ),
    },
    {
        label: 'Next 7 Days',
        startDateTime: formatISODateTime(now),
        endDateTime: formatISODateTime(
            new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        ),
    },
];

const assetPagination = ref<IPagination>({
    page: 1,
    pages: 0,
    sortBy: 'desc',
    orderBy: 'id',
    perPage: 100,
    total: 0,
});

const originalMission = ref<Mission | null>(null);

const props = defineProps<{
    mission: Mission;
}>();

const requiredSections = ref<string[]>([]);

// const pirs = ref<IPir[]>([]);
const assets = ref<Asset[]>([]);
const mappedAssets = ref<any[]>([]);
const reportTypes = ref<string[]>([]);

// Add a new ref to track selected IR IDs

const fetchOperationAssets = async () => {
    const apiResponse = (await adminAssetStore.fetchAssets(
        assetPagination.value,
        {
            operationId: operationStore.currentOperationId,
            status: 'active',
        },
        ['platform'],
    )) as IAssetCollectionData;

    if (apiResponse && apiResponse.data && apiResponse.data.assets) {
        assets.value = apiResponse.data.assets;
    }

    if (props.mission?.assets) {
        mappedAssets.value = props.mission.assets.map((asset: any) => asset.id);
    }
};

const emits = defineEmits(['fetch-mission']);

const updateMission = async () => {
    isLoading.value = true;
    const missionId = props.mission.id;
    const updateMissionPayload = {
        name: props.mission.name,
        description: props.mission.description,
        type: props.mission.type,
        classification: props.mission.classification,
        priority: props.mission.priority,
        // @todo: submittedReport dynamic value
        reportTypeConfiguration: {
            reportTypes: reportTypes.value,
            submittedReport: '1',
        },
        startAt: missionStartDateTime.value,
        endAt: missionEndDateTime.value,
        operationId: operationStore.currentOperationId,
        indicatorsDescription: props.mission.indicatorsDescription,
        warningsDescription: props.mission.warningsDescription,
        assetIds: mappedAssets.value,
        requiredSections: requiredSections.value,
    };

    console.log(updateMissionPayload);

    await missionStore.updateMission(Number(missionId), updateMissionPayload);
    emits('fetch-mission', missionId, 2);
    isLoading.value = false;
};

const discardChanges = () => {
    isLoading.value = true;
    const missionId = props.mission.id;
    emits('fetch-mission', missionId, 2);
    isLoading.value = false;
};

const handleRangeChanged = (startDateTime: string, endDateTime: string) => {
    isLoading.value = true;
    missionStartDateTime.value = startDateTime;
    missionEndDateTime.value = endDateTime;
    isLoading.value = false;
};

watch(
    () => props.mission,
    (newMission) => {
        if (originalMission.value) {
            missionHasChanges.value = !(
                JSON.stringify(originalMission.value) ===
                JSON.stringify(newMission)
            );
        }
    },
    { deep: true },
);

//watch mappedAssets
watch(
    () => mappedAssets.value,
    (newMappedAssets) => {
        if (originalMission.value && originalMission.value.assets) {
            const originalAssetIds = originalMission.value.assets.map(
                (asset: any) => asset.id,
            );
            // Check if arrays have different lengths or different content
            assetsHaveChanges.value =
                newMappedAssets.length !== originalAssetIds.length ||
                !newMappedAssets.every((id) => originalAssetIds.includes(id));
        }
    },
);

const unsavedChanges = computed(() => {
    return missionHasChanges.value || assetsHaveChanges.value;
});

onMounted(async () => {
    await fetchOperationAssets();

    // Initialize reportTypeConfiguration if not already set
    if (props.mission.reportTypeConfiguration) {
        reportTypes.value = props.mission.reportTypeConfiguration.reportTypes;
    }

    missionStartDateTime.value = props.mission.startAt;
    missionEndDateTime.value = props.mission.endAt;
    // Deep copy props.mission to originalMission
    originalMission.value = JSON.parse(JSON.stringify(props.mission));

    isLoading.value = false;
});
</script>
