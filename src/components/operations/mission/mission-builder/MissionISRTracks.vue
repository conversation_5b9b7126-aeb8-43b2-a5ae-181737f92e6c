<template>
    <div v-if="isLoading">
	    <v-skeleton-loader
			class="mx-auto"
			type="card"
			:loading="isLoading"
			width="100%"
			height="300"
		></v-skeleton-loader>
    </div>
    <v-card class="lumio-card h-100 d-flex flex-column" style="min-height: 300px;" v-else>
        <v-card-title>
            <h4>ISR Tracks</h4>
            <v-btn
                color="primary"
                append-icon="mdi-arrow-right"
                @click="emits('next-step')"
            >
                Go To Summary
            </v-btn>
        </v-card-title>
        <v-card-text class="py-4 flex-grow-1" style="height: calc(100% - 60px);">
            <v-row v-if="mission" class="pa-0 h-100" style="min-height: 300px;">
                <v-col cols="4" class="bg-grey-lighten-2 h-100" style="min-height: 300px;">
	                <template v-if="currentIsrTracks.length === 0">
		                <v-alert class="mx-2 bg-amber" type="info"  elevation="2">

			                <span class="text-h6">No ISR Tracks</span>
			                <br />
			                <span class="text-h6">
				                Please add ISR Tracks to this mission
			                </span>
		                </v-alert>
	                </template>
	                <template v-else>
		                <IsrTrackItem :key="isrTrack.id"
		                              class="ml-2"
		                              v-for="isrTrack in currentIsrTracks"
		                              :isr-track="isrTrack"
		                              :mission="mission"
		                              :engaged-mission-assets="currentEngagedAssets"
		                              @add-asset-to-isr-track="handleAddAssetToTrack"
		                              @remove-asset-from-isr-track="handleRemoveAssetFromTrack"
		                ></IsrTrackItem>

	                </template>

                </v-col>
                <v-col cols="8" class="pa-0 h-100 d-flex" style="min-height: 300px;">
                    <div
	                    class="h-100 w-100"
                        style="min-height: 300px;"
                        v-if="
                            mission &&
                            operation?.locationCoordinates?.coordinates
                        "
                    >
                        <template v-if="loadingMap">
                            <v-skeleton-loader
                                class="mx-auto"
                                type="card"
                                :loading="loadingMap"
                                width="100%"
                                height="300"
                            ></v-skeleton-loader>
                        </template>
                        <template v-else>
                            <EsriMapViewer
	                            :key="new Date().getTime()"
                                :map-items="parseMapItems"
                                :operation="operation"
	                            :max-height="'100%'"
                                :centerCoordinates="getOperationCenter"
                                :zoom="getOperationZoom"
                                @elementSelected="handleElementClicked"
                            ></EsriMapViewer>
                        </template>
                    </div>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import type { Mission } from '@/types/Mission';
import { Operation } from '@/types/Operation';
import { useIsrTrackStore } from '@/stores/isr-track.store';
import { IIsrTrack } from '@/types/IsrTrack.type';
import { parseMapItemsHelper } from '@/composables/misc.helper';
import { SymbolItem } from '@/types/EsriMap';
import { IPagination } from '@/types/Global.type';
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue';
import { useSnackbar } from '@/composables/useSnackbar';
import {useEngagedMissionAssetStore} from "@/stores/admin/engaged-mission-asset.store";
import {IEngagedMissionAsset} from "@/types/EngagedMissionAsset.type";
const isrTrackStore = useIsrTrackStore();
const engagedMissionAssetSTore = useEngagedMissionAssetStore();
const isLoading = ref<boolean>(true);
const currentIsrTracks = ref<IIsrTrack[]>([]);
const currentEngagedAssets = ref<IEngagedMissionAsset[]>([]);
const selectedAssets = ref<{ [key: string]: number[] }>({});
const props = defineProps<{
    mission?: Mission;
    operation?: Operation;
}>();
const { showSnackbar } = useSnackbar();

const loadingMap = ref<boolean>(true);
const emits = defineEmits(['fetch-mission', 'next-step']);
const getIsrTracks = async () => {
    const response = await isrTrackStore.fetchIsrTracks(
        {
            page: 1,
            perPage: 50,
            sortBy: 'desc',
            orderBy: 'id',
        } as IPagination,
        {
            operationId: props.operation?.id,
        },
        [],
    );
	console.log('response', response);
    if (!response.data) return;
    currentIsrTracks.value = response.data?.isrTracks;
    //update selectedAssets
    currentIsrTracks.value.forEach((isrTrack: IIsrTrack) => {
        selectedAssets.value[isrTrack.id] = isrTrack.assets
            ? isrTrack.assets.map((asset) => asset.id)
            : [];
    });

    isLoading.value = false;
};
const getEngagedAssets = async () => {
	const response = await engagedMissionAssetSTore.fetchEngagedMissionAssets(
		{
			page: 1,
			perPage: 50,
			sortBy: 'desc',
			orderBy: 'id',
		} as IPagination,
		{
			missionId: (props.mission?.id as string) || '',
		}
	);
	if (!response.data) return;
	currentEngagedAssets.value = response.data?.engagedMissionAssets;
};


onMounted(async () => {
    await getIsrTracks();
	await getEngagedAssets();
    loadingMap.value = false;
});

//TODO: rewrite this shit
const parseMapItems = computed((): SymbolItem[] => {
    if (!currentIsrTracks.value) return [];
    return parseMapItemsHelper(
        {
            isr_tracks: currentIsrTracks.value,
	        engaged_assets: currentEngagedAssets.value,
        },
        true,
    ) as SymbolItem[];
});

const getOperationCenter = computed(() => {
    if (!props.operation) return [0, 0];
    return props.operation.locationCoordinates?.coordinates;
});

const getOperationZoom = computed(() => {
    return props.operation?.zoom || 10;
});

const handleElementClicked = (graphic: any) => {
    // currentlySelectedIsrTrackId.value = graphic.id.toString();
};


const handleAddAssetToTrack = async (payload: { isrTrackId: string; assetId: number }) => {
    try{
        isLoading.value = true;
		loadingMap.value = true;
        const { isrTrackId, assetId } = payload;
		const missionId = props.mission?.id ? props.mission.id : null;
	    if(!missionId) {
			showSnackbar({
				text: 'Mission is not available',
				color: 'error',
			});
			return;
		}
		const engagedMissionAssetResponse = await engagedMissionAssetSTore.createEngagedMissionAsset({
			assetId: assetId,
			isrTrackId: parseInt(isrTrackId.toString()),
			missionId: parseInt(missionId.toString()),
		});

		console.log('engagedMissionAssetResponse', engagedMissionAssetResponse);

	    await getIsrTracks();
	    await getEngagedAssets();

        showSnackbar({
            text: 'Asset added to ISR Track',
            color: 'success',
        });
    } catch (error) {

    } finally {
        isLoading.value = false;
		loadingMap.value = false;
    }

};
const handleRemoveAssetFromTrack = async (id:number) => {
    try{
        isLoading.value = true;
	    loadingMap.value = true;
		await engagedMissionAssetSTore.deleteEngagedMissionAsset(id.toString());

	    await getIsrTracks();
	    await getEngagedAssets();
        showSnackbar({
            text: 'Asset removed from ISR Track',
            color: 'success',
        });
    } catch (error) {

    } finally {
        isLoading.value = false;
		loadingMap.value = false;
    }

};
</script>

<style scoped>


</style>

