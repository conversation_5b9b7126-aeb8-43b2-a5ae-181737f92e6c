<template>
  <v-card class="lumio-card">
    <v-card-title>
      <h4>Mission Collection Plan</h4>
    </v-card-title>
    <v-card-text>
  <div v-if="missionsList && missionsList.length > 0" class="ma-4">
    <v-data-table
      :headers="headers"
      :items="missionsList"
      :items-per-page="paginationState.perPage"
      :server-items-length="paginationState.total"
    >
    </v-data-table>
  </div>
  <div v-else class="pa-4">
    <v-alert type="info" color="bg-gray-lightened-2" icon="mdi-information-outline" class="my-4">
      <div class="">
        <div>
          <h4>No Missions</h4>
          <p>You have no missions planned.</p>
        </div>
      </div>
    </v-alert>
  </div>
    </v-card-text>
  </v-card>
</template>


<script setup lang="ts">
import { useMissionStore } from '@/stores/admin/mission.store';
import {Mission} from "@/types/Mission";
import {DataTableHeader, IPagination} from "@/types/Global.type";
const missionStore = useMissionStore();
const props = defineProps<{
  currentOperationId: string | number | null
}>();
const emits = defineEmits(['refresh-operation']);

const paginationState = ref<IPagination>({
  page: 1,
  perPage: 30,
  pages: 1,
  total: 0,
  sortBy: 'createdAt',
  orderBy: 'id',
});

const missionsList = ref<Mission[]>([]);

const getMissions = async () => {
  try {
    const response = await missionStore.getMissions(
      paginationState.value,
      {
        operationId: props.currentOperationId?.toString(),
        status: 'planned'
      }
    );
    missionsList.value = response?.data?.missions
    paginationState.value = response?.data?.pagination

  } catch (error) {
    console.error(error);
  }
};
const headers:DataTableHeader[] = [
  {
    title: 'Name',
    align: 'start',
    sortable: false,
    key: 'name',
  },
	{
		title: "Start Time",
		align: "start",
		sortable: false,
		key: "startAt",
	},
	{
		title: "End Time",
		align: "start",
		sortable: false,
		key: "endAt",
	},
  {
    title: 'Description',
    align: 'start',
    sortable: false,
    key: 'description',
  },
  {
    title: 'Type',
    align: 'start',
    sortable: false,
    key: 'type',
  },
  {
    title: 'Classification',
    align: 'start',
    sortable: false,
    key: 'classification',
  },
  {
    title: 'Priority',
    align: 'start',
    sortable: false,
    key: 'priority',
  },
  {
    title: 'Status',
    align: 'start',
    sortable: false,
    key: 'status',
  },
  ]


onMounted(getMissions);


</script>
