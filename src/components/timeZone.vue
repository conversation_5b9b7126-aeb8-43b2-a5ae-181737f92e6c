<!-- src/components/TimezoneSelect.vue -->
<template>
    <div class="timezone-select">
        <div class="d-flex">
            <v-autocomplete
                v-model="store.mainTimezone"
                :items="filteredTimezones"
                item-title="label"
                item-value="id"
                label="Main Timezone"
                style="width: 300px; max-width: 100%"
                @update:model-value="handleMainTimezoneUpdate"
                required
                density="comfortable"
                class=""
            >
            </v-autocomplete>
            <v-btn
                variant="text"
                class="mt-4"
                @click="store.addTimezone({ timezone: 'Zulu' })"
            >
                <v-icon>mdi-plus</v-icon> Add
            </v-btn>
        </div>

        <v-divider v-if="!props.single" class="my-4"
            >Supporting Timezones</v-divider
        >

        <!-- Supporting timezones -->
        <div v-if="!props.single">
            <div
                v-for="(tz, index) in store.otherTimezones"
                :key="index"
                class="d-flex"
            >
                <v-btn
                    variant="text"
                    class="text-red bold"
                    @click="store.removeTimezone(index)"
                >
                    <v-icon>mdi-trash-can-outline</v-icon>
                </v-btn>
                <v-autocomplete
                    v-model="store.otherTimezones[index]"
                    :items="getFilteredTimezonesForIndex(index)"
                    item-title="label"
                    item-value="id"
                    density="compact"
                    class="pa-0 mr-4"
                    :label="`Supporting Timezone ${index + 1}`"
                    @update:model-value="
                        (val) => store.updateTimezone(index, val)
                    "
                >
                </v-autocomplete>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { useTimezoneStore } from '@/stores/timezone';

const store = useTimezoneStore();

const props = defineProps({
    single: Boolean,
});

const emit = defineEmits(['update']);

// Filter out 'etc' timezones like in original React component
const filteredTimezones = computed(() =>
    store.availableTimezones.filter((tz) => tz.kind !== 'etc'),
);

// Filter out already selected timezones for each selector
const getFilteredTimezonesForIndex = (currentIndex) => {
    return filteredTimezones.value.filter((tz) => {
        const usedTimezones = [
            store.mainTimezone,
            ...store.otherTimezones.slice(0, currentIndex),
        ];
        return !usedTimezones.includes(tz.id);
    });
};

// Create a map of timezone objects for easy lookup
const tzMap = computed(() => {
    return new Map(store.availableTimezones.map((tz) => [tz.id, tz]));
});

const handleMainTimezoneUpdate = (val) => {
    store.setMainTimezone(val);
    emit('update', val);
};
</script>
