<template>
	<div class="pa-4 text-center">
		<v-dialog
			v-model="isDialogOpen"
			persistent
			width="1200px"
			@update:modelValue="handleDialogUpdate"
		>
			<template v-slot:activator="{ props }">
				<slot name="activator" v-bind="props" />
			</template>

			<v-card>
				<v-card-text>
					<v-row dense>
						<v-col cols="2" md="2" sm="2">
							<v-text-field
								prefix="PIR:"
								label="PIR Number*"
								v-model="pirItem.pirNumber"
								required
							/>
						</v-col>

						<v-col cols="8" md="8" sm="8">
							<v-text-field
								label="Question*"
								v-model="pirItem.question"
								required
							/>
						</v-col>

						<v-col cols="2" md="2" sm="2">
							<v-checkbox
								v-model="pirItem.isActive"
								label="Active"
								required
							/>
						</v-col>

						<v-col cols="10" md="10" sm="10">
							<v-text-field
								label="Description*"
								v-model="pirItem.description"
								required
							/>
						</v-col>
					</v-row>

					<small class="text-caption text-medium-emphasis">*indicates required field</small>
				</v-card-text>

				<v-divider />

				<v-card-actions>
					<v-spacer />

					<v-btn
						text="Cancel"
						variant="plain"
						@click="closeDialog"
					/>

					<v-btn
						color="primary"
						text="Add PIR to Operation"
						variant="tonal"
						:disabled="!isFormValid"
						@click="createPir"
					/>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useAdminPirStore } from '@/stores/admin/pir.store';
import { useSnackbar } from '@/composables/useSnackbar';
import type { IPir } from '@/types/Pir';
import {Priority} from "@/types/Global.type";

// Props
const props = defineProps<{
	isOpen: boolean,
	operationId: string,  // Fixed typo in prop name
}>();

// Emits
const emit = defineEmits(['update:modelValue', 'pir-created']);

// Store and composables
const adminPirStore = useAdminPirStore();  // Fixed store usage
const { showSnackbar } = useSnackbar();

// Local state
const isDialogOpen = ref(false);

// Watch prop changes
watch(() => props.isOpen, (newValue) => {
	isDialogOpen.value = newValue;
});

// Initial PIR state
const pirItem = ref<IPir>({
	pirNumber: '',
	question: '',
	description: '',
	isActive: false,
	operationId: '',
	originator: '',
	priority: Priority.MEDIUM,
});

// Computed
const isFormValid = computed(
	() => pirItem.value.pirNumber &&
		pirItem.value.question &&
		pirItem.value.description
);

// Methods
const handleDialogUpdate = (value: boolean) => {
	emit('update:modelValue', value);
	if (!value) {
		resetForm();
	}
};

const resetForm = () => {
	pirItem.value = {
		pirNumber: '',
		question: '',
		description: '',
		isActive: false,
		operationId: '',
		originator: '',
		priority: Priority.MEDIUM,
	};
};

const closeDialog = () => {
	isDialogOpen.value = false;
	emit('update:modelValue', false);
};

const createPir = async () => {
	try {
		pirItem.value.operationId = props.operationId;
		await adminPirStore.createPir(pirItem.value);
		emit('pir-created', props.operationId, pirItem.value);
		closeDialog();
		showSnackbar({
			text: 'PIR created successfully',
			color: 'success',
		});
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: 'Error creating PIR',
			color: 'error',
		});
	}
};
</script>
