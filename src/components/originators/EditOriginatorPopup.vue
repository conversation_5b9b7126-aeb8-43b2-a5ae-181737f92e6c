<template>

	<v-progress-linear
		v-if="isLoading"
		indeterminate
		color="primary"
		class="my-4"
	></v-progress-linear>

	<div class="d-flex flex-wrap justify-start " v-else>
		<v-text-field v-model="originatorToEdit.title"
		              label="Title"
		              variant="outlined"
		              density="compact"
		              color="primary"
		              base-color="primary"
		              hide-details
		              class="my-5 bg-white w-75 flex-grow-0"
		>
		</v-text-field>

		<v-textarea v-model="originatorToEdit.description"
		            label="Description"
		            variant="outlined"
		            density="compact"
		            color="primary"
		            base-color="primary"
		            hide-details
		            class="my-5 bg-white w-100 h-33"
		>
		</v-textarea>
		<v-textarea v-model="originatorToEdit.contactDetails"
		            label="Contact Details"
		            variant="outlined"
		            density="compact"
		            color="primary"
		            base-color="primary"
		            hide-details
		            class="my-5 bg-white w-100 h-33"
		>
		</v-textarea>


	</div>
	<div class="d-flex justify-space-between">
		<v-btn @click="handleUpdateOriginator"
		       variant="flat"
		       color="primary"
		       text="Update Originator"
		       prepend-icon="mdi-plus"
		>
		</v-btn>
		<v-btn @click="emit('close-update-popup')"
		       variant="text"
		       size="small"
		       color="primary"
		       text="Cancel"
		       prepend-icon="mdi-close"
		>
		</v-btn>
	</div>

</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, nextTick } from 'vue';

import { IOriginatorSingleResponse, useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { IOriginator } from '@/types/Originator.type';


const { showSnackbar } = useSnackbar();
const adminOriginatorStore = useAdminOriginatorStore();
const props = defineProps<{
	originatorId: string;
}>();

const emit = defineEmits(['update-originators', 'close-update-popup']);
const isLoading = ref(true);

const originatorToEdit = ref<IOriginator>({
	id: '',
	title: '',
	contactDetails: '',
	description: '',
});

async function fetchOriginatorById() {
	const response = await adminOriginatorStore.fetchOriginatorById(props.originatorId);

	if (response.success && response.data?.originator) {  // Changed from data.data.originator to data.originator
		originatorToEdit.value = {
			id: response.data.originator.id ?? '',
			title: response.data.originator.title ?? '',
			contactDetails: response.data.originator.contactDetails ?? '',
			description: response.data.originator.description ?? ''
		};
	} else {
		showSnackbar({
			text: "Failed to fetch Originator",
			color: 'error'
		});
	}
	isLoading.value = false;
}

async function handleUpdateOriginator() {
	try {
		const response = await adminOriginatorStore.updateOriginator(props.originatorId, originatorToEdit.value as IOriginator) as IOriginatorSingleResponse;
		if (response.success) {
			showSnackbar({
				text: "Originator updated successfully",
				color: 'success'
			});
			emit('update-originators');
			await nextTick();
			emit('close-update-popup');
		} else {
			showSnackbar({
				text: "Failed to update Originator",
				color: 'error'
			});
		}
	} catch (error) {
		console.error(error);
		showSnackbar({
			text: "An error occurred while updating Originator",
			color: 'error'
		});
	}
}

onMounted(async () => {
	await fetchOriginatorById();
});
</script>