<template>
	<v-card class="ma-3 bg-grey-lighten-4 border border-solid border-grey-lighten-4 rounded-lg">
		<v-card-title class="d-flex justify-space-between">
			<h3>Add Originator</h3>
			<v-btn

				class="ma-2"
				variant="flat"
				icon
				density="compact"
				@click="emit('close-section')"
			>
				<v-icon>mdi-close</v-icon>
			</v-btn>
		</v-card-title>
		<v-card-text>
			<div class="d-flex flex-wrap justify-start " >
				<v-text-field v-model="originatorToAdd.title"
				              label="Title"
				              variant="outlined"
				              density="compact"
				              color="primary"
				              base-color="primary"
				              hide-details
				              class="my-5 bg-white w-100 flex-grow-0"
				>
				</v-text-field>

				<v-textarea v-model="originatorToAdd.description"
				            label="Description"
				            variant="outlined"
				            density="compact"
				            color="primary"
				            base-color="primary"
				            hide-details
				            class="my-5 bg-white w-100 h-33"
				>
				</v-textarea>
				<v-textarea v-model="originatorToAdd.contactDetails"
				            label="Contact Information"
				            variant="outlined"
				            density="compact"
				            color="primary"
				            base-color="primary"
				            hide-details
				            class="my-5 bg-white w-100 h-33"
				>
				</v-textarea>


			</div>

			<v-btn @click="handleAddOriginator"
			       variant="flat"
			       color="primary"
			       text="Add Originator"
			       prepend-icon="mdi-plus"
			>
			</v-btn>
		</v-card-text>
	</v-card>
</template>


<script setup lang="ts">
import { ref, defineEmits } from 'vue';
import { useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { IOriginator } from '@/types/Originator.type';

const { showSnackbar } = useSnackbar();
const adminOriginatorStore = useAdminOriginatorStore();


const originatorToAdd = ref<Omit<IOriginator, 'id'>>({
	title: '',
	description: '',
	contactDetails: '',
});

const emit = defineEmits(['close-section', 'update-originators']);


const handleAddOriginator = async() => {
	try {
		const {messages, success} = await adminOriginatorStore.createOriginator(originatorToAdd.value as IOriginator);
		if(success) {
			showSnackbar({
				text: "Originator added successfully",
				color: 'success'
			})
			originatorToAdd.value = {
				title: '',
				description: '',
				contactDetails: '',
			};
			emit('update-originators');
		} else {
			showSnackbar({
				pos: 'top-center',
				text: "Failed to add Originator",
				color: 'error'
			})
			showSnackbar({
				pos: 'top-center',
				text: messages[0].message as string,
				color: 'error'
			})
		}
		//reset originatorToAdd


	} catch (error) {
		console.error(error);
	} finally {
		//loading.value = false;
	}
}
</script>
