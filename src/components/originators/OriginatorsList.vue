<template>

	<v-card class="lumio-card">
		<v-card-title>
			<h4>Originators</h4>
			<div>
				<v-btn
					color="primary"
					prepend-icon="mdi-plus"
					class="ma-2"
					variant="flat"
					v-if="!isAddingOriginators"
					@click="isAddingOriginators = true"
				>
					Add Originators to Organization
				</v-btn>
			</div>
		</v-card-title>
		<v-card-text>
			<v-row class="" v-if="isAddingOriginators">
				<v-col>
					<add-originator
						@close-section="isAddingOriginators = false"
						@update-originators="getOriginators"
					></add-originator>
				</v-col>
			</v-row>

			<div>
				<v-data-table-server
					v-model:items-per-page="paginationState.perPage"
					:headers="headers"
					:items="originators"
					:items-length="paginationState.total"
					:loading="loading"
					item-value="name"
				>
					<template v-slot:item.title="{ item }">
						{{ item.title }} <br>
						<small>({{ item.designation }})</small>
					</template>
					<template v-slot:item.action="{ item }">
						<v-btn
							variant="flat"
							color="primary"
							size="small"
							@click="showEditPopup(item)"
						>
							<v-icon>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							variant="flat"
							color="error"
							size="small"
							class="ml-2"
							@click="deleteOriginator(item.id as string)"
						>
							<v-icon>mdi-delete</v-icon>
						</v-btn>
					</template>
				</v-data-table-server>
				<v-row justify="center">
					<v-col cols="8">
						<v-container class="max-width">
							<v-pagination
								v-model="paginationState.page"
								:length="paginationState.pages"
								class="my-4"
							></v-pagination>
						</v-container>
					</v-col>
				</v-row>

				<v-dialog
					v-model="isEditing"
					max-width="800"
					:close-on-back="false"
					:close-on-content-click="false"
					persistent
				>
					<v-card
						prepend-icon="mdi-tooltip-edit-outline"
						title="Edit Originator"
					>
						<v-card-text>
							<edit-originator-popup
								v-if="isEditing"
								:originator-id="editOriginatorId as string"
								@close-update-popup="isEditing = false"
								@update-originators="getOriginators"
								@delete-originator="deleteOriginator"
							/>
						</v-card-text>
					</v-card>
				</v-dialog>
			</div>
		</v-card-text>

	</v-card>



</template>

<script setup lang="ts">
import { IOriginatorCollectionResponse, useAdminOriginatorStore } from '@/stores/admin/originator.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { ref, onMounted } from 'vue';
import { IOriginator } from '@/types/Originator.type';
import { DataTableHeader, IPagination } from '@/types/Global.type';
import AddOriginator from '@/components/originators/AddOriginator.vue';

const adminOriginatorStore = useAdminOriginatorStore();
const { showSnackbar } = useSnackbar();
const loading = ref(false);
const originators = ref<IOriginator[]>([]);
const isEditing = ref(false);
const editOriginatorId = ref<string|number|null>(null);
const paginationState = ref<IPagination>({
	page: 1,
	perPage: 50,
	total: 0,
	pages: 0,
	sortBy: 'desc',
	orderBy: 'id',
});
const isAddingOriginators = ref(false);

const headers: DataTableHeader[] = [
	{
		title: 'Originator Title',
		align: 'start',
		sortable: false,
		key: 'title',
	},
	{
		title: 'Description',
		align: 'start',
		sortable: false,
		key: 'description',
	},
	{
		title: 'Contact Details',
		align: 'start',
		sortable: false,
		key: 'contactDetails',
	},
	{
		title: 'Action',
		align: 'center',
		sortable: false,
		key: 'action',
	},
]

const showEditPopup = (item: IOriginator) => {
	editOriginatorId.value = item.id as string;
	isEditing.value = true;
}


const getOriginators = async() =>{
	const { data, success, messages } = await adminOriginatorStore.fetchOriginators(paginationState.value) as IOriginatorCollectionResponse;
	if(!success && messages && messages[0]){
		showSnackbar({
			text: messages[0].message as string,
			color: 'error',
		});
	}
	paginationState.value = data?.pagination as IPagination
	originators.value = data?.originators as IOriginator[];
}

const deleteOriginator = async (originatorId: string) => {
	try {
		const confirmed  = confirm('Are you sure you want to delete this Originator?');
		if(!confirmed) return;

		loading.value = true;
		await adminOriginatorStore.deleteOriginator(originatorId);
		originators.value = originators.value.filter((originator) => originator.id !== originatorId);
		showSnackbar({
			text: 'Originator deleted successfully',
			color: 'success',
		});
		await getOriginators();
	} catch (error) {
		showSnackbar({
			text: 'Error deleting Originator',
			color: 'error',
		});
	} finally {
		loading.value = false;
	}
};


onMounted(async () => {
	await getOriginators();
});

</script>
