<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

// Define interface for period item
interface PeriodItem {
	startDateTime: string;
	endDateTime: string;
	label: string;
}

// Define component props
interface Props {
	periods?: PeriodItem[];
	startDateTime?: string;
	endDateTime?: string;
	startToday?: boolean; // Prevents selecting start date before today
}

const props = withDefaults(defineProps<Props>(), {
	periods: () => [],
	startDateTime: '',
	endDateTime: '',
	startToday: false
});

// Define emits
const emit = defineEmits<{
	(e: 'range-changed', startDateTime: string, endDateTime: string): void
}>();

// Component state
const showPicker = ref(false);
const currentMonth = ref(new Date());

// Initialize with current date if no initial values
const today = new Date();
const startDate = ref(props.startDateTime ? new Date(props.startDateTime) :
	(props.startToday ? today : new Date(2025, 1, 1))); // Use today if startToday is true
const endDate = ref(props.endDateTime ? new Date(props.endDateTime) : new Date(2025, 1, 22));     // Feb 22, 2025

// Time selection
const startHour = ref(0);
const startMinute = ref(0);
const endHour = ref(23);
const endMinute = ref(59);

// Format date for display
const formatDate = (date: Date): string => {
	return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
};

// Format date with time
const formatDateTime = (date: Date, hour: number, minute: number): string => {
	const formattedDate = formatDate(date);
	const formattedHour = (hour + 1).toString().padStart(2, '0');
	const formattedMinute = minute.toString().padStart(2, '0');
	return `${formattedDate} ${formattedHour}:${formattedMinute}`;
};

// Formatted month for calendar
const formatMonth = (date: Date): string => {
	return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }).toUpperCase();
};

// Get next month
const nextMonth = computed((): Date => {
	const date = new Date(currentMonth.value);
	date.setMonth(date.getMonth() + 1);
	return date;
});

// Formatted months
const currentMonthFormatted = computed((): string => formatMonth(currentMonth.value));
const nextMonthFormatted = computed((): string => formatMonth(nextMonth.value));

// Get days in a month
const getDaysInMonth = (year: number, month: number): number => {
	return new Date(year, month + 1, 0).getDate();
};

// Get days array for calendar
const getDaysArray = (date: Date): (Date | null)[] => {
	const year = date.getFullYear();
	const month = date.getMonth();
	const daysInMonth = getDaysInMonth(year, month);
	const firstDayOfMonth = new Date(year, month, 1).getDay();

	const days: (Date | null)[] = [];

	// Add empty slots for days before the first day of month
	for (let i = 0; i < firstDayOfMonth; i++) {
		days.push(null);
	}

	// Add days of the month
	for (let i = 1; i <= daysInMonth; i++) {
		days.push(new Date(year, month, i));
	}

	return days;
};

// Computed properties for calendar days
const currentMonthDays = computed((): (Date | null)[] => getDaysArray(currentMonth.value));
const nextMonthDays = computed((): (Date | null)[] => getDaysArray(nextMonth.value));

// Check if a date is before today (ignoring time)
const isBeforeToday = (date: Date): boolean => {
	const today = new Date();
	return new Date(date.getFullYear(), date.getMonth(), date.getDate()) <
		new Date(today.getFullYear(), today.getMonth(), today.getDate());
};

// Handle date selection
const selectDate = (date: Date | null): void => {
	if (!date) return;

	// If startToday is true, prevent selecting dates before today as start date
	if (props.startToday && isBeforeToday(date)) {
		return;
	}

	// If both dates are already selected, reset and start new selection
	if (startDate.value && endDate.value &&
		startDate.value.getTime() !== endDate.value.getTime()) {
		startDate.value = date;
		endDate.value = date;
		return;
	}

	// If no start date or the clicked date is before start date
	if (!startDate.value || date < startDate.value) {
		startDate.value = date;
		return;
	}

	// If clicking on start date again
	if (date.getTime() === startDate.value.getTime()) {
		return;
	}

	// Otherwise, set as end date
	endDate.value = date;
};

// Check if a date is the start date
const isStartDate = (date: Date | null): boolean => {
	if (!date || !startDate.value) return false;
	return date.toDateString() === startDate.value.toDateString();
};

// Check if a date is the end date
const isEndDate = (date: Date | null): boolean => {
	if (!date || !endDate.value) return false;
	return date.toDateString() === endDate.value.toDateString();
};

// Check if a date is in the selected range
const isInRange = (date: Date | null): boolean => {
	if (!date || !startDate.value || !endDate.value) return false;

	return date > startDate.value && date < endDate.value;
};

// Check if a date is today
const isToday = (date: Date | null): boolean => {
	if (!date) return false;
	return date.toDateString() === new Date().toDateString();
};

// Apply selection and emit event
const applySelection = (): void => {
	const start = new Date(startDate.value);
	start.setHours(startHour.value);
	start.setMinutes(startMinute.value);

	const end = new Date(endDate.value);
	end.setHours(endHour.value);
	end.setMinutes(endMinute.value);

	emit('range-changed', start.toISOString(), end.toISOString());
	showPicker.value = false;
};

// Cancel selection
const cancelSelection = (): void => {
	showPicker.value = false;
};

// Apply predefined period
const applyPeriod = (period: PeriodItem): void => {
	if (period.startDateTime && period.endDateTime) {
		startDate.value = new Date(period.startDateTime);
		endDate.value = new Date(period.endDateTime);

		// Extract time from datetime strings
		const startTime = new Date(period.startDateTime);
		const endTime = new Date(period.endDateTime);

		startHour.value = startTime.getHours();
		startMinute.value = startTime.getMinutes();
		endHour.value = endTime.getHours();
		endMinute.value = endTime.getMinutes();
	}
};

// Initialize time values from props
onMounted((): void => {
	if (props.startDateTime) {
		const date = new Date(props.startDateTime);
		startHour.value = date.getHours();
		startMinute.value = date.getMinutes();
		// Set current month to the month of initial start date
		currentMonth.value = new Date(date.getFullYear(), date.getMonth(), 1);
	} else {
		// Default to February 2025 as shown in the image
		currentMonth.value = new Date(2025, 1, 1);
	}

	if (props.endDateTime) {
		const date = new Date(props.endDateTime);
		endHour.value = date.getHours();
		endMinute.value = date.getMinutes();
	}
});

// Generate hours (1-24) and minutes (0-59) for dropdowns
const hours = Array.from({ length: 24 }, (_, i) => i + 1);
const minutes = Array.from({ length: 60 }, (_, i) => i);

// Display formatted range with time
const formattedRange = computed((): string => {
	return `${formatDateTime(startDate.value, startHour.value, startMinute.value)} — ${formatDateTime(endDate.value, endHour.value, endMinute.value)}`;
});

// Navigate to the previous month
const goToPreviousMonth = (): void => {
	const date = new Date(currentMonth.value);
	date.setMonth(date.getMonth() - 1);
	currentMonth.value = date;
};

// Navigate to the next month
const goToNextMonth = (): void => {
	const date = new Date(currentMonth.value);
	date.setMonth(date.getMonth() + 1);
	currentMonth.value = date;
};
</script>

<template>
	<div class="date-range-picker">
		<div class="date-range-display" @click="showPicker = !showPicker">
			{{ formattedRange }}
		</div>

		<v-dialog v-model="showPicker" max-width="500">
			<v-card class="lumio-card">
				<v-card-title class="px-3 py-1">
					Select Date & Time Range
				</v-card-title>
				<v-card-text class="pa-2">
					<div class="date-range-picker-dropdown">
						<div class="date-range-picker-container">
							<!-- Left sidebar with predefined periods -->
							<div class="predefined-periods">
								<div class="period-header">Periods</div>
								<div
									v-for="period in periods"
									:key="period.label"
									class="period-item"
									@click="applyPeriod(period)"
								>
									{{ period.label }}
								</div>
								<div class="text-left ma-2 pa-3 text-grey-darken-1">
									<i><small>Or select custom range on the right</small></i>
								</div>
							</div>

							<!-- Right side with date inputs and calendars -->
							<div class="date-selection">
								<div class="date-inputs">
									<div class="date-input-group border pa-2 bg-grey-lighten-5">
										<div class="date-label">Start date</div>
										<div class="date-display">{{ formatDate(startDate) }}</div>
										<div class="time-selectors d-flex ">
											<select class="bg-white" v-model="startHour" style="width: 100%;">
												<option v-for="hour in hours" :key="`start-hour-${hour}`" :value="hour - 1">
													{{ hour.toString().padStart(2, '0') }}
												</option>
											</select>
											:
											<select class="bg-white" v-model="startMinute"  style="width: 100%;">
												<option v-for="minute in minutes" :key="`start-minute-${minute}`" :value="minute">
													{{ minute.toString().padStart(2, '0') }}
												</option>
											</select>
										</div>
									</div>
									<div class="date-separator">—</div>
									<div class="date-input-group border pa-2 bg-grey-lighten-5">
										<div class="date-label">End date</div>
										<div class="date-display">{{ formatDate(endDate) }}</div>
										<div class="time-selectors d-flex ">
											<select class="bg-white" v-model="endHour" style="width: 100%;">
												<option v-for="hour in hours" :key="`end-hour-${hour}`" :value="hour - 1">
													{{ hour.toString().padStart(2, '0') }}
												</option>
											</select>
											:
											<select class="bg-white" v-model="endMinute" style="width: 100%;"    >
												<option v-for="minute in minutes" :key="`end-minute-${minute}`" :value="minute">
													{{ minute.toString().padStart(2, '0') }}
												</option>
											</select>
										</div>
									</div>
								</div>

								<div class="calendars-container">
									<!-- Calendar header row with weekdays -->
									<div class="weekdays-header">
										<div v-for="day in ['S', 'M', 'T', 'W', 'T', 'F', 'S']" :key="day" class="weekday">
											{{ day }}
										</div>
									</div>

									<!-- Current month header -->
									<div class="month-header">
										<button class="month-nav-button" @click="goToPreviousMonth">&lt;</button>
										<span>{{ currentMonthFormatted }}</span>
										<button class="month-nav-button" @click="goToNextMonth">&gt;</button>
									</div>

									<!-- Current month calendar -->
									<div class="calendar-month">
										<div class="calendar-days">
											<div
												v-for="(day, index) in currentMonthDays"
												:key="`current-${index}`"
												class="calendar-day"
												:class="{
                    'empty': !day,
                    'start-date': isStartDate(day),
                    'end-date': isEndDate(day),
                    'in-range': isInRange(day),
                    'today': isToday(day),
                    'disabled': day && startToday && isBeforeToday(day)
                  }"
												@click="day ? selectDate(day) : null"
											>
												{{ day ? day.getDate() : '' }}
											</div>
										</div>
									</div>

									<!-- Next month header -->
									<div class="month-header">
										<button class="month-nav-button" @click="goToPreviousMonth">&lt;</button>
										<span>{{ nextMonthFormatted }}</span>
										<button class="month-nav-button" @click="goToNextMonth">&gt;</button>
									</div>

									<!-- Next month calendar -->
									<div class="calendar-month">
										<div class="calendar-days">
											<div
												v-for="(day, index) in nextMonthDays"
												:key="`next-${index}`"
												class="calendar-day"
												:class="{
                    'empty': !day,
                    'start-date': isStartDate(day),
                    'end-date': isEndDate(day),
                    'in-range': isInRange(day),
                    'today': isToday(day),
                    'disabled': day && startToday && isBeforeToday(day)
                  }"
												@click="day ? selectDate(day) : null"
											>
												{{ day ? day.getDate() : '' }}
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</v-card-text>
				<v-card-actions class="d-flex justify-space-between px-4 py-0">
					<v-btn color="primary" variant="text" @click="cancelSelection">Cancel</v-btn>
					<v-btn color="primary" variant="flat" @click="applySelection">Apply</v-btn>
				</v-card-actions>

			</v-card>
		</v-dialog>


	</div>
</template>

<style scoped>
.date-range-picker {
	position: relative;
	display: inline-block;
	/*font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; */
}

.date-range-display {
	padding: 10px 10px;
	border: 1px solid #3c3c3c;
	border-radius: 4px;
	cursor: pointer;
	background-color: #fff;
}

.date-range-picker-dropdown {

	background: white;
	border: 1px solid #ddd;
	border-radius: 4px;
}

.date-range-picker-container {
	display: flex;
	width: 100%;
}

.predefined-periods {
	width: 170px;
	border-right: 1px solid #eee;
	padding: 0;
	background-color: #f7f9fc;
	border-top-left-radius: 8px;
	border-bottom-left-radius: 8px;
}

.period-header {
	padding: 8px 12px;
	font-weight: 600;
	background-color: rgb(244, 244, 244);
	color: #333;
	border-top-left-radius: 8px;
}

.period-item {
	padding: 6px 12px;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	color: #333;
	font-size: 13px;
}

.period-item:hover {
	background-color: #f5f6db;
}

.next-indicator {
	font-size: 16px;
	color: #999;
}

.date-selection {
	flex: 1;
	padding: 5px 12px;
}

.date-inputs {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.date-input-group {
	flex: 1;
	border-radius: 5px;
}

.date-label {
	font-size: 11px;
	color: #666;
}

.date-display {
	font-weight: 500;
	color: #333;
	font-size: 13px;
}

.date-separator {
	margin: 0 15px;
	color: #666;
}

.time-selectors {
	display: flex;
	align-items: center;
}

.time-selectors select {
	padding: 3px 6px;
	border: 1px solid #ddd;
	border-radius: 4px;
	margin: 0 2px;
	font-size: 12px;
}

.calendars-container {
	margin-bottom: 12px;
}

.weekdays-header {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	text-align: center;
	margin-bottom: 4px;
}

.weekday {
	font-size: 11px;
	color: #666;
	padding: 3px;
}

.month-header {
	font-weight: 600;
	margin: 8px 0 4px;
	color: #333;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 13px;
}

.month-nav-button {
	background: none;
	border: none;
	cursor: pointer;
	color: #666;
	font-size: 14px;
	padding: 0 4px;
	border-radius: 4px;
}

.month-nav-button:hover {
	background-color: rgba(239, 250, 26, 0.38);
	color: rgb(30, 30, 30);
}

.calendar-month {
	margin-bottom: 8px;
}

.calendar-days {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 2px;
}

.calendar-day {
	border:1px solid #f6f6f6;
	height: 26px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	position: relative;
	font-size: 11px;
	color: #333;
	border-radius: 5%;
}

.calendar-day:hover:not(.empty) {
	background-color: rgba(239, 250, 26, 0.45);
}

.calendar-day.start-date,
.calendar-day.end-date {
	background-color: rgb(30, 30, 30) !important;
	color: white !important;
	font-weight: 500 !important;
}

.calendar-day.in-range {
	background-color: #effa1a;
}

.calendar-day.today {
	font-weight: bold;
	color: rgb(30, 30, 30);
	border: 1px dotted rgb(149, 147, 147);
	background: #fbfdda;
}

.calendar-day.empty,
.calendar-day.disabled {
	cursor: default;
}

.calendar-day.disabled {
	color: #ccc;
	pointer-events: none;
}

.actions {
	display: flex;
	justify-content: flex-end;
	gap: 8px;
}

.cancel-button, .apply-button {
	padding: 6px 12px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-weight: 500;
	font-size: 13px;
}

.cancel-button {
	background-color: transparent;
	color: rgb(30, 30, 30);
}

.apply-button {
	background-color: rgb(30, 30, 30);
	color: white;
}

/* Fix for better alignment */
.calendar-days {
	grid-template-rows: repeat(6, 26px);
}
</style>