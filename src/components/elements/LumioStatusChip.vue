<template>
	<v-chip
        v-if="status"
		:class="fullWidth ? 'w-100' : ''"
		density="compact"
		variant="flat"
		size="small"
		label
		:tag="fullWidth ? 'div' : 'span'"
		exact
		:text="status.trim()"
		:style="`
	      backgroundColor: ${getStatusColor(status)};
	      color: ${getStatusTextColor(status)};
	      font-weight: bold;

	      display: ${fullWidth ? 'flex' : 'inline'};
	      justify-content: center;
	      align-items: center;
	    `"
	>
	</v-chip>
</template>

<script setup lang="ts">

import {getStatusColor, getStatusTextColor} from "@/composables/misc.helper";

defineProps({
	status: {
		type: String,
		required: true,
	},
	fullWidth: {
		type: Boolean,
		default: true,
	},
});
</script>