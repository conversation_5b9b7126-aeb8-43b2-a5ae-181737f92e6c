<template>
	<v-chip
        v-if="priority"
		:class="fullWidth ? 'w-100' : ''"
		density="compact"
		variant="flat"
		size="small"
		label
		:tag="fullWidth ? 'div' : 'span'"
		exact
		:text="priority.trim()"
		:style="`
	      backgroundColor: ${getPriorityColor(priority)};
	      color: ${getPriorityTextColor(priority)};
	      font-weight: bold;

	      display: ${fullWidth ? 'flex' : 'inline'};
	      justify-content: center;
	      align-items: center;
	    `"
	>
	</v-chip>
</template>

<script setup lang="ts">

import {getPriorityColor, getPriorityTextColor} from "@/composables/misc.helper";

defineProps({
	priority: {
		type: String,
		required: true,
	},
	fullWidth: {
		type: Boolean,
		default: true,
	},
});
</script>