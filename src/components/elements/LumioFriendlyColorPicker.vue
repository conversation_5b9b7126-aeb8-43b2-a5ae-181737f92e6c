<template>
	<v-card
		class="lumio-card mb-4">
		<v-card-title>
			<h5>Pick Color</h5>
		</v-card-title>
		<v-card-text class="pa-4">
			<div class="color-grid">
				<div
					v-for="color in colors"
					:key="color"
					:style="`background: ${color}`"
					:class="`color-grid-item ${selectedColor === color ? 'color-grid-item-selected' : ''}`"
					@click="selectedColor = color"
				>
					&nbsp;
				</div>
			</div>
		</v-card-text>
		<v-card-actions>
			<v-btn
				variant="text"
				@click="$emit('cancel-color-pick')">
				CANCEL
			</v-btn>
			<v-btn
				variant="flat"
				color="primary"
				@click="$emit('color-selected', selectedColor)"
			>
				SET COLOR
			</v-btn>
		</v-card-actions>

	</v-card>

</template>

<script setup type="ts">

import { fetchFriendlyColors } from '@/composables/misc.helper';

const colors = ref([]);
const selectedColor = ref('');

const props = defineProps({
	color: {
		type: String,
		required: true,
	}
});

const emits = defineEmits(['color-selected', 'cancel-color-pick']);

onMounted(async () => {
	colors.value = fetchFriendlyColors();
	if(props.color) {
		selectedColor.value = props.color;
	}
});
</script>

<style scoped lang="scss">

.color-grid {
	display: grid;
	grid-template-columns: repeat(10, 1fr);
	gap: 5px;
}
.color-grid-item {
	width: 100%;
	height: 20px;
	border-radius: 5px;
	cursor: pointer;
}
.color-grid-item-selected {
	border: 2px solid #000;
}

</style>