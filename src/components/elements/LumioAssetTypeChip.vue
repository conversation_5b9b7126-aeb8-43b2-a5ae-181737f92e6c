<template>
	<v-chip
        v-if="assetType"
		:class="fullWidth ? 'w-100' : ''"
		density="compact"
		variant="flat"
		size="small"
		label
		:tag="fullWidth ? 'div' : 'span'"
		exact
		:text="assetType.trim()"
		:style="`
	      backgroundColor: ${getAssetTypeColor(assetType)};
	      color: ${getAssetTypeTextColor(assetType)};
	      font-weight: bold;

	      display: ${fullWidth ? 'flex' : 'inline'};
	      justify-content: center;
	      align-items: center;
	    `"
	>
	</v-chip>
</template>

<script setup lang="ts">

import {getAssetTypeColor, getAssetTypeTextColor} from "@/composables/misc.helper";

defineProps({
	assetType: {
		type: String,
		required: true,
	},
	fullWidth: {
		type: Boolean,
		default: true,
	},
});
</script>