<template>
	<div class="timeline-container">
		<table class="timeline-table">
			<thead>
			<tr>
				<th class="info-column" style="text-align: right;padding-right:5px;">
					<div class="py-0 d-flex justify-center align-center">
						<v-select
							class="w-100"
							density="compact"
							variant="solo-filled"
							v-model="stepString"
							:items="getIntervals"
							hide-details
							:label="`Interval`"
						></v-select>
						<div class="w-100 text-xs">{{ hoursToShow }} Hours:</div>
					</div>

				</th>
				<th class="timeline-column" :colspan="visibleColumns">
					<div v-if="isCurrentTimeVisible" class="current-time-overlay">
						<div class="current-time-indicator" :style="{ left: `${calculateCurrentTimePosition()}%` }"></div>
<!--						<div class="current-time-label rotate90" :style="{ left: `${calculateCurrentTimePosition()}%`, top:'50px', zIndex: 10 }">-->
<!--							{{ formatTime(currentTime) }}-->
<!--						</div>-->
					</div>
					<div class="timeline-header">
						<div v-for="(hourLabel, index) in hourLabels" :key="index+hourLabel.hour" class="hour-cell" >
							<template v-if="step > 1">
								{{ ((index+1) * step) - step + 1 }} - {{ (index+1) * step }}
							</template>
							<template v-else>
								{{ (index+1) * step }}
							</template>

						</div>
					</div>
				</th>
			</tr>
			<tr>
				<th class="info-column" style="text-align: right;padding-right:10px;">

					Dates:
				</th>
				<th class="timeline-column" :colspan="visibleColumns">
					<div v-if="isCurrentTimeVisible" class="current-time-overlay">
						<div class="current-time-indicator" :style="{ left: `${calculateCurrentTimePosition()}%` }"></div>
						<div class="current-time-label" :style="{ left: `${calculateCurrentTimePosition()}%`, top: '0' }">
							<!--							{{ formatTime(currentTime) }}-->
						</div>
					</div>
					<div class="timeline-header">
						<div v-for="(hourLabel, index) in hourLabels" :key="index" class="hour-cell" style="font-size:9px;">
							<div class="d-flex justify-center flex-column">
								<div class="startTime" v-if="hourLabel.startDay !== hourLabel.day">
									<span v-html="`${hourLabel.startDay}`"></span>
								</div>
								<div  v-if="hourLabel.startDay !== hourLabel.day">
									<v-icon>mdi-arrow-down-thin</v-icon>
								</div>
								<div class="endTime">
									<span v-html="`${hourLabel.day}`"></span>
								</div>
							</div>

						</div>
					</div>
				</th>
			</tr>
			<tr>
				<th class="info-column" style="text-align: right;padding-right:10px;">Time:</th>
				<th class="timeline-column" :colspan="visibleColumns">
					<div v-if="isCurrentTimeVisible" class="current-time-overlay">
						<div class="current-time-indicator" :style="{ left: `${calculateCurrentTimePosition()}%` }"></div>
						<div class="current-time-label" :style="{ left: `${calculateCurrentTimePosition()}%`, top: '0' }">
							<!--							{{ formatTime(currentTime) }}-->
						</div>
					</div>
					<div class="timeline-header">
						<div v-for="(hourLabel, index) in hourLabels" :key="index" class="hour-cell">
							{{ hourLabel.hour }}
						</div>
					</div>
				</th>
			</tr>
			</thead>
			<tbody>
			<tr v-for="item in items" :key="item.id" :class="item.rowClasses">
				<td class="info-column py-2">
					<div class="pl-4">

						<h5>
							<LumioPriorityChip :priority="item.priority" :full-width="false" class="mr-2" />
							<strong v-html="item.label"></strong> (ISR-{{item.isrDesignation}})
						</h5>
					</div>
				</td>
				<td class="timeline-column" :colspan="visibleColumns">
					<div v-if="isCurrentTimeVisible" class="current-time-overlay">
						<div class="current-time-indicator" :style="{ left: `${calculateCurrentTimePosition()}%` }"></div>
<!--						<div class="current-time-label" :style="{ left: `${calculateCurrentTimePosition()}%` }">-->

<!--						</div>-->
					</div>
					<div class="timeline-content">
						<div
							class="timeline-segment cursor-pointer"
							@click="showDetails(item)"
							:class="[{
                  'current-active-mission': isActiveBeforeTimeline(item.startDateTime),
                  'current-continuing-mission': continuesAfterTimeline(item.endDateTime)
                }, item.segmentClasses]"
							:style="{
                  left: `${calculateStartPosition(item.startDateTime)}%`,
                  width: `${calculateWidth(item.startDateTime, item.endDateTime)}%`,
                  backgroundColor: item.itemBackgroundColor || defaultBackgroundColor,
                  color: getContrastingTextColor(item.itemBackgroundColor),
                }"
						>
							{{item.callSign}}

						</div>
					</div>
				</td>
			</tr>
			</tbody>
		</table>
		<v-dialog
			v-model="dialogOpen"
			max-width="600px"
			transition="dialog-bottom-transition"
			:close-on-content-click="false"
			:close-on-esc="true"
			:close-on-overlay-click="true">
			<v-card class="lumio-card">
				<v-card-title :style="`
							border: 4px solid ${currentItemSelected?.itemBackgroundColor};
							border-left:10px solid ${currentItemSelected?.itemBackgroundColor};
							border-right:10px solid ${currentItemSelected?.itemBackgroundColor};`">
					<h4 class="px-4 " >
						{{ currentItemSelected?.assetName }}
						<v-chip size="small" label color="secondary" variant="flat" class="ml-2">
							{{ currentItemSelected?.callSign }}
						</v-chip>
					</h4>
					<v-btn variant="text" icon @click="dialogOpen = false">
						<v-icon>mdi-close</v-icon>
					</v-btn>
				</v-card-title>
				<v-card-text>
					<div class="d-flex mb-2">

						<span class="item-label">Mission: </span>
						<span class="item-date" v-html="currentItemSelected?.label"></span>

					</div>
					<div class="d-flex mb-2">

						<span class="item-label">Asset: </span>
						<span class="item-date" v-html="currentItemSelected?.assetName"></span>

					</div>
					<div class="d-flex mb-2">
						<span class="item-label">Callsign: </span>
						<span class="item-date">{{ currentItemSelected?.callSign }}</span>
					</div>
					<div class="d-flex mb-2">

						<span class="item-label">Start: </span>
						<span class="item-date">{{ currentItemSelected?.startDateTime ? formatDate(currentItemSelected?.startDateTime) : 'N/A' }}</span>

					</div>
					<div class="d-flex mb-2">

						<span class="item-label">End: </span>
						<span class="item-date">{{ currentItemSelected?.endDateTime ? formatDate(currentItemSelected?.endDateTime) : 'N/A' }}</span>

					</div>
					<div class="d-flex mb-2">
						<span class="item-label">Priority: </span>
						<LumioPriorityChip :priority="currentItemSelected?.priority || 'N/A'" :full-width="false"></LumioPriorityChip>
					</div>
					<div class="d-flex mb-2">
						<span class="item-label">ISR Track: </span>
						<span class="item-date"><v-chip label size="small"  variant="flat" density="compact" color="primary" ><strong class="text-secondary">ISR-{{currentItemSelected?.isrDesignation}}</strong></v-chip>  {{ currentItemSelected?.isrTrackName }}</span>
					</div>
				</v-card-text>
			</v-card>
		</v-dialog>
	</div>
	<div class="pa-10">

	</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { TimelineItem } from '@/types/Global.type';
import {getContrastingTextColor} from "@/composables/misc.helper";

interface Props {
	items: TimelineItem[];
	showCurrentTime?: boolean;
	timelineStart?: Date | string;
	hoursToShow?: number;
//	step?: number; // New prop for specifying the time step in hours
}

const props = withDefaults(defineProps<Props>(), {
	showCurrentTime: true,
	timelineStart: () => new Date(),
	hoursToShow: 24,
//	step: 1 // Default step is 1 hour
});

const defaultBackgroundColor = 'rgb(66, 66, 66)';
const currentTime = ref(new Date());
const currentItemSelected = ref<TimelineItem | null>(null);
// Calculate the number of visible columns based on hoursToShow and step
const visibleColumns = computed(() => Math.ceil(props.hoursToShow / step.value));

const step = ref<number>(1);
const stepString = computed({
	get: () => step.value.toString(),
	set: (val: string) => (step.value = parseInt(val)),
});
const dialogOpen = ref(false);

const getIntervals = computed(() => {
	if (props.hoursToShow > 168) {
		return ['48', '72', '96', '120', '144'];
	}
	if (props.hoursToShow > 144) {
		return ['24', '48', '72', '96', '120'];
	}
	if (props.hoursToShow > 120) {
		return ['24','48', '72', '96'];
	}
	if (props.hoursToShow > 95) {
		return ['24', '48', '72'];
	}
	if (props.hoursToShow > 71) {
		return ['12', '24', '48'];
	}
	if (props.hoursToShow > 48) {
		return ['4', '6', '12'];
	}
	if (props.hoursToShow > 24) {
		return ['1', '2', '4',  '6', '12'];
	}
	return ['1', '4', '6'];
});

// Update current time every minute
setInterval(() => {
	currentTime.value = new Date();
}, 60000);

// Generate hour labels based on timeline start time and step
const hourLabels = computed(() => {
	const startTime = parseDateTime(props.timelineStart);
	const labels = [];

	for (let i = 0; i < visibleColumns.value; i++) {
		const labelTime = new Date(startTime.getTime());
		const labelPrevTime = new Date(startTime.getTime());
		// Increment by the step amount for each label
		labelTime.setHours(startTime.getHours() + i * step.value);
		labelPrevTime.setHours((startTime.getHours() + i * step.value) - step.value);
		let formattedLabel = {
			hour: '0',
			day: '',
			startDay: '',
		}
		formattedLabel.hour = `${labelTime.getHours()}:${String(labelTime.getMinutes()).padStart(2, '0')}`;
		formattedLabel.day =
			`${labelTime.toLocaleDateString('en-US', { month: 'short' })} ${labelTime.getDate()}`;
		formattedLabel.startDay =
			`${labelPrevTime.toLocaleDateString('en-US', { month: 'short' })} ${labelPrevTime.getDate()}`;
		labels.push(formattedLabel);
	}

	return labels;
});

const isCurrentTimeVisible = computed(() => {
	return props.showCurrentTime;
});

// Convert string dates to Date objects
const parseDateTime = (dateTime: Date | string): Date => {
	if (dateTime instanceof Date) {
		return dateTime;
	}
	return new Date(dateTime);
};

// Format date for display
const formatDate = (dateTime: Date | string): string => {
	const date = parseDateTime(dateTime);
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'numeric',
		day: 'numeric',
		hour: 'numeric',
		minute: 'numeric'
	});
};

// Format time for display
// eslint-disable-next-line @ts-ignore
// const formatTime = (dateTime: Date | string): string => {
// 	const date = parseDateTime(dateTime);
// 	return date.toLocaleTimeString('en-US', {
// 		hour: '2-digit',
// 		minute: '2-digit',
// 		day: '2-digit',
// 		month: '2-digit',
// 	});
// };

// Format time range for display
// const formatTimeRange = (startDateTime: Date | string, endDateTime: Date | string): string => {
// 	const start = parseDateTime(startDateTime);
// 	const end = parseDateTime(endDateTime);
// 	return `${start.toLocaleDateString()} ${formatTime(start)} - ${formatTime(end)}`;
// };

// Calculate start position as percentage based on timeline hours, accounting for step
const calculateStartPosition = (startDateTime: Date | string): number => {
	const start = parseDateTime(startDateTime);
	const timelineStart = parseDateTime(props.timelineStart);

	// Check if item starts before timeline starts
	if (start < timelineStart) {
		return 0; // Align with timeline start
	}

	// Calculate hours difference between item start and timeline start
	const diffMs = start.getTime() - timelineStart.getTime();
	const diffHours = diffMs / (1000 * 60 * 60);

	// Convert to percentage based on total hours shown
	return (diffHours / props.hoursToShow) * 100;
};

// Calculate width as percentage of total hours shown
const calculateWidth = (startDateTime: Date | string, endDateTime: Date | string): number => {
	let start = parseDateTime(startDateTime);
	const end = parseDateTime(endDateTime);
	const timelineStart = parseDateTime(props.timelineStart);
	const timelineEnd = new Date(timelineStart.getTime() + props.hoursToShow * 60 * 60 * 1000);

	// If item starts before timeline starts, adjust start time
	if (start < timelineStart) {
		start = new Date(timelineStart);
	}

	// Calculate actual end time (capped by timeline end)
	const actualEnd = end > timelineEnd ? timelineEnd : end;

	// Calculate duration in hours
	const durationMs = actualEnd.getTime() - start.getTime();
	const durationHours = durationMs / (1000 * 60 * 60);

	// Convert to percentage based on total hours shown
	return (durationHours / props.hoursToShow) * 100;
};

// Check if item starts before timeline start
const isActiveBeforeTimeline = (startDateTime: Date | string): boolean => {
	const start = parseDateTime(startDateTime);
	const timelineStart = parseDateTime(props.timelineStart);
	return start < timelineStart;
};

// Check if item continues after timeline end
const continuesAfterTimeline = (endDateTime: Date | string): boolean => {
	const end = parseDateTime(endDateTime);
	const timelineStart = parseDateTime(props.timelineStart);
	const timelineEnd = new Date(timelineStart.getTime() + props.hoursToShow * 60 * 60 * 1000);
	return end > timelineEnd;
};

// Check if current time is within item's time range
// const isWithinTimeRange = (startDateTime: Date | string, endDateTime: Date | string): boolean => {
// 	const start = parseDateTime(startDateTime);
// 	const end = parseDateTime(endDateTime);
// 	const now = currentTime.value;
//
// 	return now >= start && now <= end;
// };

// Calculate current time position as percentage of total hours shown
const calculateCurrentTimePosition = (): number => {
	const now = currentTime.value;
	const timelineStart = parseDateTime(props.timelineStart);

	// Calculate hours difference between current time and timeline start
	const diffMs = now.getTime() - timelineStart.getTime();
	const diffHours = diffMs / (1000 * 60 * 60);

	// Convert to percentage based on total hours shown
	return (diffHours / props.hoursToShow) * 100;
};


const showDetails = (item: TimelineItem) => {
	currentItemSelected.value = item;
	dialogOpen.value = true;
};

//set interval for on mont
onMounted(() => {
	step.value = getIntervals.value[0] ? parseInt(getIntervals.value[0]) : 1;
});

</script>

<style scoped>
.timeline-container {
	position: relative;
	width: 100%;
	overflow-x: hidden;
	scrollbar-gutter: stable;

}

.timeline-table {
	width: 100%;
	border-collapse: collapse;
}

.timeline-table th, .timeline-table td {
	border: 1px solid #ccc;
}

.info-column {
	width: 400px;
	min-width:400px;
}

.item-label {
	color: #555;
}

.item-date {
	font-weight: bold;

}

.item-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-weight: bold;
}

.priority-high {
	background-color: #ffdddd;
	color: #cc0000;
}

.priority-medium {
	background-color: #ffffdd;
	color: #cccc00;
}

.priority-low {
	background-color: #ddffdd;
	color: #00cc00;
}

.timeline-column {
	position: relative;
	min-width: 800px;
	width: 100%;
}

.timeline-header {
	display: flex;
}

.hour-cell {
	flex: 1;
	text-align: center;
	border-right: 1px solid #ccc;
	font-size: 9px;
	overflow: hidden;
	white-space: nowrap;
	padding-left: 2px;
	padding-right: 2px;
}

.timeline-content {
	position: relative;
	height: 50px;
}

.timeline-segment {
	position: absolute;
	height: 40px;
	top: 5px;
	border-radius: 4px;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 10px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 12px;
}
.timeline-segment span {

	overflow: hidden;
	background-color: rgba(0, 0, 0, 0.5);
	padding:3px;

}

.current-active-mission {
	border-left: 6px solid #ff0000;
	padding-left: 12px;
}

.current-active-mission::before {
	content: '←';
	color: white;
	position: absolute;
	left: 15px;
	font-size: 30px;
	top: 45%;
	transform: translateY(-45%);
}

.current-continuing-mission {
	border-right: 6px solid #ff0000;
	padding-right: 12px;
}

.current-active-mission.current-continuing-mission {
	border-left: 6px solid #ff0000;
	border-right: 6px solid #ff0000;

}
.current-continuing-mission::after {
	content: '→';
	color: white;
	position: absolute;
	right: 15px;
	font-size: 30px;
	top: 45%;
	transform: translateY(-45%);
}


.current-time-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 100;
}

.current-time-indicator {
	position: absolute;
	top:-20px;
	height: 100%;
	width: 2px;
	background-color: rgba(255, 0, 0, 0.44);
	z-index: 10;
}

.current-time-label {
	position: absolute;
	color: red;
	font-size: 12px;
	top:-15px;
	background-color: white;
	transform: translateX(-50%);
}

.isr-track-segment {
	border: 2px dashed #effa1a;
	box-shadow: 0 0 5px #2e2e2e;
}

.rotate90{
	transform: rotate(90deg);
}

.item-label{
	width:150px;
	min-width:150px;
	text-align: right;
	padding-right: 10px;
}

</style>
