<template>
    <div class="date-time-picker-container" :class="customClasses.container">
        <div
            class="date-time-input-wrapper"
            :class="customClasses.inputWrapper"
            @click="togglePicker"
        >
            <input
                type="text"
                :value="formattedDateTime"
                readonly
                class="date-time-input"
                :class="customClasses.input"
                :placeholder="placeholder"
            />
            <button
                type="button"
                class="date-time-toggle-btn"
                :class="customClasses.toggleButton"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                >
                    <rect
                        x="3"
                        y="4"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                    ></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
            </button>
        </div>

        <!-- Modal Overlay -->
        <Teleport to="body">
            <transition name="fade">
                <div
                    v-if="isOpen"
                    class="date-time-modal-overlay"
                    :class="customClasses.overlay"
                >
                    <div class="date-time-modal" :class="customClasses.modal">
                        <div
                            class="date-time-modal-header"
                            :class="customClasses.modalHeader"
                        >
                            <h3
                                class="date-time-modal-title"
                                :class="customClasses.modalTitle"
                            >
                                Select Date & Time
                            </h3>
                        </div>

                        <div
                            class="date-time-modal-body"
                            :class="customClasses.modalBody"
                        >
                            <div
                                class="date-time-picker-header"
                                :class="customClasses.header"
                            >
                                <button
                                    type="button"
                                    class="month-nav-btn prev"
                                    :class="customClasses.navButton"
                                    @click="prevMonth"
                                    :disabled="isPrevMonthDisabled"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    >
                                        <polyline
                                            points="15 18 9 12 15 6"
                                        ></polyline>
                                    </svg>
                                </button>
                                <div
                                    class="current-month"
                                    :class="customClasses.currentMonth"
                                >
                                    {{ currentMonthName }} {{ currentYear }}
                                </div>
                                <button
                                    type="button"
                                    class="month-nav-btn next"
                                    :class="customClasses.navButton"
                                    @click="nextMonth"
                                    :disabled="isNextMonthDisabled"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    >
                                        <polyline
                                            points="9 18 15 12 9 6"
                                        ></polyline>
                                    </svg>
                                </button>
                            </div>

                            <div
                                class="calendar-grid"
                                :class="customClasses.calendarGrid"
                            >
                                <div
                                    class="weekday-header"
                                    :class="customClasses.weekdayHeader"
                                >
                                    <div
                                        v-for="day in weekdays"
                                        :key="day"
                                        class="weekday"
                                        :class="customClasses.weekday"
                                    >
                                        {{ day }}
                                    </div>
                                </div>

                                <div
                                    class="days-grid"
                                    :class="customClasses.daysGrid"
                                >
                                    <div
                                        v-for="(day, index) in calendarDays"
                                        :key="index"
                                        class="calendar-day"
                                        :class="[
                                            customClasses.calendarDay,
                                            {
                                                'other-month': day.isOtherMonth,
                                                selected: isSelectedDate(
                                                    day.date,
                                                ),
                                                disabled: isDateDisabled(
                                                    day.date,
                                                ),
                                                today: isToday(day.date),
                                            },
                                        ]"
                                        @click="
                                            !isDateDisabled(day.date) &&
                                                selectDate(day.date)
                                        "
                                    >
                                        {{ day.dayOfMonth }}
                                    </div>
                                </div>
                            </div>

                            <div
                                class="time-picker"
                                :class="customClasses.timePicker"
                            >
                                <div
                                    class="time-label"
                                    :class="customClasses.timeLabel"
                                >
                                    Time:
                                </div>
                                <div
                                    class="time-inputs"
                                    :class="customClasses.timeInputs"
                                >
                                    <select
                                        v-model="hours"
                                        class="time-select hours"
                                        :class="customClasses.timeSelect"
                                        @change="updateTempSelectedTime"
                                    >
                                        <option
                                            v-for="hour in hoursOptions"
                                            :key="hour"
                                            :value="hour"
                                        >
                                            {{ hour }}
                                        </option>
                                    </select>
                                    <span
                                        class="time-separator"
                                        :class="customClasses.timeSeparator"
                                        >:</span
                                    >
                                    <select
                                        v-model="minutes"
                                        class="time-select minutes"
                                        :class="customClasses.timeSelect"
                                        @change="updateTempSelectedTime"
                                    >
                                        <option
                                            v-for="minute in minutesOptions"
                                            :key="minute"
                                            :value="minute"
                                        >
                                            {{ minute }}
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <div
                                v-if="props.showTimezone"
                                class="timezone-picker"
                                :class="customClasses.timePicker"
                            >
                                <div
                                    class="timezone-label"
                                    :class="customClasses.timeLabel"
                                >
                                    Timezone:
                                </div>
                                <div
                                    class="timezone-input"
                                    :class="customClasses.timeInputs"
                                >
                                    <select
                                        v-model="selectedTimezone"
                                        class="timezone-select"
                                        :class="customClasses.timezoneSelect"
                                        @change="handleTimezoneChange"
                                    >
                                        <option
                                            v-for="tz in timezoneOptions"
                                            :key="tz.value"
                                            :value="tz.value"
                                        >
                                            {{ tz.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div
                            class="date-time-modal-footer"
                            :class="customClasses.modalFooter"
                        >
                            <button
                                type="button"
                                class="modal-btn cancel"
                                :class="customClasses.cancelButton"
                                @click="cancelSelection"
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                class="modal-btn submit"
                                :class="customClasses.submitButton"
                                @click="applySelection"
                            >
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            </transition>
        </Teleport>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import dayjs from 'dayjs';

interface CalendarDay {
    date: Date;
    dayOfMonth: number;
    isOtherMonth: boolean;
}

interface CustomClasses {
    container?: string;
    inputWrapper?: string;
    input?: string;
    toggleButton?: string;
    overlay?: string;
    modal?: string;
    modalHeader?: string;
    modalTitle?: string;
    modalBody?: string;
    modalFooter?: string;
    header?: string;
    navButton?: string;
    currentMonth?: string;
    calendarGrid?: string;
    weekdayHeader?: string;
    weekday?: string;
    daysGrid?: string;
    calendarDay?: string;
    timePicker?: string;
    timeLabel?: string;
    timeInputs?: string;
    timeSelect?: string;
    timeSeparator?: string;
    timezoneSelect?: string;
    cancelButton?: string;
    submitButton?: string;
}

// Props
const props = withDefaults(
    defineProps<{
        modelValue?: Date | null | string;
        minDateTime?: Date | null;
        maxDateTime?: Date | null;
        format?: string;
        placeholder?: string;
        customClasses?: CustomClasses;
        open?: boolean;
        showTimezone?: boolean;
        timezone?: string;
        label?: string;
    }>(),
    {
        modelValue: null,
        minDateTime: null,
        maxDateTime: null,
        format: 'YYYY-MM-DD HH:mm',
        placeholder: 'Select date and time',
        customClasses: () => ({}),
        open: false,
        showTimezone: false,
        timezone: '+0000',
    },
);
// Emits
const emit = defineEmits<{
    (e: 'update:modelValue', value: Date | null): void;
    (e: 'change', value: Date): void;
    (e: 'update:open', value: boolean): void;
    (e: 'update:timezone', value: string): void;
}>();

// Format functions
const padZero = (num: number): string => num.toString().padStart(2, '0');

// Time options for dropdowns
const hoursOptions = Array.from({ length: 24 }, (_, i) => padZero(i));
const minutesOptions = Array.from({ length: 60 }, (_, i) => padZero(i));

// Refs
const isOpen = ref(props.open);
const currentDate = ref(new Date());
const selectedDate = ref<Date | null>(null);
const tempSelectedDate = ref<Date | null>(null);
const hours = ref('00');
const minutes = ref('00');
const selectedTimezone = ref(props.timezone);

// Initialize dates based on props.modelValue
if (props.modelValue) {
    if (typeof props.modelValue === 'string') {
        // Convert string to Date
        selectedDate.value = new Date(props.modelValue);
        tempSelectedDate.value = new Date(props.modelValue);
    } else {
        // It's already a Date object
        selectedDate.value = props.modelValue;
        tempSelectedDate.value = props.modelValue
            ? new Date(props.modelValue)
            : null;
    }

    if (selectedDate.value) {
        currentDate.value = new Date(selectedDate.value);
        hours.value = padZero(selectedDate.value.getHours());
        minutes.value = padZero(selectedDate.value.getMinutes());
    }
}

// Common timezone options (city-based with offsets)
const timezoneOptions = [
    { label: 'Zulu', value: '+0000' },
    { label: 'London (GMT+00:00)', value: '+0000' },
    { label: 'Paris, Berlin, Rome (GMT+01:00)', value: '+0100' },
    { label: 'Athens, Cairo (GMT+02:00)', value: '+0200' },
    { label: 'Moscow (GMT+03:00)', value: '+0300' },
    { label: 'Dubai (GMT+04:00)', value: '+0400' },
    { label: 'New Delhi (GMT+05:30)', value: '+0530' },
    { label: 'Bangkok, Jakarta (GMT+07:00)', value: '+0700' },
    { label: 'Singapore, Beijing, Hong Kong (GMT+08:00)', value: '+0800' },
    { label: 'Tokyo, Seoul (GMT+09:00)', value: '+0900' },
    { label: 'Sydney (GMT+10:00)', value: '+1000' },
    { label: 'Auckland (GMT+12:00)', value: '+1200' },
    { label: 'Honolulu (GMT-10:00)', value: '-1000' },
    { label: 'Anchorage (GMT-09:00)', value: '-0900' },
    { label: 'Los Angeles, Vancouver (GMT-08:00)', value: '-0800' },
    { label: 'Denver, Edmonton (GMT-07:00)', value: '-0700' },
    { label: 'Chicago, Mexico City (GMT-06:00)', value: '-0600' },
    { label: 'New York, Toronto (GMT-05:00)', value: '-0500' },
    { label: 'Halifax (GMT-04:00)', value: '-0400' },
    { label: 'São Paulo (GMT-03:00)', value: '-0300' },
];

// Weekdays array
const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

//@ts-ignore
const formatDate = (date: Date | null): string => {
    if (!date) return '';

    const year = date.getFullYear();
    const month = padZero(date.getMonth() + 1);
    const day = padZero(date.getDate());
    const hrs = padZero(date.getHours());
    const mins = padZero(date.getMinutes());

    return `${year}-${month}-${day} ${hrs}:${mins} `;
};

// Format timezone string to ISO format with offset
const formatDateWithTimezone = (
    date: Date | null,
    timezone: string,
): string => {
    if (!date) return '';

    const isoDate = new Date(date).toISOString().substring(0, 19); // YYYY-MM-DDTHH:mm:ss
    return `${dayjs(isoDate).format('MMM-D, YYYY  HH:mm')}`;
};

// Computed properties
const formattedDateTime = computed(() =>
    formatDateWithTimezone(selectedDate.value, selectedTimezone.value),
);

const currentMonthName = computed(() => {
    return new Intl.DateTimeFormat('en-US', { month: 'long' }).format(
        currentDate.value,
    );
});

const currentYear = computed(() => {
    return currentDate.value.getFullYear();
});

const calendarDays = computed(() => {
    const year = currentDate.value.getFullYear();
    const month = currentDate.value.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    const daysInMonth = lastDay.getDate();
    const firstWeekday = firstDay.getDay(); // 0 for Sunday

    const days: CalendarDay[] = [];

    // Add days from previous month to fill the first row
    const prevMonth = new Date(year, month, 0);
    const daysInPrevMonth = prevMonth.getDate();

    for (let i = 0; i < firstWeekday; i++) {
        const day = daysInPrevMonth - firstWeekday + i + 1;
        const date = new Date(year, month - 1, day);
        days.push({
            date,
            dayOfMonth: day,
            isOtherMonth: true,
        });
    }

    // Add days of current month
    for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i);
        days.push({
            date,
            dayOfMonth: i,
            isOtherMonth: false,
        });
    }

    // Add days from next month to complete the grid (6 rows x 7 days = 42 cells)
    const remainingCells = 42 - days.length;
    for (let i = 1; i <= remainingCells; i++) {
        const date = new Date(year, month + 1, i);
        days.push({
            date,
            dayOfMonth: i,
            isOtherMonth: true,
        });
    }

    return days;
});

const isPrevMonthDisabled = computed(() => {
    if (!props.minDateTime) return false;

    const firstDayOfCurrentMonth = new Date(
        currentDate.value.getFullYear(),
        currentDate.value.getMonth(),
        1,
    );

    const minYear = props.minDateTime.getFullYear();
    const minMonth = props.minDateTime.getMonth();
    const currentYear = firstDayOfCurrentMonth.getFullYear();
    const currentMonth = firstDayOfCurrentMonth.getMonth();

    return currentYear === minYear && currentMonth <= minMonth;
});

const isNextMonthDisabled = computed(() => {
    if (!props.maxDateTime) return false;

    const lastDayOfCurrentMonth = new Date(
        currentDate.value.getFullYear(),
        currentDate.value.getMonth() + 1,
        0,
    );

    const maxYear = props.maxDateTime.getFullYear();
    const maxMonth = props.maxDateTime.getMonth();
    const currentYear = lastDayOfCurrentMonth.getFullYear();
    const currentMonth = lastDayOfCurrentMonth.getMonth();

    return currentYear === maxYear && currentMonth >= maxMonth;
});

// Methods
const initFromCurrentValue = () => {
    if (props.modelValue) {
        const modelDate =
            typeof props.modelValue === 'string'
                ? new Date(props.modelValue)
                : props.modelValue;
        selectedDate.value = new Date(modelDate);
        tempSelectedDate.value = new Date(modelDate);
        currentDate.value = new Date(modelDate);
        hours.value = padZero(modelDate.getHours());
        minutes.value = padZero(modelDate.getMinutes());
    } else {
        const now = new Date();

        // Apply min/max constraints to the current date if needed
        if (props.minDateTime && now < props.minDateTime) {
            currentDate.value = new Date(props.minDateTime);
            hours.value = padZero(props.minDateTime.getHours());
            minutes.value = padZero(props.minDateTime.getMinutes());
        } else if (props.maxDateTime && now > props.maxDateTime) {
            currentDate.value = new Date(props.maxDateTime);
            hours.value = padZero(props.maxDateTime.getHours());
            minutes.value = padZero(props.maxDateTime.getMinutes());
        } else {
            currentDate.value = now;
            hours.value = padZero(now.getHours());
            minutes.value = padZero(now.getMinutes());
        }
    }
};

const togglePicker = () => {
    isOpen.value = !isOpen.value;
    emit('update:open', isOpen.value);
    if (isOpen.value) {
        initFromCurrentValue();
        // Prevent body scrolling when modal is open - removed to fix the error
    } else {
        // Restore body scrolling when modal is closed - removed to fix the error
    }
};

const closePicker = () => {
    isOpen.value = false;
    emit('update:open', false);
    // Removed to fix the error
};

const prevMonth = () => {
    currentDate.value = new Date(
        currentDate.value.getFullYear(),
        currentDate.value.getMonth() - 1,
        1,
    );
};

const nextMonth = () => {
    currentDate.value = new Date(
        currentDate.value.getFullYear(),
        currentDate.value.getMonth() + 1,
        1,
    );
};

const isSelectedDate = (date: Date): boolean => {
    if (!tempSelectedDate.value) return false;

    return (
        date.getDate() === tempSelectedDate.value.getDate() &&
        date.getMonth() === tempSelectedDate.value.getMonth() &&
        date.getFullYear() === tempSelectedDate.value.getFullYear()
    );
};

const isToday = (date: Date): boolean => {
    const today = new Date();
    return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    );
};

const isDateDisabled = (date: Date): boolean => {
    // Check min date constraint
    if (props.minDateTime) {
        const minDate = new Date(props.minDateTime);
        minDate.setHours(0, 0, 0, 0);

        const compareDate = new Date(date);
        compareDate.setHours(0, 0, 0, 0);

        if (compareDate < minDate) {
            return true;
        }
    }

    // Check max date constraint
    if (props.maxDateTime) {
        const maxDate = new Date(props.maxDateTime);
        maxDate.setHours(23, 59, 59, 999);

        const compareDate = new Date(date);
        compareDate.setHours(23, 59, 59, 999);

        if (compareDate > maxDate) {
            return true;
        }
    }

    return false;
};

const selectDate = (date: Date) => {
    tempSelectedDate.value = new Date(date);

    // Set hours and minutes from previously selected time
    if (tempSelectedDate.value) {
        tempSelectedDate.value.setHours(
            parseInt(hours.value, 10),
            parseInt(minutes.value, 10),
        );
    }
};

// Handle timezone change
const handleTimezoneChange = () => {
    emit('update:timezone', selectedTimezone.value);
};

const updateTempSelectedTime = () => {
    if (tempSelectedDate.value) {
        tempSelectedDate.value.setHours(
            parseInt(hours.value, 10),
            parseInt(minutes.value, 10),
        );
    }
};

const applySelection = () => {
    if (tempSelectedDate.value) {
        // Validate against min and max constraints
        let isValid = true;

        if (props.minDateTime && tempSelectedDate.value < props.minDateTime) {
            isValid = false;
        }

        if (props.maxDateTime && tempSelectedDate.value > props.maxDateTime) {
            isValid = false;
        }

        if (isValid) {
            // Create the date with timezone
            selectedDate.value = new Date(tempSelectedDate.value);

            // Parse timezone offset from the selected timezone value (format: +0100, -0800, etc.)
            const tzHours = parseInt(
                selectedTimezone.value.substring(0, 3),
                10,
            );
            const tzMinutes = parseInt(selectedTimezone.value.substring(3), 10);

            // Apply timezone offset
            const timezoneDate = new Date(tempSelectedDate.value);

            // Account for the local timezone offset first (to neutralize it)
            const localOffset = timezoneDate.getTimezoneOffset();

            // Then apply the selected timezone (converting to minutes: hours * 60 + minutes)
            const targetOffset = -(tzHours * 60 + tzMinutes);

            // Apply the total offset difference
            timezoneDate.setMinutes(
                timezoneDate.getMinutes() + localOffset + targetOffset,
            );

            // Store the final date
            selectedDate.value = timezoneDate;

            emit('update:modelValue', selectedDate.value);

            // Create date object with timezone info for the change event
            // const dateWithTimezone = {
            // 	date: selectedDate.value,
            // 	timezone: selectedTimezone.value,
            // 	displayValue: selectedDate.value.toString() // Format like: Wed Mar 19 2025 18:30:00 GMT+0100 (...)
            // };

            // Emit with timezone information
            // emit('change', dateWithTimezone);
            closePicker();
        }
    }
};

const cancelSelection = () => {
    // Reset to the previously selected date
    tempSelectedDate.value = selectedDate.value
        ? new Date(selectedDate.value)
        : null;

    if (selectedDate.value) {
        hours.value = padZero(selectedDate.value.getHours());
        minutes.value = padZero(selectedDate.value.getMinutes());
    }

    closePicker();
};

// Handle ESC key press to close the modal
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen.value) {
        cancelSelection();
    }
};

// Lifecycle hooks
onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
    if (props.open) {
        initFromCurrentValue();
        // Removed to fix the error
    }
});

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown);
    // Removed to fix the error
});

// Watch for prop changes
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            if (typeof newValue === 'string') {
                selectedDate.value = new Date(newValue);
            } else {
                selectedDate.value = newValue;
            }

            if (!isOpen.value) {
                tempSelectedDate.value = selectedDate.value
                    ? new Date(selectedDate.value)
                    : null;
            }
        } else {
            selectedDate.value = null;
            if (!isOpen.value) {
                tempSelectedDate.value = null;
            }
        }
    },
);

watch(
    () => props.open,
    (newValue) => {
        if (isOpen.value !== newValue) {
            isOpen.value = newValue;
            if (isOpen.value) {
                initFromCurrentValue();
                // Removed to fix the error
            } else {
                // Removed to fix the error
            }
        }
    },
);

watch(
    () => props.timezone,
    (newValue) => {
        if (selectedTimezone.value !== newValue) {
            selectedTimezone.value = newValue;
        }
    },
);
</script>

<style scoped>
.date-time-picker-container {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 300px;
}

.date-time-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
}

.date-time-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    cursor: pointer;
}

.date-time-toggle-btn {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: #777;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
}

/* Modal Overlay */
.date-time-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.date-time-modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 360px;
    overflow: hidden;
    animation: modal-appear 0.2s ease-out;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.date-time-modal-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.date-time-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.date-time-modal-body {
    padding: 16px;
}

.date-time-modal-footer {
    padding: 16px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.date-time-picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.month-nav-btn {
    background: none;
    border: none;
    color: #333;
    cursor: pointer;
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.month-nav-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
}

.month-nav-btn:disabled {
    color: #ccc;
    cursor: not-allowed;
}

.current-month {
    font-weight: 600;
    font-size: 16px;
}

.calendar-grid {
    margin-bottom: 20px;
}

.weekday-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    margin-bottom: 8px;
}

.weekday {
    text-align: center;
    font-size: 12px;
    font-weight: 700;
    color: #777;
    padding: 6px 0;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
}

.calendar-day {
    text-align: center;
    padding: 8px 0;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
}

.calendar-day:hover:not(.disabled) {
    background-color: #f0f0f0;
}

.calendar-day.other-month {
    color: #ccc;
}

.calendar-day.selected {
    background-color: #1e1e1e;
    color: white;
}

.calendar-day.today:not(.selected) {
    border: 1px solid #1e1e1e;
}

.calendar-day.disabled {
    color: #ddd;
    cursor: not-allowed;
}

.time-picker {
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

.time-label {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 500;
}

.time-inputs,
.timezone-input {
    display: flex;
    align-items: center;
}

.time-select,
.timezone-select {
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

.time-select {
    width: 60px;
    text-align: center;
}

.timezone-select {
    width: 100%;
    max-width: 300px;
}

.time-separator {
    margin: 0 8px;
    font-size: 16px;
    font-weight: 500;
}

.timezone-picker {
    margin-top: 16px;
    display: flex;
    align-items: center;
}

.modal-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modal-btn.cancel {
    background-color: #fff;
    color: #333;
    padding: 8px 16px;
}

.modal-btn.cancel:hover {
    background-color: #e0e0e0;
}

.modal-btn.submit {
    background-color: #1e1e1e;
    color: white;
    padding: 8px 16px;
}

.modal-btn.submit:hover {
    background-color: #333;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* Mobile optimizations */
@media (max-width: 480px) {
    .date-time-modal {
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
    }

    .calendar-day {
        padding: 10px 0;
        font-size: 16px;
    }

    .time-input {
        width: 50px;
        padding: 8px;
        font-size: 16px;
    }

    .modal-btn {
        padding: 12px 20px;
        font-size: 16px;
    }
}

/* Additional CSS for better mobile touch targets */
@media (hover: none) and (pointer: coarse) {
    .calendar-day,
    .month-nav-btn,
    .modal-btn,
    .date-time-toggle-btn {
        min-height: 44px;
    }

    .time-select,
    .timezone-select {
        min-height: 44px;
    }

    .timezone-picker {
        flex-direction: column;
        align-items: flex-start;
    }

    .timezone-label {
        margin-bottom: 8px;
    }

    .timezone-select {
        width: 100%;
    }
}
</style>
