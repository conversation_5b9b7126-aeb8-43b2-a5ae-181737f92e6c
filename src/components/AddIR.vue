<template>
    <v-container>
        <v-row>
            <v-col cols="12">
                <v-autocomplete
                    v-model="originator"
                    :items="originators"
                    label="Originator"
                    required
                ></v-autocomplete>
            </v-col>
            <v-col cols="12">
                <v-autocomplete
                    v-model="pir"
                    :items="pirs"
                    item-title="name"
                    item-value="id"
                    label="PIR"
                    required
                ></v-autocomplete>
            </v-col>
            <v-col cols="12">
                <v-autocomplete
                    v-model="associatedRFIs"
                    :items="rfis"
                    item-title="name"
                    item-value="id"
                    label="Associated RFIs"
                    multiple
                    chips
                ></v-autocomplete>
            </v-col>
            <v-col cols="12">
                <v-menu
                    v-model="dateMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                >
                    <template v-slot:activator="{ props }">
                        <v-text-field
                            v-model="formattedDate"
                            label="LTIOV"
                            readonly
                            v-bind="props"
                        ></v-text-field>
                    </template>
                    <v-date-picker
                        v-model="ltiov"
                        @update:model-value="dateMenu = false"
                    ></v-date-picker>
                </v-menu>
            </v-col>
            <v-col cols="12">
                <v-btn-toggle v-model="priority" mandatory>
                    <v-btn color="success" value="3">Low</v-btn>
                    <v-btn color="warning" value="2">Medium</v-btn>
                    <v-btn color="error" value="1">High</v-btn>
                </v-btn-toggle>
            </v-col>
            <v-col cols="12">
                <v-textarea
                    v-model="ir"
                    label="Information Requirement"
                    required
                ></v-textarea>
            </v-col>
            <v-col cols="12">
                <v-btn
                    color="primary"
                    :disabled="!isFormValid"
                    @click="createIR"
                >
                    Create IR
                </v-btn>
            </v-col>
        </v-row>
    </v-container>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
    defaultIR: String,
    defaultPIR: Object,
    originators: {
        type: Array,
        default: () => [],
    },
    rfis: {
        type: Array,
        default: () => [],
    },
    pirs: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(['add-ir']);

const pir = ref(props.defaultPIR || null);
const ir = ref(props.defaultIR || null);
const originator = ref(null);
const priority = ref(null);
const ltiov = ref(null);
const dateMenu = ref(false);
const associatedRFIs = ref([]);

const formattedDate = computed(() => {
    if (ltiov.value) {
        return new Date(ltiov.value).toLocaleDateString();
    }
    return '';
});

const isFormValid = computed(() => {
    return originator.value && ir.value && priority.value && ltiov.value;
});

const createIR = () => {
    if (isFormValid.value) {
        emit('add-ir', {
            originator: originator.value,
            ir: ir.value,
            ltiov: new Date(ltiov.value),
            priority: parseInt(priority.value),
            associatedRFIs: associatedRFIs.value.map((rfi) => rfi.id),
        });
    }
};
</script>
