<template>
	<v-form @submit.prevent="updatePlatform" dense>
		<v-row dense>
			<v-col cols="12" md="5">
				<v-text-field
					v-model="form.name"
					label="Platform Name"
					variant="outlined"
					:error-messages="errors.name"
					density="compact"
					required
				/>
			</v-col>
			<v-col cols="12" md="3">
				<v-select
					v-model="form.countryIsoCode"
					label="Country"
					:items="formattedCountries"
					item-title="display"
					item-value="code"
					variant="outlined"
					density="compact"
					:error-messages="errors.countryIsoCode"
					required
				>
					<template v-slot:selection="{ item }">
						<div class="d-flex align-center">
							<span :class="`fi fi-${(item.raw?.code) ? item.raw.code.toLowerCase() : ''} mr-2`"></span>
							<span>{{ item.raw.display }}</span>
						</div>
					</template>
					<template v-slot:item="{ item, props }">
						<v-list-item v-bind="props">
							<template v-slot:prepend>
								<span :class="`fi fi-${item.raw.code.toLowerCase()} mr-1`"></span>
							</template>
						</v-list-item>
					</template>
				</v-select>
			</v-col>
			<v-col cols="12" md="2">
				<v-select
					v-model="form.type"
					label="Platform Type"
					:items="PLATFORM_TYPES"
					variant="outlined"
					density="compact"
					:error-messages="errors.type"
				/>
			</v-col>
			<v-col cols="12" md="2">
				<v-text-field
					v-model.number="form.quantity"
					label="Quantity"
					type="number"
					variant="outlined"
					density="compact"
					hide-details
				/>
			</v-col>
		</v-row>

		<v-row dense>
			<v-col cols="12">
				<v-combobox
					v-model="form.aliases"
					label="Aliases"
					variant="outlined"
					density="compact"
					multiple
					chips
					closable-chips
					clearable
				>
					<template v-slot:details>
						Select or type to add aliases, press enter to add
					</template>
				</v-combobox>
			</v-col>
		</v-row>

		<v-row dense>
			<v-col cols="12" md="3">
				<v-text-field
					v-model.number="form.combatRadius"
					label="Combat Radius"
					type="number"
					variant="outlined"
					density="compact"
					hide-details
				/>
			</v-col>

			<v-col cols="12" md="4">
				<v-text-field
					v-model.number="form.footprintArea"
					label="Footprint Area"
					type="number"
					variant="outlined"
					density="compact"
					hide-details
				/>
			</v-col>
			<v-col cols="12" md="3">
				<v-checkbox
					v-model="form.hasCollectionCapability"
					label="Has Collection Capability"
					density="compact"
					hide-details
				/>
			</v-col>
		</v-row>

		<v-row dense>
			<v-col cols="12">
				<v-textarea
					v-model="form.description"
					label="Description"
					variant="outlined"
					density="compact"
					auto-grow
					rows="2"
					row-height="20"
					hide-details
				/>
			</v-col>
		</v-row>

		<v-row class="mt-4">
			<v-col class="d-flex justify-space-between">
				<v-btn
					color="error"
					variant="outlined"
					size="small"
					@click="confirmDelete"
				>
					Delete
				</v-btn>
				<div>
					<v-btn
						color="black"
						variant="outlined"
						size="small"
						class="mr-2"
						@click="$emit('close-update-popup')"
					>
						Cancel
					</v-btn>
					<v-btn
						color="primary"
						type="submit"
						size="small"
						:loading="loading"
					>
						Update Platform
					</v-btn>
				</div>
			</v-col>
		</v-row>
	</v-form>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useAdminPlatformStore, IPlatformSingleResponse } from '@/stores/admin/platform.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { PLATFORM_TYPES, type Platform } from '@/types/Platform';
import { useNatoCountries } from '@/composables/useNatoCounries';

const props = defineProps<{
	platformId: string;
}>();

const emit = defineEmits(['close-update-popup', 'update-platforms', 'delete-platform']);

const adminPlatformStore = useAdminPlatformStore();
const { showSnackbar } = useSnackbar();
const loading = ref(false);
const { natoCountries } = useNatoCountries();

const form = reactive<Partial<Platform>>({
	name: '',
	description: null,
	aliases: [],
	countryIsoCode: '',
	quantity: 1,
	hasCollectionCapability: false,
	type: 'air',
	combatRadius: null,
	footprintArea: null,
});

const errors = reactive({
	name: '',
	aliases: '',
	countryIsoCode: '',
	quantity: '',
	type: '',
	combatRadius: '',
	footprintArea: '',
});

// Add these computed properties to format countries for display
const formattedCountries = computed(() => {
	return natoCountries.map(country => ({
		code: country.code,
		name: country.name,
		display: `${country.code} - ${country.name}`
	}));
});

const resetErrors = () => {
	Object.keys(errors).forEach(key => {
		errors[key as keyof typeof errors] = '';
	});
};

const validateForm = (): boolean => {
	resetErrors();
	let isValid = true;

	if (!form.name?.trim()) {
		errors.name = 'Platform name is required';
		isValid = false;
	}

	if (!form.countryIsoCode?.trim()) {
		errors.countryIsoCode = 'Country is required';
		isValid = false;
	}

	return isValid;
};

const fetchPlatform = async () => {
	try {
		loading.value = true;
		const { data, success, messages } = await adminPlatformStore.getPlatformById(props.platformId) as IPlatformSingleResponse;

		if (!success && messages && messages[0]) {
			showSnackbar({
				text: messages[0].message as string,
				color: 'error',
			});
			return;
		}

		if (data?.platform) {
			// Populate form with platform data
			Object.assign(form, data.platform);

			// Handle null arrays
			if (!form.aliases) {
				form.aliases = [];
			}
		}
	} catch (error) {
		showSnackbar({
			text: 'Failed to load platform data',
			color: 'error',
		});
	} finally {
		loading.value = false;
	}
};

const updatePlatform = async () => {
	if (!validateForm()) return;

	try {
		loading.value = true;
		const { success, messages } = await adminPlatformStore.updatePlatform(props.platformId, form);

		if (!success && messages && messages[0]) {
			showSnackbar({
				text: messages[0].message as string,
				color: 'error',
			});
			return;
		}

		showSnackbar({
			text: 'Platform updated successfully',
			color: 'success',
		});

		emit('update-platforms');
		emit('close-update-popup');
	} catch (error) {
		showSnackbar({
			text: 'Error updating platform',
			color: 'error',
		});
	} finally {
		loading.value = false;
	}
};

const confirmDelete = () => {
	emit('delete-platform', props.platformId);
};

onMounted(() => {
	fetchPlatform();
});
</script>
