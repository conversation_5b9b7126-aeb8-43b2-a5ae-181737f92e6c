<template>
	<v-card  variant="outlined" class="lumio-card">
		<v-card-title class="text-h6">
			Add New Platform
			<v-btn
				icon
				class="ml-auto"
				@click="$emit('close-section')">
				<v-icon>mdi-close</v-icon>
			</v-btn>
		</v-card-title>

		<v-card-text class="pa-5">
			<v-form @submit.prevent="savePlatform" dense>
				<v-row dense>
					<v-col cols="12" md="5">
						<v-text-field
							v-model="form.name"
							label="Platform Name"
							variant="outlined"
							:error-messages="errors.name"
							density="compact"
							required
						/>
					</v-col>
					<v-col cols="12" md="3">
						<v-select
							v-model="form.countryIsoCode"
							label="Country"
							:items="formattedCountries"
							item-title="display"
							item-value="code"
							variant="outlined"
							density="compact"
							:error-messages="errors.countryIsoCode"
							required
						>
							<template v-slot:selection="{ item }">
								<div class="d-flex align-center">
									<span
										:class="`fi fi-${(item.raw?.code) ? item.raw.code.toLowerCase() : ''} mr-2`"></span>
									<span>{{ item.raw.display }}</span>
								</div>
							</template>
							<template v-slot:item="{ item, props }">
								<v-list-item v-bind="props">
									<template v-slot:prepend>
										<span :class="`fi fi-${item.raw.code.toLowerCase()} mr-1`"></span>
									</template>
								</v-list-item>
							</template>
						</v-select>
					</v-col>
					<v-col cols="12" md="2">
						<v-select
							v-model="form.type"
							label="Platform Type"
							:items="PLATFORM_TYPES"
							variant="outlined"
							density="compact"
							:error-messages="errors.type"
						/>
					</v-col>


					<v-col cols="12" md="2">
						<v-text-field
							v-model.number="form.quantity"
							label="Quantity"
							type="number"
							variant="outlined"
							density="compact"
							hide-details
						/>
					</v-col>
				</v-row>

				<v-row dense>
					<v-col cols="12">
						<v-combobox
							v-model="form.aliases"
							label="Aliases"
							variant="outlined"
							density="compact"
							multiple
							chips
							closable-chips
							clearable
						>
							<template v-slot:details>
								Select or type to add aliases, pres enter to add
							</template>
						</v-combobox>
					</v-col>
				</v-row>

				<v-row dense>
					<v-col cols="12" md="3">
						<v-text-field
							v-model.number="form.combatRadius"
							label="Combat Radius"
							type="number"
							variant="outlined"
							density="compact"
							hide-details
						/>
					</v-col>

					<v-col cols="12" md="4">
						<v-text-field
							v-model.number="form.footprintArea"
							label="Footprint Area"
							type="number"
							variant="outlined"
							density="compact"
							hide-details
						/>
					</v-col>
					<v-col cols="12" md="3">
						<v-checkbox
							v-model="form.hasCollectionCapability"
							label="Has Collection Capability"
							density="compact"
							hide-details
						/>
					</v-col>
				</v-row>

				<v-row dense>
					<v-col cols="12">
						<v-textarea
							v-model="form.description"
							label="Description"
							variant="outlined"
							density="compact"
							auto-grow
							rows="2"
							row-height="20"
							hide-details
						/>
					</v-col>
				</v-row>

				<v-row class="mt-4">
					<v-col class="d-flex justify-end">
						<v-btn
							color="black"
							variant="outlined"
							size="small"
							class="mr-2"
							@click="$emit('close-section')"
						>
							Cancel
						</v-btn>
						<v-btn
							color="primary"
							type="submit"
							size="small"
							:loading="loading"
						>
							Add Platform
						</v-btn>
					</v-col>
				</v-row>
			</v-form>
		</v-card-text>
	</v-card>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useAdminPlatformStore, IPlatformSingleResponse } from '@/stores/admin/platform.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { PLATFORM_TYPES, type Platform } from '@/types/Platform';
import { useNatoCountries } from '@/composables/useNatoCounries';

const emit = defineEmits(['close-section', 'update-platforms']);

const adminPlatformStore = useAdminPlatformStore();
const { showSnackbar } = useSnackbar();
const { natoCountries } = useNatoCountries();
const loading = ref(false);

const form = reactive<Partial<Platform>>({
	name: '',
	description: null,
	aliases: [],
	countryIsoCode: 'au',
	quantity: 1,
	hasCollectionCapability: false,
	cost: null,
	costCurrency: null,
	type: 'air', // Default to air
	combatRadius: null,
	footprintArea: null,
});

const errors = reactive({
	name: '',
	aliases: '',
	countryIsoCode: '',
	quantity: '',
	cost: '',
	costCurrency: '',
	type: '',
	combatRadius: '',
	footprintArea: '',
});

const resetErrors = () => {
	Object.keys(errors).forEach(key => {
		errors[key as keyof typeof errors] = '';
	});
};

const resetForm = () => {
	form.name = '';
	form.description = null;
	form.aliases = [];
	form.countryIsoCode = '';
	form.quantity = 1;
	form.hasCollectionCapability = false;
	form.cost = null;
	form.costCurrency = null;
	form.type = 'air';
	form.combatRadius = null;
	form.footprintArea = null;
};

const validateForm = (): boolean => {
	resetErrors();
	let isValid = true;

	if (!form.name?.trim()) {
		errors.name = 'Platform name is required';
		isValid = false;
	}

	if (!form.countryIsoCode?.trim()) {
		errors.countryIsoCode = 'Country is required';
		isValid = false;
	}

	return isValid;
};

// Add these computed properties to format countries for display
const formattedCountries = computed(() => {
	return natoCountries.map(country => ({
		code: country.code,
		name: country.name,
		display: `${country.code} - ${country.name}`
	}));
});


const savePlatform = async () => {
	if (!validateForm()) return;

	try {
		loading.value = true;
		const { success, messages } = await adminPlatformStore.createPlatform(form) as IPlatformSingleResponse;

		if (!success && messages && messages[0]) {
			showSnackbar({
				text: messages[0].message as string,
				color: 'error',
			});
			return;
		}

		showSnackbar({
			text: 'Platform created successfully',
			color: 'success',
		});

		resetForm();
		emit('update-platforms');
		emit('close-section');
	} catch (error) {
		showSnackbar({
			text: 'Error creating platform',
			color: 'error',
		});
	} finally {
		loading.value = false;
	}
};
</script>
