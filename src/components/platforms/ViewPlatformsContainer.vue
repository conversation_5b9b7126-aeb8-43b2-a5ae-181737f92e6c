<template>
	<v-card class="lumio-card">
		<v-card-title class="text-h5">
			<h4>Platform Managers</h4>
			<v-btn
				color="primary"
				prepend-icon="mdi-plus"
				class="ma-2"
				variant="flat"
				v-if="!isAddingPlatform"
				@click="isAddingPlatform = true"
			>
				Add Platform
			</v-btn>
		</v-card-title>
		<v-card-text>
			<v-row v-if="isAddingPlatform">
				<v-col class="pa-5">
					<add-platform
						@close-section="isAddingPlatform = false"
						@update-platforms="getPlatforms"
					></add-platform>
				</v-col>
			</v-row>

			<div>
				<v-data-table-server
					v-model:items-per-page="paginationState.perPage"
					:headers="headers"
					:items="platforms"
					:items-length="paginationState.total"
					:loading="loading"
					item-value="name"
					density="compact"
				>
					<template v-slot:item.name="{ item }">
						<a href="#" @click.prevent="showEditPopup(item)" class="text-decoration-none text-primary">
							{{ item.name }}
							<small v-if="item.designation">({{ item.designation }})</small>
						</a>
					</template>
					<template v-slot:item.type="{ item }">
						<v-chip :color="getPlatformTypeColor(item.type)" variant="flat" size="small" density="compact">
							{{ item.type }}
						</v-chip>
					</template>
					<template v-slot:item.countryIsoCode="{ item }">
						<div class="d-flex align-center">
							<span :class="`fi fi-${item.countryIsoCode.toLowerCase()} mr-2`"></span> {{ item.countryIsoCode }}
						</div>
					</template>
					<template v-slot:item.action="{ item }">
						<v-btn
							icon
							variant="text"
							color="primary"
							size="small"
							@click="showEditPopup(item)"
							class="mr-2"
						>
							<v-icon>mdi-pencil</v-icon>
						</v-btn>
						<v-btn
							variant="text"
							icon
							color="error"
							size="small"
							@click="deletePlatform(item.id.toString())"
						>
							<v-icon>mdi-delete</v-icon>
						</v-btn>
					</template>
				</v-data-table-server>
				<v-row justify="center">
					<v-col cols="8">
						<v-container class="max-width">
							<v-pagination
								v-model="paginationState.page"
								:length="paginationState.pages"
								class="my-4"
							></v-pagination>
						</v-container>
					</v-col>
				</v-row>
			</div>
		</v-card-text>
	</v-card>

	<v-dialog
		v-model="isEditing"
		max-width="1200"
		:close-on-back="false"
		:close-on-content-click="false"
		persistent
	>
		<v-card
			prepend-icon="mdi-tooltip-edit-outline"
			title="Edit Platform"
		>
			<v-card-text>
				<edit-platform-popup
					v-if="isEditing"
					:platform-id="editPlatformId as string"
					@close-update-popup="isEditing = false"
					@update-platforms="getPlatforms"
					@delete-platform="deletePlatform"
				/>
			</v-card-text>
		</v-card>
	</v-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useAdminPlatformStore, IPlatformCollectionResponse } from '@/stores/admin/platform.store';
import { useSnackbar } from '@/composables/useSnackbar';
import { DataTableHeader, IPagination } from '@/types/Global.type';
import type { Platform } from '@/types/Platform';

const adminPlatformStore = useAdminPlatformStore();
const { showSnackbar } = useSnackbar();
const loading = ref(false);
const platforms = ref<Platform[]>([]);
const isEditing = ref(false);
const editPlatformId = ref<string|number|null>(null);
const isAddingPlatform = ref(false);

const paginationState = ref<IPagination>({
	page: 1,
	perPage: 50,
	total: 0,
	pages: 0,
	sortBy: 'desc',
	orderBy: 'id',
});

const headers: DataTableHeader[] = [
	{
		title: 'Name',
		align: 'start',
		sortable: true,
		key: 'name',
	},
	{
		title: 'Type',
		align: 'start',
		sortable: true,
		key: 'type',
	},
	{
		title: 'Country',
		align: 'start',
		sortable: true,
		key: 'countryIsoCode',
	},
	{
		title: 'Description',
		align: 'start',
		sortable: false,
		key: 'description',
	},
	{
		title: 'Action',
		align: 'center',
		sortable: false,
		key: 'action',
	},
];

const showEditPopup = (item: Platform) => {
	editPlatformId.value = item.id.toString();
	isEditing.value = true;
};

const getPlatforms = async () => {
	try {
		loading.value = true;
		const { data, success, messages } = await adminPlatformStore.fetchPlatforms(paginationState.value) as IPlatformCollectionResponse;

		if (!success && messages && messages[0]) {
			showSnackbar({
				text: messages[0].message as string,
				color: 'error',
			});
			return;
		}

		if (data) {
			paginationState.value = {
				...paginationState.value,
				total: data.total,
				pages: Math.ceil(data.total / paginationState.value.perPage)
			};
			platforms.value = data.platforms;
		}
	} catch (error) {
		showSnackbar({
			text: 'Failed to load platforms',
			color: 'error',
		});
	} finally {
		loading.value = false;
	}
};

const deletePlatform = async (platformId: string) => {
	try {
		const confirmed = confirm('Are you sure you want to delete this platform?');
		if (!confirmed) return;

		loading.value = true;
		await adminPlatformStore.deletePlatform(platformId);
		showSnackbar({
			text: 'Platform deleted successfully',
			color: 'success',
		});
		await getPlatforms();
	} catch (error) {
		showSnackbar({
			text: 'Error deleting platform',
			color: 'error',
		});
	} finally {
		loading.value = false;
		isEditing.value = false;
	}
};

const getPlatformTypeColor = (type: string) => {
	switch (type) {
		case 'space':
			return 'purple';
		case 'air':
			return 'blue';
		case 'land':
			return 'green';
		case 'sea':
			return 'cyan';
		case 'hybrid':
			return 'orange';
		default:
			return 'grey';
	}
};

onMounted(async () => {
	await getPlatforms();
});
</script>
