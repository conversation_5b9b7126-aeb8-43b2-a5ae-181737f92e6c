<template>
	<v-breadcrumbs
    :items="breadcrumbItems" class="pa-2 ma-0 bg-white"
    density="compact"
    rounded
    style="font-size: 13px;"
    tile
  >
		<template v-slot:divider>
			<v-icon icon="mdi-chevron-right"></v-icon>
		</template>
	</v-breadcrumbs>
	<v-divider></v-divider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

interface BreadcrumbItem {
	title: string;
	href: string;
	disabled?: boolean;
}

const props = defineProps<{
	currentPageTitle?: string;
}>();

const route = useRoute();

const breadcrumbItems = computed<BreadcrumbItem[]>(() => {
	const items: BreadcrumbItem[] = [
		{
			title: 'HOME',
			href: '/',
		}
	];

	// Get route parts and filter out empty strings and numeric values (IDs)
	const routeParts = route.path
		.split('/')
		.filter(part => part !== '' && isNaN(Number(part)));

	// Build the breadcrumb path progressively
	let currentPath = '';
	routeParts.forEach((part, index) => {
		currentPath += `/${part}`;
		items.push({
			title: (part.charAt(0).toUpperCase() + part.slice(1)).toUpperCase(),
			href: currentPath,
		});
	});

	// Add the current page title if provided
	if (props.currentPageTitle) {
		items.push({
			title: props.currentPageTitle.toUpperCase(),
			href: '#',
			disabled: true,
		});
	}

	return items;
});
</script>
