<template>
    <template v-if="operation">
        <v-list-item title="MANAGE OPERATION" subtitle="Admin" class="mt-4 ml-2"></v-list-item>
        <template v-for="link in opsLinks" :key="link.name">
            <v-list-item
                :to="link.to"
                :class="`ml-2`"
                density="compact"
                active-class="text-secondary"
            >
                <template #prepend>
                    <v-list-item-action start>
                        <v-icon color="secondary" size="18" class="ml-2">
                            {{ link.icon }}
                        </v-icon>
                    </v-list-item-action>
                </template>

                <template #default>
                    <v-list-item-title class="text-body-2 text-white">
                        {{ link.name }}
                    </v-list-item-title>
                </template>
            </v-list-item>
        </template>
        <v-divider></v-divider>
    </template>
    <v-list-item title="MANAGE ORGANIZATION" subtitle="Admin" class="mt-4 ml-2"></v-list-item>
    <template v-for="link in links" :key="link.name">
        <v-list-item
            :to="link.to"
            :class="`ml-2`"
            density="compact"
            active-class="text-secondary"
        >
            <template #prepend>
                <v-list-item-action start>
                    <v-icon color="secondary" size="18" class="ml-2">
                        {{ link.icon }}
                    </v-icon>
                </v-list-item-action>
            </template>

            <template #default>
                <v-list-item-title class="text-body-2 text-white">
                    {{ link.name }}
                </v-list-item-title>
            </template>
        </v-list-item>
    </template>
    <v-divider></v-divider>
    <v-list-item>
        <v-btn
            color="secondary"
            class="d-flex align-center text-sm-body-1"
            prepend-icon="mdi-airplane"
            append-icon="mdi-lock"
            :to="'/platforms'"
        >
            Manage Platforms
        </v-btn>
        <!--		<v-list-item-title class="d-flex align-center text-sm-body-1 bg-secondary py-3 mx-0 text-center">-->
        <!--			<v-icon class="mr-2" color="black">mdi-airplane</v-icon>-->
        <!--			<span class="text-black">Platforms</span>-->
        <!--		</v-list-item-title>-->
    </v-list-item>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
const operationStore = useOperationStore();
const { operation } = storeToRefs(operationStore);

const opsLinks = computed(() => {
    return [
        {
            name: 'Users',
            to: '/admin/operation/users',
            icon: 'mdi-account-group-outline',
            admin: true,
        },
        {
            name: 'Originators',
            to: '/admin/originators',
            icon: 'mdi-account-arrow-right-outline',
            admin: true,
        },
        {
            name: 'Assets',
            to: '/admin/operation/assets',
            icon: 'mdi-tank',
            admin: true,
        },
    ];
});
const links = computed(() => {
    return [
        // {
        //     name: 'Add user',
        //     to: '/admin/add-user',
        //     icon: 'mdi-plus',
        //     admin: true,
        // },
        {
            name: 'Users',
            to: '/admin/users',
            icon: 'mdi-account-multiple-outline',
            admin: true,
        },
        // {
        //     name: 'Add role',
        //     to: '/admin/add-role',
        //     icon: 'mdi-plus',
        //     admin: true,
        // },
        {
            name: 'Roles',
            to: '/admin/view-roles',
            icon: 'mdi-account-card',
            admin: true,
        },
        {
            name: 'Operations',
            to: '/admin/operations',
            icon: 'mdi-map-marker',
            admin: true,
        },
        // {
        //     name: 'Operation Builder',
        //     to: '/admin/operations/builder',
        //     icon: 'mdi-map-marker',
        //     admin: true,
        // },
    ];
});
</script>

<style scoped>
:deep(.v-list-item__spacer) {
    width: 0 !important;
    flex-grow: 0 !important;
}

.v-list-item {
    min-height: 35px !important;
    padding: 0 8px !important;
}

.v-list-group__items .v-list-item {
    padding-inline-start: 8px !important;
}

.v-list-item--active {
    background: rgba(238, 247, 63, 0.1);
}

.v-list-item:hover {
    background: rgba(238, 247, 63, 0.05);
}
</style>
