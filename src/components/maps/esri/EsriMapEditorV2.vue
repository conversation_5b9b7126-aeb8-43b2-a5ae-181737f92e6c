<template>
	<div class="h-100">
		<div
			id="esriMapEditor"
			:style="maxHeight ? `height:${getHeight(maxHeight)}; max-height: ${getHeight(maxHeight)};` : ''"
		>
			<div id="measurements" class="esri-widget h-100"></div>
		</div>
		<v-dialog
			v-model="isModalOpen"
			width="800"
			location="top"
			persistent
			absolute
		>
			<v-card class="lumio-card" v-if="currentGraphicElement">
				<v-card-title>
					<h3>{{ (currentGraphicElement.id) ? 'Edit' : 'Create' }} {{ (currentGraphicElement.id) ?
						currentGraphicElement.title : typeOfItem }}</h3>
					<v-btn icon color="primary" @click="isModalOpen = false" variant="text">
						<v-icon>mdi-close</v-icon>
					</v-btn>

				</v-card-title>
				<v-card-text>
					<template v-if="!currentGraphicElement.id">
						<v-select
							v-model="typeOfItem"
							:items="getItemsBasedOnCurrentShapeType"
							label="Type"
							variant="outlined"
							density="compact"
							class="ma-2 flex-grow-1"
						></v-select>
						<div v-if="typeOfItem === 'TAI' || typeOfItem === 'NAI'">
							<CreateAOIPartialPopup
								:isTargetable="typeOfItem === 'TAI'"
								:mapId="parseInt(currentGraphicElement?.uid?.toString() ?? '0')"
								:operationId="parseInt(operation.id)"
								:coordinates="currentGraphicElement.coordinates as ICoordinates[]"
								@item-cancelled="cancelCreateNAI"
								@item-created="createAOI"
							/>
						</div>
						<div v-else-if="typeOfItem === 'ISR Tracks'">
							<CreateISRTrackPartialPopup
								:mapId="parseInt(currentGraphicElement?.uid?.toString() ?? '0')"
								:operationId="parseInt(operation.id)"
								:coordinates="currentGraphicElement.coordinates as ICoordinates[]"
								@item-cancelled="cancelCreateISRTrack"
								@item-created="createISRTrack"
							/>
						</div>
					</template>
					<template v-else>

						<template v-if="currentGraphicElement.itemType === 'aoi' && currentGraphicElement.id">
							<EditAOIPartialPopup
								:aoiId="currentGraphicElement?.id?.toString() ?? ''"
								@item-cancelled="cancelCreateNAI"
								@item-updated="updateAOI"
							/>
						</template>
						<template v-else-if="currentGraphicElement.itemType === 'isr_track'">

							<EditISRTrackPartialPopup
								:isrTrackId="currentGraphicElement.id.toString()"
								@item-cancelled="cancelCreateISRTrack"
								@item-updated="updateISRTrack"
							/>
						</template>
					</template>
				</v-card-text>
			</v-card>
		</v-dialog>
		<v-dialog v-model="isDeleteDialogOpen" max-width="400">
			<v-card>
				<v-card-title class="text-h5">
					Confirm Deletion
				</v-card-title>
				<v-card-text v-if="pendingDeleteGraphic">
					<div>
						<strong>Name:</strong> {{ pendingDeleteGraphic.attributes?.name }}
					</div>
					<div v-if="pendingDeleteGraphic.attributes?.designation">
						<strong>Designation:</strong> {{ pendingDeleteGraphic.attributes.designation }}
					</div>
					<div v-if="pendingDeleteGraphic.attributes?.itemType">
						<strong>Type:</strong> {{ pendingDeleteGraphic.attributes.itemType }}
					</div>
					<div v-if="pendingDeleteGraphic.attributes?.description">
						<strong>Description:</strong> {{ pendingDeleteGraphic.attributes.description }}
					</div>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn
						color="grey-darken-1"
						variant="text"
						@click="isDeleteDialogOpen = false"
					>
						Cancel
					</v-btn>
					<v-btn
						color="red-darken-1"
						variant="text"
						@click="confirmDelete"
					>
						Delete
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

	</div>


</template>

<script setup lang="ts">
import { useGeometryConverter } from '@/composables/useGeometryConverter';

import { onMounted, ref, watch, nextTick, computed } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import Sketch from '@arcgis/core/widgets/Sketch';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';
import { CustomGraphic } from '@/types/EsriMap';
import { Polygon, Polyline, Point } from '@arcgis/core/geometry';

import PopupTemplate from '@arcgis/core/PopupTemplate';

import { SymbolItem } from '@/types/EsriMap';
import * as reactiveUtils from '@arcgis/core/core/reactiveUtils';
import CreateAOIPartialPopup from '@/components/operations/aois/CreateAOIPartialPopup.vue';
import CreateISRTrackPartialPopup from '@/components/operations/isr_tracks/CreateISRTrackPartialPopup.vue';
import { Aoi } from '@/types/Aoi.type';
import { ICoordinates } from '@/types/Global.type';
import { IIsrTrack } from '@/types/IsrTrack.type';

import { addItem, genSymbol, getHeight } from '@/composables/esri/MapGraphicLayers'

interface PlainGraphic {
	uid?: string;
	attributes?: {
		name?: string;
		description?: string;
		itemType?: string;
		designation?: string;
	};
}

interface ExtendedGraphic extends __esri.Graphic {
	uid?: string;
	geometry: __esri.Geometry & {
		paths?: number[][];
		rings?: number[][];
		type: string;
	};
	attributes: {
		isUserMade?: boolean;
		name?: string;
		description?: string;
		id?: number | null;
		[key: string]: any;
	};
}

interface ExtendedHit {
	graphic: ExtendedGraphic;
	type: string;
}

type DrawElement = 'point' | 'polygon' | 'polyline';

const isDeleteDialogOpen = ref(false);
const pendingDeleteGraphic = ref<PlainGraphic | null>(null);
// Add these functions to handle deletion
const handleDelete = (graphic: any) => {
	const plainGraphic = {
		uid: graphic.uid,
		attributes: {
			name: graphic.attributes?.name || 'Unnamed',
			description: graphic.attributes?.description || '',
			itemType: graphic.attributes?.itemType || '',
			designation: graphic.attributes?.designation || ''
		}
	};

	// Store the plain object instead of the ESRI graphic
	pendingDeleteGraphic.value = plainGraphic;
	isDeleteDialogOpen.value = true;

};

const confirmDelete = async () => {

	if (pendingDeleteGraphic.value && pendingDeleteGraphic.value.uid) {
		// Find the original graphic in the layer
		const graphicToDelete = graphicsLayer.graphics.find(
			(g) => (g as CustomGraphic).uid === pendingDeleteGraphic.value?.uid
		);
		if (graphicToDelete) {
			// Remove from graphics layer
			//lets call API to delete
			emit('element-deleted', graphicToDelete.attributes.id, graphicToDelete.attributes.itemType);

			graphicsLayer.remove(graphicToDelete);
		}
	}

	// Reset and close the dialog
	pendingDeleteGraphic.value = null;
	isDeleteDialogOpen.value = false;
};

const geometryConverter = useGeometryConverter();
const typeOfItem = ref();

const props = withDefaults(
	defineProps<{
		drawElements?: DrawElement[] | null;
		maxHeight?: number | null | string;
		mapItems?: SymbolItem[];  // Changed from SymbolItem[] | []
		centerCoordinates?: number[];
		zoom?: number | string;
		operation: any;
	}>(),
	{
		maxHeight: null,
		mapItems: () => [],  // Return empty array
		drawElements: () => ['polygon', 'polyline', 'rectangle'] as DrawElement[],
		centerCoordinates: undefined,
		zoom: 10
	}
);

const currentGraphicElement = ref<SymbolItem | null>(null);

const isModalOpen = ref(false);

const emit = defineEmits([
	'element-created',
	'element-selected',
	'element-updated',
	'element-deleted',
	'coordinates-updated',
	'zoom-updated',
	'center-updated'
]);

let view: MapView;
let map: Map;
let graphicsLayer: GraphicsLayer;
let sketch: Sketch;

const initializeMap = async () => {
	if (view) {
		view.destroy();
	}
	if (graphicsLayer) {
		map.remove(graphicsLayer);
	}

	await nextTick();

	map = new Map({
		basemap: 'gray'
	});

	graphicsLayer = new GraphicsLayer();

	map.add(graphicsLayer);

	// Initialize map view and popup handling
	view = new MapView({
		container: 'esriMapEditor',
		map: map,
		zoom: Number(props.zoom),
		center: props.centerCoordinates ?? [-118.805, 34.027],
		popupEnabled: true,
		constraints: {
			rotationEnabled: false
		}
	});

	// Initialize and configure the Sketch widget
	sketch = new Sketch({
		label: 'Draw TAI or NAI or ISR Track',
		layer: graphicsLayer,
		view: view,
		availableCreateTools: props.drawElements ?? ['polygon', 'polyline'],
		creationMode: 'single',
		visibleElements: {
			createTools: {
				polygon: true,
				polyline: true,
				rectangle: true
			},
			selectionTools: {
				'rectangle-selection': false,
				'lasso-selection': false
			},
			settingsMenu: false,
			undoRedoMenu: false,
			duplicateButton: false
		},
		defaultUpdateOptions: {
			// Add tool options that don't include delete functionality
			toggleToolOnClick: true,
			multipleSelectionEnabled: false
		}
	});


	// Add the Sketch widget to the view's UI
	view.ui.add(sketch, 'bottom-left');

	const basemapToggle = new BasemapToggle({
		view: view,
		nextBasemap: 'satellite'
	});
	view.ui.add(basemapToggle, 'top-right');

	const fullscreen = new Fullscreen({ view });
	view.ui.add(fullscreen, 'top-right');

	const scalebar = new ScaleBar({ view, unit: 'metric' });
	view.ui.add(scalebar, 'bottom-right');

	// Handle sketch events
	sketch.on('create', (event) => {
		if (event.state === 'complete') {
			const graphic = event.graphic;

			graphic.attributes = {
				id: null,
				title: '(NOT SAVED - PLEASE CLICK EDIT)',
				name: '(UNSAVED)',
				label: '(TBD)',
				itemType: (graphic.geometry.type === 'polyline') ? 'isr_track' : 'aoi',
				designation: '',
				description: '',
				type: graphic.geometry.type === 'polyline' ? 'LineString' : 'Polygon',
				coordinates: [],
				selected: true,
				elementsColor: [111, 111, 111, 89],
				elementsBorderColor: [255, 255, 255, 255],
				elementsBorderThickness: 2,
				elementsBorderType: 'dashed',
				isApproved: false,
				isTargetable: false,
				isUserMade: true
			};

			if (graphic.geometry.type === 'polyline') {
				graphic.symbol = genSymbol({
					id: null,
					type: graphic.geometry.type,
					coordinates: [],
					title: '(NOT SAVED)',
					elementsColor: [111, 111, 111, 89],
					elementsBorderColor: [111, 111, 111, 66],
					elementsBorderThickness: 2,
					elementsBorderType: 'dashed',
					selected: true
				});
			} else {
				graphic.symbol = genSymbol({
					id: null,
					type: graphic.geometry.type,
					coordinates: [],
					title: '(NOT SAVED)',
					elementsColor: [111, 111, 111, 89],
					elementsBorderColor: [111, 111, 111, 66],
					elementsBorderThickness: 2,
					elementsBorderType: 'dashed',
					selected: true
				});
			}

		}
	});

	sketch.on('update', (event) => {
		const graphic = event.graphics[0];

		// Check for moves (both during and after)
		if (event.tool === 'move' || event.tool === 'reshape') {
			if (graphic?.attributes?.id) {
				let coordinates;

				// Extract coordinates based on geometry type
				if (graphic.geometry.type === 'polygon') {
					coordinates = (graphic.geometry as Polygon).rings;
				} else if (graphic.geometry.type === 'polyline') {
					coordinates = (graphic.geometry as Polyline).paths;
				} else if (graphic.geometry.type === 'point') {
					const pointGeometry = graphic.geometry as Point;
					coordinates = [pointGeometry.longitude, pointGeometry.latitude];
				}

				// Emit coordinates update event
				emit('coordinates-updated', {
					id: graphic.attributes.id,
					coordinates: coordinates
				});
			}
		}

		// Handle completion state
		if (event.state === 'complete') {

		}
	});

	view.watch('center', (center) => {
		emit('center-updated', {
			latitude: center.latitude,
			longitude: center.longitude
		});
	});

	view.watch('zoom', (zoom) => {
		emit('zoom-updated', zoom);
	});


	// Handle click events on graphics using the new popup API


	view.on('click', (event) => {
		view.hitTest(event).then((response) => {
			try {
				const arrayOfGraphics = response.results as ExtendedHit[];
				const element = arrayOfGraphics.find(item => {
					return (item.graphic?.attributes?.isUserMade);
				});

				if (!element) return;
				const graphic: ExtendedGraphic = element.graphic;
				const item = graphic.attributes;
				if (graphic) {
					graphic.popupTemplate  = new PopupTemplate({
						title: item.title ?? item.designation ?? '',
						content: [{
							type: 'custom',
							outFields: ['*'],
							creator: () => {

								if (item.id) {
									const buttonEdit = document.createElement('button');
									buttonEdit.innerText = 'EDIT ' + item.title;
									buttonEdit.className = 'esri-button esri-button--secondary';
									buttonEdit.addEventListener('click', () => {
										const action = {
											id: 'edit-this'
										};
										//@ts-ignore
										view.popup.triggerAction(action);

									});
									return buttonEdit;
								} else {
									const buttonCreate = document.createElement('button');
									buttonCreate.innerText = 'CREATE';
									buttonCreate.className = 'esri-button esri-button--secondary';
									buttonCreate.addEventListener('click', () => {
										const action = {
											id: 'edit-this'
										};
										//@ts-ignore
										view.popup.triggerAction(action);
									});
									return buttonCreate;
								}
							}
						}, {
							type: 'text',
							text: item.id ? `<strong>Description:</strong> ${item.description ?? ''}<br>
                             <strong>Type:</strong> ${item.itemType ?? ''}<br>
                             <strong>Designation:</strong> ${item.designation ?? ''}<br>
                             <strong>Targetable:</strong> ${item.isTargetable ? 'Yes' : 'No'}<br>
                             <strong>Approved:</strong> ${item.isApproved ? 'Yes' : 'No'}`
								: 'No information yet'
						}],
						actions: [
							{
								type: 'button',
								title: item.id ? `EDIT ${item.title ?? item.designation ?? ''}` : 'CREATE ITEM',
								id: 'edit-this',
								icon: item.id ? 'pencil' : 'plus'
							}, {
								type: 'button',
								title: `DELETE ${item.title ?? item.designation ?? ''}`,
								id: 'delete-this',
								icon: 'trash'
							}
						]
					});

					view.openPopup({
						location: event.mapPoint,
						features: [graphic]
					});
				}
			} catch (error) {

			}
		});
	});

	view.on('key-down', (event) => {
		if (event.key === 'Delete' || event.key === 'Backspace') {
			event.stopPropagation();
			// If there's a selected graphic, show the deletion dialog
			if (sketch.viewModel.state === 'active' && sketch.viewModel.updateGraphics.length > 0) {
				const graphicToDelete = sketch.viewModel.updateGraphics.getItemAt(0);
				// Reuse the existing handleDelete function
				handleDelete(graphicToDelete);
			}
		}
	});

	if (props.mapItems) {
		addItems(props.mapItems);
	}


	view.when(() => {
		reactiveUtils.on(
			() => view.popup,
			'trigger-action',
			(event) => {
				if (event.action.id === 'edit-this') {
					const activeGraphic = view.popup.features[0] as ExtendedGraphic;
					const geometryType = activeGraphic.geometry.type;
					currentGraphicElement.value = {
						coordinates: geometryType === 'polyline'
							? ('paths' in activeGraphic.geometry ? activeGraphic.geometry.paths?.[0] ?? [] : [])
							: ('rings' in activeGraphic.geometry ? activeGraphic.geometry.rings?.[0] ?? [] : []),
						id: activeGraphic.attributes?.id ?? null,
						title: activeGraphic.attributes?.name ?? 'N/A',
						name: activeGraphic.attributes?.name ?? 'N/A',
						itemType: activeGraphic.attributes?.itemType ?? '',
						designation: activeGraphic.attributes?.designation ?? null,
						description: activeGraphic.attributes?.description ?? 'N/A',
						type: geometryType,
						selected: activeGraphic.attributes?.selected ?? null,
						elementsColor: activeGraphic.attributes?.elementsColor ?? null,
						elementsBorderColor: activeGraphic.attributes?.elementsBorderColor ?? null,
						elementsBorderThickness: activeGraphic.attributes?.elementsBorderThickness ?? null,
						elementsBorderType: activeGraphic.attributes?.elementsBorderType ?? null,
						isApproved: activeGraphic.attributes?.isApproved ?? null,
						isTargetable: activeGraphic.attributes?.isTargetable ?? null,
						isUserMade: true
					};
					view.popup.close();
					isModalOpen.value = true;
				}
			}
		);
		//catch delete event
		reactiveUtils.on(
			() => view.popup,
			'trigger-action',
			(event) => {
				if (event.action.id === 'delete-this') {
					const activeGraphic = view.popup.features[0] as CustomGraphic; //graphic
					handleDelete(activeGraphic);
					view.popup.close();
					// const uid = activeGraphic.uid as string ?? null;
					// if(uid) {
					//   removeItemByUID(uid);
					// }
				}
			}
		);
	});

};

//   @item-cancelled="cancelCreateNAI"
const cancelCreateNAI = () => {
	isModalOpen.value = false;
	typeOfItem.value = null;
};

// @item-created="createAOI"
const createAOI = (aoi: Aoi, mapId: string) => {
	isModalOpen.value = false;
	typeOfItem.value = null;
	const symbolItem = convertAoiToSymbolItem(aoi);
	updateLocalMapItem(symbolItem, mapId);
	emit('element-created', aoi, 'aoi');
};

const updateAOI = (aoi: Aoi, mapId: string) => {
	isModalOpen.value = false;
	typeOfItem.value = null;
	const symbolItem = convertAoiToSymbolItem(aoi);
	updateLocalMapItem(symbolItem, mapId);
	emit('element-updated', aoi, 'aoi');
};

const createISRTrack = async (isrTrack: IIsrTrack, mapId: string) => {
	isModalOpen.value = false;
	const symbolItem = convertIsrTrackToSymbolItem(isrTrack);
	updateLocalMapItem(symbolItem, mapId);
	emit('element-created', isrTrack, 'isr_track');
};

const updateISRTrack = (isrTrack: IIsrTrack, mapId: string) => {
	isModalOpen.value = false;
	typeOfItem.value = null;
	const symbolItem = convertIsrTrackToSymbolItem(isrTrack);
	updateLocalMapItem(symbolItem, mapId);
	emit('element-updated', isrTrack, 'isr_track');
};

const convertAoiToSymbolItem = (aoi: Aoi): SymbolItem => {
	const parsedId = aoi.id ? parseInt(aoi.id.toString()) : 0;
	const arcGISType = geometryConverter.geoJSONToArcGIS(aoi.mapElement?.elementType?.toLowerCase() || 'polygon');
	return {
		id: isNaN(parsedId) ? null : parsedId,
		title: aoi.name,
		name: aoi.name,
		itemType: 'aoi',
		designation: aoi.designation,
		description: aoi.description,
		type: arcGISType ?? 'polygon',
		coordinates: aoi.mapElement?.element.coordinates
			? Array.isArray(aoi.mapElement.element.coordinates[0])
				? aoi.mapElement.element.coordinates as number[][]
				: [aoi.mapElement.element.coordinates as number[]]
			: [],
		selected: false,
		elementsColor: [...(aoi.mapElement?.elementColor || [])],
		elementsBorderColor: [...(aoi.mapElement?.borderColor || [])],
		elementsBorderThickness: aoi.mapElement?.borderThickness || 2,
		elementsBorderType: aoi.mapElement?.borderType || 'solid',
		isApproved: aoi.isApproved,
		isTargetable: aoi.isTargetable,
		isUserMade: true
	};
};

const convertIsrTrackToSymbolItem = (isrTrack: IIsrTrack): SymbolItem => {
	const parsedId = isrTrack.id ? parseInt(isrTrack.id.toString()) : 0;
	const arcGISType = geometryConverter.geoJSONToArcGIS(isrTrack.mapElement?.elementType?.toLowerCase() || 'polyline');
	return {
		id: isNaN(parsedId) ? null : parsedId,
		title: isrTrack.label ?? 'ISR TRACK ' + isrTrack.designation,
		name: isrTrack.label ?? 'ISR TRACK ' + isrTrack.designation,
		itemType: 'isr_track',
		designation: isrTrack.designation,
		description: isrTrack.designation,
		type: arcGISType ?? 'polyline',
		coordinates: isrTrack.mapElement?.element.coordinates
			? Array.isArray(isrTrack.mapElement.element.coordinates[0])
				? isrTrack.mapElement.element.coordinates as number[][]
				: [isrTrack.mapElement.element.coordinates as number[]]
			: [],
		selected: false,
		elementsColor: [...(isrTrack.mapElement?.elementColor || [])],
		elementsBorderColor: [...(isrTrack.mapElement?.borderColor || [])],
		elementsBorderThickness: isrTrack.mapElement?.borderThickness || 2,
		elementsBorderType: isrTrack.mapElement?.borderType || 'solid',
		isApproved: true,
		isTargetable: false,
		isUserMade: true
	};
};

const addItems = (items: any) => {
	if (!items) return [];
	items.forEach((item: any) => {
			addItem(item, graphicsLayer);
		}
	);
};

const removeItemByUID = (uid: string) => {
	// find and remove graphic by uid
	const graphic = graphicsLayer.graphics.find((graphic) => (graphic as CustomGraphic).uid === uid);
	if (graphic) {
		graphicsLayer.remove(graphic);
	}
};

const updateLocalMapItem = (item: SymbolItem, uid: string) => {
	// graphicsLayer.graphics.find((graphic) => graphic.uid === uid);
	removeItemByUID(uid);
	addItem(item, graphicsLayer);
};

watch(() => props.mapItems, (newItems) => {
	if (!graphicsLayer) return;
	if (newItems) {
		graphicsLayer.removeAll();
		addItems(newItems);
	}
}, { deep: true });

const getItemsBasedOnCurrentShapeType = computed(() => {

	if (!currentGraphicElement.value || !currentGraphicElement.value.type) return [];

	if (currentGraphicElement.value.type === 'polygon') {
		return ['TAI', 'NAI'];
	} else if (currentGraphicElement.value.type === 'polyline') {
		return ['ISR Tracks'];
	}
	return ['Collection Assets'];
});

const cancelCreateISRTrack = () => {
	isModalOpen.value = false;
	// Add any relevant logic here
};

onMounted(async () => {
	esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;
	await initializeMap();
});


</script>

<style scoped lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

#esriMapEditor{
	min-height: 200px;
}

#esriMapEditor,
html,
body,
.arcgis-map {
	height: 100%;
	width: 100%;
	margin: 0;
	padding: 0;
}

/* Hide the entire info panel */
:deep(.esri-sketch__info-panel) {
	display: none !important;
}
</style>
