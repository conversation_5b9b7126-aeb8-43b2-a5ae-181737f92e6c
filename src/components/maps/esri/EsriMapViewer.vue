<template>
    <div class="esri-map-container h-100">
        <div
            id="esriMapViewer"
            class="h-100"
            :style="maxHeight ? `${getHeightStyle(maxHeight)}` : ''"
        >
            <div id="measurements" class="esri-widget h-100"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, nextTick } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';

import { addItem } from '@/composables/esri/MapGraphicLayers';

interface ExtendedGraphic extends __esri.Graphic {
    uid?: string;
    geometry: __esri.Geometry & {
        paths?: number[][];
        rings?: number[][];
        type: string;
    };
    attributes: {
        isUserMade?: boolean;
        name?: string;
        description?: string;
        id?: number | null;
        [key: string]: any;
    };
}

interface ExtendedHit {
    graphic: ExtendedGraphic;
    type: string;
}

import { SymbolItem } from '@/types/EsriMap';

const emits = defineEmits(['element-selected']);
const props = withDefaults(
    defineProps<{
        maxHeight?: number | null | string;
        mapItems?: SymbolItem[]; // Changed from SymbolItem[] | []
        centerCoordinates?: number[];
        zoom?: number | string;
        operation: any;
    }>(),
    {
        maxHeight: null,
        mapItems: () => [], // Return empty array
        centerCoordinates: undefined,
        zoom: 10,
    },
);

let view: MapView;
let map: Map;
let graphicsLayer: GraphicsLayer;

const initializeMap = async () => {
    if (view) {
        view.destroy();
    }
    if (graphicsLayer) {
        map.remove(graphicsLayer);
    }

    await nextTick();

    map = new Map({
        basemap: 'gray',
    });

    graphicsLayer = new GraphicsLayer();

    map.add(graphicsLayer);

    // Initialize map view and popup handling
    view = new MapView({
        container: 'esriMapViewer',
        map: map,
        zoom: Number(props.zoom),
        center: props.centerCoordinates ?? [-118.805, 34.027],
        popupEnabled: true,
        constraints: {
            rotationEnabled: false,
        },
    });

    const basemapToggle = new BasemapToggle({
        view: view,
        nextBasemap: 'satellite',
    });
    view.ui.add(basemapToggle, 'top-right');

    const fullscreen = new Fullscreen({ view });
    view.ui.add(fullscreen, 'top-right');

    const scalebar = new ScaleBar({ view, unit: 'metric' });
    view.ui.add(scalebar, 'bottom-right');

    if (props.mapItems) {
        addItems(props.mapItems);
    }

    view.on('click', (event) => {
        view.hitTest(event).then((response) => {
            try {
                const arrayOfGraphics = response.results as ExtendedHit[];
                const element = arrayOfGraphics.find((item) => {
                    return item.graphic?.attributes?.isUserMade;
                });

                if (!element) return;

                const graphic: ExtendedGraphic = element.graphic;
                emits('element-selected', {
                    id: graphic.attributes.id,
                    itemType: graphic.attributes.itemType,
                });
            } catch (error) {}
        });
    });
};

const addItems = (items: any) => {
    if (!items) return [];
    items.forEach((item: any) => {
        console.log('item', item);
        addItem(item, graphicsLayer);
    });
};
onMounted(async () => {
    esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;
    await initializeMap();
});

const getHeight = (height: string | null | number) => {
    if (!height) {
        return '85%';
    }

    if (height.toString() === '100%') {
        return '100%';
    }

    if (height.toString().includes('%')) {
        //make sure height is not more than 80%
        //convert to int
        if (Number(height.toString().replace('%', '')) > 84) {
            return '85%';
        }
        return height;
    } else {
        return height + 'px';
    }
};

// New function to handle height styling with min-height
const getHeightStyle = (height: string | null | number) => {
    const heightValue = getHeight(height);
    if (heightValue === '100%') {
        return 'min-height: 400px; height: 100%;';
    }
    return `min-height: 400px; height: ${heightValue};`;
};
</script>

<style scoped lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

.esri-map-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 400px;
}

#esriMapViewer {
    min-height: 400px;
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
}

#esriMapEditor,
html,
body,
.arcgis-map {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
}
</style>
