<template>
	<div class="h-100">
	<div
		id="esriMapSingleAreaEditor"
		:style="maxHeight ? `height:${getHeight(maxHeight)}; max-height: ${getHeight(maxHeight)};` : ''"
	>
		<div id="measurements" class="esri-widget h-100"></div>
	</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch, nextTick } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import Sketch from '@arcgis/core/widgets/Sketch';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';
import Graphic from '@arcgis/core/Graphic';
import { SymbolItem } from '@/types/EsriMap';
import { Polygon } from '@arcgis/core/geometry';
import { CIMSymbol } from '@arcgis/core/symbols';
import PopupTemplate from '@arcgis/core/PopupTemplate';
import * as reactiveUtils from '@arcgis/core/core/reactiveUtils';
import { getHeight } from '@/composables/esri/MapGraphicLayers';


const props = withDefaults(
	defineProps<{
		maxHeight?: number | null | string;
		mapItem?: SymbolItem | null;
		centerCoordinates?: number[];
		zoom?: number | string;
	}>(),
	{
		maxHeight: null,
		mapItem: null,
		centerCoordinates: undefined,
		zoom: 10
	}
);

const emit = defineEmits([
	'area-created',
	'area-selected',
	'area-updated',
	'area-deleted',
	'zoom-updated',
	'center-updated'
]);

let view: MapView;
let map: Map;
let graphicsLayer: GraphicsLayer;
let sketch: Sketch;

const initializeMap = async () => {
	if (view) {
		view.destroy();
	}
	if (graphicsLayer && map) {
		map.remove(graphicsLayer);
	}

	await nextTick();

	map = new Map({
		basemap: 'gray'
	});

	graphicsLayer = new GraphicsLayer();
	map.add(graphicsLayer);

	// Initialize map view
	view = new MapView({
		container: 'esriMapSingleAreaEditor',
		map: map,
		zoom: Number(props.zoom),
		center: props.centerCoordinates ?? [-118.805, 34.027],
		popupEnabled: true,
		constraints: {
			rotationEnabled: false
		}
	});

	// Initialize and configure the Sketch widget for rectangle only
	sketch = new Sketch({
		layer: graphicsLayer,
		view: view,
		availableCreateTools: ['rectangle'],
		creationMode: 'single',
		visibleElements: {
			createTools: {
				rectangle: true,
				polygon: false,
				polyline: false,
				point: false,
				circle: false
			},
			selectionTools: {
				'rectangle-selection': false,
				'lasso-selection': false
			},
			settingsMenu: false,
			undoRedoMenu: false,
			duplicateButton: false
		}
	});

	// Add widgets to the view
	view.ui.add(sketch, 'top-left');

	const basemapToggle = new BasemapToggle({
		view: view,
		nextBasemap: 'satellite'
	});
	view.ui.add(basemapToggle, 'top-right');

	const fullscreen = new Fullscreen({ view });
	view.ui.add(fullscreen, 'top-right');

	const scalebar = new ScaleBar({ view, unit: 'metric' });
	view.ui.add(scalebar, 'bottom-right');

	// Watch for center and zoom changes
	view.watch('center', (center) => {
		emit('center-updated', {
			latitude: center.latitude,
			longitude: center.longitude
		});
	});

	view.watch('zoom', (zoom) => {
		emit('zoom-updated', zoom);
	});

	view.on("drag", ["Shift"], (event) => {
		// When dragging ends (mouse up after drag)
		if (event.action === "end" && event.stopPropagation) {
			event.stopPropagation();

			// Find the selected graphic
			if (sketch.updateGraphics.length > 0) {
				const graphic = sketch.updateGraphics.getItemAt(0);
				const polygon = graphic.geometry as Polygon;
				const coordinates = polygon.rings[0];

				emit('area-updated', {
					coordinates: coordinates,
					type: 'polygon'
				});
			}
		}
	});

	// Handle sketch events
	sketch.on('create', (event) => {
		if (event.state === 'complete') {
			const graphic = event.graphic;

			// Create attributes for the graphic
			graphic.attributes = {
				id: null,
				title: 'Area',
				name: 'Area',
				itemType: 'area',
				type: 'polygon',
				coordinates: [],
				isUserMade: true
			};

			// Apply symbol
			graphic.symbol = genSymbol({
				id: null,
				type: 'polygon',
				coordinates: [],
				title: 'Area',
				elementsColor: [111, 111, 111, 89],
				elementsBorderColor: [111, 111, 111, 255],
				elementsBorderThickness: 2,
				elementsBorderType: 'solid'
			});

			// Hide sketch tool since we only want one rectangle
			sketch.visible = false;

			// Extract coordinates for the event emission
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];

			// Emit the created area event
			emit('area-created', {
				coordinates: coordinates,
				type: 'polygon'
			});
		}
	});

	// Handle update events (resize, move)
	sketch.on('update', (event) => {
		// This will detect the end of a move/resize operation
		if (event.state === 'start') {
			// Optional: track that editing has started
		} else if (event.state === 'active' && event.toolEventInfo && (event.toolEventInfo.type === 'move-stop' || event.toolEventInfo.type === 'scale-stop')) {
			// Track active editing but don't emit yet
			const graphic = event.graphics[0];
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];
			emit('area-updated', {
				coordinates: coordinates,
				type: 'polygon'
			});
		} else if (event.state === 'complete' && event.graphics.length > 0) {
			const graphic = event.graphics[0];
			const polygon = graphic.geometry as Polygon;
			const coordinates = polygon.rings[0];

			emit('area-updated', {
				coordinates: coordinates,
				type: 'polygon'
			});
		}
	});

	// Set up click handler
	view.on('click', (event) => {
		event.stopPropagation();

		view.hitTest(event).then((response) => {
			if (response.results.length > 0) {
				//@ts-ignore
				const graphic = response.results[0]?.graphic;

				if (graphic && graphic.attributes?.isUserMade) {
					// Configure popup template with delete action only
					const itemPopupTemplate = new PopupTemplate({
						title: "Area",
						content: [
							{
								type: 'text',
								text: 'Click delete to remove this area.'
							}
						],
						actions: [
							{
								type: 'button',
								title: "DELETE AREA",
								id: 'delete-area',
								icon: 'trash'
							}
						]
					});

					graphic.popupTemplate = itemPopupTemplate;

					view.openPopup({
						location: event.mapPoint,
						features: [graphic]
					});
				}
			}
		});
	});

	// Handle popup actions
	view.when(() => {
		reactiveUtils.on(
			() => view.popup,
			'trigger-action',
			(event) => {
				if (event.action.id === 'delete-area') {
					const graphic = view.popup.features[0];

					if (graphic) {
						// Remove the graphic
						graphicsLayer.remove(graphic);

						// Close the popup
						view.popup.close();

						// Show the sketch tool again
						sketch.visible = true;

						// Emit deletion event
						emit('area-deleted');
					}
				}
			}
		);
	});

	// If mapItem is provided, add it to the map
	if (props.mapItem) {
		//make sure this item has coordinates and coordinates exist lenght over 0
		if(!props.mapItem.coordinates || props.mapItem.coordinates.length === 0) return;


		addItem(props.mapItem);
		// Hide sketch since we already have an area
		sketch.visible = false;
	}
};

// Generate symbol for the area
const genSymbol = (item: SymbolItem) => {
	return new CIMSymbol({
		data: {
			type: 'CIMSymbolReference',
			symbol: {
				type: 'CIMPolygonSymbol',
				symbolLayers: [
					{
						type: 'CIMSolidFill',
						enable: true,
						color: [...(item.elementsColor ?? [0, 0, 0, 40])]
					},
					{
						type: 'CIMSolidStroke',
						enable: true,
						color: [...(item.elementsBorderColor ?? [0, 0, 0, 255])],
						width: item.elementsBorderThickness ?? 2,
						capStyle: 'Round',
						joinStyle: 'Round',
						miterLimit: 10,
						effects: item.elementsBorderType === 'dashed' ? [
							{
								type: 'CIMGeometricEffectDashes',
								dashTemplate: [4, 4],
								lineDashEnding: 'NoConstraint',
								//@ts-ignore
								controlPointEnding: 'NoConstraint'
							}
						] : []
					}
				]
			}
		}
	});
};

// Add single item to map
const addItem = (item: SymbolItem) => {
	if (!item || !item.coordinates || !item.coordinates.length || !graphicsLayer) return;

	// Clear existing graphics
	graphicsLayer.removeAll();

	const polygonGraphic = new Graphic({
		geometry: new Polygon({
			rings: [item.coordinates as number[][]],
			spatialReference: { wkid: 102100 }
		}),
		symbol: genSymbol(item),
		attributes: {
			name: item.title ?? 'Area',
			title: item.title ?? 'Area',
			description: item.description ?? '',
			itemType: 'area',
			id: item.id ?? null,
			coordinates: item.coordinates,
			isUserMade: true,
			type: 'polygon'
		}
	});

	graphicsLayer.add(polygonGraphic);

	// Make sure sketch is hidden
	if (sketch) {
		sketch.visible = false;
	}
};

// Watch for changes in mapItem prop
watch(() => props.mapItem, (newItem) => {
	if (!graphicsLayer) return;

	graphicsLayer.removeAll();

	if (newItem) {
		addItem(newItem);
		if (sketch) {
			sketch.visible = false;
		}
	} else if (sketch) {
		sketch.visible = true;
	}
}, { deep: true });

// Setup and cleanup
onMounted(async () => {
	esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;
	await initializeMap();
});

onUnmounted(() => {
	if (view) {
		view.destroy();
	}
});

</script>

<style scoped lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

#esriMapEditor{
	min-height: 200px;
}

#esriMapSingleAreaEditor,
html,
body,
.arcgis-map {
	height: 100%;
	width: 100%;
	margin: 0;
	padding: 0;
}
</style>
