<template>
	<div class="map-container">
		<div ref="mapElement" class="map"></div>
		<div v-if="showControls" class="map-controls">
			<div class="layer-toggle">
				<input type="checkbox" id="map-visible" v-model="showMap">
				<label for="map-visible">Map</label>
			</div>
			<div class="layer-toggle">
				<input type="checkbox" id="hillshade-visible" v-model="showHillshade">
				<label for="hillshade-visible">Hillshade</label>
			</div>
			<div class="layer-toggle">
				<input type="checkbox" id="contours-visible" v-model="showContours">
				<label for="contours-visible">Contours</label>
			</div>
			<div class="layer-toggle">
				<input type="checkbox" id="elevation-visible" v-model="showElevation">
				<label for="elevation-visible">Elevation</label>
			</div>
			<div>
				{{initialCenter}}
			</div>
			<div>

			</div>
		</div>
	</div>

</template>

<script setup lang="ts">
import { ref, onMounted, watch, toRefs } from 'vue';
import type { PropType } from 'vue';
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile'; // For raster layers (hillshade, elevation, and now contours)
import XYZ from 'ol/source/XYZ';       // Source for raster layers
import VectorTileLayer from 'ol/layer/VectorTile'; // For PBF layers (base map)
import VectorTileSource from 'ol/source/VectorTile'; // Source for PBF layers
import MVT from 'ol/format/MVT';                  // Parser for PBF layers (base map)
import { Style, Stroke, Fill, Circle as CircleStyle, Text } from 'ol/style'; // Styling components for base map
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, Zoom } from 'ol/control';
import 'ol/ol.css';

// --- Props ---
const props = defineProps({
	serverUrl: {
		type: String,
		default: 'https://tiles.lumio.au' // tileserver-gl for style PNG endpoints
	},
	baseMapPBFUrl: {
		type: String,
		default: 'https://map.lumio.au/osm/{z}/{x}/{y}.pbf' // t-rex PBFs for base map
	},
	initialCenter: {
		type: Array as unknown as PropType<[number, number]>,
		default: () => [33.3, 35.0] // Cyprus center [Longitude, Latitude]
	},
	initialZoom: {
		type: Number,
		default: 12 // Starting zoom
	},
	showControls: {
		type: Boolean,
		default: true
	}
});

// Make props reactive
const { serverUrl, baseMapPBFUrl, initialCenter, initialZoom } = toRefs(props);

// --- Refs ---
const mapElement = ref<HTMLElement | null>(null);
const olMap = ref<Map | null>(null);

// --- Layer Visibility State ---
const showMap = ref(true);
const showHillshade = ref(true);
const showContours = ref(true);
const showElevation = ref(true);

// --- Layer Variables ---
let baseMapLayer: VectorTileLayer | null = null;
let hillshadeLayer: TileLayer<XYZ> | null = null;
let contoursLayer: TileLayer<XYZ> | null = null; // Changed to TileLayer<XYZ> for PNG tiles
let elevationLayer: TileLayer<XYZ> | null = null;

// --- Styling Function for Base Map (PBF from t-rex) --// This function remains as it styles the vector base map on the client-side

// --- Styling Function for Base Map (PBF from t-rex) ---
const createVectorStyle = () => {
	// --- Style Definitions ---
	const waterFillColor = 'rgba(129,153,255,0.52)'; // Light blue for water
	const landuseGenericFillColor = 'rgba(232, 232, 232, 0.7)'; // Light grey for general landuse
	const forestFillColor = 'rgba(173, 204, 159, 0.7)'; // Light green for forests/woods
	const residentialFillColor = 'rgba(220, 220, 220, 0.7)'; // Slightly different grey for residential
	const commercialFillColor = 'rgba(210, 210, 225, 0.7)'; // Light purple/grey for commercial
	const industrialFillColor = 'rgba(200, 200, 210, 0.7)'; // Darker purple/grey for industrial

	const buildingFillColor = 'rgba(217, 208, 201, 0.7)'; // Beige for buildings
	const buildingStrokeColor = 'rgba(189, 173, 162, 0.7)'; // Darker beige for building outlines

	const roadDefaultStrokeColor = 'rgba(175,182,188,0.71)'; // Default road color (minor roads)
	const roadMotorwayColor = 'rgba(224, 108, 108, 0.8)';   // Reddish for motorways
	const roadPrimaryColor = 'rgba(247, 178, 125, 0.8)';    // Orange for primary roads
	const roadSecondaryTertiaryColor = 'rgba(252, 216, 148, 0.8)'; // Yellowish for secondary/tertiary

	const pointDefaultColor = 'rgba(29,29,30,0.6)';         // Dark grey for unspecified points
	const placeMarkerFillColor = 'rgba(243,228,47,0.7)';   // Yellowish for place markers
	const placeMarkerStrokeColor = '#666';                  // Dark grey stroke for place markers

	const labelFillColor = '#333333';        // Dark grey for text
	const labelHaloColor = '#FFFFFF';        // White halo for text
	const labelHaloWidth = 2.5;              // Halo width for better readability

	// --- Helper to create Text Styles ---
	// This function creates the Text object for labels
	const createTextStyle = (feature: any, resolution: number, layerHint: string) => {
		const name = feature.get('name');
		if (!name) return undefined; // No name, no label

		let fontSize = 10; // Default font size
		let showLabel = false;
		let customPlacement: 'point' | 'line' = feature.getGeometry()?.getType() === 'Point' ? 'point' : 'line';

		const tRexLayerName = feature.get('layer'); // The layer name from t-rex config (e.g., "places", "roads")

		if (tRexLayerName === 'places') {
			const placeType = feature.get('place');
			customPlacement = 'point';

			// Adjust font size and visibility based on place type and resolution (zoom level)
			// Higher resolution value = lower zoom level
			if (placeType === 'city') {
				if (resolution < 2000) { fontSize = 16; showLabel = true; } // Show cities up to a certain zoom
				else if (resolution < 4000) { fontSize = 14; showLabel = true; }
			} else if (placeType === 'town') {
				if (resolution < 1000) { fontSize = 14; showLabel = true; }
				else if (resolution < 2000) { fontSize = 12; showLabel = true; }
			} else if (placeType === 'village') {
				if (resolution < 500) { fontSize = 12; showLabel = true; }
				else if (resolution < 1000) { fontSize = 10; showLabel = true; }
			} else if (placeType === 'hamlet' || placeType === 'suburb' || placeType === 'locality') {
				if (resolution < 200) { fontSize = 10; showLabel = true; }
			}
			// Add more place types (e.g., 'suburb', 'hamlet') and adjust resolution thresholds as needed

		} else if (tRexLayerName === 'roads' && layerHint === 'roads') { // Ensure we only label roads here
			const highwayType = feature.get('highway');
			customPlacement = 'line';
			// Only label major roads and at appropriate zoom levels
			if (['motorway', 'trunk', 'primary'].includes(highwayType) && resolution < 300) {
				fontSize = 10; showLabel = true;
			} else if (['secondary', 'tertiary'].includes(highwayType) && resolution < 150) {
				fontSize = 9; showLabel = true;
			}
			// Avoid labeling minor roads unless at very high zoom, or not at all to reduce clutter
		}
		// Add more conditions for other tRexLayerName if you have other named features

		if (!showLabel) return undefined;

		return new Text({
			text: name.toString(),
			font: `bold ${fontSize}px "Arial", "Helvetica", sans-serif`,
			fill: new Fill({ color: labelFillColor }),
			stroke: new Stroke({ color: labelHaloColor, width: labelHaloWidth }),
			placement: customPlacement,
			textAlign: (customPlacement === 'point') ? 'center' : undefined,
			textBaseline: (customPlacement === 'point') ? 'middle' : undefined,
			overflow: true, // Allow labels to be partly outside the feature / view
		});
	};

	// --- Predefined Styles for different t-rex layer names ---
	// These are cached Style objects for efficiency where properties don't change per feature.
	// For dynamic styles (like roads based on 'highway' type), we'll create them on the fly.
	const cachedStyles: { [key: string]: Style } = {
		'water': new Style({
			fill: new Fill({ color: waterFillColor })
		}),
		'buildings': new Style({
			fill: new Fill({ color: buildingFillColor }),
			stroke: new Stroke({ color: buildingStrokeColor, width: 1 })
		}),
		// Default style for points if no specific styling is matched (e.g. from a 'points' layer without 'place' type)
		'points_default_marker': new Style({
			image: new CircleStyle({
				radius: 3,
				fill: new Fill({ color: pointDefaultColor }),
				stroke: new Stroke({ color: labelHaloColor, width: 1 })
			})
		}),
		'default_polygon': new Style({ // Default for unknown polygon types
			fill: new Fill({ color: landuseGenericFillColor }),
			stroke: new Stroke({ color: 'gray', width: 0.5 })
		}),
		'default_linestring': new Style({ // Default for unknown linestring types
			stroke: new Stroke({ color: roadDefaultStrokeColor, width: 1 })
		}),
	};


	// --- Main Style Function ---
	// This function is called for each feature and must return a Style or an array of Styles.
	return (feature: any, resolution: number) => {
		const tRexLayerName = feature.get('layer'); // This is the 'name' of the layer from t-rex.toml
		const geometryType = feature.getGeometry()?.getType();
		let styleToUse: Style | Style[] | undefined;

		// 1. Handle Landuse (Polygons from planet_osm_polygon via t-rex layers)
		// Assuming your t-rex layers for landuse are named 'landuse_low_zoom', 'landuse_medium', 'landuse'
		if (tRexLayerName && tRexLayerName.startsWith('landuse')) {
			const landuseType = feature.get('landuse');
			const naturalType = feature.get('natural');
			let fillColor = landuseGenericFillColor;

			if (landuseType === 'forest' || naturalType === 'wood') {
				fillColor = forestFillColor;
			} else if (landuseType === 'residential') {
				fillColor = residentialFillColor;
			} else if (landuseType === 'commercial') {
				fillColor = commercialFillColor;
			} else if (landuseType === 'industrial') {
				fillColor = industrialFillColor;
			} // Add more landuse/natural types (grass, park, etc.)
			styleToUse = new Style({
				fill: new Fill({ color: fillColor }),
				// Optionally add text style for landuse area names at high zoom
				text: createTextStyle(feature, resolution, 'landuse')
			});
		}
			// 2. Handle Water (Polygons)
		// Assuming t-rex layers 'water_low_zoom', 'water_medium', 'water'
		else if (tRexLayerName && tRexLayerName.startsWith('water')) {
			styleToUse = cachedStyles['water']; // Use cached style
			// Optionally add text style for water body names
			const waterTextStyle = createTextStyle(feature, resolution, 'water');
			if (waterTextStyle && styleToUse instanceof Style) { // Ensure styleToUse is a single Style
				styleToUse = new Style({ // Create a new style if text is added
					fill: styleToUse.getFill() || undefined,
					stroke: styleToUse.getStroke() || undefined,
					image: styleToUse.getImage() || undefined,
					text: waterTextStyle
				});
			}
		}
		// 3. Handle Buildings (Polygons)
		else if (tRexLayerName === 'buildings') {
			styleToUse = cachedStyles['buildings']; // Use cached style
		}
			// 4. Handle Roads (LineStrings)
		// Assuming t-rex layers 'roads_low_zoom', 'roads_medium', 'roads'
		else if (tRexLayerName && tRexLayerName.startsWith('roads')) {
			const highwayType = feature.get('highway');
			let color = roadDefaultStrokeColor;
			let width = 1;
			let zIndex = 0; // To control drawing order of roads

			if (highwayType === 'motorway' || highwayType === 'trunk') {
				color = roadMotorwayColor;
				width = (resolution < 20) ? 4 : (resolution < 80) ? 3 : 2;
				zIndex = 5;
			} else if (highwayType === 'primary') {
				color = roadPrimaryColor;
				width = (resolution < 20) ? 3.5 : (resolution < 80) ? 2.5 : 1.5;
				zIndex = 4;
			} else if (highwayType === 'secondary' || highwayType === 'tertiary') {
				color = roadSecondaryTertiaryColor;
				width = (resolution < 20) ? 3 : (resolution < 80) ? 2 : 1;
				zIndex = 3;
			} else if (['residential', 'unclassified', 'service'].includes(highwayType)) {
				width = (resolution < 20) ? 2 : (resolution < 80) ? 1.5 : 0.75;
				zIndex = 2;
			} else { // Cycleways, footways, paths, etc.
				width = (resolution < 40) ? 1 : 0.5;
				color = 'grey'; // A more subtle color for paths
				zIndex = 1;
			}

			// Don't draw very minor roads at low zoom levels
			if (width < 0.75 && resolution > 100) { // Example threshold
				return undefined;
			}

			styleToUse = new Style({
				stroke: new Stroke({ color: color, width: width }),
				text: createTextStyle(feature, resolution, 'roads'), // Pass 'roads' hint
				zIndex: zIndex
			});
		}
		// 5. Handle Places (Points - for city, town, village labels)
		else if (tRexLayerName === 'places') {
			const placeTextStyle = createTextStyle(feature, resolution, 'places');
			// Only create a style if there's text to display (meaning it passed the zoom/type checks)
			if (placeTextStyle) {
				styleToUse = new Style({
					image: new CircleStyle({
						radius: feature.get('place') === 'city' ? 6 : feature.get('place') === 'town' ? 5 : 4,
						fill: new Fill({ color: placeMarkerFillColor }),
						stroke: new Stroke({ color: placeMarkerStrokeColor, width: 1.5 })
					}),
					text: placeTextStyle,
					zIndex: 10 // Ensure place labels are on top
				});
			} else {
				// Optionally, show a small dot for places even if not labeled at this zoom,
				// but usually it's better to not show them if they are not labeled.
				// return cachedStyles['points_default_marker'];
				return undefined; // No label, no marker (cleaner map)
			}
		}
		// Fallback for other layer types or unstyled t-rex layers
		else {
			if (geometryType === 'Polygon' || geometryType === 'MultiPolygon') {
				styleToUse = cachedStyles['default_polygon'];
			} else if (geometryType === 'LineString' || geometryType === 'MultiLineString') {
				styleToUse = cachedStyles['default_linestring'];
			} else if (geometryType === 'Point' || geometryType === 'MultiPoint') {
				styleToUse = cachedStyles['points_default_marker']; // Default for other points
			}
		}
		return styleToUse;
	};
};


// --- Function to Create Raster Tile Layers (XYZ) ---
// This function is now used for hillshade, contours, and elevation from style endpoints
const createRasterTileLayer = (url: string, visible = true, zIndex = 0, opacity = 1.0, maxZoom = 13) => {
	return new TileLayer({
		source: new XYZ({
			url,
			tileSize: 256, // Standard for most tileserver-gl PNGs
			wrapX: true,
			maxZoom,
			crossOrigin: 'anonymous',
			tileLoadFunction: function(imageTile: any, src: string) {
				const img = imageTile.getImage() as HTMLImageElement;
				let retries = 0; const maxRetries = 3;
				const tryLoad = function() { img.src = src; };
				img.onerror = function() {
					if (retries < maxRetries) {
						retries++; console.warn(`Failed to load tile: ${src}, retrying (${retries}/${maxRetries})...`); setTimeout(tryLoad, 1000 * retries);
					} else { console.warn(`Failed to load tile after ${maxRetries} retries: ${src}`); img.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='; } // Placeholder for failed tile
				};
				tryLoad();
			}
		}),
		visible, zIndex, opacity
	});
};

// --- Map Initialization (onMounted) ---
onMounted(() => {
	if (!mapElement.value) return;

	// --- Layer Definitions ---
	// Base Map (OSM from t-rex, client-side styled PBF) - UNCHANGED
	baseMapLayer = new VectorTileLayer({
		source: new VectorTileSource({
			format: new MVT(),
			url: baseMapPBFUrl.value,
			maxZoom: 22,
		}),
		style: createVectorStyle(),
		visible: showMap.value,
		zIndex: 0,
		declutter: true,
	});

	// Hillshade (PNG from tileserver style endpoint) - UPDATED URL
	hillshadeLayer = createRasterTileLayer(
		`${serverUrl.value}/data/hillshade/{z}/{x}/{y}.png`, // Assuming 'hillshade-only' is your style name
		showHillshade.value,
		1,    // zIndex
		0.3,  // Opacity
		12    // Max zoom for hillshade
	);

	// Contours (PNG from tileserver style endpoint) - NEW DEFINITION
	contoursLayer = createRasterTileLayer(
		`${serverUrl.value}/styles/contours-only/{z}/{x}/{y}.png`, // Assuming 'contours-only' is your style name
		showContours.value,
		2,    // zIndex (above hillshade)
		0.7,  // Opacity (style itself might have opacity, adjust as needed)
		16    // Max zoom for contours
	);

	// Elevation (PNG from tileserver style endpoint) - UPDATED URL
	// Note: The 'terrain' style might include hillshade and contours as well, depending on its definition.
	// If you need *only* elevation, ensure your 'terrain' style or a dedicated elevation style reflects that.
	elevationLayer = createRasterTileLayer(
		`${serverUrl.value}/data/elevation/{z}/{x}/{y}.png`, // Assuming 'terrain' style includes/is your elevation rendering
		showElevation.value,
		0,    // zIndex (can be same as base, drawn based on layer order if zIndex is same)
		0.4,  // Opacity (client-side, multiplies with style's opacity if any)
		12    // Max zoom for this terrain/elevation style
	);

	// Ensure z-indexes are explicitly set if needed, though defined in createRasterTileLayer
	// baseMapLayer zIndex is 0
	// if (elevationLayer) elevationLayer.setZIndex(0); // Can be redundant if set in creation
	// if (hillshadeLayer) hillshadeLayer.setZIndex(1);
	// if (contoursLayer) contoursLayer.setZIndex(2);

	// --- Map Creation ---
	olMap.value = new Map({
		layers: [baseMapLayer, elevationLayer, hillshadeLayer, contoursLayer], // Order matters for layers with the same zIndex
		target: mapElement.value!,
		controls: defaultControls({
			attributionOptions: { collapsible: false }

		}).extend([new Zoom()]),
		view: new View({
			center: fromLonLat(initialCenter.value),
			zoom: initialZoom.value,
			maxZoom: 20
		})
	});
});




// --- Watchers for Layer Visibility ---
watch(showMap, (visible) => baseMapLayer?.setVisible(visible));
watch(showHillshade, (visible) => hillshadeLayer?.setVisible(visible));
watch(showContours, (visible) => contoursLayer?.setVisible(visible)); // contoursLayer is now TileLayer
watch(showElevation, (visible) => elevationLayer?.setVisible(visible));


</script>

<style scoped>
.map-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.map {
	width: 100%;
	height: 100%;
}

.map-controls {
	position: absolute;
	bottom: 10px;
	left: 10px;
	background: white;
	padding: 10px;
	border-radius: 4px;
	box-shadow: 0 0 10px rgba(0,0,0,0.2);
	z-index: 1000; /* Ensure controls are above the map */
}

.layer-toggle {
	margin-bottom: 5px;
}

/* OpenLayers control styling */
:global(.ol-zoom) { /* Use :global for OL default classes if needed, or style OL specific classes */
	top: 10px;
	left: auto !important; /* Override OL default */
	right: 10px;
	background-color: white;
	border-radius: 4px;
	box-shadow: 0 0 10px rgba(0,0,0,0.2);
	padding: 2px;
}

:global(.ol-zoom-in),
:global(.ol-zoom-out) {
	margin: 1px;
	font-weight: bold;
	color: #1E1E1E; /* Dark grey text */
}

:global(.ol-zoom button:hover) {
	background-color: #EEF73F; /* Lumio yellow */
}
</style>