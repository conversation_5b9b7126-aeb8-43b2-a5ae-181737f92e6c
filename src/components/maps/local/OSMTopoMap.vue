<!-- OSMTopoMap.vue -->
<template>
	<div class="map-container">
		<div ref="mapElement" class="map"></div>
		<div v-if="showControls" class="map-controls">
			<div class="layer-toggle">
				<input type="checkbox" id="roads" v-model="showRoads">
				<label for="roads">Roads</label>
			</div>
			<div class="layer-toggle">
				<input type="checkbox" id="landuse" v-model="showLanduse">
				<label for="landuse">Land Use</label>
			</div>
			<div class="layer-toggle">
				<input type="checkbox" id="contours" v-model="showContours">
				<label for="contours">Contours</label>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, PropType } from 'vue';
import { Map, View } from 'ol';
// import TileLayer from 'ol/layer/Tile';
import VectorTile from 'ol/layer/VectorTile';
import VectorSource from 'ol/source/Vector';
import VectorTileSource from 'ol/source/VectorTile';
import MVT from 'ol/format/MVT';
import { fromLonLat } from 'ol/proj';
import { Stroke, Style, Fill, Text } from 'ol/style';
import { Feature } from 'ol';
import { Polygon } from 'ol/geom';
import { Vector as VectorLayer } from 'ol/layer';
import { createXYZ } from 'ol/tilegrid';
import { defaults as defaultControls, Zoom } from 'ol/control';
import 'ol/ol.css';

export default defineComponent({
	name: 'OSMTopoMap',
	props: {
		serverUrl: {
			type: String,
			default: 'https://map.lumio.au'
		},
		initialCenter: {
			type: Array as unknown as PropType<[number, number]>,
			default: () => [-2.0, 40.0] // Default center on Spain
		},
		initialZoom: {
			type: Number,
			default: 5
		},
		showControls: {
			type: Boolean,
			default: true
		}
	},
	setup(props) {
		const mapElement = ref<HTMLElement | null>(null);
		const map = ref<Map | null>(null);

		// Layer visibility controls
		const showRoads = ref(true);
		const showLanduse = ref(true);
		const showContours = ref(true);

		// Type definitions for layer references
		let roadsLayer: VectorTile<VectorTileSource> | null = null;
		let roadsCaseLayer: VectorTile<VectorTileSource> | null = null;
		let landuseLayer: VectorTile<VectorTileSource> | null = null;
		let contoursLayer: VectorTile<VectorTileSource> | null = null;
		let contourLabelsLayer: VectorTile<VectorTileSource> | null = null;

		onMounted(() => {
			if (!mapElement.value) return;

			// Create vector tile source
			const vectorSource = new VectorTileSource({
				format: new MVT(),
				url: `${props.serverUrl}/osm/{z}/{x}/{y}.pbf`,
				tileGrid: createXYZ({
					maxZoom: 22
				}),
				attributions: 'Lumio OpenStreetMap'
			});

			// Create background layer
			const backgroundLayer = new VectorLayer({
				source: new VectorSource(),
				style: new Style({
					fill: new Fill({
						color: '#f8f4f0'
					})
				})
			});

			// Create a vector background rectangle
			const extent = [-20026376.39, -20048966.10, 20026376.39, 20048966.10]; // EPSG:3857 extent
			const backgroundFeature = new Feature(new Polygon([[
				[extent[0], extent[1]],
				[extent[0], extent[3]],
				[extent[2], extent[3]],
				[extent[2], extent[1]],
				[extent[0], extent[1]]
			]]));
			backgroundLayer.getSource()?.addFeature(backgroundFeature);

			// Create landuse layer
			landuseLayer = new VectorTile({
				source: vectorSource,
				style: (feature) => {
					const landuse = feature.get('landuse');
					let color = '#eee';

					// Match colors from the style.json
					if (landuse === 'forest') color = '#cad9bf';
					else if (landuse === 'residential') color = '#e0dfdf';
					else if (landuse === 'commercial') color = '#f2dad9';
					else if (landuse === 'recreation_ground') color = '#d9f2da';
					else if (landuse === 'industrial') color = '#d1d3d4';
					else if (landuse === 'retail') color = '#f2dad9';

					return new Style({
						fill: new Fill({
							color: color
						})
					});
				},
				declutter: true,
				visible: showLanduse.value
			});

			// Roads case layer (outlines)
			roadsCaseLayer = new VectorTile({
				source: vectorSource,
				style: (feature, resolution) => {
					const zoom = Math.log2(156543.03392804097 / (resolution || 1)) - 0.5;

					// Calculate width based on zoom level
					let width = 1;
					if (zoom > 5) width = 1 + (zoom - 5) * 0.2;
					if (zoom > 10) width = 2 + (zoom - 10) * 0.2;
					if (zoom > 15) width = 3 + (zoom - 15) * 0.4;
					if (width > 5) width = 5;

					return new Style({
						stroke: new Stroke({
							color: '#888888',
							width: width
						})
					});
				},
				declutter: true,
				visible: showRoads.value
			});

			// Roads layer
			roadsLayer = new VectorTile({
				source: vectorSource,
				style: (feature, resolution) => {
					const zoom = Math.log2(156543.03392804097 / (resolution || 1)) - 0.5;

					// Calculate width based on zoom level
					let width = 0.5;
					if (zoom > 5) width = 0.5 + (zoom - 5) * 0.1;
					if (zoom > 10) width = 1 + (zoom - 10) * 0.2;
					if (zoom > 15) width = 2 + (zoom - 15) * 0.4;
					if (width > 4) width = 4;

					return new Style({
						stroke: new Stroke({
							color: '#ffffff',
							width: width
						})
					});
				},
				declutter: true,
				visible: showRoads.value
			});

			// Contours layer
			contoursLayer = new VectorTile({
				source: vectorSource,
				style: (feature) => {
					const elev = feature.get('elev') || 0;
					const isMajor = elev % 100 === 0;

					return new Style({
						stroke: new Stroke({
							color: isMajor ? '#423e3c' : '#b3b1ac',
							width: isMajor ? 0.8 : 0.4
						})
					});
				},
				declutter: true,
				visible: showContours.value
			});

			// Contour label layer
			contourLabelsLayer = new VectorTile({
				source: vectorSource,
				minZoom: 12,
				style: (feature) => {
					const elev = feature.get('elev') || 0;
					if (elev % 100 !== 0) {
						return undefined; // Return undefined instead of null
					}

					return new Style({
						text: new Text({
							text: elev.toString(),
							font: '10px Roboto-Regular',
							fill: new Fill({
								color: '#9f9a93'
							}),
							stroke: new Stroke({
								color: '#FFFFFF',
								width: 1
							})
						})
					});
				},
				declutter: true,
				visible: showContours.value
			});

			// Create the map
			map.value = new Map({
				layers: [
					backgroundLayer,
					landuseLayer,
					roadsCaseLayer,
					roadsLayer,
					contoursLayer,
					contourLabelsLayer
				],
				target: mapElement.value,
				controls: defaultControls().extend([
					new Zoom()
				]),
				view: new View({
					center: fromLonLat(props.initialCenter),
					zoom: props.initialZoom
				})
			});
		});

		// Watch for visibility changes
		watch(showRoads, (visible: boolean) => {
			if (roadsLayer) roadsLayer.setVisible(visible);
			if (roadsCaseLayer) roadsCaseLayer.setVisible(visible);
		});

		watch(showLanduse, (visible: boolean) => {
			if (landuseLayer) landuseLayer.setVisible(visible);
		});

		watch(showContours, (visible: boolean) => {
			if (contoursLayer) contoursLayer.setVisible(visible);
			if (contourLabelsLayer) contourLabelsLayer.setVisible(visible);
		});

		return {
			mapElement,
			showRoads,
			showLanduse,
			showContours
		};
	}
});
</script>

<style scoped>
.map-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.map {
	width: 100%;
	height: 100%;
}

.map-controls {
	position: absolute;
	bottom: 10px;
	left: 10px;
	background: white;
	padding: 10px;
	border-radius: 4px;
	box-shadow: 0 0 10px rgba(0,0,0,0.2);
	z-index: 1000;
}

.layer-toggle {
	margin-bottom: 5px;
}

/* OpenLayers control styling */
.ol-zoom {
  top: 10px;
  left: auto;
  right: 10px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
  padding: 2px;
}

.ol-zoom-in, 
.ol-zoom-out {
  margin: 1px;
  font-weight: bold;
  color: #1E1E1E;
}

.ol-zoom button:hover {
  background-color: #EEF73F;
}
</style>