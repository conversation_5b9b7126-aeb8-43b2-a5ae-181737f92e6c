<template>
    <v-app-bar class="app-header bg-grey-darken-4" fixed v-if="!isLoadingBar">
        <!-- Logo Section -->
        <template v-slot:prepend>
            <v-btn
                variant="text"
                icon="mdi-menu"
                color="#effa1a"
                @click="emit('toggleDrawer')"
            ></v-btn>
            <router-link class="text-decoration-none" to="/">
                <h2
                    class="text-body-sm-4 font-weight-regular color-primary text-left py-4 pl-4"
                >
                    DeepHelm
                </h2>
            </router-link>
        </template>

        <!-- Operation Picker Section - Takes 50% width -->
        <div class="operation-picker-container d-flex align-center">
            <OperationPickerMenu />
        </div>
        <v-spacer></v-spacer>
        <router-link to="/map" style="color: white">
            <v-icon class="color-secondary"> mdi-map </v-icon>
        </router-link>
        <v-menu
            v-model="menu"
            :close-on-content-click="false"
            location="bottom end"
        >
            <template v-slot:activator="{ props }">
                <v-skeleton-loader v-if="isLoading" type="avatar" width="40" />
                <div
                    class="d-flex align-center cursor-pointer"
                    v-else
                    v-bind="props"
                >
                    <v-avatar size="40">
                        <img
                            v-if="user && userData?.avatar"
                            :src="userData.avatar as string"
                            :alt="userData.firstName as string"
                        />
                        <v-icon class="color-primary" v-else
                            >mdi-account-circle</v-icon
                        >
                    </v-avatar>
                    <span class="ml-2 color-primary" v-if="user && userData">
                        {{ userData.firstName ?? '' }}
                        {{ userData.lastName ?? '' }}
                    </span>
                    <span class="ml-2 text-white" v-else>-Unknown-</span>
                    <v-icon class="ml-2 color-primary">mdi-chevron-down</v-icon>
                </div>
            </template>

            <v-card>
                <v-list>
                    <v-list-item>
                        <v-list-item-title>
                            {{ userData?.firstName }} {{ userData?.lastName }}
                        </v-list-item-title>
                        <v-list-item-subtitle class="text-subtitle-2 text-grey mr-4">
                            {{ userData?.email }}
                        </v-list-item-subtitle>
                        <template v-slot:append>
                            <v-btn
                                @click="signOutHandler"
                                size="small"
                                variant="outlined"
                                prepend-icon="mdi-logout"
                            >
                                Log Out
                            </v-btn>
                        </template>
                    </v-list-item>
                </v-list>

                <v-divider></v-divider>

                <v-list :lines="false" density="compact" nav>
                    <v-list-subheader class="text-grey"
                        >Organization</v-list-subheader
                    >
                    <v-list-item v-if="userOrganization" color="primary">
<!--                        <template v-slot:prepend>-->
<!--                            <v-icon icon="mdi-sitemap-outline"></v-icon>-->
<!--                        </template>-->
                        <v-list-item-title>
                            <span
                                :class="` fi fi-${
                                    userOrganization.isoCountryCode?.toLowerCase() ||
                                    ''
                                }`"
                            ></span>
                            {{ userOrganization.name }}
                        </v-list-item-title>
                        <template v-slot:append>
                            <v-btn
                                class=""
                                variant="outlined"
                                color="primary"
                                size="small"
                                @click="openSwitchOrgPopup"
                                prepend-icon="mdi-reload"
                            >
                                Change
                            </v-btn>
                        </template>
                    </v-list-item>
                </v-list>
            </v-card>
        </v-menu>
        <v-dialog
            v-model="isOrganizationPickerOpen"
            width="800"
            location="top"
            persistent
            absolute
        >
            <OrganizationPickerPopup
                :current-organization-id="
                    userOrganization?.id?.toString() || ''
                "
                :user-id="user?.id?.toString() || ''"
                @pickOrg="switchOrganization"
                @cancelPick="isOrganizationPickerOpen = false"
            ></OrganizationPickerPopup>
        </v-dialog>
    </v-app-bar>
    <div v-else class="w-100">
        <v-progress-linear></v-progress-linear>
    </div>
</template>

<script lang="ts" setup>
import { Organization } from '@/types/Organization';
import type { User } from '@/types/User';
import { useAuthStore } from '@/stores/auth';
import { useAdminUserStore } from '@/stores/admin/user.store';
import { useOperationStore } from '@/stores/operation.store';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import OperationPickerMenu from '@/components/operations/OperationPickerMenu.vue';

const router = useRouter();
const auth = useAuthStore();
const operationStore = useOperationStore();
const userStore = useAdminUserStore();
const menu = ref(false);
const userData = ref<User | null>(null);
const isLoading = ref(true);
const isOrganizationPickerOpen = ref(false);
const emit = defineEmits(['toggleDrawer']);
const isLoadingBar = ref(false);
const { user } = storeToRefs(useAuthStore());

const usersOrganizations = ref<Organization[] | []>([]);
const userOrganization = ref<Organization | null>(null);

onMounted(async () => {
    if (user.value) {
        userData.value = user.value;
        await getUserCurrentOrganization();
        console.log(
            'userOrganization',
            userOrganization.value,
            usersOrganizations.value,
        );
        isLoadingBar.value = false;
    }
});

// userData.value = user.value as User;

watch(
    () => user.value,
    (newUser) => {
        userData.value = newUser as User;
        isLoading.value = false;
    },
    { immediate: true },
);

const getUserCurrentOrganization = async () => {
    if (user.value) {
        let response = (
            await userStore.getUserOrganization(user.value?.id.toString())
        ).data;
        console.log('getUserCurrentOrganization', response);
        userOrganization.value = response.organization;
    } else {
        userOrganization.value = null;
    }
};

const signOutHandler = async () => {
    menu.value = false; // Close menu before signing out
    //reset persistant operation store
    // reset
    await operationStore.reset();
    await auth.signOut();
    await router.push('/signin');
};

const openSwitchOrgPopup = async () => {
    isOrganizationPickerOpen.value = true;
};

const switchOrganization = async (orgId: string) => {
    isLoadingBar.value = true;
    isOrganizationPickerOpen.value = false;

    if (!user.value) {
        console.error('User not found');
        return;
    }

    const userId = user.value.id.toString();
    const response = (await userStore.switchUserOrganization(userId, orgId))
        .data;
    console.log(response);
    await auth.setAuthTokens(
        response.tokens.accessToken,
        response.tokens.refreshToken,
    );
    await operationStore.resetCurrentOperation();
    isLoadingBar.value = false;
    await router.push('/home');
};
</script>

<style scoped>
.app-header {
    background-image: url('@/assets/pattern.webp') !important;
}

.operation-picker-container {
    width: 50%;
    min-width: 300px;
    max-width: 800px;
}

.cursor-pointer {
    cursor: pointer;
}
</style>
