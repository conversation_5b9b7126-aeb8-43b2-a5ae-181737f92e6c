<template>
    <div class="">
        <div
            id="actionBtns"
            class="position-absolute"
            v-if="editingAllowed && isEditing"
        >
            <v-btn
                color="primary"
                prepend-icon="mdi-plus"
                position="relative"
                style="z-index: 1000"
                class="ma-4 ml-16 primary"
                v-if="editMode"
                @click="getAllMapElements"
                >Done Drawing</v-btn
            >
            <v-btn
                color="secondary"
                prepend-icon="mdi-drawing-box"
                position="relative"
                style="z-index: 1000"
                class="ma-4 ml-16 primary"
                v-else
                @click="toggleEdit"
                >Draw Areas On The Map</v-btn
            >
            <v-btn
                color="info"
                prepend-icon="mdi-map-marker-radius"
                position="relative"
                style="z-index: 1000"
                class="ma-4 ml-16 primary"
                @click="findFirstLocation"
                >Locate On The Map</v-btn
            >
        </div>
    </div>
    <div
        id="viewDiv"
        :style="
            maxHeight
                ? `height:${maxHeight}px; max-height: ${maxHeight}px;`
                : ''
        "
    >
        <div id="measurements" class="esri-widget h-100"></div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, nextTick, ref } from 'vue';
import esriConfig from '@arcgis/core/config';
import Map from '@arcgis/core/Map';
import MapView from '@arcgis/core/views/MapView';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import Fullscreen from '@arcgis/core/widgets/Fullscreen';
import ScaleBar from '@arcgis/core/widgets/ScaleBar';
import Sketch from '@arcgis/core/widgets/Sketch';
import Graphic from '@arcgis/core/Graphic';
import Point from '@arcgis/core/geometry/Point';
import BasemapToggle from '@arcgis/core/widgets/BasemapToggle';
import CIMSymbol from '@arcgis/core/symbols/CIMSymbol';
import Polyline from '@arcgis/core/geometry/Polyline';
import Polygon from '@arcgis/core/geometry/Polygon';

type DrawElement = 'point' | 'circle' | 'square' | 'rectangle' | 'polyline';

const emit = defineEmits([
  'elements-selected',
  'polygon-clicked',
  'zoom-changed',
  'center-changed'
]);

const linePoints = ref<[number, number][]>([]);
const currentCenter = ref<number[]>([]);
const currentZoom = ref<number>(0);

const props = withDefaults(
    defineProps<{
        drawElements?: DrawElement[] | null;
        maxHeight?: number | null;
        existingCoordinates?:
            | {
                  type: 'Point' | 'Polygon' | 'LineString';
                  coordinates: number[] | number[][] | number[][][];
                  title?: string;
                  taiId?: number;
                  selected?: boolean;
              }[]
            | null;
        centerCoordinates?: number[];
        editingAllowed?: boolean;
        isEditing?: boolean;
        zoom?: number | string;
        elementsColor?: number[];
        singleMode?: boolean;
    }>(),
    {
        maxHeight: null,
        existingCoordinates: null,
        drawElements: () =>
            ['point', 'rectangle', 'circle', 'polyline'] as DrawElement[],
        centerCoordinates: undefined,
        editingAllowed: true,
        isEditing: false,
        zoom: 10,
        singleMode: false,
    },
);

let graphicsLayer: GraphicsLayer;
let view: MapView;
let map: Map;
const editMode = ref(false);

const toggleEdit = () => {
    editMode.value = !editMode.value;
};

watch(
    () => editMode.value,
    (newVal) => {
        editMode.value = newVal;
        initializeMap();
    },
);

const initializeMap = async () => {
    // Cleanup existing view and layer if they exist
    if (view) {
        view.destroy();
    }
    if (graphicsLayer) {
        map.remove(graphicsLayer);
    }
    linePoints.value = [];

    // Wait for the next DOM update
    await nextTick();

    // Reinitialize map components
    map = new Map({
        basemap: 'gray',
    });


    view = new MapView({
        container: 'viewDiv',
        map: map,
        zoom: Number(props.zoom),
        center: props.centerCoordinates ?? [-118.805, 34.027],
        popupEnabled: true,
    });

    view.watch('center', (center) => {
        currentCenter.value = webMercatorToGeographic(
            center.longitude,
            center.latitude,
        );
      emit('center-changed', {
        coordinates: currentCenter.value,
        raw: center
      });
    });

    view.watch('zoom', (newZoom) => {
        currentZoom.value = newZoom;
      emit('zoom-changed', {
        zoom: newZoom
      });
    });

    const basemapToggle = new BasemapToggle({
        view: view,
    });
    view.ui.add(basemapToggle, 'top-right');

    const fullscreen = new Fullscreen({
        view: view,
    });
    view.ui.add(fullscreen, 'top-right');

    graphicsLayer = new GraphicsLayer();
    map.add(graphicsLayer);

    const scalebar = new ScaleBar({
        view: view,
        unit: 'metric',
    });

    view.ui.add(scalebar, 'bottom-right');

    if (editMode.value === true) {
        const sketch = new Sketch({
            layer: graphicsLayer,
            view: view,
            availableCreateTools: props.drawElements ?? [
                'point',
                'circle',
                'polyline',
            ],
            creationMode: 'update',
            // @ts-ignore
            updateOnGraphicClick: true,
            visibleElements: {
                createTools: {
                    point: true,
                    circle: true,
                },
                selectionTools: {
                    'lasso-selection': false,
                    'rectangle-selection': false,
                },
                settingsMenu: true,
                undoRedoMenu: false,
            },
        });

        // Add event listener for create event
        sketch.on('create', (event) => {
            if (props.singleMode && event.state === 'complete') {
                // Store the newly created graphic
                const newGraphic = event.graphic;
                // Clear all existing graphics
                graphicsLayer.removeAll();
                // Add the new graphic back to the layer
                graphicsLayer.add(newGraphic);
            }
        });

        view.ui.add(sketch, 'bottom-left');

        const measurements = document.getElementById('measurements');
        // @ts-ignore
        view.ui.add(measurements, 'manual');
    }

    if (props.existingCoordinates) {
        addItems(props.existingCoordinates);
    }

    if (!props.isEditing) {
        view.on('pointer-move', (event) => {
            event.stopPropagation();

            view.hitTest(event).then((response) => {
                if (response.results.length > 0) {
                    // @ts-ignore
                    const graphic = response.results[0].graphic;

                    if (graphic.popupTemplate) {
                        view.openPopup({
                            features: [graphic],
                            // @ts-ignore
                            location: event.mapPoint,
                        });
                    }
                } else {
                    view.popup.close();
                }
            });
        });
    }

    view.on('click', (event) => {
        view.hitTest(event).then((response) => {
            if (response.results.length > 0) {
                // @ts-ignore
                const graphic = response.results[0].graphic;

                if (graphic.geometry.type === 'polygon') {
                    emit('polygon-clicked', {
                        geometry: graphic.geometry.toJSON(),
                        attributes: graphic.attributes,
                        taiId: graphic.attributes.taiId,
                    });
                }
            }
        });
    });
};

const getAllGraphics = () => {
    return graphicsLayer.graphics.toArray();
};

const genSymbol = ({
    name,
    approved,
    taiId,
    selected,
}: {
    name: string;
    approved: boolean;
    taiId?: number;
    selected?: boolean;
}) =>
    new CIMSymbol({
        data: {
            type: 'CIMSymbolReference',
            symbol: {
                type: 'CIMPolygonSymbol',
                symbolLayers: [
                    {
                        type: 'CIMSolidFill',
                        enable: true,
                        color: selected ? [51, 158, 255, 150] : [0, 0, 0, 40],
                    },
                    {
                        type: 'CIMSolidStroke',
                        enable: true,
                        color: props.elementsColor ?? [0, 0, 0, 255],
                        width: 2,
                        ...(approved
                            ? {}
                            : {
                                  effects: [
                                      {
                                          type: 'CIMGeometricEffectDashes',
                                          dashTemplate: [5, 5],
                                          lineDashEnding: 'FullGap',
                                          offsetAlongLine: 0,
                                      },
                                  ],
                              }),
                    },
                    {
                        type: 'CIMVectorMarker',
                        enable: true,
                        size: 10,
                        frame: {
                            xmin: 0,
                            ymin: 0,
                            xmax: 0,
                            ymax: 0,
                        },
                        markerGraphics: [
                            {
                                type: 'CIMMarkerGraphic',
                                geometry: { x: 0, y: 0 },
                                symbol: {
                                    type: 'CIMTextSymbol',
                                    fontFamilyName: 'Arial',
                                    height: 10,
                                    verticalAlignment: 'Center',
                                    horizontalAlignment: 'Center',
                                    symbol: {
                                        type: 'CIMPolygonSymbol',
                                        symbolLayers: [
                                            {
                                                type: 'CIMSolidStroke',
                                                enable: true,
                                                color: [255, 255, 255, 255],
                                                width: 1,
                                            },
                                            {
                                                type: 'CIMSolidFill',
                                                enable: true,
                                                color: [0, 0, 0, 255],
                                            },
                                        ],
                                    },
                                },
                                textString: name,
                            },
                        ],
                    },
                ],
            },
        },
    });

const addItems = (
    items:
        | {
              type: string;
              coordinates: number[] | number[][] | number[][][];
              title?: string;
          }[]
        | null,
) => {
    if (!items) return [];
    items.forEach(
        (item: {
            type: string;
            coordinates: number[] | number[][] | number[][][];
            title?: string;
            taiId?: number;
            selected?: boolean;
        }) => {
            if (item.type && item.type === 'Point') {
                const point = new Point({
                    longitude: (item.coordinates as number[])[0],
                    latitude: (item.coordinates as number[])[1],
                });

                const pointGraphic = new Graphic({
                    geometry: point,
                    symbol: genSymbol({
                        name: item.title ?? '',
                        approved: true,
                        selected: item.selected,
                        taiId: item.taiId,
                    }),
                });

                graphicsLayer.add(pointGraphic);
            }

            if (item.type && item.type === 'Polygon') {
                const initialPolygonGraphic = new Graphic({
                    geometry: new Polygon({
                        rings: item.coordinates as number[][][],
                    }),
                    symbol: genSymbol({
                        name: item.title ?? '',
                        approved: true,
                        selected: item.selected,
                        taiId: item.taiId,
                    }),
                    attributes: {
                        Name: 'Graphic',
                        Description: 'I am a polygon',
                        isInitial: true,
                        taiId: item.taiId,
                    },
                    popupTemplate: {
                        title: 'This is a polygon',
                        content: 'Description here',
                    },
                });
                graphicsLayer.add(initialPolygonGraphic);
            }

            if (item.type === 'LineString') {
                const polyline = new Polyline({
                    paths: [item.coordinates as number[][]],
                });
                const polylineGraphic = new Graphic({
                    geometry: polyline,
                    symbol: genSymbol({
                        name: item.title ?? '',
                        approved: true,
                    }),
                });
                graphicsLayer.add(polylineGraphic);
            }
        },
    );
};

const webMercatorToGeographic = (x: number, y: number) => {
    // Constants for conversion
    const RADIUS = 6378137.0; // Earth's radius in meters
    // const HALF_PI = Math.PI / 2;

    // Convert x coordinate
    const longitude = (x / RADIUS) * (180 / Math.PI);

    // Convert y coordinate
    const latitude = Math.PI / 2 - 2 * Math.atan(Math.exp(-y / RADIUS));
    const latitudeDegrees = latitude * (180 / Math.PI);

    return [longitude, latitudeDegrees];
};

const findFirstLocation = async () => {
    const graphics = getAllGraphics();
    const firstGraphic = graphics[0];
    if (!firstGraphic) return;

    const geometry = firstGraphic.geometry.toJSON();
    // const type = firstGraphic.geometry.type;

    //center map on first graphic
    view.goTo({
        target: firstGraphic,
        zoom: String(props.zoom),
    });

    //set center coordinates
    currentCenter.value = geometry.coordinates;
};

const getAllMapElements = () => {
    const graphics = getAllGraphics();

    const elements = graphics.map((graphic) => {
        const geometry = graphic.geometry.toJSON();
        const type = graphic.geometry.type;

        const element = {
            locationCoordinates: {
                type: type,
                coordinates: [] as number[],
                nativeCoordinates: [],
            },
        };

        if (type === 'point') {
            element.locationCoordinates.coordinates = [geometry.x, geometry.y];
        } else if (type === 'polygon') {
            const points = geometry.rings[0];
            element.locationCoordinates.coordinates = points.map(
                (point: number[]) =>
                    webMercatorToGeographic(point[0], point[1]),
            );
        } else if (type === 'polyline') {
            const points = geometry.paths[0];
            element.locationCoordinates.coordinates = points.map(
                (point: number[]) =>
                    webMercatorToGeographic(point[0], point[1]),
            );
            element.locationCoordinates.type = 'polyline';
        }
        editMode.value = false;

        return element;
    });
    const emitData = {
        elements: elements,
        centerCoordinates: currentCenter.value,
        zoom: currentZoom.value,
    };
    emit('elements-selected', emitData);
    return elements;
};

// beforeUnmount(() => {
//     editMode.value = props.isEditing;
// });

onMounted(() => {
    esriConfig.apiKey = import.meta.env.VITE_ARCGIS_API_KEY;
    editMode.value = props.isEditing;
    initializeMap();
});

// onBeforeUnmount(() => {
//     view.destroy();
// });
</script>

<style scoped lang="scss">
@import 'https://js.arcgis.com/4.30/@arcgis/core/assets/esri/themes/light/main.css';

#viewDiv,
html,
body,
arcgis-map {
    // min-height: 600px;
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
}
</style>
