import {User} from "@/types/User";
import {Asset} from "@/types/Asset";
import {IPagination, Priority, IMiscCoordinates, IPoint} from "@/types/Global.type";
import {Operation} from "@/types/Operation";


export enum ISRStatus {
  CREATED = 'created',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
}


export interface IGlobalIsrCollectionData {
  globalIsrs: IGlobalIsr[];
  pagination: IPagination;
}

export interface IGlobalIsrSingleData {
  globalIsr: IGlobalIsr;
}


export interface IGlobalIsr {
  id: string | number;
  designation: string;
  label: string | null;
  type: "line" | "point" | "polygon";
  coordinates: {
    type: "GeometryCollection";
    geometries: Array<IMiscCoordinates>;
  } | null;
  zoom: number | null;
  centerCoordinates: IPoint | null; // Keeping as any since model uses any
  status: ISRStatus; // You'll need to import this enum
  priority: Priority; // You'll need to import this enum
  commenceAt: Date | null;
  concludeAt: Date | null;
  ltiovDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
  assetId: number;
  operationId: number;
  createdByUserId: number;
  // Relationships
  asset: Asset | null;
  operation: Operation | null;
  createdByUser: User | null;
}
