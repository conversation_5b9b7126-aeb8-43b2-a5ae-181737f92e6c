import Graphic from '@arcgis/core/Graphic';
import { ArcGISType } from '@/composables/useGeometryConverter';
import { ICoordinates } from '@/types/Global.type';

export type DrawElement = 'point' | 'circle' | 'square' | 'rectangle' | 'polyline';

export interface Coordinates {
  type: 'Point' | 'Polygon' | 'LineString';
  coordinates: number[] | number[][] | number[][][];
  title?: string;
  taiId?: number;
  selected?: boolean;
}

export interface MapProps {
  drawElements?: DrawElement[] | null;
  maxHeight?: number | null;
  existingCoordinates?: Coordinates[] | null;
  centerCoordinates?: number[];
  zoom?: number | string;
  elementsColor?: number[];
  singleMode?: boolean;
}

export interface CustomGraphic extends Graphic {
  uid: string;
}


export interface GraphicElement {
  locationCoordinates: {
    type: string;
    coordinates: number[];
    nativeCoordinates: any[];
  };
}

export interface EmitData {
  elements: GraphicElement[];
  centerCoordinates: number[];
  zoom: number;
}

export interface DrawingCompleteEvent {
  type: string;
  coordinates: number[] | number[][];
  center: number[];
  zoom: number;
}

export interface SymbolItem {
  id: number | null;
  uid?: string;
  title?: string;
  name?: string;
  designation?: string;
  description?: string;
  itemType?: 'aoi' | 'isr_track' | 'collection';
  type: ArcGISType,
  coordinates: number[] | number[][] | number[][][] | ICoordinates[] | ICoordinates[][] | ICoordinates;
  selected?: boolean;
  elementsColor?: number[];
  elementsBorderColor?: number[];
  elementsBorderThickness?: number;
  elementsBorderType?: string;
  isApproved?: boolean;
  isTargetable?: boolean;
  isUserMade?: boolean;
  marker?: string | null | undefined;
  markerCount?: number;

}

export interface Symbol {
  id: number;
  designation: string;
  name: string;
  description: string;
  selected?: boolean;
  elementsColor?: number[];
}
