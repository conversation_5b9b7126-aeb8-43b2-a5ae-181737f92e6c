import { IPagination } from '@/types/Global.type';
import {User} from "@/types/User";
import {IPir} from "@/types/Pir";
import {Mission} from "@/types/Mission";

export interface IIr {
  id: number | string,
  irNumber: number | string,
  designation: string,
  originator: string,
  informationRequirement: string,
  ltiovDate: Date,
  priority: string,
  pirId: number | string,
  createdByUserId: number | string | null,
	isAnswered?: boolean,
  createdAt: Date,
  updatedAt: Date,
  // Relationship
  createdByUser?: User,
  pir?: IPir,
  missions?: Mission[]
    status?: string,
}

export interface IPirCollectionData {
  informationRequirements: IIr[]
  pagination: IPagination;
}

export interface IPirSingleData {
  informationRequirement: IIr
}

export interface IPirFilters {
  [key: string]: string | number | undefined; // Allow additional parameters
}
