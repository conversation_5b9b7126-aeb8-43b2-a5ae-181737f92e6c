import {IPagination, Priority} from '@/types/Global.type';
import {IIr} from "@/types/IR.type";

export interface IPir {
    id?: string;
    pirNumber?: string;
    designation?: string;
    question: string;
	priority: Priority;
	originator: string;
	description?: string;
	isActive?: boolean;
	operationId?: string;
	createdAt?: string;
	updatedAt?: string;
	operation?: object;
    informationRequirements?: IIr[];
}

export interface IPirCollectionData {
    pirs: IPir[];
    pagination: IPagination;
}

export interface IPirSingleData {
    pir: IPir;
}

export interface IPirFilters {
    [key: string]: string | number | undefined; // Allow additional parameters
}
