import { Operation } from '@/types/Operation';
import { User } from '@/types/User';
import {IPagination, IMapElement} from '@/types/Global.type';

export interface IAoiCollectionData {
	aois: Aoi[];
	pagination: IPagination;
}

export interface IAoiSingleData {
	aoi: Aoi;
}

export interface Aoi {
	id?: string | number,
	designation?: string,
	name: string,
	description: string,
  isTargetable: boolean,
	isApproved: boolean,
	operationId: string | number | null,
	requestedByUserId?: string | number,
	approvedByUserId?: string | number,
	createdAt?: string,
	updatedAt?: string,
	operation?: Operation,
	requestedByUser?: User,
	approvedByUser?: User,
  mapElement?: IMapElement | null,
}

export interface CreateAoiPayload {
	name: string;
  operationId: string | number | null,
  description: string;
  // designation: string;
  zoom: number,
  // location?: IPoint | null;
	// area?: IArea | null;
  mapElement?: IMapElement | null;
}

export interface IAoiFilters {
	[key: string]: string | number | undefined; // Allow additional parameters
}
