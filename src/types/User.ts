import type { Role } from './Role';
import type { Operation } from './Operation';
export interface User {
    accountType: string;
    avatarPath: string | null;
    createdAt: string;
    deletedAt: string | null;
    email: string;
    firstName: string;
    id: number | string;
    isActive: boolean;
    isOnline: boolean;
    isVerified: boolean;
    lastName: string;
    positionId: number | string | null;
    refreshToken: string;
    updatedAt: string;
    avatar: string | null;
    roles?: Role[];
    operations?: Operation[];
}

export interface OpUser {
    accountType: string;
    avatarPath: string | null;
    createdAt: string;
    email: string;
    firstName: string;
    id: number;
    isActive: boolean;
    isOnline: boolean;
    isVerified: boolean;
    lastName: string;
    updatedAt: string;
}
