import { Platform } from './Platform';
import { Operation } from './Operation';
import {IPagination} from "@/types/Global.type";
import {IIsrTrack} from "@/types/IsrTrack.type";
import {Mission} from "@/types/Mission";


//
export interface IAssetCollectionData {
	data: {
		assets: Asset[];
		pagination: IPagination;
	}
}

export interface IAssetSingleData {

	data: {
		asset: Asset;
	}
}

export interface Asset {
	id: number,
	name: string,
	title: string,
	designation: string,
	platformId: string,
	operationId: number,
	callSign?: string,
    color?: string,
	createdAt: Date;
	updatedAt: Date;
	assetDetails: string,
	status: AssetStatus,
	platform?: Platform
	operation?: Operation
	isrTracks?: IIsrTrack[],
    missions?: Mission[],
    engagedMissions?: Mission[],
    engagedIsrTracks?: IIsrTrack[],
}

export enum AssetStatus {
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	ENGAGED = 'engaged',
	REQUESTED = 'requested',
	PENDING_APPROVAL = 'pending_approval',
	WITHDRAWN = 'withdrawn',
	CANCELLED = 'cancelled',
	REJECTED = 'rejected'
}
