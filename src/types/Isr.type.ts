import {User} from "@/types/User";
import {Asset} from "@/types/Asset";
import {Mission} from "@/types/Mission";
import {IPagination, Priority, IMiscCoordinates, IPoint, ISRStatus} from "@/types/Global.type";



export interface IIsrCollectionData {
  isrs: Isr[];
  pagination: IPagination;
}

export interface IIsrSingleData {
  isr: Isr;
}


export interface Isr {
  id: string | number;
  designation: string;
  label: string | null;
  type: "line" | "point" | "polygon";
  isEditable?: boolean,
  isGlobal?: boolean,
  coordinates: {
    type: "GeometryCollection";
    geometries: Array<IMiscCoordinates>;
  } | null;
  zoom: number | null;
  centerCoordinates: IPoint | null; // Keeping as any since model uses any
  status: ISRStatus; // You'll need to import this enum
  priority: Priority; // You'll need to import this enum
  commenceAt: Date | null;
  concludeAt: Date | null;
  ltiovDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
  assetId: number;
  missionId: number;
  createdByUserId: number;
  // Relationships
  asset: Asset | null;
  mission: Mission | null;
  createdByUser: User | null;
}
