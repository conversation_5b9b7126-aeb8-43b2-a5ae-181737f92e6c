import { IPagination } from '@/types/Global.type';

import {Mission} from "@/types/Mission";
import {Asset} from "@/types/Asset";
import {IIsrTrack} from "@/types/IsrTrack.type";

export interface IEngagedMissionAsset {
    assetId: number;
    missionId: number;
    isrTrackId: number;
    id?: string | number;
    startAt?: Date | string | null;
    endAt?: Date | string | null;
    asset?: Asset
    mission?: Mission
    isrTrack?: IIsrTrack
}

export interface IEngagedMissionAssetCollectionData {
    engagedMissionAssets: IEngagedMissionAsset[]
    pagination: IPagination;
}

export interface IEngagedMissionAssetSingleData {
    engagedMissionAsset: IEngagedMissionAsset
}

export interface IEngagedMissionAssetFilters {
    [key: string]: string | number | undefined; // Allow additional parameters
}
