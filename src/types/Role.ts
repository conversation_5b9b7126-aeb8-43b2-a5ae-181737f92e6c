export interface Role {
    id?: string;
    roleName: string;
    roleDescription: string;
    unitLevel: string;
    staffDesignation: string;
    functionalArea: string;
    rank: string;
    isCommandRole: boolean;
    isStaffRole: boolean;
    typicalUnitSize: string;
    roleType?: string;
    createdAt?: string;
    updatedAt?: string;
    organizationId?: null | undefined;
    isSystemRole?: boolean;
    subordinateRoles?: Role[];
    superiorRoles?: Role[];
}
