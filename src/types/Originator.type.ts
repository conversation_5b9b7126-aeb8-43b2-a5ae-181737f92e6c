import { IPagination } from '@/types/Global.type';


export interface IOriginatorCollectionData {
	originators: IOriginator[];
	pagination: IPagination;
}

export interface IOriginatorSingleData {
	originator: IOriginator;
}

export interface IOriginator {
	id: number | string,
	title: string,
	designation?: string,
	description: string,
	organizationId?: string | number
	contactDetails: string | null,
	createdAt?: string
	updatedAt?: string
}

export interface IOriginatorFilters {
	[key: string]: string | number | undefined; // Allow additional parameters
}
