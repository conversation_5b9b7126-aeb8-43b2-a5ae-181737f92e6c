import { User } from '@/types/User';
import {IPagination, Priority, IMapElement, ISRStatus} from '@/types/Global.type';
import {Asset} from "@/types/Asset";
import {Mission} from "@/types/Mission";

export interface IIsrTrack {
    id: string | number;
    createdByUserId?: number;
    createdByUser?: User;
    designation?: string;
    label?: string | null;
    operationId?: number;
    status?: ISRStatus;
    priority?: Priority;
    commenceAt?: Date | null;
    concludeAt: Date | null;
    ltiovDate?: Date | null;
    mapElementId?: number | null | undefined;
    mapElement?: IMapElement | null,
    assets?: Asset[] | null | undefined;
    engagedMissions?: Mission[],
    engagedAssets: Asset[],
}

export interface IIsrTrackCollectionData {
    isrTracks: IIsrTrack[];
    pagination: IPagination;
}

export interface IIsrTrackSingleData {
    isrTrack: IIsrTrack;
}



