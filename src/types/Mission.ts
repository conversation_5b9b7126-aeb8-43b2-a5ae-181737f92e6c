import { Aoi } from '@/types/Aoi.type';
import { Operation } from '@/types/Operation';
import { Asset } from '@/types/Asset';
import { User } from '@/types/User';
import { IPagination } from '@/types/Global.type';
import { Isr } from '@/types/Isr.type';
import {IIr} from "@/types/IR.type";
import {IIsrTrack} from "@/types/IsrTrack.type";
import {IEngagedMissionAsset} from "@/types/EngagedMissionAsset.type";

//
export interface IMissionCollectionData {
    missions: Mission[];
    pagination: IPagination;
}

export interface IMissionSingleData {
    mission: Mission;
}

export enum MissionStatus {
	PENDING_APPROVAL = 'pending_approval',
	PLANNED = 'planned',
	ACTIVE = 'active',
	INACTIVE = 'inactive',
	COMPLETED = 'completed',
	FAILED = 'failed',
	ON_HOLD = 'on_hold',
	CANCELLED = 'cancelled',
	REJECTED = 'rejected',
	WITHDRAWN = 'withdrawn',
	DELAYED = 'delayed',
	SUSPENDED = 'suspended',
}

export interface Mission {
    id: string | number | null;
    name: string;
    description: string;
    designation: string;
    type: string;
    classification: string;
    priority: string;
    reportTypeConfiguration: {
        reportTypes: string[];
		submittedReport: string | null;
    };
    startAt: Date | string;
    endAt: Date | string;
    status: string;
    createdAt: string;
    updatedAt: string;
    operationId: string | number | null;
    indicatorsDescription: string;
    warningsDescription: string;
    requestedByUserId: string | number | null;
    approvedByUserId: string | number | null;
    requestedByUser: User | null;
    approvedByUser: User | null;
    informationRequirements: IIr[];
    missions?: Mission[]; // Optional array of sub-missions
    isrs?: Isr[]; // Optional array of ISRs
    tais?: Aoi[]; // Optional array of Areas of Interest
    assets?: Asset[]; // Optional array of Assets
    operation?: Operation | null | undefined; // Optional Operation
    engagedAssets?: Asset[];
    engagedIsrTracks?: IIsrTrack[];
    engagedMissionAssets?: IEngagedMissionAsset[];
}
