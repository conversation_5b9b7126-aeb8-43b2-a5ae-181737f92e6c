export interface Platform {
    id: number;
    name: string;
    aliases: string[] | null;
    designation: string | null;
    quantity: number | null;
    countryIsoCode: string;
    description: string | null;
    hasCollectionCapability?: boolean;
    cost: number | null;
    costCurrency: string | null;
    type: PlatformType;
    combatRadius: number | null;
    footprintArea: number | null;
    configuration: object | null;
    createdAt: Date;
    updatedAt: Date;
}

export interface IPlatformFilters {
	searchTerm?: string;
	countryIsoCode?: string;
	platformType?: string;
}

export interface IPlatformSingleData {
	platform: Platform;
}

export interface IPlatformCollectionData {
	platforms: Platform[];
	total: number;
}

export type PlatformType = 'space' | 'air' | 'land' | 'sea' | 'hybrid' | 'other';

export const PLATFORM_TYPES: PlatformType[] = [
	'space',
	'air',
	'land',
	'sea',
	'hybrid',
	'other'
];
