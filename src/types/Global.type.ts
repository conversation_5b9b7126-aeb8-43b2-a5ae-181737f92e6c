


export interface MapElementData {
  elements: Array<{
    locationCoordinates: {
      coordinates: ICoordinates[];
    };
  }>;
  centerCoordinates: [number, number];
  zoom: number;
}

export type DataTableHeader = {
	key: string
	title: string
	align?: 'start' | 'end' | 'center'
	sortable?: boolean
	width?: string | number
	fixed?: boolean
}

export interface IApiError {
	success: false;
	error: string;
	data: null;
	messages: IApiMessage[];
}

export interface IApiMessage {
	type: 'error' | 'success' | 'warning' | 'info';
	message: string;
}

export interface IApiResponse<T> {
	success: boolean | string;
	data: T | null;
	messages: IMessage[];
	error?: string;
}

export interface IPagination {
	page: number;
	perPage: number;
	pages: number;
	sortBy: string;
	orderBy: string;
	total: number;
}


export interface IMessage {
	type: 'error' | 'success' | 'warning' | 'info' | unknown,
  //string can only be 'error' | 'success' | 'warning' | 'info'
	message: string | unknown;
}

export enum ISRStatus {
  CREATED = 'created',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
}

export interface ISearchPayload {
	params: {
		searchTerm?: string,
		fields?: string[],
	}
}

export interface IGetParams {
	params: {
		page?: number;
		perPage?: number;
		sortBy?: string;
		orderBy?: string;
		load?: string;
		[key: string]: string | number | undefined; // Allow additional parameters
	}
}

export enum RFIStatus {
	CREATED = 'created',
	IN_PROGRESS = 'in_progress',
	REJECTED = 'rejected',
	RESOLVED = 'resolved',
	UNRESOLVED = 'unresolved',
}

export enum Priority {
	HIGHEST = "highest",
	HIGH = "high",
	MEDIUM = "medium",
	LOW = "low",
	NONE = "none"
}

export type ISupportGeometry = ILineString | IPoint | IArea;

export type ICoordinates = [number, number];

// Location (Point) interface
export interface ILocation {
	type: 'Point';
	coordinates: ICoordinates;  // [longitude, latitude]
}

// Area (Polygon) interface
export interface IArea {
	type: 'Polygon';
	coordinates: ICoordinates[][]; // Array of linear rings, each ring is array of positions
}

// Point interface
export interface IPoint {
	type: 'Point';
	coordinates: ICoordinates;  // [longitude, latitude]
}

export interface ILineString {
	type: 'LineString';
	coordinates: ICoordinates[];  // [[lng, lat], [lng, lat], ...]
}

export type IMiscCoordinateTypes = 'Point' | 'Polygon' | 'LineString';


export interface IMiscCoordinates {
  type: IMiscCoordinateTypes;
  coordinates: any;
}

export interface IPoint {
  type: 'Point';
  coordinates: ICoordinates;
}

export interface DrawCompleteElement {
  locationCoordinates: {
    type: string;
    coordinates: [number, number][] | [number, number];
  };
}

export interface DrawCompleteData {
  elements: DrawCompleteElement[];
  centerCoordinates: [number, number];
  zoom: number;
}

export type RGBAColor = [number, number, number, number]
export type GeometryType = 'Point' | 'Polygon' | 'LineString';

export enum BorderType {
  SOLID = 'solid',
  DOTTED = 'dotted',
  DASHED = 'dashed',
  NONE = 'none'
}

export interface IMapElement {
  id: string | number | null;
  element: ISupportGeometry;
  elementType: GeometryType;
  elementColor?: RGBAColor;
  borderType?: BorderType;
  borderThickness?: number;
  borderColor?: RGBAColor;
}

export interface TimelineItem {
	id: string | number;
	label: string;
	startDateTime:  string;
	endDateTime:  string;
	ltiovDate?:  string;
    assetName?: string;
    callSign: string;
    isrTrackName?: string;
    isrDesignation?: string;
    isrTrackOtherAssets?: string[];
	itemBackgroundColor?: string;
	itemLabel?: string;
	priority: string;
	segmentClasses?: string;
	rowClasses?: string;
}
