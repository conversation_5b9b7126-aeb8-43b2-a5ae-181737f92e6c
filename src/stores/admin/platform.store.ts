// src/stores/admin/platform.store.ts
import { defineStore } from 'pinia';
import api from '@/axios/';
import { handleApiError } from '@/composables/apiHandler';
import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import type { Platform, IPlatformFilters, IPlatformSingleData, IPlatformCollectionData } from '@/types/Platform';

export type IPlatformCollectionResponse = IApiResponse<IPlatformCollectionData>;
export type IPlatformSingleResponse = IApiResponse<IPlatformSingleData>;

export const useAdminPlatformStore = defineStore('admin-platform', {
	actions: {
		async fetchPlatforms(
			pagination: IPagination,
			filters: IPlatformFilters = {},
			load: string[] = []
		): Promise<IPlatformCollectionResponse> {
			try {
				const payload: IGetParams = {
					params: {
						page: pagination.page,
						perPage: pagination.perPage,
						sortBy: pagination.sortBy,
						orderBy: pagination.orderBy,
						load: load.join(','),
						...filters,
					},
				};
				return (await api.get('/platforms', payload))
					.data as IPlatformCollectionResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async getPlatformById(id: string, load: string[] = []): Promise<IPlatformSingleResponse> {
			try {
				const payload: IGetParams = {
					params: {
						load: load.join(','),
					},
				};
				const { data } = await api.get(`/platforms/${id}`, payload);
				return data as IPlatformSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async createPlatform(platform: Partial<Platform>): Promise<IPlatformSingleResponse> {
			try {
				return (await api.post('/platforms', platform))
					.data as IPlatformSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async updatePlatform(id: string, platform: Partial<Platform>): Promise<IPlatformSingleResponse> {
			try {
				return (await api.put(`/platforms/${id}`, platform))
					.data as IPlatformSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async deletePlatform(id: string) {
			try {
				const { data } = await api.delete(`/platforms/${id}`);
				return data;
			} catch (error) {
				return handleApiError(error);
			}
		},

		// Keep existing search function for backward compatibility
		async searchPlatforms(searchTerm?: string, countryIsoCode?: string | undefined, platformType?: string | undefined) {
			try {
				const payload: IPlatformFilters = {};
				if(searchTerm && searchTerm.length) payload.searchTerm = searchTerm;
				if(countryIsoCode && countryIsoCode.length) payload.countryIsoCode = countryIsoCode;
				if(platformType && platformType.length) payload.platformType = platformType;

				const { data } = await api.get('/platforms/search-platforms', {
					params: payload
				} as any);
				return data;
			} catch (error) {
				return handleApiError(error);
			}
		}
	}
});
