import { defineStore } from 'pinia';
import api from '@/axios';
import { useSnackbar } from '@/composables/useSnackbar';

export interface ITask {
    id: number;
    title: string;
    description?: string;
    shortDescription?: string | null;
    dueAt?: string | null;
    status: 'created' | 'pending' | 'in_progress' | 'completed' | 'cancelled';
    completedAt?: string | null;
    priority: 'highest' | 'high' | 'medium' | 'low';
    operationId: number;
    createdByUserId: number;
    createdAt?: string;
    updatedAt?: string;
    labels?: string[] | null;
    archivedAt?: string | null;
    members?: Array<{
        id: number;
        firstName: string;
        lastName: string;
    }>;
    createdByUser?: {
        id: number;
        firstName: string;
        lastName: string;
    };
}

export interface ITaskResponse {
    data: {
        task: ITask;
    };
}

export interface ITaskCollectionResponse {
    data: {
        tasks: ITask[];
        pagination: {
            page: number;
            perPage: number;
            total: number;
            pages: number;
        };
    };
}

export interface ITaskAssignMembersRequest {
    userIds: number[];
}

export const useTaskStore = defineStore('task', {
    state: () => ({}),

    actions: {
        async createTask(taskData: Partial<ITask>): Promise<ITaskResponse> {
            try {
                const response = await api.post('/tasks', taskData);
                return response.data;
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to create task',
                    color: 'error',
                });
                throw error;
            }
        },

        async updateTask(
            taskId: number,
            taskData: Partial<ITask>,
        ): Promise<ITaskResponse> {
            try {
                const response = await api.put(`/tasks/${taskId}`, taskData);
                return response.data;
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to update task',
                    color: 'error',
                });
                throw error;
            }
        },

        async fetchTaskById(taskId: number): Promise<ITaskResponse> {
            try {
                const response = await api.get(`/tasks/${taskId}?load=members`);
                return response.data;
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to fetch task',
                    color: 'error',
                });
                throw error;
            }
        },

        async fetchTasks(
            pagination: any,
            filters: any = {},
        ): Promise<ITaskCollectionResponse> {
            try {
                const response = await api.get('/tasks?load=members', {
                    params: {
                        ...pagination,
                        ...filters,
                    },
                });
                return response.data;
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to fetch tasks',
                    color: 'error',
                });
                throw error;
            }
        },

        async deleteTask(taskId: number): Promise<void> {
            try {
                await api.delete(`/tasks/${taskId}`);
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to delete task',
                    color: 'error',
                });
                throw error;
            }
        },

        async assignMembers(taskId: number, data: ITaskAssignMembersRequest) {
            try {
                const response = await api.post(
                    `/tasks/${taskId}/assign-members`,
                    data,
                );
                return response.data;
            } catch (error) {
                const { showSnackbar } = useSnackbar();
                showSnackbar({
                    text: 'Failed to assign members to task',
                    color: 'error',
                });
                throw error;
            }
        },
    },
});
