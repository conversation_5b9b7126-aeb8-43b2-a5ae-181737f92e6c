import { defineStore } from 'pinia';
import type { I<PERSON><PERSON>inator, IOriginatorFilters, IOriginatorSingleData, IOriginatorCollectionData } from '@/types/Originator.type';


import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific PIR responses
export type IOriginatorCollectionResponse = IApiResponse<IOriginatorCollectionData>;
export type IOriginatorSingleResponse = IApiResponse<IOriginatorSingleData>;


export const useAdminOriginatorStore = defineStore('admin-originator', {
	actions: {

		async fetchOriginators(pagination: IPagination, filters:IOriginatorFilters = {}, load: string[] = []):Promise<IOriginatorCollectionResponse> {
			try {
				const payload:IGetParams = {
					params: {
						page: pagination.page,
						perPage: pagination.perPage,
						sortBy: pagination.sortBy,
						orderBy: pagination.orderBy,
						load: load.join(','),
						...filters
					}
				};
				return (await api.get('/originators',payload)).data as IOriginatorCollectionResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async fetchOriginatorById(originatorId: string):Promise<IOriginatorSingleResponse> {
			try {
				return (await api.get(`/originators/${originatorId}`)).data as IOriginatorSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async createOriginator(originator: IOriginator):Promise<IOriginatorSingleResponse> {
			try {
				return (await api.post('/originators', originator)).data as IOriginatorSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async updateOriginator(originatorId: string, originator: IOriginator):Promise<IOriginatorSingleResponse> {
			try {
				return (await api.put(`/originators/${originatorId}`, originator)).data as IOriginatorSingleResponse;
			} catch (error) {
				return handleApiError(error);
			}
		},

		async deleteOriginator(originatorId: string) {
			try {
				const {data} = await api.delete(`/originators/${originatorId}`);
				return data;
			} catch (error) {
				return handleApiError(error);
			}
		},

	}

});
