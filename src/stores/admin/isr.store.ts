import { defineStore } from 'pinia';
import type {IIsrSingleData, IIsrCollectionData, Isr} from '@/types/Isr.type';


import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific PIR responses
export type IIsrCollectionResponse = IApiResponse<IIsrCollectionData>;
export type IIsrSingleResponse = IApiResponse<IIsrSingleData>;


export const useAdminIsrStore = defineStore('admin-isr', {
  actions: {

    async fetchIsrs(pagination: IPagination, filters:any = {}, load: string[] = []):Promise<IIsrCollectionResponse> {
      try {
        const payload:IGetParams = {
          params: {
            page: pagination.page,
            perPage: pagination.perPage,
            sortBy: pagination.sortBy,
            orderBy: pagination.orderBy,
            load: load.join(','),
            ...filters
          }
        };
        return (await api.get('/isrs',payload)).data as IIsrCollectionResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async fetchIsrById(isrId: string):Promise<IIsrSingleResponse> {
      try {
        return (await api.get(`/isrs/${isrId}`)).data as IIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async createIsr(isr: any):Promise<IIsrSingleResponse> {
      try {
        return (await api.post('/isrs', isr)).data as IIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async updateIsr(isrId: string, isr: Isr):Promise<IIsrSingleResponse> {
      try {
        return (await api.put(`/isrs/${isrId}`, isr)).data as IIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async deleteIsr(isrId: string) {
      try {
        const {data} = await api.delete(`/isrs/${isrId}`);
        return data;
      } catch (error) {
        return handleApiError(error);
      }
    },

  }

});
