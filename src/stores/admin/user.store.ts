import { defineStore } from 'pinia';
import api from '@/axios/';

//TODO: add handleApiError
export const useAdminUserStore = defineStore('admin-user', {
	actions: {

		async searchUsers(searchTerm: string, fields?: string[]) {
			try {
				const { data } = await api.get('/users/search', {
					params: {
						searchTerm,
						fields: fields?.join(','),
					},
				});
				return data;
			} catch (error) {
				console.error(error);
				throw error;
			}
		},

		async getUserById(id: string | undefined, load: string[] = []) {
			try {
				const { data } = await api.get(`/users/${id}`, {
					params: {
						load: load.join(',')
					},
				});
				return data;
			} catch (error) {
				console.error(error);
				throw error;
			}
		},

		async getUsers(page: number = 1, perPage: number = 10, orderBy: string = 'id', sortBy: string = 'desc') {
			try {
				const { data } = await api.get('/users', {
					params: {
						page,
						perPage,
						orderBy,
						sortBy,
						load: 'roles',
					},
				});
				return data;
			} catch (error) {
				console.error(error);
				throw error;
			}
		},

		async deleteUser(id: string | undefined) {
			try {
				const { data } = await api.delete(`/users/${id}`);
				return data;
			} catch (error) {
				console.error(error);
				throw error;
			}
		},

        async getUserOrganization(id: string | undefined) {
            try {
                const { data } = await api.get(`/users/${id}/organization`);
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getUserOrganizations(id: string | undefined) {
            try {
                const { data } = await api.get(`/users/${id}/organizations`);
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async switchUserOrganization(id:string, organizationId:string){
            try {
                const { data } = await api.post(`/users/${id}/switch-organizations`, {newOrgId: organizationId});
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        }

	}

});
