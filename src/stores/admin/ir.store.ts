import {IPirFilters, IPirSingleData, IPirCollectionData } from '@/types/IR.type';
import { defineStore } from 'pinia';
import api from '@/axios';

import {
  IPagination,
  IGetParams,
  ISearchPayload,
  IApiResponse,
} from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';

export type IIRCollectionResponse = IApiResponse<IPirCollectionData>;
export type IIRSingleResponse = IApiResponse<IPirSingleData>;


export const useAdminIRStore = defineStore('admin-ir', {


    state: () => ({
       irs: [] as any[],
    }),
		//crurd
    actions: {
        async fetchIrs(
            pagination: IPagination,
            filters: IPirFilters = {},
            load: string[] = [],
        ): Promise<IIRCollectionResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/information-requirements', payload))
                    .data as IIRCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchIrById(irId: string): Promise<IIRSingleResponse> {
            try {
                return (await api.get(`/information-requirements/${irId}`))
                    .data as IIRSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createIr(ir: any): Promise<IIRSingleResponse> {
            try {
                return (await api.post('/information-requirements', ir))
                    .data as IIRSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updateIr(irId: string, ir: any): Promise<IIRSingleResponse> {
            try {
                return (await api.put(`/information-requirements/${irId}`, ir))
                    .data as IIRSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deleteIr(irId: string) {
            try {
                const { data } = await api.delete(`/information-requirements/${irId}`);
                return data.data;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async searchIrs(
            search: string,
            fields: string[] = ['question'],
            operationId: string | null = null,
        ): Promise<IIRCollectionResponse> {
            try {
                const payload: ISearchPayload = {
                    params: {
                        searchTerm: search,
                        fields,
                    },
                };
                return (await api.get('/information-requirements/search', payload))
                    .data as IIRCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },
    },

});
