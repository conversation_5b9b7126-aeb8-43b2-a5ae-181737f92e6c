import { defineStore } from 'pinia';
import type {
    Aoi,
    IAoiFilters,
    IAoiSingleData,
    IAoiCollectionData,
} from '@/types/Aoi.type';

import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific PIR responses
export type IAoiCollectionResponse = IApiResponse<IAoiCollectionData>;
export type IAoiSingleResponse = IApiResponse<IAoiSingleData>;

export const useAdminAoiStore = defineStore('admin-aoi', {
    actions: {
        async fetchAois(
            pagination: IPagination,
            filters: IAoiFilters = {},
            load: string[] = [],
        ): Promise<IAoiCollectionResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/aois', payload))
                    .data as IAoiCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchAoiById(aoiId: string, load: string[] = []): Promise<IAoiSingleResponse> {
            try {
               const payload: IGetParams = {
                    params: {
                        load: load.join(','),
                    },
                };
                return (await api.get(`/aois/${aoiId}`, payload))
                    .data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createAoi(aoi: any): Promise<IAoiSingleResponse> {
            try {
                return (await api.post('/aois', aoi))
                    .data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updateAoi(aoiId: string, aoi: Aoi): Promise<IAoiSingleResponse> {
            try {
                return (await api.put(`/aois/${aoiId}`, aoi))
                    .data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async turnIntoTai(aoiId: string): Promise<IAoiSingleResponse> {
            try {
                return (
                    await api.put(`/aois/${aoiId}`, {
                        isTargetable: true,
                    })
                ).data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async approveAoi(aoiId: string): Promise<IAoiSingleResponse> {
            try {
                return (
                    await api.put(`/aois/${aoiId}/toggle-approval`, {
                        isApproved: true,
                    })
                ).data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async toTai(aoiId: string): Promise<IAoiSingleResponse> {
            try {
                return (
                    await api.put(`/aois/${aoiId}/to-tai`, {
                        isTargetable: true,
                    })
                ).data as IAoiSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deleteAoi(aoiId: string) {
            try {
                const { data } = await api.delete(`/aois/${aoiId}`);
                return data;
            } catch (error) {
                return handleApiError(error);
            }
        },
    },
});
