import { defineStore } from 'pinia';
// import { User } from '@/types/User';
import api from '@/axios/';
import {
    IPir,
    IPirCollectionData,
    IPirFilters,
    IPirSingleData,
} from '@/types/Pir';
import {
    IPagination,
    IGetParams,
    ISearchPayload,
    IApiResponse,
} from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';

// Type aliases for specific PIR responses
export type IPirCollectionResponse = IApiResponse<IPirCollectionData>;
export type IPirSingleResponse = IApiResponse<IPirSingleData>;

// Combined type that can handle both collection and single responses
// export type IPirResponse = IPirCollectionResponse | IPirSingleResponse;

export const useAdminPirStore = defineStore('admin-pir', {
    state: () => ({
        pirs: [] as IPir[],
    }),

    actions: {
        async fetchPirs(
            pagination: IPagination,
            filters: IPirFilters = {},
            load: string[] = [],
        ): Promise<IPirCollectionResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/pirs', payload))
                    .data as IPirCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchPirById(pirId: string): Promise<IPirSingleResponse> {
            try {
                return (await api.get(`/pirs/${pirId}`))
                    .data as IPirSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createPir(pir: IPir): Promise<IPirSingleResponse> {
            try {
                return (await api.post('/pirs', pir))
                    .data as IPirSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updatePir(pirId: string, pir: IPir): Promise<IPirSingleResponse> {
            try {
                return (await api.put(`/pirs/${pirId}`, pir))
                    .data as IPirSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deletePir(pirId: string) {
            try {
                const { data } = await api.delete(`/pirs/${pirId}`);
                return data.data;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async searchPirs(
            search: string,
            fields: string[] = ['question'],
            operationId: string | null = null,
        ): Promise<IPirCollectionResponse> {
            try {
                const payload: ISearchPayload = {
                    params: {
                        searchTerm: search,
                        fields,
                    },
                };
                return (await api.get('/pirs/search', payload))
                    .data as IPirCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async addIrToPir(ir: any): Promise<IPirSingleResponse> {
            try {
                return (await api.post(`/information-requirements`, ir))
                    .data as IPirSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async convertRFIToIR(rfiId: string): Promise<IPirSingleResponse> {
            try {
                return (await api.post(`/check-on-route`)) // @todo: change to valid endpoint
                    .data as IPirSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },
    },
});
