import { defineStore } from 'pinia';
import type {
    IEngagedMissionAsset,
    IEngagedMissionAssetFilters,
    IEngagedMissionAssetSingleData,
    IEngagedMissionAssetCollectionData,
} from '@/types/EngagedMissionAsset.type';

import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific Engaged Mission Asset responses
export type IEngagedMissionAssetCollectionResponse = IApiResponse<IEngagedMissionAssetCollectionData>;
export type IEngagedMissionAssetSingleResponse = IApiResponse<IEngagedMissionAssetSingleData>;

export const useEngagedMissionAssetStore = defineStore('engaged-mission-asset', {
    actions: {
        async fetchEngagedMissionAssets(
            pagination: IPagination,
            filters: IEngagedMissionAssetFilters = {},
            load: string[] = [],
        ): Promise<IEngagedMissionAssetCollectionResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/engaged-mission-assets', payload))
                    .data as IEngagedMissionAssetCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchEngagedMissionAssetById(id: string, load: string[] = []): Promise<IEngagedMissionAssetSingleResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        load: load.join(','),
                    },
                };
                return (await api.get(`/engaged-mission-assets/${id}`, payload))
                    .data as IEngagedMissionAssetSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createEngagedMissionAsset(asset: IEngagedMissionAsset): Promise<IEngagedMissionAssetSingleResponse> {
            try {
                return (await api.post('/engaged-mission-assets', asset))
                    .data as IEngagedMissionAssetSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updateEngagedMissionAsset(id: string, asset: IEngagedMissionAsset): Promise<IEngagedMissionAssetSingleResponse> {
            try {
                return (await api.put(`/engaged-mission-assets/${id}`, asset))
                    .data as IEngagedMissionAssetSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deleteEngagedMissionAsset(id: string) {
            try {
                const { data } = await api.delete(`/engaged-mission-assets/${id}`);
                return data;
            } catch (error) {
                return handleApiError(error);
            }
        },
    },
});