import { defineStore } from 'pinia';
import api from '@/axios/';

import {IAssetCollectionData, IAssetSingleData} from "@/types/Asset";
import { handleApiError } from '@/composables/apiHandler';
import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';


export type IApiCollectionResponse = IApiResponse<IAssetCollectionData>;
export type IApiSingleResponse = IApiResponse<IAssetSingleData>;

export const useAdminAssetStore = defineStore('admin-asset', {
	actions: {

		// /assets/search
		async searchAssets(searchTerm?: string, countryIsoCode?: string | undefined, platformType?: string | undefined):Promise<IApiCollectionResponse> {
			try {
        return (await api.get('/assets/search', {
					params: {
						searchTerm: searchTerm,
						countryIsoCode: countryIsoCode,
						platformType: platformType,
					},
				})).data as IApiCollectionResponse;
			} catch (error) {
        return handleApiError(error);
			}
		},

		///assets/:id
		async getAssetById(id: string | undefined) : Promise<IApiSingleResponse> {
			try {
				return (await api.get(`/assets/${id}`)).data as IApiSingleResponse;
			} catch (error) {
        return handleApiError(error);
			}
		},

		// /assets/create POST
		async createAsset(platformId: string, operationId: string, callSign:string, status: string = 'pending_approval',assetDetails: any = {}): Promise<IApiSingleResponse> {
			try {
				const payload = {
					title: callSign,
          callSign: callSign,
					platformId: platformId,
					operationId: operationId,
					assetDetails: assetDetails,
					status: status,
				};
				return (await api.post('/assets', payload)).data as IApiSingleResponse;
			} catch (error) {
        return handleApiError(error);
			}
		},

    // /assets GET
    async fetchAssets(pagination: IPagination, filters:any = {}, load: string[] = []):Promise<IAssetCollectionData> {
      try {
        const payload:IGetParams = {
          params: {
            page: pagination.page,
            perPage: pagination.perPage,
            sortBy: pagination.sortBy,
            orderBy: pagination.orderBy,
            load: load.join(','),
            ...filters
          }
        };
        return (await api.get('/assets',payload)).data as IAssetCollectionData;
      } catch (error) {
        //@ts-ignore - this is fine
        return handleApiError(error);
      }
    },

		// /assets/:id PUT
		async updateAsset(id: string, payload: any): Promise<IApiSingleResponse> {
			try {
				return (await api.put(`/assets/${id}`, payload)).data as IApiSingleResponse;
			} catch (error) {
        return handleApiError(error);
			}
		},

		// /assets/:id DELETE
		async deleteAsset(id: string): Promise<IApiSingleResponse> {
			try {
				return (await api.delete(`/assets/${id}`)).data as IApiSingleResponse;
			} catch (error) {
        return handleApiError(error);
			}

		}
	}
});

