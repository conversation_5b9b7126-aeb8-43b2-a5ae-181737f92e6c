import { defineStore } from 'pinia';
import type {
  IGlobalIsrCollectionData,
  IGlobalIsrSingleData,
} from '@/types/GlobalIsr.type';


import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific PIR responses
export type IGlobalIsrCollectionResponse = IApiResponse<IGlobalIsrCollectionData>;
export type IGlobalIsrSingleResponse = IApiResponse<IGlobalIsrSingleData>;

const apiPath = '/global-isrs';

export const useAdminGlobalIsrStore = defineStore('admin-global-isr', {
  actions: {

    async fetchIsrs(pagination: IPagination, filters:any = {}, load: string[] = []):Promise<IGlobalIsrCollectionResponse> {
      try {
        const payload:IGetParams = {
          params: {
            page: pagination.page,
            perPage: pagination.perPage,
            sortBy: pagination.sortBy,
            orderBy: pagination.orderBy,
            load: load.join(','),
            ...filters
          }
        };
        return (await api.get(apiPath,payload)).data as IGlobalIsrCollectionResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async fetchIsrById(isrId: string):Promise<IGlobalIsrSingleResponse> {
      try {
        return (await api.get(`${apiPath}/${isrId}`)).data as IGlobalIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async createIsr(isr: any):Promise<IGlobalIsrSingleResponse> {
      try {
        return (await api.post(apiPath, isr)).data as IGlobalIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async updateIsr(isrId: string, isr: any):Promise<IGlobalIsrSingleResponse> {
      try {
        return (await api.put(`${apiPath}/${isrId}`, isr)).data as IGlobalIsrSingleResponse;
      } catch (error) {
        return handleApiError(error);
      }
    },

    async deleteIsr(isrId: string) {
      try {
        const {data} = await api.delete(`${apiPath}/${isrId}`);
        return data;
      } catch (error) {
        return handleApiError(error);
      }
    },

  }

});
