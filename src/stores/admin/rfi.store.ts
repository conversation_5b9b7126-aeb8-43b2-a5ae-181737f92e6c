import { defineStore } from 'pinia';
import type {
    IRFI,
    IRFIFilters,
    IRFICollectionData,
    IRFISingleData,
} from '@/types/RFI.type';

import { IPagination, IGetParams, IApiResponse } from '@/types/Global.type';
import { handleApiError } from '@/composables/apiHandler';
import api from '@/axios';

// Type aliases for specific PIR responses
export type IRFICollectionResponse = IApiResponse<IRFICollectionData>;
export type IRFISingleResponse = IApiResponse<IRFISingleData>;

export const useAdminRFIStore = defineStore('admin-rfi', {
    actions: {
        async fetchRFIs(
            pagination: IPagination,
            filters: IRFIFilters = {},
            load: string[] = [],
        ): Promise<IRFICollectionResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/rfis', payload))
                    .data as IRFICollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchRFIById(
            rfiId: string | number,
        ): Promise<IRFISingleResponse> {
            try {
                return (await api.get(`/rfis/${rfiId}`))
                    .data as IRFISingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createRFI(rfi: IRFI): Promise<IRFISingleResponse> {
            try {
                return (await api.post('/rfis', rfi))
                    .data as IRFISingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updateRFI(
            rfiId: string | number,
            rfi: IRFI,
        ): Promise<IRFISingleResponse> {
            try {
                return (await api.put(`/rfis/${rfiId}`, rfi))
                    .data as IRFISingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deleteRFI(rfiId: string) {
            try {
                const { data } = await api.delete(`/rfis/${rfiId}`);
                return data;
            } catch (error) {
                return handleApiError(error);
            }
        },
    },
});
