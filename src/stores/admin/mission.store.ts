import { defineStore } from 'pinia';
import api from '@/axios/';
import { IGetParams, IPagination } from '@/types/Global.type';


//TODO: add handleApiError
export const useMissionStore = defineStore('mission', {
    state: () => ({
        mission: null,
    }),
    actions: {
        async createMission(mission: any) {
            try {
                const { data } = await api.post('/missions', mission);
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getMissions(
            pagination: IPagination,
            filters: any = {},
            load: string[] = [],
        ) {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/missions', payload)).data;
            } catch (error) {
                throw error;
            }
        },

        async getMissionsLog(
            pagination: IPagination,
            filters: any = {},
            load: string[] = [],
        ) {
            try {
                const payload: IGetParams = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters,
                    },
                };
                return (await api.get('/missions/log', payload)).data;
            } catch (error) {
                throw error;
            }
        },
        async updateMission(id: number, mission: any) {
            try {
                const { data } = await api.put(
                    `/missions/${id}`,
                    mission,
                );
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        async getMissionById(id: string | number, load: string[] = []) {
            try {
                const { data } = await api.get(`/missions/${id}`, {
                    params: {
                        load: load.join(','),
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addTaisToMission(id: string | number, taisIds: number[]) {
            ///missions/:id/add-tais
            try {
                const { data } = await api.post(
                    `/missions/${id}/add-tais`,
                    {
                        taiIds: taisIds,
                    },
                );
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

      async addGlobalIsrToMission(missionId: string | number, globalIsrIds: string[]) {
          try {
              const { data } = await api.post(
                  `/missions/${missionId}/add-global-isrs`,
                  {
                      globalIsrIds,
                  },
              );
              return data;
          } catch (error) {
              console.error(error);
              throw error;
          }
      },

        async addIRToMission(missionId: string | number, irIds: number[]) {
            try {
                const { data } = await api.put(
                    `/missions/${missionId}/information-requirements`,
                    {
                        irIds,
                    },
                );
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
    },
});
