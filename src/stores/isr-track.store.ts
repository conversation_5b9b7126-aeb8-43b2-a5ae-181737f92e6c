import { defineStore } from 'pinia';
import { IApiResponse, IGetParams, IPagination } from '@/types/Global.type';
import { IIsrTrackCollectionData, IIsrTrackSingleData } from '@/types/IsrTrack.type';
import { handleApiError } from '@/composables/apiHandler';

import api from '@/axios';

export type IIsrTrackCollectionResponse = IApiResponse<IIsrTrackCollectionData>;
export type IIsrTrackSingleResponse = IApiResponse<IIsrTrackSingleData | null>;

export const useIsrTrackStore = defineStore('isr-track', {

    actions: {

        async fetchIsrTracks(pagination: IPagination, filters:any = {}, load: string[] = []):Promise<IIsrTrackCollectionResponse> {
            try {
                const payload:any = {
                    params: {
                        page: pagination.page,
                        perPage: pagination.perPage,
                        sortBy: pagination.sortBy,
                        orderBy: pagination.orderBy,
                        load: load.join(','),
                        ...filters
                    }
                };
                return (await api.get('/isr-tracks',payload)).data as IIsrTrackCollectionResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async fetchIsrTrackById(isrTrackId: string, load: string[] = []):Promise<IIsrTrackSingleResponse> {
            try {
                const payload: IGetParams = {
                    params: {
                        load: load.join(','),
                    },
                };
                return (await api.get(`/isr-tracks/${isrTrackId}`, payload)).data as IIsrTrackSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async createIsrTrack(isrTrack: any):Promise<IIsrTrackSingleResponse> {
            try {
                return (await api.post('/isr-tracks', isrTrack)).data as IIsrTrackSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async updateIsrTrack(isrTrackId: string, isrTrack: any):Promise<IIsrTrackSingleResponse> {
            try {
                return (await api.put(`/isr-tracks/${isrTrackId}`, isrTrack)).data as IIsrTrackSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async assignAssetsToIsrTrack(isrTrackId: string, assetIds: number[]):Promise<IIsrTrackSingleResponse> {
            try {
                return (await api.post(`/isr-tracks/${isrTrackId}/assign-assets`, { assetIds })).data as IIsrTrackSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },
        async deleteAssetFromIsrTrack(isrTrackId: string, assetId: string):Promise<IIsrTrackSingleResponse> {
            try {
                return (await api.delete(`/isr-tracks/${isrTrackId}/assign-assets/${assetId}`)).data as IIsrTrackSingleResponse;
            } catch (error) {
                return handleApiError(error);
            }
        },

        async deleteIsrTrack(isrTrackId: string) {
            try {
                const {data} = await api.delete(`/isr-tracks/${isrTrackId}`);
                return data;
            } catch (error) {
                return handleApiError(error);
            }
        },
    }
});
