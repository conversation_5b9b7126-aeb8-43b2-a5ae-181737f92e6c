// Utilities
import { defineStore } from 'pinia';
import { ActivateBody } from '@/types/ActivateBody';
import { User } from '@/types/User';
import { UpdateUserBody } from '@/types/UpdateUserBody';
import api from '@/axios';

// Define the state interface
interface AuthState {
    user: User | null;
    accessToken: string | null;
    refreshToken: string | null;
    accessRules: any;
}

export const useAuthStore = defineStore('auth', {
    persist: true,
    state: (): AuthState => ({
        user: null as User | null,
        accessToken: null,
        refreshToken: null,
        accessRules: null,
    }),

    getters: {
        isAuthenticated: (state: AuthState) => !!state.user,
        getUserRole: (state: AuthState) => {
            return state.user?.accountType;
        },
        isAdmin: (state: AuthState) => {
            return (
                state.user &&
                'accountType' in state.user &&
                state.user.accountType === 'org_admin'
            );
        },
    },
    actions: {
        setAuthTokens(accessToken: string, refreshToken: string) {
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            localStorage.setItem(
                'accessToken',
                this.accessToken ?? ('' as string),
            );
            localStorage.setItem(
                'refreshToken',
                this.refreshToken ?? ('' as string),
            );
        },
        async signIn(email: string, password: string) {
            try {
                const { data } = await api.post('/users/login', {
                    email,
                    password,
                });

                this.user = data.data.user;
                this.accessToken = data.data.accessToken;
                this.refreshToken = data.data.user.refreshToken;

                localStorage.setItem(
                    'accessToken',
                    this.accessToken ?? ('' as string),
                );
                localStorage.setItem(
                    'refreshToken',
                    this.refreshToken ?? ('' as string),
                );

                return true;
            } catch (error) {
                console.error(error);
                throw "User cannot be signed in, try another email or contact your organization's admin for assistance";
            }
        },

        async activateUser(hash: string, body: ActivateBody) {
            try {
                const { data } = await api.post(`/users/activate/${hash}`, {
                    ...body,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        signOut() {
            this.user = null;
            this.accessToken = null;
            this.refreshToken = null;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            //get rido of operations
            localStorage.removeItem('operations');
        },

        async updateUser(id: string, body: UpdateUserBody) {
            try {
                const { data } = await api.patch(`/users/${id}`, {
                    ...body,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async forgotPassword(email: string) {
            try {
                const { data } = await api.post(
                    '/users/reset-password-request',
                    {
                        email,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async changePassword(
            email: string,
            resetToken: string,
            newPassword: string,
        ) {
            try {
                // url decoded email

                const { data } = await api.post(
                    '/users/reset-password-request',
                    {
                        email,
                        resetToken,
                        newPassword,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getAccessRules() {
            try {
                const { data } = await api.get('/organizations/access-rules');
                const { accessRules } = data.data;
                this.accessRules = accessRules;
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
    },
});
