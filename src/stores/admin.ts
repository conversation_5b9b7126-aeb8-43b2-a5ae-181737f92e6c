import { defineStore } from 'pinia';
import { User } from '@/types/User';
import api from '@/axios/';
import type { Role } from '@/types/Role';
import { Organization } from '@/types/Organization';
import type { Operation } from '@/types/Operation';
import type { UserOperation } from '@/types/UserOperation';

export const useAdminStore = defineStore('admin', {
    state: () => ({
        users: [] as User[],
    }),

    actions: {
        async fetchUsers(
            page: number = 1,
            perPage: number = 10,
            orderBy: string = 'id',
            sortBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/users', {
                    params: {
                        page,
                        perPage,
                        orderBy,
                        sortBy,
                    },
                });
                this.users = data.data;

                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async assignRoleToUser(
            userId: string,
            roleIds: string[] | undefined[],
        ) {
            try {
                const { data } = await api.patch(`/users/${userId}/roles`, {
                    roleIds,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateAssignedRoles(userId: string, roleIds: string[]) {
            try {
                const { data } = await api.put(`/users/${userId}/roles`, {
                    roleIds,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteRoleFromUser(userId: string, roleId: string) {
            try {
                const { data } = await api.delete(
                    `/users/${userId}/roles/${roleId}`,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getUserById(id: string) {
            try {
                const { data } = await api.get(`/users/${id}`, {
                    params: {
                        load: 'roles, operations',
                    },
                });
                return data.data.user;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addUser(
            firstName: string,
            lastName: string,
            email: string,
            password: string,
            positionId: string = '1',
            timezone?: string,
        ) {
            try {
                const { data } = await api.post('/users', {
                    firstName,
                    lastName,
                    email,
                    password,
                    positionId,
                    timezone,
                });

                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async createUser(payload: any) {
            try {
                const { data } = await api.post('users', payload);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addRole(role: Role) {
            try {
                const { data } = await api.post('/roles', role);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteRole(id: string | undefined) {
            try {
                const { data } = await api.delete(`/roles/${id}`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async fetchRoles(
            page: number = 1,
            perPage: number = 10,
            orderBy: string = 'id',
            sortBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/roles', {
                    params: {
                        page,
                        perPage,
                        orderBy,
                        sortBy,
                    },
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getRoleById(id: string) {
            try {
                const { data } = await api.get(`/roles/${id}`, {
                    params: {
                        load: 'superiorRoles,subordinateRoles',
                    },
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async searchRoles(search: string) {
            try {
                const { data } = await api.get('/roles/search', {
                    params: {
                        searchTerm: search,
                    },
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateRole(id: string, role: Role) {
            try {
                const { data } = await api.put(`/roles/${id}`, role);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addSuperiorRoles(
            roleId: string,
            superiorRoleIds: string[] | undefined,
        ) {
            try {
                const { data } = await api.patch(
                    `/roles/${roleId}/superior-roles`,
                    {
                        superiorRoleIds,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addSubordinateRoles(
            roleId: string,
            subordinateRoleIds: string[] | undefined,
        ) {
            try {
                const { data } = await api.patch(
                    `/roles/${roleId}/subordinate-roles`,
                    {
                        subordinateRoleIds,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteSuperiorRole(
            roleId: string | undefined,
            superiorRoleId: string | undefined,
        ) {
            try {
                const { data } = await api.delete(
                    `/roles/${roleId}/superior-roles/${superiorRoleId}`,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteSubordinateRole(
            roleId: string | undefined,
            subordinateRoleId: string | undefined,
        ) {
            try {
                const { data } = await api.delete(
                    `/roles/${roleId}/subordinate-roles/${subordinateRoleId}`,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async changeSubordinateRoles(
            roleId: string | undefined,
            subordinateRoleIds: string[] | undefined,
        ) {
            try {
                const { data } = await api.put(
                    `/roles/${roleId}/subordinate-roles`,
                    {
                        subordinateRoleIds,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async changeSuperiorRoles(
            roleId: string | undefined,
            superiorRoleIds: string[] | undefined,
        ) {
            try {
                const { data } = await api.put(
                    `/roles/${roleId}/superior-roles`,
                    {
                        superiorRoleIds,
                    },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addOrganization(organization: Organization) {
            try {
                const { data } = await api.post('/organizations', organization);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async fetchOrganizations(
            page: number = 1,
            perPage: number = 10,
            sortBy: string = 'createdAt',
            orderBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/organizations', {
                    params: {
                        page,
                        perPage,
                        // sortBy,
                        // orderBy,
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async searchOrganizations(search: string) {
            try {
                const { data } = await api.get('/organizations/search', {
                    params: {
                        searchTerm: search,
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteOrganization(id: string | undefined) {
            try {
                const { data } = await api.delete(`/organizations/${id}`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getOrganization(id: string | undefined) {
            try {
                const { data } = await api.get(`/organizations/${id}`);
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateOrganization(
            id: string | undefined,
            organization: Organization,
        ) {
            try {
                const { data } = await api.put(
                    `/organizations/${id}`,
                    organization,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addOperation(operation: {
            name: string;
            locationCoordinates: {
                type: string;
                coordinates: number[];
            };
            designation: string;
            description: string;
            type: string;
        }) {
            try {
                const { data } = await api.post('/operations', operation);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async fetchOperations(
            page: number = 1,
            perPage: number = 10,
            orderBy: string = 'id',
            sortBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/operations', {
                    params: {
                        page,
                        perPage,
                        orderBy,
                        sortBy,
                        load: 'users',
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getOperationById(
            id: string | undefined,
            load: string[] = ['users', 'assets'],
        ) {
            try {
                const { data } = await api.get(`/operations/${id}`, {
                    params: {
                        load: load.join(','),
                    },
                });
                return data.data as {
                    operation: Operation;
                };
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateOperation(id: string | undefined, operation: Operation) {
            try {
                const { data } = await api.put(`/operations/${id}`, operation);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateOperationStatus(id: string | undefined, isActive: boolean) {
            try {
                const { data } = await api.patch(`/operations/${id}/status`, {
                    isActive,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteOperation(id: string | undefined) {
            try {
                const { data } = await api.delete(`/operations/${id}`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async searchOperations(search: string) {
            try {
                const { data } = await api.get('/operations/search', {
                    params: {
                        searchTerm: search,
                    },
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async assignUsersToOperation(operationId: string, payload: any) {
            try {
                const { data } = await api.post(
                    `/operations/${operationId}/add-users`,
                    payload,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async removeUserFromOperation(
            operationId: string,
            userId: string | number,
        ) {
            try {
                const { data } = await api.delete(
                    `/operations/${operationId}/remove-user/${userId}`,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateUsersListOperation(
            operationId: string,
            users: UserOperation[],
        ) {
            try {
                const { data } = await api.put(
                    `/operations/${operationId}/update-users`,
                    { users },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateSingleUserOperation(
            operationId: string,
            users: UserOperation[],
        ) {
            try {
                const { data } = await api.patch(
                    `/operations/${operationId}/update-users`,
                    { users },
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async assignUserToOperation(
            userId: string,
            operationUser: {
                operationId: string | undefined | null;
                accessType: string;
            },
        ) {
            try {
                const { data } = await api.post(
                    `/users/${userId}/operations`,
                    operationUser,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async searchPlatforms(
            searchTerm?: string,
            countryIsoCode?: string | undefined,
            platformType?: string | undefined,
        ) {
            try {
                const params: {
                    searchTerm?: string;
                    countryIsoCode?: string;
                    platformType?: string;
                } = {};
                if (searchTerm) params.searchTerm = searchTerm;
                if (countryIsoCode) params.countryIsoCode = countryIsoCode;
                if (platformType) params.platformType = platformType;

                const { data } = await api.get('/platforms/search', {
                    params,
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async createAsset(payload: any) {
            try {
                const { data } = await api.post('/assets', payload);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async resetOperation(id: number | string) {
            try {
                const { data } = await api.post(`/operations/${id}/reset`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async resetEverything() {
            try {
                const { data } = await api.post('/danger/reset');
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
    },
});
