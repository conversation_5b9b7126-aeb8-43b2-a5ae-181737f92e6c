import { defineStore } from 'pinia';

interface SnackbarState {
    show: boolean | undefined;
    text: string;
    color: 'success' | 'info' | 'warning' | 'error' | string | undefined;
    timeout: number | undefined;
    button?: {
        text: string;
        to?: string;
        action?: () => void;
    };
}

export const useSnackbarStore = defineStore('snackbar', {
    state: (): SnackbarState => ({
        show: false,
        text: '',
        color: 'info',
        timeout: 5000,
        button: undefined,
    }),
    actions: {
        showSnackbar(config: Partial<SnackbarState>) {
            this.show = true;
            this.text = config.text || '';
            this.color = config.color || 'info';
            this.timeout = config.timeout || undefined;
            this.button = config.button;
        },
        hideSnackbar() {
            this.show = false;
        },
    },
});
