import { defineStore } from 'pinia';
import api from '@/axios/';

export const useUtilsStore = defineStore('utils', {
    state: () => ({}),

    actions: {
        async getUserById(id: string) {
            try {
                const { data } = await api.get(`/users/${id}`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async updateDesignation(){
            try {
                const { data } = await api.get(`/danger/fix-designation`);
                console.log(data);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        }
    },


});
