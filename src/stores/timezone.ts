// src/stores/timezoneStore.js
import { defineStore } from 'pinia';
import { FixedOffsetZone, IANAZone } from 'luxon';

export const useTimezoneStore = defineStore('timezone', {
    state: () => ({
        mainTimezone: 'Zulu',
        otherTimezones: [] as string[],
    }),

    getters: {
        availableTimezones() {
            const today = new Date().getTime();

            // 1. Military (NATO) timezones
            const military = [
                'Mike',
                'Lima',
                'Kilo',
                'India',
                'Hotel',
                'Golf',
                'Foxtrot',
                'Echo',
                'Delta',
                'Charlie',
                'Bravo',
                'Alpha',
                'Zulu',
                'November',
                'Oscar',
                'Papa',
                'Quebec',
                'Romeo',
                'Sierra',
                'Tango',
                'Uniform',
                'Victor',
                'Whiskey',
                'X-Ray',
                'Yankee',
            ].map((name, ix) => {
                const offset = 12 - ix;
                const offsetLabel =
                    (offset < 0 ? '-' : '+') +
                    String(Math.abs(offset)).padStart(2, '0');
                return {
                    id: name,
                    label: `${name} (${offsetLabel}:00)`,
                    zone: new FixedOffsetZone(offset * 60),
                    kind: 'military',
                };
            });

            // 2. IANA timezones
            let zones = [];
            if (Intl.supportedValuesOf) {
                const ianaZones = Intl.supportedValuesOf('timeZone').map(
                    (id) => {
                        const zone = new IANAZone(id);
                        const name = zone.name.replace(/_/g, ' ');
                        const offsetName = zone.offsetName(today, {
                            format: 'long',
                            locale: 'en-AU',
                        });
                        const offset = zone.formatOffset(today, 'short');

                        return {
                            id,
                            label: `${name}/${offsetName} (${offset})`,
                            zone,
                            kind: name.startsWith('Etc') ? 'etc' : 'location',
                        };
                    },
                );

                zones = [...ianaZones, ...military];

                // Sort zones as in original React component
                zones.sort((x, y) => {
                    if (x.kind !== y.kind) {
                        return x.kind > y.kind ? -1 : 1;
                    }
                    const offsetX = x.zone.offset(today);
                    const offsetY = y.zone.offset(today);
                    if (offsetX === offsetY) {
                        return x.id < y.id ? -1 : 1;
                    }
                    return offsetX - offsetY;
                });

                return zones;
            }

            // Fallback to military zones if Intl.supportedValuesOf is not available
            return military;
        },
    },

    actions: {
        setMainTimezone(timezone: string) {
            this.mainTimezone = timezone;
        },

        addTimezone(payload: { timezone: string }) {
            this.otherTimezones.push(payload.timezone);
        },

        removeTimezone(index: number) {
            this.otherTimezones.splice(index, 1);
        },

        updateTimezone(index: number, newZone: string) {
            this.otherTimezones[index] = newZone;
        },
    },
});
