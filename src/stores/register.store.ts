import { defineStore } from 'pinia';
import api from '@/axios/';
import type { IRegistration } from '@/types/Registration.type';



export const useRegisterStore = defineStore('register', {
    actions: {
        async register(registration: IRegistration) {
            try {
                const { data } = await api.post('/register', registration);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        async verifyEmail(email: string, verifyHash: string) {
            try {
                return (await api.post('/register/verify-email', {
                    email,
                    verifyHash,
                })).data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
    },
});
