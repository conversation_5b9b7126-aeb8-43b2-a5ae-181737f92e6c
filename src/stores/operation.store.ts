import { defineStore } from 'pinia';
import api from '@/axios/';
import type { Operation } from '@/types/Operation';

interface OperationParams {
    page?: number;
    perPage?: number;
    orderBy?: string;
    sortBy?: string;
    load?: string;
    searchTerm?: string;
}

interface OperationResponse {
    data: {
        operation: Operation;
    };
}

export const useOperationStore = defineStore('operation', {
    persist: true,
    state: () => ({
        operation: null as Operation | null,
        currentOperationId: '' as string,
    }),

    getters: {
        getOperation(state) {
            return state.operation;
        },
        getCurrentOperationId(state) {
            return state.currentOperationId;
        },
    },
    actions: {
        async resetCurrentOperation(){
            this.operation = null;
            this.currentOperationId = '';
        },
        async setOperationCurrent(
            id: string,
            existingOperation: Operation | null = null,
        ) {
            if (existingOperation) {
                this.operation = existingOperation;
                this.currentOperationId =
                    existingOperation.id?.toString() || '';
                return;
            }

            const { operation } = await this.getOperationById(id);
            if (operation && operation.id) {
                this.operation = operation;
                this.currentOperationId = operation.id.toString();
            } else {
                //
            }
        },
        async refreshCurrentOperation() {
            const { operation } = await this.getOperationById(
                this.currentOperationId,
            );
            if (operation && operation.id) {
                this.operation = operation;
                this.currentOperationId = operation.id.toString();
            } else {
                //
            }
        },
        async getOperationById(
            id: string | undefined,
            load: string[] = [],
        ): Promise<OperationResponse['data']> {
            try {
                const { data } = await api.get(`/operations/${id}`, {
                    params: {
                        load: load.length ? load.join(',') : undefined,
                    } as OperationParams,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async fetchOperationWithUsers(operationId: string) {
            try {
                const { data } = await api.get(
                    `/operations/${operationId}/users`,
                );
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async getOperations(
            page: number = 1,
            perPage: number = 10,
            orderBy: string = 'id',
            sortBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/operations', {
                    params: {
                        page,
                        perPage,
                        orderBy,
                        sortBy,
                        load: 'users',
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async addOperation(operation: Operation) {
            try {
                const { data } = await api.post('/operations', operation);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async fetchOperations(
            page: number = 1,
            perPage: number = 100,
            orderBy: string = 'id',
            sortBy: string = 'desc',
        ) {
            try {
                const { data } = await api.get('/operations', {
                    params: {
                        page,
                        perPage,
                        orderBy,
                        sortBy,
                        load: 'users',
                    },
                });
                return data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async deleteOperation(id: string | undefined) {
            try {
                const { data } = await api.delete(`/operations/${id}`);
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async searchOperations(search: string) {
            try {
                const { data } = await api.get('/operations/search', {
                    params: {
                        searchTerm: search,
                    },
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

      async updateOperation(id: string, operation: Partial<Operation>) {
        try {
          const { data } = await api.put(`/operations/${id}`, operation);
          return data.data;
        } catch (error) {
          console.error(error);
          throw error;
        }
      },

        async getISRSync(
            id: number | string,
            type: string,
            currentTimestamp: null | string,
			endDate: null | Date | string,
        ) {
            try {
				let params = {};
				//if type is custom params will be startDate and endDate
				if(type === "custom") {
					params = {
						type,
						currentTimestamp,
						endDate
					}
				} else {
					params = {
						type,
						currentTimestamp
					}
				}
                const { data } = await api.get(`/operations/${id}/sync`, {
                    params: params,
                });
                return data.data;
            } catch (error) {
                console.error(error);
                throw error;
            }
        },

        async reset() {
            this.operation = null;
            this.currentOperationId = '';
        },
    },
});
