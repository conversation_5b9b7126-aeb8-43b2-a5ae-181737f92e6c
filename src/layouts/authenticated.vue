<script lang="ts" setup>
import { useAuthStore } from '@/stores/auth';

import OperationMenu from '@/components/operations/OpeationMenu.vue';
import GlobalAdminMenu from '@/components/GlobalAdminMenu.vue';
import { storeToRefs } from 'pinia';
import { ref, onMounted, provide } from 'vue';

const auth = useAuthStore();
const { accessRules } = storeToRefs(auth);
import { initializeAccessRules } from '@/composables/useAccessControl';

// State to track if rules are loaded
const rulesLoaded = ref(false);

// doing permissions call for the case if superadmin changed access rules in realtime
onMounted(async () => {
    await auth.getAccessRules();
    initializeAccessRules(accessRules.value);
    rulesLoaded.value = true;
});

// Provide the loaded state to child components
provide('accessRulesLoaded', rulesLoaded);

const drawer = ref(true);
</script>

<template>
    <v-layout class="h-screen bg-grey-lighten-4">
        <AppHeader @toggleDrawer="drawer = !drawer" />
        <v-navigation-drawer
            v-model="drawer"
            color="#1E1E1E"
            expand-on-hover
            style="overflow-y: hidden"
        >
            <v-divider></v-divider>
            <OperationMenu />
            <v-divider></v-divider>
            <GlobalAdminMenu v-if="auth.isAdmin"></GlobalAdminMenu>
        </v-navigation-drawer>
        <v-main scrollable style="overflow-y: scroll; overflow-x: scroll;">
            <div class=" mb-10 thisISTest">
                <template v-if="rulesLoaded">
                    <router-view />
                </template>
                <template v-else>
                    <div
                        class="d-flex justify-center align-center"
                        style="height: 100vh"
                    >
                        <v-progress-circular
                            indeterminate
                            color="primary"
                        ></v-progress-circular>
                    </div>
                </template>
            </div>
        </v-main>
        <!--        <v-footer class="pa-2 ma-0 text-center bg-grey" app>-->
        <!--            <small class="text-white"-->
        <!--                >Build Version:-->
        <!--                <span class="text-secondary">{{ appVersion }}> </span></small-->
        <!--            >-->
        <!--        </v-footer>-->
    </v-layout>
</template>
