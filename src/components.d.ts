/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddIR: typeof import('./components/AddIR.vue')['default']
    AddIrModal: typeof import('./components/operations/pirs/AddIrModal.vue')['default']
    AddOriginator: typeof import('./components/originators/AddOriginator.vue')['default']
    AddPirDialog: typeof import('./components/pirs/AddPirDialog.vue')['default']
    AddPIRToOperation: typeof import('./components/operations/pirs/AddPIRToOperation.vue')['default']
    AddPlatform: typeof import('./components/platforms/AddPlatform.vue')['default']
    AddPlatformToOperation: typeof import('./components/operations/assets/AddPlatformToOperation.vue')['default']
    AddPlatformToOperationSearchPlatformRow: typeof import('./components/operations/assets/AddPlatformToOperationSearchPlatformRow.vue')['default']
    AddRFIToOperation: typeof import('./components/operations/rfis/AddRFIToOperation.vue')['default']
    AddUsersToOperation: typeof import('./components/operations/users/AddUsersToOperation.vue')['default']
    AddUserToOperationSearchRow: typeof import('./components/operations/users/AddUserToOperationSearchRow.vue')['default']
    AOICard: typeof import('./components/operations/aois/AOICard.vue')['default']
    AOIRow: typeof import('./components/operations/aois/AOIRow.vue')['default']
    AppHeader: typeof import('./components/AppHeader.vue')['default']
    AssetColorPickerPopup: typeof import('./components/operations/assets/AssetColorPickerPopup.vue')['default']
    AssetDashboard: typeof import('./components/operations/assets/AssetDashboard.vue')['default']
    AssignUserTaskModal: typeof import('./components/tasks/AssignUserTaskModal.vue')['default']
    Breadcrumbs: typeof import('./components/Breadcrumbs.vue')['default']
    CollectionOverlayItems: typeof import('./components/operations/aois/CollectionOverlayItems.vue')['default']
    ColorPickerPopup: typeof import('./components/operations/aois/partial/ColorPickerPopup.vue')['default']
    ConvertToIRModal: typeof import('./components/operations/rfis/ConvertToIRModal.vue')['default']
    CreateAOIPartialPopup: typeof import('./components/operations/aois/CreateAOIPartialPopup.vue')['default']
    CreateISRTrackPartialPopup: typeof import('./components/operations/isr_tracks/CreateISRTrackPartialPopup.vue')['default']
    CreateMapElementPopup: typeof import('./components/CreateMapElementPopup.vue')['default']
    CreateNAICard: typeof import('./components/operations/aois/CreateNAICard.vue')['default']
    CreateTaskModal: typeof import('./components/tasks/CreateTaskModal.vue')['default']
    EditAOIPartialPopup: typeof import('./components/operations/aois/EditAOIPartialPopup.vue')['default']
    EditIrModal: typeof import('./components/operations/pirs/EditIrModal.vue')['default']
    EditIsr: typeof import('./components/operations/mission/isr/EditIsr.vue')['default']
    EditISRTrackPartialPopup: typeof import('./components/operations/isr_tracks/EditISRTrackPartialPopup.vue')['default']
    EditNAICard: typeof import('./components/operations/aois/EditNAICard.vue')['default']
    EditOriginatorPopup: typeof import('./components/originators/EditOriginatorPopup.vue')['default']
    EditPIRPopup: typeof import('./components/operations/pirs/EditPIRPopup.vue')['default']
    EditPlatformPopup: typeof import('./components/platforms/EditPlatformPopup.vue')['default']
    EditRFIPopup: typeof import('./components/operations/rfis/EditRFIPopup.vue')['default']
    EsriMap: typeof import('./components/EsriMap.vue')['default']
    EsriMapEditorV2: typeof import('./components/maps/esri/EsriMapEditorV2.vue')['default']
    EsriMapSingleArea: typeof import('./components/maps/esri/EsriMapSingleArea.vue')['default']
    EsriMapViewer: typeof import('./components/maps/esri/EsriMapViewer.vue')['default']
    GlobalAdminMenu: typeof import('./components/GlobalAdminMenu.vue')['default']
    GlobalEditIsr: typeof import('./components/operations/global_isr/GlobalEditIsr.vue')['default']
    GlobalListIsrs: typeof import('./components/operations/global_isr/GlobalListIsrs.vue')['default']
    GlobalViewEditIsrsMap: typeof import('./components/operations/global_isr/GlobalViewEditIsrsMap.vue')['default']
    IrRow: typeof import('./components/operations/pirs/IrRow.vue')['default']
    IsrSyncBoardAltContainer: typeof import('./components/operations/mission/IsrSyncBoardAltContainer.vue')['default']
    IsrSyncBoardContainer: typeof import('./components/operations/mission/IsrSyncBoardContainer.vue')['default']
    IsrSyncBoardPanel: typeof import('./components/operations/mission/IsrSyncBoardPanel.vue')['default']
    IsrTrackAssetsMissions: typeof import('./components/operations/mission/mission-builder/partial/IsrTrackAssetsMissions.vue')['default']
    IsrTrackItem: typeof import('./components/operations/mission/mission-builder/partial/IsrTrackItem.vue')['default']
    ListIsrs: typeof import('./components/operations/mission/isr/ListIsrs.vue')['default']
    LumioAssetTypeChip: typeof import('./components/elements/LumioAssetTypeChip.vue')['default']
    LumioDateTimePicker: typeof import('./components/elements/LumioDateTimePicker.vue')['default']
    LumioDateTimeRangePicker: typeof import('./components/elements/LumioDateTimeRangePicker.vue')['default']
    LumioFriendlyColorPicker: typeof import('./components/elements/LumioFriendlyColorPicker.vue')['default']
    LumioPriorityChip: typeof import('./components/elements/LumioPriorityChip.vue')['default']
    LumioStatusChip: typeof import('./components/elements/LumioStatusChip.vue')['default']
    LumioSyncTimeline: typeof import('./components/elements/LumioSyncTimeline.vue')['default']
    MissionAnalytics: typeof import('./components/operations/mission/MissionAnalytics.vue')['default']
    MissionAreasOfInterest: typeof import('./components/operations/mission/mission-builder/MissionAreasOfInterest.vue')['default']
    MissionBuilder: typeof import('./components/operations/mission/mission-builder/MissionBuilder.vue')['default']
    MissionCollectionPlan: typeof import('./components/operations/mission/MissionCollectionPlan.vue')['default']
    MissionCollectionWashboard: typeof import('./components/operations/mission/MissionCollectionWashboard.vue')['default']
    MissionCollectionWhitelist: typeof import('./components/operations/mission/MissionCollectionWhitelist.vue')['default']
    MissionInformation: typeof import('./components/operations/mission/mission-builder/MissionInformation.vue')['default']
    MissionInformationRequirements: typeof import('./components/operations/mission/mission-builder/MissionInformationRequirements.vue')['default']
    MissionISRTracks: typeof import('./components/operations/mission/mission-builder/MissionISRTracks.vue')['default']
    MissionLog: typeof import('./components/operations/mission/MissionLog.vue')['default']
    MissionSummary: typeof import('./components/operations/mission/mission-builder/MissionSummary.vue')['default']
    OpeationMenu: typeof import('./components/operations/OpeationMenu.vue')['default']
    OperationCard: typeof import('./components/operations/OperationCard.vue')['default']
    OperationPickerMenu: typeof import('./components/operations/OperationPickerMenu.vue')['default']
    OrganizationPickerPopup: typeof import('./components/organizations/OrganizationPickerPopup.vue')['default']
    OriginatorsList: typeof import('./components/originators/OriginatorsList.vue')['default']
    OSMTopoMap: typeof import('./components/maps/local/OSMTopoMap.vue')['default']
    OSMTopoMapV2: typeof import('./components/maps/local/OSMTopoMapV2.vue')['default']
    PirRow: typeof import('./components/operations/pirs/PirRow.vue')['default']
    RFIList: typeof import('./components/operations/rfis/RFIList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SubmitReportDialogBox: typeof import('./components/operations/mission/SubmitReportDialogBox.vue')['default']
    TasksCalendar: typeof import('./components/tasks/TasksCalendar.vue')['default']
    TimeZone: typeof import('./components/timeZone.vue')['default']
    ViewAOICard: typeof import('./components/operations/aois/ViewAOICard.vue')['default']
    ViewEditIsrsMap: typeof import('./components/operations/mission/isr/ViewEditIsrsMap.vue')['default']
    ViewManageMissions: typeof import('./components/operations/mission/ViewManageMissions.vue')['default']
    ViewManageOperationAssets: typeof import('./components/operations/assets/ViewManageOperationAssets.vue')['default']
    ViewManageOperationAssetsRow: typeof import('./components/operations/assets/ViewManageOperationAssetsRow.vue')['default']
    ViewManageOperationPIRs: typeof import('./components/operations/pirs/ViewManageOperationPIRs.vue')['default']
    ViewManageOperationUsers: typeof import('./components/operations/users/ViewManageOperationUsers.vue')['default']
    ViewManageOperationUsersRow: typeof import('./components/operations/users/ViewManageOperationUsersRow.vue')['default']
    ViewPlatformsContainer: typeof import('./components/platforms/ViewPlatformsContainer.vue')['default']
    ViewSummaryModal: typeof import('./components/operations/mission/mission-builder/ViewSummaryModal.vue')['default']
  }
}
