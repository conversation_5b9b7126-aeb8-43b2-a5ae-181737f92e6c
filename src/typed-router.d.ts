/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/404': RouteRecordInfo<'/404', '/404', Record<never, never>, Record<never, never>>,
    '/admin/add-role': RouteRecordInfo<'/admin/add-role', '/admin/add-role', Record<never, never>, Record<never, never>>,
    '/admin/add-user': RouteRecordInfo<'/admin/add-user', '/admin/add-user', Record<never, never>, Record<never, never>>,
    '/admin/edit-role/[id]': RouteRecordInfo<'/admin/edit-role/[id]', '/admin/edit-role/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/admin/operation/': RouteRecordInfo<'/admin/operation/', '/admin/operation', Record<never, never>, Record<never, never>>,
    '/admin/operation/assets/': RouteRecordInfo<'/admin/operation/assets/', '/admin/operation/assets', Record<never, never>, Record<never, never>>,
    '/admin/operation/irs/': RouteRecordInfo<'/admin/operation/irs/', '/admin/operation/irs', Record<never, never>, Record<never, never>>,
    '/admin/operation/isrs/': RouteRecordInfo<'/admin/operation/isrs/', '/admin/operation/isrs', Record<never, never>, Record<never, never>>,
    '/admin/operation/mission/builder': RouteRecordInfo<'/admin/operation/mission/builder', '/admin/operation/mission/builder', Record<never, never>, Record<never, never>>,
    '/admin/operation/pirs/': RouteRecordInfo<'/admin/operation/pirs/', '/admin/operation/pirs', Record<never, never>, Record<never, never>>,
    '/admin/operation/pirs/[id]': RouteRecordInfo<'/admin/operation/pirs/[id]', '/admin/operation/pirs/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/admin/operation/rcm/': RouteRecordInfo<'/admin/operation/rcm/', '/admin/operation/rcm', Record<never, never>, Record<never, never>>,
    '/admin/operation/tais/': RouteRecordInfo<'/admin/operation/tais/', '/admin/operation/tais', Record<never, never>, Record<never, never>>,
    '/admin/operation/users/': RouteRecordInfo<'/admin/operation/users/', '/admin/operation/users', Record<never, never>, Record<never, never>>,
    'admin-operations-index': RouteRecordInfo<'admin-operations-index', '/admin/operations', Record<never, never>, Record<never, never>>,
    '/admin/operations/[operationId]/': RouteRecordInfo<'/admin/operations/[operationId]/', '/admin/operations/:operationId', { operationId: ParamValue<true> }, { operationId: ParamValue<false> }>,
    '/admin/operations/[operationId]/edit': RouteRecordInfo<'/admin/operations/[operationId]/edit', '/admin/operations/:operationId/edit', { operationId: ParamValue<true> }, { operationId: ParamValue<false> }>,
    '/admin/operations/builder': RouteRecordInfo<'/admin/operations/builder', '/admin/operations/builder', Record<never, never>, Record<never, never>>,
    'admin-operations-create': RouteRecordInfo<'admin-operations-create', '/admin/operations/create', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step1NameType': RouteRecordInfo<'/admin/operations/steps/step1NameType', '/admin/operations/steps/step1NameType', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step2AreaOfOperation': RouteRecordInfo<'/admin/operations/steps/step2AreaOfOperation', '/admin/operations/steps/step2AreaOfOperation', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step3AreaOfResponsibilityTimezone': RouteRecordInfo<'/admin/operations/steps/step3AreaOfResponsibilityTimezone', '/admin/operations/steps/step3AreaOfResponsibilityTimezone', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step4Pirs': RouteRecordInfo<'/admin/operations/steps/step4Pirs', '/admin/operations/steps/step4Pirs', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step5Boards': RouteRecordInfo<'/admin/operations/steps/step5Boards', '/admin/operations/steps/step5Boards', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step6DataSources': RouteRecordInfo<'/admin/operations/steps/step6DataSources', '/admin/operations/steps/step6DataSources', Record<never, never>, Record<never, never>>,
    '/admin/operations/steps/step7Plugins': RouteRecordInfo<'/admin/operations/steps/step7Plugins', '/admin/operations/steps/step7Plugins', Record<never, never>, Record<never, never>>,
    '/admin/organizations/': RouteRecordInfo<'/admin/organizations/', '/admin/organizations', Record<never, never>, Record<never, never>>,
    '/admin/organizations/create': RouteRecordInfo<'/admin/organizations/create', '/admin/organizations/create', Record<never, never>, Record<never, never>>,
    '/admin/organizations/edit/[id]': RouteRecordInfo<'/admin/organizations/edit/[id]', '/admin/organizations/edit/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/admin/originators/': RouteRecordInfo<'/admin/originators/', '/admin/originators', Record<never, never>, Record<never, never>>,
    '/admin/users': RouteRecordInfo<'/admin/users', '/admin/users', Record<never, never>, Record<never, never>>,
    '/admin/view-role/[id]': RouteRecordInfo<'/admin/view-role/[id]', '/admin/view-role/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/admin/view-roles': RouteRecordInfo<'/admin/view-roles', '/admin/view-roles', Record<never, never>, Record<never, never>>,
    '/admin/view-user/[id]': RouteRecordInfo<'/admin/view-user/[id]', '/admin/view-user/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/edit-user/[id]': RouteRecordInfo<'/edit-user/[id]', '/edit-user/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/forgot-password': RouteRecordInfo<'/forgot-password', '/forgot-password', Record<never, never>, Record<never, never>>,
    '/home': RouteRecordInfo<'/home', '/home', Record<never, never>, Record<never, never>>,
    '/map': RouteRecordInfo<'/map', '/map', Record<never, never>, Record<never, never>>,
    '/map2': RouteRecordInfo<'/map2', '/map2', Record<never, never>, Record<never, never>>,
    '/platforms': RouteRecordInfo<'/platforms', '/platforms', Record<never, never>, Record<never, never>>,
    '/register': RouteRecordInfo<'/register', '/register', Record<never, never>, Record<never, never>>,
    '/reset-password': RouteRecordInfo<'/reset-password', '/reset-password', Record<never, never>, Record<never, never>>,
    '/signIn': RouteRecordInfo<'/signIn', '/signIn', Record<never, never>, Record<never, never>>,
    '/task/[id]': RouteRecordInfo<'/task/[id]', '/task/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    '/under-development/[page]': RouteRecordInfo<'/under-development/[page]', '/under-development/:page', { page: ParamValue<true> }, { page: ParamValue<false> }>,
    '/update-designation-vf32fas690': RouteRecordInfo<'/update-designation-vf32fas690', '/update-designation-vf32fas690', Record<never, never>, Record<never, never>>,
    '/verify': RouteRecordInfo<'/verify', '/verify', Record<never, never>, Record<never, never>>,
    '/weather-board': RouteRecordInfo<'/weather-board', '/weather-board', Record<never, never>, Record<never, never>>,
  }
}
