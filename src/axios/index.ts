import axios from 'axios';

const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
});

// Function to get the access token from storage
const getAccessToken = () => {
    return localStorage.getItem('accessToken');
};

// Function to get the refresh token from storage
const getRefreshToken = () => {
    return localStorage.getItem('refreshToken');
};

// Function to set the access token in storage
const setAccessToken = (token: string) => {
    localStorage.setItem('accessToken', token);
};

// Function to set the refresh token in storage
const setRefreshToken = (token: string) => {
    localStorage.setItem('refreshToken', token);
};

// Function to refresh the access token
const refreshAccessToken = async () => {
    try {
        const response = await axios.post(
            `${import.meta.env.VITE_API_URL}/users/refresh-token`,
            {
                refreshToken: getRefreshToken(),
            },
        );
        const { accessToken, refreshToken } = response.data;
        setAccessToken(accessToken);
        setRefreshToken(refreshToken);
        return accessToken;
    } catch (error) {
        // Handle refresh token error (e.g., logout user)
        // You might want to redirect to login page or dispatch a logout action here
        throw error;
    }
};

// Request interceptor
api.interceptors.request.use(
    (config) => {
        const token = getAccessToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);

// Response interceptor
api.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        // If the error is due to an expired token (assuming server returns 401)
        if (error.response.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                const accessToken = await refreshAccessToken();
                axios.defaults.headers.common[
                    'Authorization'
                ] = `Bearer ${JSON.parse(accessToken)}`;
                return api(originalRequest);
            } catch (refreshError) {
                // Handle refresh failure (e.g., redirect to login)
                return Promise.reject(refreshError);
            }
        }

        return Promise.reject(error);
    },
);

export default api;
