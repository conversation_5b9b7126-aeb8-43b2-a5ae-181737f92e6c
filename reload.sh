#!/bin/bash

set -e

echo "Stopping all running containers..."
docker stop $(docker ps -aq) 2>/dev/null || true

echo "Removing all containers..."
sudo docker rm $(sudo docker ps -aq) 2>/dev/null || true

echo "Removing all images..."
sudo docker rmi $(sudo docker images -q) 2>/dev/null || true

echo "Removing all networks..."
docker network rm $(docker network ls -q) 2>/dev/null || true

echo "Rebuilding and starting containers..."
docker-compose up --build -d

echo "Process completed. New containers are now running in detached mode."

echo "Current running containers:"
docker ps