{
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vite/client",
      "vite-plugin-vue-layouts/client",
      "unplugin-vue-router/client",
      "node"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "src/**/*",
    "src/**/*.vue",
    "src/env.d.ts"
  ],
  "exclude": ["dist", "node_modules", "cypress"],
  "references": [{ "path": "./tsconfig.node.json" }],
}
